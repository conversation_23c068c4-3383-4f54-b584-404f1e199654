'use client';

import { PlusCircle, Search, Settings, Users } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import React, { useState } from 'react';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import {
  RecipientFilters,
  RecipientList,
} from '@/components/features/recipients';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { useRecipients } from '@/lib/stores/queries/useRecipients';

interface RecipientFilterValues {
  hasAddress: boolean;
  hasEmail: boolean;
  hasPhone: boolean;
  search: string;
}

export default function RecipientsPage() {
  const t = useTranslations('recipients');
  const tCommon = useTranslations('common');
  const tForms = useTranslations('forms');

  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<RecipientFilterValues>({
    hasAddress: false,
    hasEmail: false,
    hasPhone: false,
    search: '',
  });

  // Build query filters for API
  const queryFilters = {
    ...(filters.search && { search: filters.search }),
    ...(filters.hasEmail && { hasEmail: true }),
    ...(filters.hasPhone && { hasPhone: true }),
    ...(filters.hasAddress && { hasAddress: true }),
  };

  // Fetch data
  const {
    data: recipients = [],
    error,
    isLoading,
    refetch,
  } = useRecipients(queryFilters);

  // Handle filter changes
  const handleFiltersChange = (newFilters: RecipientFilterValues) => {
    setFilters(newFilters);
    setSearchTerm(newFilters.search);
  };

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <AppBreadcrumb homeHref="/" homeLabel={tCommon('dashboard')} />
        <PageHeader
          description={t('pageDescription')}
          icon={Users}
          title={t('pageTitle')}
        >
          <div className="flex items-center gap-2">
            <ActionButton
              actionType="primary"
              asChild
              icon={<PlusCircle className="mr-2 size-4" />}
            >
              <Link href="/recipients/add">{t('addRecipient')}</Link>
            </ActionButton>

            {/* Settings Button */}
            <Dialog>
              <DialogTrigger asChild>
                <ActionButton
                  actionType="secondary"
                  icon={<Settings className="size-4" />}
                >
                  {tCommon('settings')}
                </ActionButton>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogTitle>{t('recipientSettings')}</DialogTitle>
                <DialogDescription>
                  {t('recipientSettingsDescription')}
                </DialogDescription>
                {/* TODO: Add recipient dashboard settings component */}
                <div className="p-4 text-center text-muted-foreground">
                  {tCommon('comingSoon')}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </PageHeader>

        {/* Search and Filters */}
        <div className="mb-6 rounded-lg bg-card p-4 shadow-md">
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                className="pl-10"
                onChange={e => {
                  setSearchTerm(e.target.value);
                  setFilters(prev => ({ ...prev, search: e.target.value }));
                }}
                placeholder={tForms('placeholders.searchRecipients')}
                value={searchTerm}
              />
            </div>
          </div>

          <RecipientFilters
            filters={filters}
            hasActiveFilters={Object.keys(filters).length > 0}
            onClearFilters={() => setFilters({})}
            onFilterChange={handleFiltersChange}
            recipients={recipients || []}
          />
        </div>

        {/* Recipient List */}
        <DataLoader
          data={recipients}
          emptyComponent={
            <div className="rounded-lg bg-card py-12 text-center shadow-md">
              <Users className="mx-auto mb-6 size-16 text-muted-foreground" />
              <h3 className="mb-2 text-2xl font-semibold text-foreground">
                {searchTerm || Object.values(queryFilters).some(Boolean)
                  ? t('noRecipientsMatch')
                  : t('noRecipientsYet')}
              </h3>
              <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
                {searchTerm || Object.values(queryFilters).some(Boolean)
                  ? t('tryAdjustingFilters')
                  : t('getStartedWithRecipients')}
              </p>
              {!searchTerm && !Object.values(queryFilters).some(Boolean) && (
                <ActionButton
                  actionType="primary"
                  asChild
                  icon={<PlusCircle className="size-4" />}
                  size="lg"
                >
                  <Link href="/recipients/add">{t('addFirstRecipient')}</Link>
                </ActionButton>
              )}
            </div>
          }
          error={error?.message || null}
          isLoading={isLoading}
          loadingComponent={<SkeletonLoader count={3} variant="card" />}
          onRetry={refetch}
        >
          {recipientsData => <RecipientList recipients={recipientsData} />}
        </DataLoader>
      </div>
    </ErrorBoundary>
  );
}
