"use strict";exports.id=6413,exports.ids=[6413],exports.modules={15079:(e,t,s)=>{s.d(t,{bq:()=>d,eb:()=>h,gC:()=>g,l6:()=>l,yv:()=>m});var r=s(60687),i=s(22670),a=s(61662),n=s(89743),o=s(58450),c=s(43210),u=s(22482);let l=i.bL;i.YJ;let m=i.WT,d=c.forwardRef(({children:e,className:t,...s},n)=>(0,r.jsxs)(i.l9,{className:(0,u.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:n,...s,children:[e,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(a.A,{className:"size-4 opacity-50"})})]}));d.displayName=i.l9.displayName;let f=c.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.PP,{className:(0,u.cn)("flex cursor-default items-center justify-center py-1",e),ref:s,...t,children:(0,r.jsx)(n.A,{className:"size-4"})}));f.displayName=i.PP.displayName;let p=c.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.wn,{className:(0,u.cn)("flex cursor-default items-center justify-center py-1",e),ref:s,...t,children:(0,r.jsx)(a.A,{className:"size-4"})}));p.displayName=i.wn.displayName;let g=c.forwardRef(({children:e,className:t,position:s="popper",...a},n)=>(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{className:(0,u.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,ref:n,...a,children:[(0,r.jsx)(f,{}),(0,r.jsx)(i.LM,{className:(0,u.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,r.jsx)(p,{})]})}));g.displayName=i.UC.displayName,c.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.JU,{className:(0,u.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:s,...t})).displayName=i.JU.displayName;let h=c.memo(c.forwardRef(({children:e,className:t,...s},a)=>{let n=c.useCallback(e=>{"function"==typeof a?a(e):a&&(a.current=e)},[a]);return(0,r.jsxs)(i.q7,{className:(0,u.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:n,...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(o.A,{className:"size-4"})})}),(0,r.jsx)(i.p4,{children:e})]})}));h.displayName=i.q7.displayName,c.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.wv,{className:(0,u.cn)("-mx-1 my-1 h-px bg-muted",e),ref:s,...t})).displayName=i.wv.displayName},19203:(e,t,s)=>{s.d(t,{P:()=>i});var r=s(49278);class i{static showSuccessToast(e,t,s){if(!e.showSuccessToast)return;let{entityType:i,entity:a,successMessage:n}=e;try{switch(i){case"employee":a?r.Ok.entityCreated(a):r.JP.success("Employee Created",n);break;case"vehicle":a?r.G7.entityCreated(a):r.JP.success("Vehicle Added",n);break;case"task":a?r.z0.entityCreated(a):r.JP.success("Task Created",n);break;case"delegation":a?r.Qu.entityCreated(a):r.JP.success("Delegation Created",n);break;case"serviceRecord":a&&s?r.oz.serviceRecordCreated(a.vehicleName||"Vehicle",a.serviceType||"Service"):r.JP.success("Service Record Added",n);break;default:r.JP.success("Success",n||"Operation completed successfully")}}catch(e){r.JP.success("Success",n||"Operation completed successfully")}}static showErrorToast(e,t,s){if(!e.showErrorToast)return;let{entityType:i,errorMessage:a}=e,n=t.message||a||"An unexpected error occurred";try{switch(i){case"employee":r.Ok.entityCreationError(n);break;case"vehicle":r.G7.entityCreationError(n);break;case"task":r.z0.entityCreationError(n);break;case"delegation":r.Qu.entityCreationError(n);break;case"serviceRecord":r.oz.serviceRecordCreationError(n);break;default:r.JP.error("Error",n)}}catch(e){r.JP.error("Error",n)}}static showUpdateSuccessToast(e,t,s){if(!e.showSuccessToast)return;let{entityType:i,entity:a,successMessage:n}=e;try{switch(i){case"employee":a?r.Ok.entityUpdated(a):r.JP.success("Employee Updated",n);break;case"vehicle":a?r.G7.entityUpdated(a):r.JP.success("Vehicle Updated",n);break;case"task":a?r.z0.entityUpdated(a):r.JP.success("Task Updated",n);break;case"delegation":a?r.Qu.entityUpdated(a):r.JP.success("Delegation Updated",n);break;case"serviceRecord":a&&s?r.oz.serviceRecordUpdated(a.vehicleName||"Vehicle",a.serviceType||"Service"):r.JP.success("Service Record Updated",n);break;default:r.JP.success("Success",n||"Update completed successfully")}}catch(e){r.JP.success("Success",n||"Update completed successfully")}}static showUpdateErrorToast(e,t,s){if(!e.showErrorToast)return;let{entityType:i,errorMessage:a}=e,n=t.message||a||"An unexpected error occurred";try{switch(i){case"employee":r.Ok.entityUpdateError(n);break;case"vehicle":r.G7.entityUpdateError(n);break;case"task":r.z0.entityUpdateError(n);break;case"delegation":r.Qu.entityUpdateError(n);break;case"serviceRecord":r.oz.serviceRecordUpdateError(n);break;default:r.JP.error("Update Failed",n)}}catch(e){r.JP.error("Update Failed",n)}}static createCustomEntityToastService(e,t){return(0,r.Gb)(e,t)}}},28439:(e,t,s)=>{s.d(t,{SY:()=>o});let r={maxAttempts:3,delay:1e3,exponentialBackoff:!0,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")||e.message.includes("502")||e.message.includes("503")||e.message.includes("504")},i={announceStatus:!0,focusManagement:"first-error",screenReaderAnnouncements:!0},a={debounceMs:300,enableDeduplication:!0,cacheResults:!1,timeoutMs:3e4},n={showSuccessToast:!0,showErrorToast:!0,successMessage:"Operation completed successfully",errorMessage:"An unexpected error occurred",entityType:"generic"};class o{static mergeRetryConfig(e){return{...r,...e}}static mergeAccessibilityConfig(e){return{...i,...e}}static mergePerformanceConfig(e){return{...a,...e}}static mergeToastConfig(e){return{...n,...e}}}},54827:(e,t,s)=>{s.d(t,{k:()=>u});var r=s(43210),i=s(28439),a=s(19203);class n{constructor(e){this.config=e}announceStatus(e,t="polite"){if(!this.config.announceStatus||!this.config.screenReaderAnnouncements)return;let s=document.getElementById("form-submission-announcements");s||((s=document.createElement("div")).id="form-submission-announcements",s.setAttribute("aria-live",t),s.setAttribute("aria-atomic","true"),s.className="sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden",document.body.appendChild(s)),s.textContent=e,setTimeout(()=>{s&&s.textContent===e&&(s.textContent="")},1e3)}generateAriaAttributes(e,t,s){return{"aria-busy":e,"aria-invalid":t,"aria-describedby":this.config.errorDescribedBy||(t?"form-error":void 0),"aria-live":"submitting"===s||"validating"===s?"polite":"off"}}manageFocus(e,t){if("none"!==this.config.focusManagement)switch(e){case"error":"first-error"===this.config.focusManagement&&t&&t("first-error");break;case"success":if("success-message"===this.config.focusManagement){let e=document.getElementById("form-success-message");e&&e.focus()}else"next-field"===this.config.focusManagement&&t&&t("next-field");break;case"retry":t&&t("retry-button")}}createErrorMessage(e){let t=document.createElement("div");return t.id=this.config.errorDescribedBy||"form-error",t.setAttribute("role","alert"),t.setAttribute("aria-live","assertive"),t.className="sr-only",t.textContent=e,t}updateErrorMessage(e){let t=this.config.errorDescribedBy||"form-error",s=document.getElementById(t);e?s?s.textContent=e:(s=this.createErrorMessage(e),document.body.appendChild(s)):s&&s.remove()}getStatusMessage(e,t,s){switch(e){case"validating":return"Validating form data...";case"submitting":return"Submitting form...";case"retrying":return`Retrying submission... (Attempt ${t||1}/${s||3})`;case"success":return"Form submitted successfully";case"error":return"Form submission failed";default:return""}}setupKeyboardNavigation(){let e=e=>{if("Escape"===e.key){let e=document.querySelector("[data-form-cancel]");e&&e.click()}if((e.ctrlKey||e.metaKey)&&"Enter"===e.key){let e=document.querySelector('[type="submit"]');e&&!e.disabled&&e.click()}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}announceProgress(e,t,s){if(!this.config.screenReaderAnnouncements)return;let r=`Step ${e} of ${t}: ${s}`;this.announceStatus(r,"polite")}cleanup(){let e=document.getElementById("form-submission-announcements");e&&e.remove(),this.updateErrorMessage(null)}}class o{constructor(e){this.currentAttempt=0,this.config=e}shouldRetry(e){return this.currentAttempt<this.config.maxAttempts&&(!this.config.retryCondition||this.config.retryCondition(e))}getRetryDelay(){let e=this.config.delay;return this.config.exponentialBackoff?e*Math.pow(2,this.currentAttempt):e}incrementAttempt(){return this.currentAttempt+=1,this.currentAttempt}resetAttempts(){this.currentAttempt=0}getCurrentAttempt(){return this.currentAttempt}getMaxAttempts(){return this.config.maxAttempts}async sleep(e){return new Promise(t=>setTimeout(t,e))}async executeRetry(e){if(!this.shouldRetry(Error("Manual retry")))throw Error("Maximum retry attempts exceeded");let t=this.getRetryDelay();return this.incrementAttempt(),await this.sleep(t),e()}getRetryStatus(){return{currentAttempt:this.currentAttempt,maxAttempts:this.config.maxAttempts,hasRetriesLeft:this.currentAttempt<this.config.maxAttempts,nextDelay:this.getRetryDelay()}}withConfig(e){return new o({...this.config,...e})}}class c{constructor(e){this.submissionStartTime=null,this.debounceTimer=null,this.config=e,this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}startTiming(){this.submissionStartTime=Date.now()}endTiming(e){if(!this.submissionStartTime)return 0;let t=Date.now()-this.submissionStartTime;return this.updateMetrics(e,t),this.submissionStartTime=null,t}updateMetrics(e,t){let s=this.metrics.totalSubmissions+1,r=e?this.metrics.successfulSubmissions+1:this.metrics.successfulSubmissions,i=e?this.metrics.failedSubmissions:this.metrics.failedSubmissions+1,a=this.metrics.averageDuration*this.metrics.totalSubmissions+t;this.metrics={totalSubmissions:s,successfulSubmissions:r,failedSubmissions:i,averageDuration:a/s}}getMetrics(){return{...this.metrics}}resetMetrics(){this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}debounce(e,t=this.config.debounceMs){return(...s)=>{this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout(()=>{e(...s)},t)}}clearDebounce(){this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null)}createTimeoutPromise(e=this.config.timeoutMs){return new Promise((t,s)=>{setTimeout(()=>{s(Error(`Request timeout after ${e}ms`))},e)})}async withTimeout(e,t=this.config.timeoutMs){return Promise.race([e,this.createTimeoutPromise(t)])}getSuccessRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.successfulSubmissions/this.metrics.totalSubmissions*100}getFailureRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.failedSubmissions/this.metrics.totalSubmissions*100}isPerformanceAcceptable(e){let t={maxAverageDuration:5e3,minSuccessRate:95,...e};return this.metrics.averageDuration<=t.maxAverageDuration&&this.getSuccessRate()>=t.minSuccessRate}generateReport(){let e=this.getSuccessRate(),t=this.getFailureRate(),s=this.isPerformanceAcceptable(),r=[];return this.metrics.averageDuration>3e3&&r.push("Consider optimizing form validation or submission logic"),e<90&&r.push("High failure rate detected - review error handling"),this.metrics.totalSubmissions>100&&this.metrics.averageDuration>1e3&&r.push("Consider implementing caching for better performance"),{metrics:this.getMetrics(),successRate:e,failureRate:t,isAcceptable:s,recommendations:r}}cleanup(){this.clearDebounce(),this.submissionStartTime=null}}let u=(e,t={})=>{let s=i.SY.mergeRetryConfig(t.retry),u=i.SY.mergeAccessibilityConfig(t.accessibility),l=i.SY.mergePerformanceConfig(t.performance),m=i.SY.mergeToastConfig(t.toast),d=(0,r.useRef)(new n(u)).current,f=(0,r.useRef)(new o(s)).current,p=(0,r.useRef)(new c(l)).current,[g,h]=(0,r.useState)("idle"),[b,y]=(0,r.useState)(null),[S,w]=(0,r.useState)(null),[x,v]=(0,r.useState)(null),[A,C]=(0,r.useState)(null),[R,k]=(0,r.useState)(null),[E,T]=(0,r.useState)(null),M=(0,r.useRef)(null),P="submitting"===g||"validating"===g,N="success"===g,D="validating"===g,U="retrying"===g,j=f.getCurrentAttempt();(0,r.useEffect)(()=>()=>{M.current&&M.current.abort(),p.cleanup(),d.cleanup()},[p,d]);let J=(0,r.useCallback)(()=>{y(null),w(null),d.updateErrorMessage(null),"error"===g&&h("idle")},[g,d]),z=(0,r.useCallback)(()=>{h("idle"),y(null),w(null),v(null),C(null),k(null),T(null),f.resetAttempts(),p.resetMetrics(),d.updateErrorMessage(null)},[f,p,d]),F=(0,r.useCallback)(()=>{M.current&&M.current.abort(),h("idle"),d.announceStatus("Form submission cancelled")},[d]),V=(0,r.useCallback)(async(r,i=!1)=>{try{p.startTiming(),M.current=new AbortController;let n=i?"retrying":"submitting";h(n);let o=d.getStatusMessage(n,j,s.maxAttempts);if(d.announceStatus(o),t.onSubmitStart&&await t.onSubmitStart(r),t.preSubmitValidation&&(h("validating"),d.announceStatus("Validating form data..."),!await t.preSubmitValidation(r)))throw Error("Validation failed");h(n);let c=r;t.transformData&&(c=await t.transformData(r));let u=e(c),l=await p.withTimeout(u),g=l;if(t.transformResult&&(g=await t.transformResult(l)),t.postSubmitValidation&&!await t.postSubmitValidation(g))throw Error("Post-submission validation failed");let b=p.endTiming(!0);h("success"),k(g),C(Date.now()),v(r),T(b),f.resetAttempts(),a.P.showSuccessToast(m,r,g),d.announceStatus("Form submitted successfully","assertive"),d.manageFocus("success",t.formFocus),t.resetOnSuccess&&t.formReset&&t.formReset(),t.onSuccess&&await t.onSuccess(r,g),t.onSubmitComplete&&await t.onSubmitComplete(r,!0)}catch(c){let e=c instanceof Error?c:Error(String(c)),n=p.endTiming(!1);if(!i&&f.shouldRetry(e)){h("retrying");let e=f.getRetryDelay();return f.incrementAttempt(),d.announceStatus(`Retrying in ${e}ms... (Attempt ${f.getCurrentAttempt()}/${s.maxAttempts})`),await f.sleep(e),V(r,!0)}h("error");let o=e.message||m.errorMessage||"An unexpected error occurred";y(o),w(e),T(n),a.P.showErrorToast(m,e,r),d.updateErrorMessage(o),d.announceStatus(`Error: ${o}`,"assertive"),d.manageFocus("error",t.formFocus),t.onError&&await t.onError(e,r),t.onSubmitComplete&&await t.onSubmitComplete(r,!1)}},[e,t,f,p,d,m,s.maxAttempts,j]),$=(0,r.useCallback)(async(e,t)=>{t&&t.preventDefault(),p.debounce(()=>V(e),l.debounceMs)()},[V,p,l.debounceMs]),B=(0,r.useCallback)(async()=>{x&&(f.resetAttempts(),await V(x))},[x,V,f]),O=d.generateAriaAttributes(P,!!b,g);return{isLoading:P,state:g,error:b,errorObject:S,isSuccess:N,isValidating:D,isRetrying:U,lastSubmittedData:x,lastSubmitted:A,lastResult:R,retryAttempt:j,handleSubmit:$,clearError:J,reset:z,retry:B,cancel:F,ariaAttributes:O,submissionDuration:E,metrics:p.getMetrics()}}}};