"use strict";exports.id=5563,exports.ids=[5563],exports.modules={18116:(e,s,a)=>{a.d(s,{A:()=>d});var l=a(60687),t=a(24851),r=a(43210),i=a(22482);let d=r.forwardRef(({className:e,...s},a)=>(0,l.jsxs)(t.bL,{className:(0,i.cn)("relative flex w-full touch-none select-none items-center",e),ref:a,...s,children:[(0,l.jsx)(t.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,l.jsx)(t.Q6,{className:"absolute h-full bg-primary"})}),(0,l.jsx)(t.zi,{className:"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));d.displayName=t.bL.displayName},26373:(e,s,a)=>{a.d(s,{V:()=>n});var l=a(60687),t=a(43967),r=a(74158);a(43210);var i=a(16488),d=a(29523),c=a(22482);function n({className:e,classNames:s,showOutsideDays:a=!0,...n}){return(0,l.jsx)(i.hv,{className:(0,c.cn)("p-3",e),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.cn)((0,d.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,c.cn)((0,d.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...s},components:{IconLeft:({className:e,...s})=>(0,l.jsx)(t.A,{className:(0,c.cn)("h-4 w-4",e),...s}),IconRight:({className:e,...s})=>(0,l.jsx)(r.A,{className:(0,c.cn)("h-4 w-4",e),...s})},showOutsideDays:a,...n})}n.displayName="Calendar"},37392:(e,s,a)=>{a.d(s,{s:()=>y});var l=a(60687),t=a(86447),r=a(3746),i=a(93704),d=a(26622),c=a(36141),n=a(58369),o=a(2093),u=a(77368);a(43210);var m=a(29523),x=a(80013),f=a(50812),h=a(18116),p=a(54987),v=a(85763);let b={cards:{icon:t.A,label:"Cards"},table:{icon:r.A,label:"Table"},list:{icon:i.A,label:"List"},calendar:{icon:d.A,label:"Calendar"},grid:{icon:c.A,label:"Grid"}},y=({config:e,entityType:s,layout:a,monitoring:r,setViewMode:i,setGridColumns:d,toggleCompactMode:c,setMonitoringEnabled:y,setRefreshInterval:g,toggleAutoRefresh:j,resetSettings:N,className:w=""})=>{let C=e.viewModes||["cards","table","list"];return(0,l.jsxs)("div",{className:`space-y-6 p-4 ${w}`,children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,l.jsx)(n.A,{className:"size-6"}),e.title," Settings"]}),(0,l.jsxs)("p",{className:"text-muted-foreground",children:["Customize your ",s," dashboard experience"]})]}),(0,l.jsxs)(v.tU,{className:"w-full",defaultValue:"layout",children:[(0,l.jsxs)(v.j7,{className:"grid w-full grid-cols-3",children:[(0,l.jsx)(v.Xi,{value:"layout",children:"Layout"}),(0,l.jsx)(v.Xi,{value:"display",children:"Display"}),(0,l.jsx)(v.Xi,{value:"refresh",children:"Refresh"})]}),(0,l.jsx)(v.av,{className:"mt-4 space-y-6",value:"layout",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(x.J,{className:"text-lg font-semibold",children:"View Mode"}),(0,l.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Choose how ",s,"s are displayed"]}),(0,l.jsx)(f.z,{className:"grid grid-cols-2 gap-4 pt-2",onValueChange:e=>i(e),value:a.viewMode,children:C.map(e=>{let s=b[e]?.icon||t.A,a=b[e]?.label||e;return(0,l.jsxs)(x.J,{className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer",htmlFor:`layout-${e}`,children:[(0,l.jsx)(f.C,{className:"sr-only",id:`layout-${e}`,value:e}),(0,l.jsx)(s,{className:"mb-3 size-6"}),a]},e)})})]}),("cards"===a.viewMode||"grid"===a.viewMode)&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(x.J,{className:"text-lg font-semibold",children:["Grid Columns: ",a.gridColumns]}),(0,l.jsx)(h.A,{defaultValue:[a.gridColumns],max:6,min:1,onValueChange:([e])=>void 0!==e&&d(e),step:1}),(0,l.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground",children:[(0,l.jsx)("span",{children:"1 column"}),(0,l.jsx)("span",{children:"6 columns"})]})]})]})}),(0,l.jsx)(v.av,{className:"mt-4 space-y-6",value:"display",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(x.J,{className:"text-lg font-semibold",children:"Compact Mode"}),(0,l.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Show more ",s,"s in less space"]})]}),(0,l.jsx)(p.d,{checked:a.compactMode,onCheckedChange:c})]}),e.enableBulkActions&&(0,l.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(x.J,{className:"text-lg font-semibold",children:"Bulk Actions"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable selection and bulk operations"})]}),(0,l.jsx)(p.d,{checked:!0,disabled:!0})]})]})}),(0,l.jsx)(v.av,{className:"mt-4 space-y-6",value:"refresh",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsxs)(x.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,l.jsx)(o.A,{className:"size-4"}),"Auto Refresh"]}),(0,l.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Automatically refresh ",s," data"]})]}),(0,l.jsx)(p.d,{checked:r.autoRefresh,onCheckedChange:j})]}),r.autoRefresh&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(x.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,l.jsx)(u.A,{className:"size-4"}),"Refresh Interval"]}),(0,l.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{label:"5 seconds",value:5e3},{label:"10 seconds",value:1e4},{label:"30 seconds",value:3e4},{label:"1 minute",value:6e4},{label:"5 minutes",value:3e5}].map(e=>(0,l.jsx)(m.$,{variant:r.refreshInterval===e.value?"default":"outline",size:"sm",onClick:()=>g(e.value),children:e.label},e.value))})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(x.J,{className:"text-lg font-semibold",children:"Real-time Updates"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable live data updates"})]}),(0,l.jsx)(p.d,{checked:r.enabled,onCheckedChange:y})]})]})})]}),(0,l.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,l.jsx)(m.$,{onClick:N,variant:"outline",children:"Reset to Defaults"})})]})}},40988:(e,s,a)=>{a.d(s,{AM:()=>d,Wv:()=>c,hl:()=>n});var l=a(60687),t=a(40599),r=a(43210),i=a(22482);let d=t.bL,c=t.l9;t.bm;let n=r.forwardRef(({align:e="center",className:s,sideOffset:a=4,...r},d)=>(0,l.jsx)(t.ZL,{children:(0,l.jsx)(t.UC,{align:e,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",s),ref:d,sideOffset:a,...r})}));n.displayName=t.UC.displayName},50812:(e,s,a)=>{a.d(s,{C:()=>n,z:()=>c});var l=a(60687),t=a(14555),r=a(73256),i=a(43210),d=a(22482);let c=i.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.bL,{className:(0,d.cn)("grid gap-2",e),...s,ref:a}));c.displayName=t.bL.displayName;let n=i.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.q7,{className:(0,d.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s,children:(0,l.jsx)(t.C1,{className:"flex items-center justify-center",children:(0,l.jsx)(r.A,{className:"size-2.5 fill-current text-current"})})}));n.displayName=t.q7.displayName},72273:(e,s,a)=>{a.d(s,{NS:()=>f,T$:()=>o,W_:()=>u,Y1:()=>m,lR:()=>x});var l=a(8693),t=a(54050),r=a(46349),i=a(87676),d=a(48839),c=a(49603);let n={all:["vehicles"],detail:e=>["vehicles",e]},o=e=>(0,r.GK)([...n.all],async()=>(await c.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,s)=>(0,r.GK)([...n.detail(e)],()=>c.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(s?.enabled??!0),staleTime:3e5,...s}),m=()=>{let e=(0,l.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,t.n)({mutationFn:e=>{let s=d.M.toCreateRequest(e);return c.vehicleApiService.create(s)},onError:e=>{s(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:s=>{e.invalidateQueries({queryKey:n.all}),a(`Vehicle "${s.licensePlate}" has been created successfully!`)}})},x=()=>{let e=(0,l.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,t.n)({mutationFn:({data:e,id:s})=>{let a=d.M.toUpdateRequest(e);return c.vehicleApiService.update(s,a)},onError:e=>{s(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:s=>{e.invalidateQueries({queryKey:n.all}),e.invalidateQueries({queryKey:n.detail(s.id)}),a(`Vehicle "${s.licensePlate}" has been updated successfully!`)}})},f=()=>{let e=(0,l.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,t.n)({mutationFn:e=>c.vehicleApiService.delete(e),onError:e=>{s(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(s,l)=>{e.invalidateQueries({queryKey:n.all}),e.removeQueries({queryKey:n.detail(l)}),a("Vehicle has been deleted successfully!")}})}}};