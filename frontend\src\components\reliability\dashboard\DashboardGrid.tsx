/**
 * @file Responsive dashboard grid component with widget management.
 * This component manages the layout and rendering of dashboard widgets based on user preferences.
 * @module components/reliability/dashboard/DashboardGrid
 */

'use client';

import React from 'react';

import { WidgetContainer } from './WidgetContainer';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  CircuitBreakerOverview,
  CircuitBreakerList,
  CircuitBreakerMetrics,
  CircuitBreakerHistory,
  CircuitBreakerAlerts,
} from '@/components/reliability/widgets/circuit-breakers';
import {
  SystemHealthCard,
  HealthStatusIndicators,
  DependencyStatusDisplay,
  HealthTrendCharts,
  SystemResourceMonitor,
} from '@/components/reliability/widgets/system-health';
import {
  PerformanceOverview,
  SystemMetricsDisplay,
  HttpRequestMetrics,
  DeduplicationMetrics,
} from '@/components/reliability/widgets/performance';
import {
  ActiveAlerts,
  AlertStatistics,
  DependencyHealth,
} from '@/components/reliability/widgets/alerts';
import { useReliabilityStore } from '@/lib/hooks';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';

/**
 * Props for the DashboardGrid component
 */
export interface DashboardGridProps {
  /** Optional CSS class name for styling */
  className?: string;
}

/**
 * Widget placeholder component for widgets that haven't been implemented yet
 */
const WidgetPlaceholder: React.FC<{
  widgetId: string;
  title: string;
  description: string;
}> = ({ widgetId, title, description }) => (
  <div className="flex h-64 items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/10">
    <div className="text-center">
      <h3 className="font-medium text-muted-foreground">{title}</h3>
      <p className="mt-1 text-sm text-muted-foreground/75">{description}</p>
      <p className="mt-2 text-xs text-muted-foreground/50">
        Widget ID: {widgetId}
      </p>
    </div>
  </div>
);

/**
 * Responsive dashboard grid component with widget management.
 *
 * This component provides:
 * - Responsive grid layout based on user preferences
 * - Widget visibility management
 * - Drag-and-drop support (future enhancement)
 * - Dynamic widget loading and rendering
 * - Empty state handling
 *
 * Features:
 * - Mobile-first responsive design
 * - Customizable grid columns (1-6)
 * - Widget expand/collapse functionality
 * - Performance optimized rendering
 * - Accessibility support
 *
 * @param props - Component props
 * @returns JSX element representing the dashboard grid
 */
export const DashboardGrid: React.FC<DashboardGridProps> = ({
  className = '',
}) => {
  // Translation hook
  const t = useTranslations('reliability.widgets');

  // Get dashboard layout preferences and state
  const layout = useReliabilityStore(
    state => state.preferences.dashboardLayout.layout
  );
  const gridColumns = useReliabilityStore(
    state => state.preferences.dashboardLayout.gridColumns
  );
  const getVisibleWidgets = useReliabilityStore(
    state => state.getVisibleWidgets
  );
  const activeTab = useReliabilityStore(state => state.ui.activeTab);
  const toggleWidget = useReliabilityStore(state => state.toggleWidget);

  // Get visible widgets in the correct order
  const visibleWidgets = getVisibleWidgets();

  /**
   * Widget configuration mapping
   */
  const widgetConfig = {
    'system-health': {
      title: t('systemHealth'),
      description: t('systemHealthDesc'),
      component: SystemHealthCard,
    },
    'health-status-indicators': {
      title: t('healthStatusIndicators'),
      description: t('healthStatusDesc'),
      component: HealthStatusIndicators,
    },
    'dependency-status': {
      title: t('dependencyStatus'),
      description: t('dependencyStatusDesc'),
      component: DependencyStatusDisplay,
    },
    'health-trends': {
      title: t('healthTrends'),
      description: t('healthTrendsDesc'),
      component: HealthTrendCharts,
    },
    'system-resources': {
      title: t('systemResources'),
      description: t('systemResourcesDesc'),
      component: SystemResourceMonitor,
    },
    'performance-overview': {
      title: t('performanceOverview'),
      description: t('performanceOverviewDesc'),
      component: PerformanceOverview,
    },
    'system-metrics': {
      title: t('systemMetrics'),
      description: t('systemMetricsDesc'),
      component: SystemMetricsDisplay,
    },
    'http-metrics': {
      title: t('httpMetrics'),
      description: t('httpMetricsDesc'),
      component: HttpRequestMetrics,
    },
    'deduplication-metrics': {
      title: t('deduplicationMetrics'),
      description: t('deduplicationMetricsDesc'),
      component: DeduplicationMetrics,
    },
    'performance-metrics': {
      title: 'Performance Metrics',
      description: 'System performance monitoring and analysis',
      component: SystemMetricsDisplay,
    },
    //   title: 'Cache & Deduplication',
    //   description: 'Request deduplication efficiency monitoring',
    //   component: DeduplicationMetrics,
    // },
    'circuit-breakers': {
      title: 'Circuit Breakers Overview',
      description: 'Circuit breaker status and failure protection',
      component: CircuitBreakerOverview,
    },
    'circuit-breaker-list': {
      title: 'Circuit Breaker Details',
      description: 'Detailed circuit breaker list with status',
      component: CircuitBreakerList,
    },
    'circuit-breaker-metrics': {
      title: 'Circuit Breaker Metrics',
      description: 'Performance metrics and failure rate charts',
      component: CircuitBreakerMetrics,
    },
    'circuit-breaker-history': {
      title: 'Circuit Breaker History',
      description: 'Historical state changes and recovery patterns',
      component: CircuitBreakerHistory,
    },
    'circuit-breaker-alerts': {
      title: 'Circuit Breaker Alerts',
      description: 'Active alerts related to circuit breaker failures',
      component: CircuitBreakerAlerts,
    },
    // 'performance-metrics': {
    //   title: 'Performance Metrics',
    //   description: 'System performance and response time metrics',
    //   component: PerformanceOverview,
    // },
    'active-alerts': {
      title: 'Active Alerts',
      description: 'Current system alerts and notifications',
      component: ActiveAlerts,
    },
    'alert-statistics': {
      title: 'Alert Statistics',
      description: 'Alert trends and statistical analysis',
      component: AlertStatistics,
    },
    'dependency-health': {
      title: 'Dependency Health',
      description: 'External service and dependency status',
      component: DependencyHealth,
    },
  };

  /**
   * Get grid CSS classes based on layout and column preferences
   */
  const getGridClasses = () => {
    if (layout === 'list') {
      return 'grid grid-cols-1 gap-6';
    }

    if (layout === 'compact') {
      return 'grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
    }

    // Grid layout with user-defined columns
    const colsMap = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 lg:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
    };

    return `grid gap-6 ${colsMap[gridColumns as keyof typeof colsMap] || colsMap[3]}`;
  };

  /**
   * Filter widgets based on active tab
   */
  const getTabFilteredWidgets = () => {
    switch (activeTab) {
      case 'health':
        return visibleWidgets.filter(id =>
          [
            'system-health',
            'dependency-health',
            'circuit-breakers',
            'circuit-breaker-list',
          ].includes(id)
        );
      case 'metrics':
        return visibleWidgets.filter(id =>
          [
            'performance-overview',
            'system-metrics',
            'http-metrics',
            'deduplication-metrics',
            'performance-metrics',
            'circuit-breaker-metrics',
          ].includes(id)
        );
      case 'alerts':
        return visibleWidgets.filter(id =>
          [
            'active-alerts',
            'alert-statistics',
            'circuit-breaker-alerts',
          ].includes(id)
        );
      case 'history':
        return visibleWidgets.filter(id =>
          [
            'alert-statistics',
            'performance-metrics',
            'circuit-breaker-history',
          ].includes(id)
        );
      case 'overview':
      default:
        return visibleWidgets;
    }
  };

  const filteredWidgets = getTabFilteredWidgets();

  /**
   * Render widget content
   */
  const renderWidget = (widgetId: string) => {
    const config = widgetConfig[widgetId as keyof typeof widgetConfig];

    if (!config) {
      return (
        <WidgetPlaceholder
          widgetId={widgetId}
          title="Unknown Widget"
          description="Widget configuration not found"
        />
      );
    }

    // Render actual widget components if available
    if (config.component) {
      const WidgetComponent = config.component;
      return <WidgetComponent />;
    }

    // Render placeholder for widgets not yet implemented
    return (
      <WidgetPlaceholder
        widgetId={widgetId}
        title={config.title}
        description={config.description}
      />
    );
  };

  // Handle empty state
  if (filteredWidgets.length === 0) {
    return (
      <div className={cn('space-y-6', className)}>
        <Alert>
          <AlertDescription>
            No widgets are currently visible for the {activeTab} tab. You can
            enable widgets in the dashboard settings.
          </AlertDescription>
        </Alert>

        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => {
              // Enable some default widgets for the current tab
              const defaultWidgets = {
                overview: [
                  'system-health',
                  'health-status-indicators',
                  'circuit-breakers',
                  'active-alerts',
                ],
                health: [
                  'health-status-indicators',
                  'dependency-status',
                  'system-resources',
                  'health-trends',
                ],
                metrics: [
                  'performance-overview',
                  'system-metrics',
                  'http-metrics',
                  'deduplication-metrics',
                  'performance-metrics',
                  'circuit-breaker-metrics',
                ],
                alerts: [
                  'active-alerts',
                  'circuit-breaker-alerts',
                  'alert-statistics',
                ],
                history: [
                  'health-trends',
                  'circuit-breaker-history',
                  'alert-statistics',
                ],
              };

              const widgets = defaultWidgets[
                activeTab as keyof typeof defaultWidgets
              ] || ['system-health'];
              widgets.forEach(widgetId => toggleWidget(widgetId as any));
            }}
          >
            Enable Default Widgets
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(getGridClasses(), className)}
      role="grid"
      aria-label={`Dashboard widgets in ${layout} layout`}
    >
      {filteredWidgets.map(widgetId => (
        <WidgetContainer
          key={widgetId}
          widgetId={widgetId}
          title={
            widgetConfig[widgetId as keyof typeof widgetConfig]?.title ||
            widgetId
          }
          className="min-h-[200px]"
        >
          {renderWidget(widgetId)}
        </WidgetContainer>
      ))}
    </div>
  );
};

/**
 * Default export for the DashboardGrid component
 */
export default DashboardGrid;
