"use strict";exports.id=2153,exports.ids=[2153],exports.modules={3940:(e,t,r)=>{r.d(t,{O_:()=>l,t6:()=>a});var i=r(43210),s=r(49278);function a(){let e=(0,i.useCallback)((e,t)=>s.JP.success(e,t),[]),t=(0,i.useCallback)((e,t)=>s.JP.error(e,t),[]),r=(0,i.useCallback)((e,t)=>s.JP.info(e,t),[]),a=(0,i.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),l=(0,i.useCallback)((e,r)=>{let i=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||i||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:a,showFormError:l}}function l(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:l}=a(),d=t||(e?(0,s.iw)(e):null),n=(0,i.useCallback)(e=>d?d.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[d,r]),o=(0,i.useCallback)(e=>d?d.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[d,r]),c=(0,i.useCallback)(e=>d?d.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[d,r]),u=(0,i.useCallback)(e=>{if(d){let t=e instanceof Error?e.message:e;return d.entityCreationError(t)}return l(e,{errorTitle:"Creation Failed"})},[d,l]);return{showEntityCreated:n,showEntityUpdated:o,showEntityDeleted:c,showEntityCreationError:u,showEntityUpdateError:(0,i.useCallback)(e=>{if(d){let t=e instanceof Error?e.message:e;return d.entityUpdateError(t)}return l(e,{errorTitle:"Update Failed"})},[d,l]),showEntityDeletionError:(0,i.useCallback)(e=>{if(d){let t=e instanceof Error?e.message:e;return d.entityDeletionError(t)}return l(e,{errorTitle:"Deletion Failed"})},[d,l]),showFormSuccess:r,showFormError:l}}(void 0,t)}},15079:(e,t,r)=>{r.d(t,{bq:()=>p,eb:()=>y,gC:()=>f,l6:()=>c,yv:()=>u});var i=r(60687),s=r(22670),a=r(61662),l=r(89743),d=r(58450),n=r(43210),o=r(22482);let c=s.bL;s.YJ;let u=s.WT,p=n.forwardRef(({children:e,className:t,...r},l)=>(0,i.jsxs)(s.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:l,...r,children:[e,(0,i.jsx)(s.In,{asChild:!0,children:(0,i.jsx)(a.A,{className:"size-4 opacity-50"})})]}));p.displayName=s.l9.displayName;let m=n.forwardRef(({className:e,...t},r)=>(0,i.jsx)(s.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,i.jsx)(l.A,{className:"size-4"})}));m.displayName=s.PP.displayName;let h=n.forwardRef(({className:e,...t},r)=>(0,i.jsx)(s.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,i.jsx)(a.A,{className:"size-4"})}));h.displayName=s.wn.displayName;let f=n.forwardRef(({children:e,className:t,position:r="popper",...a},l)=>(0,i.jsx)(s.ZL,{children:(0,i.jsxs)(s.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,ref:l,...a,children:[(0,i.jsx)(m,{}),(0,i.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,i.jsx)(h,{})]})}));f.displayName=s.UC.displayName,n.forwardRef(({className:e,...t},r)=>(0,i.jsx)(s.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:r,...t})).displayName=s.JU.displayName;let y=n.memo(n.forwardRef(({children:e,className:t,...r},a)=>{let l=n.useCallback(e=>{"function"==typeof a?a(e):a&&(a.current=e)},[a]);return(0,i.jsxs)(s.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:l,...r,children:[(0,i.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,i.jsx)(s.VF,{children:(0,i.jsx)(d.A,{className:"size-4"})})}),(0,i.jsx)(s.p4,{children:e})]})}));y.displayName=s.q7.displayName,n.forwardRef(({className:e,...t},r)=>(0,i.jsx)(s.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),ref:r,...t})).displayName=s.wv.displayName},34729:(e,t,r)=>{r.d(t,{T:()=>l});var i=r(60687),s=r(43210),a=r(22482);let l=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));l.displayName="Textarea"},48041:(e,t,r)=>{r.d(t,{z:()=>s});var i=r(60687);function s({children:e,description:t,icon:r,title:s}){return(0,i.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,i.jsx)(r,{className:"size-8 text-primary"}),(0,i.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:s})]}),t&&(0,i.jsx)("p",{className:"mt-1 text-muted-foreground",children:t})]}),e&&(0,i.jsx)("div",{className:"flex items-center gap-2",children:e})]})}r(43210)},49278:(e,t,r)=>{r.d(t,{G7:()=>p,Gb:()=>n,JP:()=>o,Ok:()=>c,Qu:()=>u,iw:()=>d,oz:()=>h,z0:()=>m});var i=r(3389);class s{show(e){return(0,i.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends s{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class l extends s{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function d(e){return new a(e)}function n(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let o=new s,c=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),p=new a({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),m=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new l},55817:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},71273:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(82614).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},71669:(e,t,r)=>{r.d(t,{C5:()=>g,MJ:()=>y,Rr:()=>v,eI:()=>h,lR:()=>f,lV:()=>o,zB:()=>u});var i=r(60687),s=r(43210),a=r(8730),l=r(27605),d=r(22482),n=r(80013);let o=l.Op,c=s.createContext({}),u=({...e})=>(0,i.jsx)(c.Provider,{value:{name:e.name},children:(0,i.jsx)(l.xI,{...e})}),p=()=>{let e=s.useContext(c),t=s.useContext(m),{getFieldState:r,formState:i}=(0,l.xW)(),a=r(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:d}=t;return{id:d,name:e.name,formItemId:`${d}-form-item`,formDescriptionId:`${d}-form-item-description`,formMessageId:`${d}-form-item-message`,...a}},m=s.createContext({}),h=s.forwardRef(({className:e,...t},r)=>{let a=s.useId();return(0,i.jsx)(m.Provider,{value:{id:a},children:(0,i.jsx)("div",{ref:r,className:(0,d.cn)("space-y-2",e),...t})})});h.displayName="FormItem";let f=s.forwardRef(({className:e,...t},r)=>{let{error:s,formItemId:a}=p();return(0,i.jsx)(n.J,{ref:r,className:(0,d.cn)(s&&"text-destructive",e),htmlFor:a,...t})});f.displayName="FormLabel";let y=s.forwardRef(({...e},t)=>{let{error:r,formItemId:s,formDescriptionId:l,formMessageId:d}=p();return(0,i.jsx)(a.DX,{ref:t,id:s,"aria-describedby":r?`${l} ${d}`:`${l}`,"aria-invalid":!!r,...e})});y.displayName="FormControl";let v=s.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:s}=p();return(0,i.jsx)("p",{ref:r,id:s,className:(0,d.cn)("text-sm text-muted-foreground",e),...t})});v.displayName="FormDescription";let g=s.forwardRef(({className:e,children:t,...r},s)=>{let{error:a,formMessageId:l}=p(),n=a?String(a?.message??""):t;return n?(0,i.jsx)("p",{ref:s,id:l,className:(0,d.cn)("text-sm font-medium text-destructive",e),...r,children:n}):null});g.displayName="FormMessage"},72273:(e,t,r)=>{r.d(t,{NS:()=>h,T$:()=>c,W_:()=>u,Y1:()=>p,lR:()=>m});var i=r(8693),s=r(54050),a=r(46349),l=r(87676),d=r(48839),n=r(49603);let o={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,a.GK)([...o.all],async()=>(await n.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>(0,a.GK)([...o.detail(e)],()=>n.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(t?.enabled??!0),staleTime:3e5,...t}),p=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:r}=(0,l.useNotifications)();return(0,s.n)({mutationFn:e=>{let t=d.M.toCreateRequest(e);return n.vehicleApiService.create(t)},onError:e=>{t(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),r(`Vehicle "${t.licensePlate}" has been created successfully!`)}})},m=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:r}=(0,l.useNotifications)();return(0,s.n)({mutationFn:({data:e,id:t})=>{let r=d.M.toUpdateRequest(e);return n.vehicleApiService.update(t,r)},onError:e=>{t(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(t.id)}),r(`Vehicle "${t.licensePlate}" has been updated successfully!`)}})},h=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:r}=(0,l.useNotifications)();return(0,s.n)({mutationFn:e=>n.vehicleApiService.delete(e),onError:e=>{t(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(t,i)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(i)}),r("Vehicle has been deleted successfully!")}})}},73227:(e,t,r)=>{r.d(t,{ZY:()=>x,AK:()=>E,b7:()=>g,xo:()=>b,si:()=>v,K:()=>j});var i=r(93425),s=r(8693),a=r(54050),l=r(43210),d=r(46349),n=r(49603),o=r(83144),c=r(57930);let u={all:["tasks"],detail:e=>["tasks",e]},p=e=>({enabled:!!e,queryFn:()=>n.taskApiService.getById(e),queryKey:u.detail(e),staleTime:3e5}),m=()=>({queryFn:()=>n.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),h=()=>({queryFn:()=>n.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),f=e=>[p(e),m(),h()];var y=r(38118);let v=e=>(0,d.GK)([...u.all],async()=>(await n.taskApiService.getAll()).data,"task",{staleTime:0,...e}),g=e=>(0,d.GK)([...u.detail(e)],async()=>await n.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),b=e=>{let[t,r,s]=(0,i.E)({queries:f(e)}),a=(0,l.useMemo)(()=>{if(t?.data&&r?.data&&s?.data)try{let e=c.J.fromApi(t.data),i=Array.isArray(r.data)?r.data:[],a=Array.isArray(s.data)?s.data:[];return(0,o.R)(e,i,a)}catch(e){throw console.error("Error enriching task data:",e),e}},[t?.data,r?.data,s?.data]),d=(0,l.useCallback)(()=>{t?.refetch(),r?.refetch(),s?.refetch()},[t?.refetch,r?.refetch,s?.refetch]);return{data:a,error:t?.error||r?.error||s?.error,isError:t?.isError||r?.isError||s?.isError,isLoading:t?.isLoading||r?.isLoading||s?.isLoading,isPending:t?.isPending||r?.isPending||s?.isPending,refetch:d}},x=()=>{let e=(0,s.jE)();return(0,a.n)({mutationFn:async e=>{let t=c.J.toCreateRequest(e);return await n.taskApiService.create(t)},onError:(t,r,i)=>{i?.previousTasks&&e.setQueryData(u.all,i.previousTasks),console.error("Failed to create task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:u.all});let r=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>{let r="optimistic-"+Date.now().toString(),i=new Date().toISOString();return[...e,{createdAt:i,dateTime:t.dateTime??null,deadline:t.deadline??null,description:t.description,driverEmployee:null,driverEmployeeId:t.driverEmployeeId??null,estimatedDuration:t.estimatedDuration??null,id:r,location:t.location??null,notes:t.notes??null,priority:t.priority,requiredSkills:t.requiredSkills??null,staffEmployee:null,staffEmployeeId:t.staffEmployeeId??null,status:t.status||"Pending",subtasks:t.subtasks?.map(e=>({completed:e.completed||!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:r,title:e.title}))||[],updatedAt:i,vehicle:null,vehicleId:t.vehicleId??null}]}),{previousTasks:r}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})},j=()=>{let e=(0,s.jE)();return(0,a.n)({mutationFn:async({data:e,id:t})=>{let r=c.J.toUpdateRequest(e);return await n.taskApiService.update(t,r)},onError:(t,r,i)=>{i?.previousTask&&e.setQueryData(u.detail(r.id),i.previousTask),i?.previousTasksList&&e.setQueryData(u.all,i.previousTasksList),console.error("Failed to update task:",t)},onMutate:async({data:t,id:r})=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(r)});let i=e.getQueryData(u.detail(r)),s=e.getQueryData(u.all);return e.setQueryData(u.detail(r),e=>{if(!e)return e;let i=new Date().toISOString();return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,y.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,y.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,y.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:r,title:e.title}))||e.subtasks||[],updatedAt:i,vehicleId:(0,y.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}),e.setQueryData(u.all,(e=[])=>e.map(e=>{if(e.id===r){let i=new Date().toISOString(),s=t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:r,title:e.title}))||e.subtasks||[];return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,y.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,y.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,y.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:s,updatedAt:i,vehicleId:(0,y.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}return e})),{previousTask:i,previousTasksList:s}},onSettled:(t,r,i)=>{e.invalidateQueries({queryKey:u.detail(i.id)}),e.invalidateQueries({queryKey:u.all})}})},E=()=>{let e=(0,s.jE)();return(0,a.n)({mutationFn:async e=>(await n.taskApiService.delete(e),e),onError:(t,r,i)=>{i?.previousTasksList&&e.setQueryData(u.all,i.previousTasksList),console.error("Failed to delete task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(t)});let r=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>e.filter(e=>e.id!==t)),e.removeQueries({queryKey:u.detail(t)}),{previousTasksList:r}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})}},78207:(e,t,r)=>{r.d(t,{A:()=>I});var i=r(60687),s=r(75699),a=r(55817),l=r(71273),d=r(80489),n=r(15036),o=r(14975),c=r(16189),u=r(43210),p=r(27605),m=r(29523),h=r(44493),f=r(92718),y=r(92929),v=r(89667),g=r(15079),b=r(3940),x=r(9275),j=r(65594);let E=x.k5(["Pending","Assigned","In_Progress","Completed","Cancelled"]),k=x.k5(["Low","Medium","High"]),w=x.Ik({completed:x.zM(),id:x.Yj().optional(),taskId:x.Yj(),title:x.Yj()}),N=x.Ik({dateTime:x.Yj().min(1,"Start date & time is required").refine(e=>(0,j.Qr)(e),{message:"Please enter a valid date and time in YYYY-MM-DD HH:MM format"}),deadline:x.Yj().refine(e=>""===e||(0,j.Qr)(e),{message:"Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format"}).optional().transform(e=>""===e?void 0:e),description:x.Yj().min(1,"Task description is required"),driverEmployeeId:x.ai().int().positive("Driver Employee ID must be a positive integer").optional().nullable(),estimatedDuration:x.au.number().int().min(1,"Estimated duration must be at least 1 minute"),id:x.Yj().uuid().optional(),location:x.Yj().min(1,"Location is required"),notes:x.Yj().optional().or(x.eu("")),priority:k,requiredSkills:x.YO(x.Yj()),staffEmployeeId:x.ai({required_error:"Staff Employee is required",invalid_type_error:"Staff Employee must be a valid selection"}).int().positive("Staff Employee ID must be a positive integer"),status:E,statusChangeReason:x.Yj().optional(),subtasks:x.YO(w).optional(),vehicleId:x.ai().int().positive("Vehicle ID must be a positive integer.").nullable().optional()}).superRefine((e,t)=>{if(e.dateTime&&e.deadline){let r=new Date(e.dateTime);new Date(e.deadline)<r&&t.addIssue({code:x.eq.custom,message:"Deadline cannot be earlier than the start date & time",path:["deadline"]})}e.vehicleId&&!e.driverEmployeeId&&t.addIssue({code:x.eq.custom,message:"Vehicle cannot be assigned without a driver",path:["vehicleId"]})});var S=r(19599),D=r(72273);function I({initialData:e,isEditing:t=!1,isLoading:r=!1,onSubmit:d}){let n=(0,c.useRouter)(),o={dateTime:e?.dateTime?(0,j.rm)(e.dateTime,"datetime-local"):(0,s.GP)(new Date,"yyyy-MM-dd'T'HH:mm"),deadline:e?.deadline?(0,j.rm)(e.deadline,"datetime-local"):"",description:e?.description||"",driverEmployeeId:e?.driverEmployeeId||null,estimatedDuration:e?.estimatedDuration||60,location:e?.location||"",notes:e?.notes||"",priority:e?.priority||"Medium",requiredSkills:e?.requiredSkills||[],...e?.staffEmployeeId&&{staffEmployeeId:e.staffEmployeeId},status:e?.status?e.status.replace("_"," "):"Pending",vehicleId:e?.vehicleId||null},u=async e=>{try{let t={...e,dateTime:(0,j.B7)(e.dateTime),deadline:e.deadline?(0,j.B7)(e.deadline):void 0,driverEmployeeId:e.driverEmployeeId?Number(e.driverEmployeeId):null,staffEmployeeId:Number(e.staffEmployeeId),vehicleId:e.vehicleId?Number(e.vehicleId):null,...e.subtasks&&{subTasks:e.subtasks}};await d(t)}catch(e){throw e}};return(0,i.jsx)(f.I,{defaultValues:o,onSubmit:u,schema:N,children:(0,i.jsxs)(h.Zp,{className:"shadow-lg",children:[(0,i.jsxs)(h.aR,{children:[(0,i.jsx)(h.ZB,{className:"text-2xl text-primary",children:t?"Edit Task":"Add New Task"}),(0,i.jsx)(h.BT,{children:"Enter the details for the task."})]}),(0,i.jsx)(h.Wu,{className:"space-y-6",children:(0,i.jsx)(C,{})}),(0,i.jsxs)(h.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,i.jsxs)(m.$,{onClick:()=>n.back(),type:"button",variant:"outline",children:[(0,i.jsx)(a.A,{className:"mr-2 size-4"}),"Cancel"]}),(0,i.jsxs)(m.$,{className:"bg-accent text-accent-foreground hover:bg-accent/90",type:"submit",disabled:r,children:[(0,i.jsx)(l.A,{className:"mr-2 size-4"}),t?"Save Changes":"Create Task"]})]})]})})}let C=()=>{let{watch:e}=(0,p.xW)(),{showFormError:t}=(0,b.t6)(),{data:r=[]}=(0,S.sZ)(),{data:s=[]}=(0,S.sZ)("driver"),a=r.filter(e=>"driver"!==e.role),{data:l,error:c}=(0,D.T$)(),m=(0,u.useMemo)(()=>l||[],[l]);return(0,u.useEffect)(()=>{c&&(console.error("Failed to fetch vehicles for form:",c),t(c.message||"Could not load vehicles.",{errorTitle:"Error Loading Vehicles"}))},[c,t]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-4",children:[(0,i.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,i.jsx)(d.A,{className:"mr-2 size-5 text-accent"}),"Task Details"]}),(0,i.jsx)(y.z,{label:"Description",name:"description",placeholder:"e.g., Pick up package from Warehouse A",type:"textarea"}),(0,i.jsx)(y.z,{label:"Location",name:"location",placeholder:"e.g., 123 Main St, City Center"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,i.jsx)(y.z,{label:"Start Date & Time",name:"dateTime",type:"datetime-local"}),(0,i.jsx)(y.z,{label:"Estimated Duration (minutes)",name:"estimatedDuration",placeholder:"e.g., 60",type:"number"})]})]}),(0,i.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-4",children:[(0,i.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,i.jsx)(n.A,{className:"mr-2 size-5 text-accent"}),"Scheduling & Assignment"]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,i.jsx)(y.z,{label:"Priority",name:"priority",render:({field:e})=>(0,i.jsxs)(g.l6,{defaultValue:e.value,onValueChange:e.onChange,children:[(0,i.jsx)(g.bq,{children:(0,i.jsx)(g.yv,{placeholder:"Select priority"})}),(0,i.jsx)(g.gC,{children:k.options.map(e=>(0,i.jsx)(g.eb,{value:e,children:e},e))})]})}),(0,i.jsx)(y.z,{label:"Deadline (Optional)",name:"deadline",type:"datetime-local"})]}),(0,i.jsx)(y.z,{label:"Status",name:"status",render:({field:e})=>(0,i.jsxs)(g.l6,{defaultValue:e.value,onValueChange:e.onChange,children:[(0,i.jsx)(g.bq,{children:(0,i.jsx)(g.yv,{placeholder:"Select status"})}),(0,i.jsx)(g.gC,{children:E.options.map(e=>(0,i.jsx)(g.eb,{value:e,children:e},e))})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,i.jsx)(y.z,{label:"Staff Employee (Required)",name:"staffEmployeeId",render:({field:e})=>(0,i.jsxs)(g.l6,{onValueChange:t=>e.onChange(Number(t)),value:e.value?String(e.value):"",children:[(0,i.jsx)(g.bq,{children:(0,i.jsx)(g.yv,{placeholder:"Select staff employee"})}),(0,i.jsx)(g.gC,{children:a.map(e=>(0,i.jsxs)(g.eb,{value:String(e.id),children:[e.fullName," (",e.role,", ",e.status,")"]},e.id))})]})}),(0,i.jsx)(y.z,{label:"Driver Employee (Optional)",name:"driverEmployeeId",render:({field:e})=>(0,i.jsxs)(g.l6,{onValueChange:t=>e.onChange("none"===t?void 0:Number(t)),value:void 0===e.value?"none":String(e.value),children:[(0,i.jsx)(g.bq,{children:(0,i.jsx)(g.yv,{placeholder:"Select driver (optional)"})}),(0,i.jsxs)(g.gC,{children:[(0,i.jsx)(g.eb,{value:"none",children:"No Driver"}),s.map(e=>(0,i.jsxs)(g.eb,{value:String(e.id),children:[e.fullName," (",e.status,")",e.availability&&`, ${e.availability.replace("_"," ")}`,e.currentLocation&&`, @ ${e.currentLocation}`]},e.id))]})]})})]}),(0,i.jsx)(y.z,{label:"Vehicle (Optional - requires driver)",name:"vehicleId",render:({field:t})=>(0,i.jsxs)(g.l6,{disabled:!e("driverEmployeeId"),onValueChange:e=>t.onChange("none"===e?void 0:Number(e)),value:void 0===t.value?"none":String(t.value),children:[(0,i.jsx)(g.bq,{children:(0,i.jsx)(g.yv,{placeholder:e("driverEmployeeId")?"Select vehicle (optional)":"Select a driver first"})}),(0,i.jsxs)(g.gC,{children:[(0,i.jsx)(g.eb,{value:"none",children:"No Vehicle"}),m.map(e=>(0,i.jsxs)(g.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,") -"," ",e.licensePlate||"N/A"]},e.id))]})]})})]}),(0,i.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-4",children:[(0,i.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,i.jsx)(o.A,{className:"mr-2 size-5 text-accent"}),"Additional Information"]}),(0,i.jsx)(y.z,{label:"Required Skills (Optional, comma-separated)",name:"requiredSkills",render:({field:e})=>(0,i.jsx)(v.p,{onChange:t=>e.onChange(t.target.value.split(",").map(e=>e.trim()).filter(Boolean)),placeholder:"e.g., Forklift License, Customer Service",value:Array.isArray(e.value)?e.value.join(", "):""})}),(0,i.jsx)(y.z,{label:"Notes (Optional)",name:"notes",placeholder:"e.g., Gate code is 1234, contact person: Jane Smith",type:"textarea"})]})]})}},83144:(e,t,r)=>{r.d(t,{R:()=>s});class i{static enrich(e,t,r){let{employeeMap:i,vehicleMap:s}=this.createLookupMaps(t,r),a=this.enrichStaffEmployee(e,i);return a=this.enrichDriverEmployee(a,i),a=this.enrichVehicle(a,s)}static createLookupMaps(e,t){let r=Array.isArray(e)?e:[],i=Array.isArray(t)?t:[];return{employeeMap:new Map(r.map(e=>[e.id,e])),vehicleMap:new Map(i.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,t){if(!e.driverEmployeeId)return e;let r=e.driverEmployee??t.get(e.driverEmployeeId)??null;return{...e,driverEmployee:r}}static enrichStaffEmployee(e,t){if(!e.staffEmployeeId)return e;let r=e.staffEmployee??t.get(e.staffEmployeeId)??null;return{...e,staffEmployee:r}}static enrichVehicle(e,t){if(!e.vehicleId)return e;let r=e.vehicle??t.get(e.vehicleId)??null;return{...e,vehicle:r}}}let s=(e,t,r)=>i.enrich(e,t,r)},92718:(e,t,r)=>{r.d(t,{I:()=>d});var i=r(60687),s=r(63442),a=r(27605),l=r(71669);let d=({children:e,defaultValues:t,onSubmit:r,schema:d,className:n="",ariaAttributes:o={}})=>{let c=(0,a.mN)({...t&&{defaultValues:t},resolver:(0,s.u)(d)}),u=async e=>{await r(e)};return(0,i.jsx)(l.lV,{...c,children:(0,i.jsx)("form",{onSubmit:c.handleSubmit(u),className:n,...o,children:e})})}},92929:(e,t,r)=>{r.d(t,{z:()=>o});var i=r(60687);r(43210);var s=r(27605),a=r(71669),l=r(89667),d=r(34729),n=r(15079);let o=({className:e="",disabled:t=!1,label:r,name:o,placeholder:c,render:u,type:p="text",options:m=[],defaultValue:h,icon:f,...y})=>{let{control:v}=(0,s.xW)();return(0,i.jsxs)(a.eI,{className:e,children:[(0,i.jsx)(a.lR,{htmlFor:o,children:r}),(0,i.jsx)(s.xI,{control:v,name:o,render:u||(({field:e,fieldState:{error:s}})=>(0,i.jsx)(a.MJ,{children:"select"===p?(0,i.jsxs)(n.l6,{onValueChange:e.onChange,value:e.value||h||"",disabled:t,children:[(0,i.jsx)(n.bq,{className:s?"border-red-500":"",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[f&&(0,i.jsx)(f,{className:"h-4 w-4 text-gray-500"}),(0,i.jsx)(n.yv,{placeholder:c||`Select ${r.toLowerCase()}`})]})}),(0,i.jsx)(n.gC,{children:m.map(e=>(0,i.jsx)(n.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===p?(0,i.jsxs)("div",{className:"relative",children:[f&&(0,i.jsx)(f,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,i.jsx)(d.T,{...e,...y,value:e.value??"",className:`${s?"border-red-500":""} ${f?"pl-10":""}`,disabled:t,id:o,placeholder:c})]}):(0,i.jsxs)("div",{className:"relative",children:[f&&(0,i.jsx)(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,i.jsx)(l.p,{...e,...y,value:e.value??"",className:`${s?"border-red-500":""} ${f?"pl-10":""}`,disabled:t,id:o,placeholder:c,type:p})]})}))}),(0,i.jsx)(a.C5,{})]})}},93425:(e,t,r)=>{r.d(t,{E:()=>f});var i=r(43210),s=r(33465),a=r(5563),l=r(35536),d=r(31212);function n(e,t){let r=new Set(t);return e.filter(e=>!r.has(e))}var o=class extends l.Q{#e;#t;#r;#i;#s;#a;#l;#d;#n=[];constructor(e,t,r){super(),this.#e=e,this.#i=r,this.#r=[],this.#s=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#s.forEach(e=>{e.subscribe(t=>{this.#o(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#s.forEach(e=>{e.destroy()})}setQueries(e,t){this.#r=e,this.#i=t,s.jG.batch(()=>{let e=this.#s,t=this.#c(this.#r);this.#n=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let r=t.map(e=>e.observer),i=r.map(e=>e.getCurrentResult()),s=r.some((t,r)=>t!==e[r]);(e.length!==r.length||s)&&(this.#s=r,this.#t=i,this.hasListeners()&&(n(e,r).forEach(e=>{e.destroy()}),n(r,e).forEach(e=>{e.subscribe(t=>{this.#o(e,t)})}),this.#u()))})}getCurrentResult(){return this.#t}getQueries(){return this.#s.map(e=>e.getCurrentQuery())}getObservers(){return this.#s}getOptimisticResult(e,t){let r=this.#c(e),i=r.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[i,e=>this.#p(e??i,t),()=>this.#m(i,r)]}#m(e,t){return t.map((r,i)=>{let s=e[i];return r.defaultedQueryOptions.notifyOnChangeProps?s:r.observer.trackResult(s,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#p(e,t){return t?(this.#a&&this.#t===this.#d&&t===this.#l||(this.#l=t,this.#d=this.#t,this.#a=(0,d.BH)(this.#a,t(e))),this.#a):e}#c(e){let t=new Map(this.#s.map(e=>[e.options.queryHash,e])),r=[];return e.forEach(e=>{let i=this.#e.defaultQueryOptions(e),s=t.get(i.queryHash);s?r.push({defaultedQueryOptions:i,observer:s}):r.push({defaultedQueryOptions:i,observer:new a.$(this.#e,i)})}),r}#o(e,t){let r=this.#s.indexOf(e);-1!==r&&(this.#t=function(e,t,r){let i=e.slice(0);return i[t]=r,i}(this.#t,r,t),this.#u())}#u(){if(this.hasListeners()){let e=this.#a,t=this.#m(this.#t,this.#n);e!==this.#p(t,this.#i?.combine)&&s.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},c=r(8693),u=r(24903),p=r(18228),m=r(16142),h=r(76935);function f({queries:e,...t},r){let l=(0,c.jE)(r),n=(0,u.w)(),f=(0,p.h)(),y=i.useMemo(()=>e.map(e=>{let t=l.defaultQueryOptions(e);return t._optimisticResults=n?"isRestoring":"optimistic",t}),[e,l,n]);y.forEach(e=>{(0,h.jv)(e),(0,m.LJ)(e,f)}),(0,m.wZ)(f);let[v]=i.useState(()=>new o(l,y,t)),[g,b,x]=v.getOptimisticResult(y,t.combine),j=!n&&!1!==t.subscribed;i.useSyncExternalStore(i.useCallback(e=>j?v.subscribe(s.jG.batchCalls(e)):d.lQ,[v,j]),()=>v.getCurrentResult(),()=>v.getCurrentResult()),i.useEffect(()=>{v.setQueries(y,t)},[y,t,v]);let E=g.some((e,t)=>(0,h.EU)(y[t],e))?g.flatMap((e,t)=>{let r=y[t];if(r){let t=new a.$(l,r);if((0,h.EU)(r,e))return(0,h.iL)(r,t,f);(0,h.nE)(e,n)&&(0,h.iL)(r,t,f)}return[]}):[];if(E.length>0)throw Promise.all(E);let k=g.find((e,t)=>{let r=y[t];return r&&(0,m.$1)({result:e,errorResetBoundary:f,throwOnError:r.throwOnError,query:l.getQueryCache().get(r.queryHash),suspense:r.suspense})});if(k?.error)throw k.error;return b(x())}}};