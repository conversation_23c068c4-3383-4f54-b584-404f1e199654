{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/domain/reliabilityApi.ts"], "sourcesContent": ["import type {\r\n  AlertApiResponse,\r\n  AlertHistoryApiResponse,\r\n  AlertStatisticsApiResponse,\r\n  CircuitBreakerStatusApiResponse,\r\n  DeduplicationMetricsApiResponse,\r\n  DependencyHealthApiResponse,\r\n  DetailedHealthApiResponse,\r\n  HealthCheckApiResponse,\r\n  MetricsApiResponse,\r\n  TestAlertsApiResponse,\r\n} from '../../../types/api';\r\nimport type {\r\n  Alert,\r\n  AlertHistory,\r\n  AlertStatistics,\r\n  CircuitBreakerStatus,\r\n  DeduplicationMetrics,\r\n  DependencyHealth,\r\n  DetailedHealthCheck,\r\n  HealthCheck,\r\n  SystemMetrics,\r\n  TestAlertsResult,\r\n} from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\n\r\nimport logger from '../../../utils/logger';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nconst ReliabilityTransformer: DataTransformer<any> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nexport class ReliabilityApiService extends BaseApiService<any, any, any> {\r\n  protected endpoint = '/reliability';\r\n  protected transformer: DataTransformer<any> = ReliabilityTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 1 * 60 * 1000, // 1 minute for reliability data\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async acknowledgeAlert(\r\n    alertId: string,\r\n    note?: string,\r\n    acknowledgedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/acknowledge`,\r\n        {\r\n          acknowledgedBy,\r\n          note,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async getActiveAlerts(): Promise<Alert[]> {\r\n    return this.executeWithInfrastructure('alerts:active', async () => {\r\n      try {\r\n        const apiResponse = await this.apiClient.get<any>('/alerts');\r\n\r\n        return apiResponse?.alerts || [];\r\n      } catch (error) {\r\n        console.error('Failed to get active alerts:', error);\r\n        return [];\r\n      }\r\n    });\r\n  }\r\n\r\n  async getAlertHistory(page = 1, limit = 50): Promise<AlertHistory> {\r\n    return this.executeWithInfrastructure(\r\n      `alerts:history:${page}:${limit}`,\r\n      async () => {\r\n        const queryParams = new URLSearchParams({\r\n          limit: limit.toString(),\r\n          page: page.toString(),\r\n        });\r\n        const response = await this.apiClient.get<AlertHistoryApiResponse>(\r\n          `/alerts/history?${queryParams.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getAlertStatistics(): Promise<AlertStatistics> {\r\n    return this.executeWithInfrastructure('alerts:statistics', async () => {\r\n      try {\r\n        const response =\r\n          await this.apiClient.get<AlertStatisticsApiResponse>(\r\n            '/alerts/statistics'\r\n          );\r\n        return response;\r\n      } catch (error) {\r\n        console.error('Failed to get alert statistics:', error);\r\n        return {\r\n          acknowledged: 0,\r\n          active: 0,\r\n          averageResolutionTime: 0,\r\n          bySeverity: { critical: 0, high: 0, low: 0, medium: 0 },\r\n          recentTrends: { last7Days: 0, last24Hours: 0, last30Days: 0 },\r\n          resolved: 0,\r\n          total: 0,\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  async getCircuitBreakerHistory(\r\n    timeframe: '1h' | '6h' | '7d' | '24h' = '24h',\r\n    breakerName?: string\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `circuit-breakers:history:${timeframe}:${breakerName || 'all'}`,\r\n      async () => {\r\n        const params = new URLSearchParams({ timeframe });\r\n        if (breakerName) {\r\n          params.append('breakerName', breakerName);\r\n        }\r\n        const response = await this.apiClient.get<any>(\r\n          `/monitoring/circuit-breakers/history?${params.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCircuitBreakerStatus(): Promise<CircuitBreakerStatus> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:circuit-breakers',\r\n      async () => {\r\n        try {\r\n          const apiResponse =\r\n            await this.apiClient.get<any>('/circuit-breakers');\r\n\r\n          const circuitBreakers = apiResponse?.circuitBreakers || [];\r\n\r\n          return {\r\n            circuitBreakers: circuitBreakers || [],\r\n            summary: {\r\n              closed:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'CLOSED')\r\n                  .length || 0,\r\n              halfOpen:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'HALF_OPEN')\r\n                  .length || 0,\r\n              open:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'OPEN')\r\n                  .length || 0,\r\n              total: circuitBreakers?.length || 0,\r\n            },\r\n          };\r\n        } catch (error) {\r\n          console.error('Failed to get circuit breaker status:', error);\r\n          return {\r\n            circuitBreakers: [],\r\n            summary: { closed: 0, halfOpen: 0, open: 0, total: 0 },\r\n          };\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCriticalAlertCount(): Promise<number> {\r\n    try {\r\n      const statistics = await this.getAlertStatistics();\r\n      return statistics.bySeverity.critical;\r\n    } catch {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  async getDeduplicationMetrics(): Promise<DeduplicationMetrics> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:deduplication',\r\n      async () => {\r\n        const response =\r\n          await this.apiClient.get<DeduplicationMetricsApiResponse>(\r\n            '/monitoring/deduplication'\r\n          );\r\n        return response as any;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getDependencyHealth(): Promise<DependencyHealth> {\r\n    return this.executeWithInfrastructure('health:dependencies', async () => {\r\n      const response = await this.apiClient.get<DependencyHealthApiResponse>(\r\n        '/health/dependencies'\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getDetailedHealth(): Promise<DetailedHealthCheck> {\r\n    return this.executeWithInfrastructure('health:detailed', async () => {\r\n      const response =\r\n        await this.apiClient.get<DetailedHealthApiResponse>('/health/detailed');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getHealthTrends(\r\n    timeframe: '1h' | '6h' | '7d' | '24h' = '24h'\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `health:trends:${timeframe}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<any>(\r\n          `/health/trends?timeframe=${timeframe}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getHttpRequestMetrics(): Promise<any> {\r\n    return this.executeWithInfrastructure('http:metrics', async () => {\r\n      const response = await this.apiClient.get<any>('/http-request-metrics');\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async getMetrics(): Promise<SystemMetrics> {\r\n    return this.executeWithInfrastructure('metrics:system', async () => {\r\n      const response = await this.apiClient.get<MetricsApiResponse>(\r\n        '/metrics',\r\n        {\r\n          headers: { Accept: 'application/json' },\r\n        }\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getReliabilityDashboardData(): Promise<{\r\n    activeAlerts: Alert[];\r\n    alertStatistics: AlertStatistics;\r\n    circuitBreakers: CircuitBreakerStatus;\r\n    detailedHealth: DetailedHealthCheck;\r\n    metrics: SystemMetrics;\r\n    systemHealth: HealthCheck;\r\n  }> {\r\n    const [\r\n      systemHealth,\r\n      detailedHealth,\r\n      circuitBreakers,\r\n      metrics,\r\n      activeAlerts,\r\n      alertStatistics,\r\n    ] = await Promise.all([\r\n      this.getSystemHealth(),\r\n      this.getDetailedHealth(),\r\n      this.getCircuitBreakerStatus(),\r\n      this.getMetrics(),\r\n      this.getActiveAlerts(),\r\n      this.getAlertStatistics(),\r\n    ]);\r\n\r\n    return {\r\n      activeAlerts,\r\n      alertStatistics,\r\n      circuitBreakers,\r\n      detailedHealth,\r\n      metrics,\r\n      systemHealth,\r\n    };\r\n  }\r\n\r\n  async getSystemHealth(): Promise<HealthCheck> {\r\n    return this.executeWithInfrastructure('health:system', async () => {\r\n      const response =\r\n        await this.apiClient.get<HealthCheckApiResponse>('/health');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async isSystemHealthy(): Promise<boolean> {\r\n    try {\r\n      const health = await this.getSystemHealth();\r\n      return health.status === 'healthy';\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async resolveAlert(\r\n    alertId: string,\r\n    reason?: string,\r\n    resolvedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/resolve`,\r\n        {\r\n          reason,\r\n          resolvedBy,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async testAlerts(): Promise<TestAlertsResult> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<any>('/alerts/test');\r\n      return {\r\n        message: response?.message || 'Test alert triggered',\r\n        success: response?.status === 'success',\r\n        testAlertId: response?.data?.id,\r\n      };\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AA2BA;;AAMA,MAAM,yBAA+C;IACnD,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,8BAA8B,8IAAA,CAAA,iBAAc;IAC7C,WAAW,eAAe;IAC1B,cAAoC,uBAAuB;IAErE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,iBACJ,OAAe,EACf,IAAa,EACb,cAAuB,EACP;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,YAAY,CAAC,EAChC;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAElD,OAAO,aAAa,UAAU,EAAE;YAClC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,EAAE;YACX;QACF;IACF;IAEA,MAAM,gBAAgB,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAyB;QACjE,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,OAAO,EACjC;YACE,MAAM,cAAc,IAAI,gBAAgB;gBACtC,OAAO,MAAM,QAAQ;gBACrB,MAAM,KAAK,QAAQ;YACrB;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,gBAAgB,EAAE,YAAY,QAAQ,IAAI;YAE7C,OAAO;QACT;IAEJ;IAEA,MAAM,qBAA+C;QACnD,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB;YACzD,IAAI;gBACF,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;gBAEJ,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;oBACL,cAAc;oBACd,QAAQ;oBACR,uBAAuB;oBACvB,YAAY;wBAAE,UAAU;wBAAG,MAAM;wBAAG,KAAK;wBAAG,QAAQ;oBAAE;oBACtD,cAAc;wBAAE,WAAW;wBAAG,aAAa;wBAAG,YAAY;oBAAE;oBAC5D,UAAU;oBACV,OAAO;gBACT;YACF;QACF;IACF;IAEA,MAAM,yBACJ,YAAwC,KAAK,EAC7C,WAAoB,EACN;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,yBAAyB,EAAE,UAAU,CAAC,EAAE,eAAe,OAAO,EAC/D;YACE,MAAM,SAAS,IAAI,gBAAgB;gBAAE;YAAU;YAC/C,IAAI,aAAa;gBACf,OAAO,MAAM,CAAC,eAAe;YAC/B;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,qCAAqC,EAAE,OAAO,QAAQ,IAAI;YAE7D,OAAO;QACT;IAEJ;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,+BACA;YACE,IAAI;gBACF,MAAM,cACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAEhC,MAAM,kBAAkB,aAAa,mBAAmB,EAAE;gBAE1D,OAAO;oBACL,iBAAiB,mBAAmB,EAAE;oBACtC,SAAS;wBACP,QACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,UAC/C,UAAU;wBACf,UACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,aAC/C,UAAU;wBACf,MACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,QAC/C,UAAU;wBACf,OAAO,iBAAiB,UAAU;oBACpC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;oBACL,iBAAiB,EAAE;oBACnB,SAAS;wBAAE,QAAQ;wBAAG,UAAU;wBAAG,MAAM;wBAAG,OAAO;oBAAE;gBACvD;YACF;QACF;IAEJ;IAEA,MAAM,wBAAyC;QAC7C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,kBAAkB;YAChD,OAAO,WAAW,UAAU,CAAC,QAAQ;QACvC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,4BACA;YACE,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;YAEJ,OAAO;QACT;IAEJ;IAEA,MAAM,sBAAiD;QACrD,OAAO,IAAI,CAAC,yBAAyB,CAAC,uBAAuB;YAC3D,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,oBAAkD;QACtD,OAAO,IAAI,CAAC,yBAAyB,CAAC,mBAAmB;YACvD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAA4B;YACtD,OAAO;QACT;IACF;IAEA,MAAM,gBACJ,YAAwC,KAAK,EAC/B;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,cAAc,EAAE,WAAW,EAC5B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,yBAAyB,EAAE,WAAW;YAEzC,OAAO;QACT;IAEJ;IAEA,MAAM,wBAAsC;QAC1C,OAAO,IAAI,CAAC,yBAAyB,CAAC,gBAAgB;YACpD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,aAAqC;QACzC,OAAO,IAAI,CAAC,yBAAyB,CAAC,kBAAkB;YACtD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,YACA;gBACE,SAAS;oBAAE,QAAQ;gBAAmB;YACxC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,8BAOH;QACD,MAAM,CACJ,cACA,gBACA,iBACA,SACA,cACA,gBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,kBAAkB;SACxB;QAED,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,kBAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB;YACnD,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe;YACzC,OAAO,OAAO,MAAM,KAAK;QAC3B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,aACJ,OAAe,EACf,MAAe,EACf,UAAmB,EACH;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAC5B;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,aAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAM;YAChD,OAAO;gBACL,SAAS,UAAU,WAAW;gBAC9B,SAAS,UAAU,WAAW;gBAC9B,aAAa,UAAU,MAAM;YAC/B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/factory.ts"], "sourcesContent": ["/**\r\n * @file Factory for creating and managing API service instances.\r\n * @module api/services/apiServiceFactory\r\n */\r\n\r\nimport { ApiClient } from '../core/apiClient';\r\nimport { DelegationApiService } from './domain/delegationApi';\r\nimport { EmployeeApiService } from './domain/employeeApi';\r\nimport { GiftApiService } from './domain/giftApi';\r\nimport { RecipientApiService } from './domain/recipientApi';\r\nimport { ReliabilityApiService } from './domain/reliabilityApi';\r\nimport { TaskApiService } from './domain/taskApi';\r\nimport { VehicleApiService } from './domain/vehicleApi';\r\nimport { getEnvironmentConfig } from '../../config/environment';\r\n// Import secure auth token provider\r\nimport { getSecureAuthTokenProvider } from '../index';\r\n\r\n/**\r\n * Get the current auth token from the secure provider\r\n * Uses the single source of truth for authentication tokens\r\n */\r\nfunction getSecureAuthToken(): string | null {\r\n  const provider = getSecureAuthTokenProvider();\r\n  if (!provider) {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return provider();\r\n  } catch (error) {\r\n    console.error(\r\n      '❌ Factory: Error getting auth token from secure provider:',\r\n      error\r\n    );\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Legacy compatibility - maintains backward compatibility\r\n * @deprecated Use setSecureAuthTokenProvider from main API module instead\r\n */\r\nexport function setFactoryAuthTokenProvider(\r\n  provider: () => string | null\r\n): void {\r\n  console.warn(\r\n    '⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.'\r\n  );\r\n  // This function is now a no-op since we use the secure provider\r\n  // The warning guides developers to use the correct function\r\n}\r\n\r\n/**\r\n * Configuration for the API service factory.\r\n */\r\nexport interface ApiServiceFactoryConfig {\r\n  authToken?: string;\r\n  baseURL: string;\r\n  headers?: Record<string, string>;\r\n  retryAttempts?: number;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Factory class for creating and managing API service instances.\r\n * Provides a centralized way to configure and access all API services.\r\n */\r\nexport class ApiServiceFactory {\r\n  private readonly apiClient: ApiClient;\r\n  private delegationService?: DelegationApiService;\r\n  private employeeService?: EmployeeApiService;\r\n  private giftService?: GiftApiService;\r\n  private recipientService?: RecipientApiService;\r\n  private reliabilityService?: ReliabilityApiService;\r\n  private taskService?: TaskApiService;\r\n  private vehicleService?: VehicleApiService;\r\n\r\n  /**\r\n   * Creates an instance of ApiServiceFactory.\r\n   * @param config - Configuration for the API services.\r\n   */\r\n  constructor(config: ApiServiceFactoryConfig) {\r\n    this.apiClient = new ApiClient({\r\n      ...config,\r\n      getAuthToken: getSecureAuthToken, // Use consistent secure naming\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the underlying ApiClient instance.\r\n   * @returns The ApiClient instance.\r\n   */\r\n  public getApiClient(): ApiClient {\r\n    return this.apiClient;\r\n  }\r\n\r\n  /**\r\n   * Gets the Delegation API service instance.\r\n   * @returns The DelegationApiService instance.\r\n   */\r\n  public getDelegationService(): DelegationApiService {\r\n    if (!this.delegationService) {\r\n      this.delegationService = new DelegationApiService(this.apiClient);\r\n    }\r\n    return this.delegationService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Employee API service instance.\r\n   * @returns The EmployeeApiService instance.\r\n   */\r\n  public getEmployeeService(): EmployeeApiService {\r\n    if (!this.employeeService) {\r\n      this.employeeService = new EmployeeApiService(this.apiClient);\r\n    }\r\n    return this.employeeService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Reliability API service instance.\r\n   * @returns The ReliabilityApiService instance.\r\n   */\r\n  public getReliabilityService(): ReliabilityApiService {\r\n    if (!this.reliabilityService) {\r\n      this.reliabilityService = new ReliabilityApiService(this.apiClient);\r\n    }\r\n    return this.reliabilityService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Task API service instance.\r\n   * @returns The TaskApiService instance.\r\n   */\r\n  public getTaskService(): TaskApiService {\r\n    if (!this.taskService) {\r\n      this.taskService = new TaskApiService(this.apiClient);\r\n    }\r\n    return this.taskService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Vehicle API service instance.\r\n   * @returns The VehicleApiService instance.\r\n   */\r\n  public getVehicleService(): VehicleApiService {\r\n    if (!this.vehicleService) {\r\n      this.vehicleService = new VehicleApiService(this.apiClient);\r\n    }\r\n    return this.vehicleService;\r\n  }\r\n}\r\n\r\n// Create a default factory instance for the application with environment-aware configuration\r\nconst envConfig = getEnvironmentConfig();\r\nconst defaultConfig: ApiServiceFactoryConfig = {\r\n  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  retryAttempts: 3,\r\n  timeout: 10_000,\r\n};\r\n\r\nexport const apiServiceFactory = new ApiServiceFactory(defaultConfig);\r\n\r\n// Export individual service instances for convenience\r\nexport const vehicleApiService = apiServiceFactory.getVehicleService();\r\nexport const delegationApiService = apiServiceFactory.getDelegationService();\r\nexport const taskApiService = apiServiceFactory.getTaskService();\r\nexport const employeeApiService = apiServiceFactory.getEmployeeService();\r\nexport const reliabilityApiService = apiServiceFactory.getReliabilityService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAqBO;AAnBR;AACA;AACA;AAGA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AAAA;;;;;;;;;AAEA;;;CAGC,GACD,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,6IAAA,CAAA,6BAA0B,AAAD;IAC1C,IAAI,CAAC,UAAU;QACb,wCAA4C;YAC1C,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,IAAI;QACF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,6DACA;QAEF,OAAO;IACT;AACF;AAMO,SAAS,4BACd,QAA6B;IAE7B,QAAQ,IAAI,CACV;AAEF,gEAAgE;AAChE,4DAA4D;AAC9D;AAiBO,MAAM;IACM,UAAqB;IAC9B,kBAAyC;IACzC,gBAAqC;IACrC,YAA6B;IAC7B,iBAAuC;IACvC,mBAA2C;IAC3C,YAA6B;IAC7B,eAAmC;IAE3C;;;GAGC,GACD,YAAY,MAA+B,CAAE;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,yIAAA,CAAA,YAAS,CAAC;YAC7B,GAAG,MAAM;YACT,cAAc;QAChB;IACF;IAEA;;;GAGC,GACD,AAAO,eAA0B;QAC/B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;GAGC,GACD,AAAO,uBAA6C;QAClD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,2JAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,SAAS;QAClE;QACA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;;GAGC,GACD,AAAO,qBAAyC;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,yJAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,SAAS;QAC9D;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;;GAGC,GACD,AAAO,wBAA+C;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,4JAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,SAAS;QACpE;QACA,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA;;;GAGC,GACD,AAAO,iBAAiC;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,qJAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,SAAS;QACtD;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;;GAGC,GACD,AAAO,oBAAuC;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,wJAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,SAAS;QAC5D;QACA,OAAO,IAAI,CAAC,cAAc;IAC5B;AACF;AAEA,6FAA6F;AAC7F,MAAM,YAAY,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD;AACrC,MAAM,gBAAyC;IAC7C,SAAS,UAAU,UAAU;IAC7B,SAAS;QACP,gBAAgB;IAClB;IACA,eAAe;IACf,SAAS;AACX;AAEO,MAAM,oBAAoB,IAAI,kBAAkB;AAGhD,MAAM,oBAAoB,kBAAkB,iBAAiB;AAC7D,MAAM,uBAAuB,kBAAkB,oBAAoB;AACnE,MAAM,iBAAiB,kBAAkB,cAAc;AACvD,MAAM,qBAAqB,kBAAkB,kBAAkB;AAC/D,MAAM,wBAAwB,kBAAkB,qBAAqB", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/apiServiceFactory.ts"], "sourcesContent": ["/**\r\n * @file API Service Factory - Backward Compatibility Export\r\n * @module api/services/apiServiceFactory\r\n *\r\n * This file provides backward compatibility for imports that expect\r\n * apiServiceFactory.ts instead of factory.ts\r\n */\r\n\r\n// Re-export everything from the factory module\r\nexport * from './factory';\r\n\r\n// Ensure all the commonly used exports are available\r\nexport {\r\n  ApiServiceFactory,\r\n  apiServiceFactory,\r\n  setFactoryAuthTokenProvider, // Legacy compatibility - deprecated\r\n  vehicleApiService,\r\n  delegationApiService,\r\n  taskApiService,\r\n  employeeApiService,\r\n  reliabilityApiService,\r\n} from './factory';\r\n\r\n// Re-export secure auth provider for convenience\r\nexport { setSecureAuthTokenProvider } from '../index';\r\n\r\nexport type { ApiServiceFactoryConfig } from './factory';\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,+CAA+C;;AAC/C;AAcA,iDAAiD;AACjD", "debugId": null}}]}