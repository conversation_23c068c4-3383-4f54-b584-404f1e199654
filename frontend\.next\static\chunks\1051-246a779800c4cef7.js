"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1051],{21354:(e,t,i)=>{i.d(t,{R:()=>a});class l{static enrich(e,t,i){let{employeeMap:l,vehicleMap:a}=this.createLookupMaps(t,i),r=this.enrichStaffEmployee(e,l);return r=this.enrichDriverEmployee(r,l),r=this.enrichVehicle(r,a)}static createLookupMaps(e,t){let i=Array.isArray(e)?e:[],l=Array.isArray(t)?t:[];return{employeeMap:new Map(i.map(e=>[e.id,e])),vehicleMap:new Map(l.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,t){var i,l;if(!e.driverEmployeeId)return e;let a=null!=(l=null!=(i=e.driverEmployee)?i:t.get(e.driverEmployeeId))?l:null;return{...e,driverEmployee:a}}static enrichStaffEmployee(e,t){var i,l;if(!e.staffEmployeeId)return e;let a=null!=(l=null!=(i=e.staffEmployee)?i:t.get(e.staffEmployeeId))?l:null;return{...e,staffEmployee:a}}static enrichVehicle(e,t){var i,l;if(!e.vehicleId)return e;let a=null!=(l=null!=(i=e.vehicle)?i:t.get(e.vehicleId))?l:null;return{...e,vehicle:a}}}let a=(e,t,i)=>l.enrich(e,t,i)},61051:(e,t,i)=>{i.d(t,{ZY:()=>I,AK:()=>g,b7:()=>h,xo:()=>E,si:()=>f,K:()=>S});var l=i(71610),a=i(26715),r=i(5041),d=i(12115),n=i(90111),o=i(75908),s=i(21354),u=i(62494);let c={all:["tasks"],detail:e=>["tasks",e]},v=e=>({enabled:!!e,queryFn:()=>o.taskApiService.getById(e),queryKey:c.detail(e),staleTime:3e5}),y=()=>({queryFn:()=>o.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),p=()=>({queryFn:()=>o.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),m=e=>[v(e),y(),p()];var k=i(54120);let f=e=>(0,n.GK)([...c.all],async()=>(await o.taskApiService.getAll()).data,"task",{staleTime:0,...e}),h=e=>(0,n.GK)([...c.detail(e)],async()=>await o.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),E=e=>{let[t,i,a]=(0,l.E)({queries:m(e)}),r=(0,d.useMemo)(()=>{if((null==t?void 0:t.data)&&(null==i?void 0:i.data)&&(null==a?void 0:a.data))try{let e=u.J.fromApi(t.data),l=Array.isArray(i.data)?i.data:[],r=Array.isArray(a.data)?a.data:[];return(0,s.R)(e,l,r)}catch(e){throw console.error("Error enriching task data:",e),e}},[null==t?void 0:t.data,null==i?void 0:i.data,null==a?void 0:a.data]),n=(0,d.useCallback)(()=>{null==t||t.refetch(),null==i||i.refetch(),null==a||a.refetch()},[null==t?void 0:t.refetch,null==i?void 0:i.refetch,null==a?void 0:a.refetch]);return{data:r,error:(null==t?void 0:t.error)||(null==i?void 0:i.error)||(null==a?void 0:a.error),isError:(null==t?void 0:t.isError)||(null==i?void 0:i.isError)||(null==a?void 0:a.isError),isLoading:(null==t?void 0:t.isLoading)||(null==i?void 0:i.isLoading)||(null==a?void 0:a.isLoading),isPending:(null==t?void 0:t.isPending)||(null==i?void 0:i.isPending)||(null==a?void 0:a.isPending),refetch:n}},I=()=>{let e=(0,a.jE)();return(0,r.n)({mutationFn:async e=>{let t=u.J.toCreateRequest(e);return await o.taskApiService.create(t)},onError:(t,i,l)=>{(null==l?void 0:l.previousTasks)&&e.setQueryData(c.all,l.previousTasks),console.error("Failed to create task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:c.all});let i=e.getQueryData(c.all);return e.setQueryData(c.all,function(){var e,i,l,a,r,d,n,o,s,u;let c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],v="optimistic-"+Date.now().toString(),y=new Date().toISOString();return[...c,{createdAt:y,dateTime:null!=(i=t.dateTime)?i:null,deadline:null!=(l=t.deadline)?l:null,description:t.description,driverEmployee:null,driverEmployeeId:null!=(a=t.driverEmployeeId)?a:null,estimatedDuration:null!=(r=t.estimatedDuration)?r:null,id:v,location:null!=(d=t.location)?d:null,notes:null!=(n=t.notes)?n:null,priority:t.priority,requiredSkills:null!=(o=t.requiredSkills)?o:null,staffEmployee:null,staffEmployeeId:null!=(s=t.staffEmployeeId)?s:null,status:t.status||"Pending",subtasks:(null==(e=t.subtasks)?void 0:e.map(e=>({completed:e.completed||!1,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:v,title:e.title})))||[],updatedAt:y,vehicle:null,vehicleId:null!=(u=t.vehicleId)?u:null}]}),{previousTasks:i}},onSettled:()=>{e.invalidateQueries({queryKey:c.all})}})},S=()=>{let e=(0,a.jE)();return(0,r.n)({mutationFn:async e=>{let{data:t,id:i}=e,l=u.J.toUpdateRequest(t);return await o.taskApiService.update(i,l)},onError:(t,i,l)=>{(null==l?void 0:l.previousTask)&&e.setQueryData(c.detail(i.id),l.previousTask),(null==l?void 0:l.previousTasksList)&&e.setQueryData(c.all,l.previousTasksList),console.error("Failed to update task:",t)},onMutate:async t=>{let{data:i,id:l}=t;await e.cancelQueries({queryKey:c.all}),await e.cancelQueries({queryKey:c.detail(l)});let a=e.getQueryData(c.detail(l)),r=e.getQueryData(c.all);return e.setQueryData(c.detail(l),e=>{var t,a,r,d;if(!e)return e;let n=new Date().toISOString();return{...e,dateTime:void 0!==i.dateTime?i.dateTime:e.dateTime,deadline:(0,k.d$)(void 0!==i.deadline?i.deadline:e.deadline),description:null!=(a=i.description)?a:e.description,driverEmployeeId:(0,k.d$)(void 0!==i.driverEmployeeId?i.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==i.estimatedDuration?i.estimatedDuration:e.estimatedDuration,location:void 0!==i.location?i.location:e.location,notes:(0,k.d$)(void 0!==i.notes?i.notes:e.notes),priority:null!=(r=i.priority)?r:e.priority,requiredSkills:void 0!==i.requiredSkills?i.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==i.staffEmployeeId?i.staffEmployeeId:e.staffEmployeeId,status:null!=(d=i.status)?d:e.status,subtasks:(null==(t=i.subtasks)?void 0:t.map(e=>{var t;return{completed:null!=(t=e.completed)&&t,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:l,title:e.title}}))||e.subtasks||[],updatedAt:n,vehicleId:(0,k.d$)(void 0!==i.vehicleId?i.vehicleId:e.vehicleId)}}),e.setQueryData(c.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(e=>{if(e.id===l){var t,a,r,d;let n=new Date().toISOString(),o=(null==(t=i.subtasks)?void 0:t.map(e=>{var t;return{completed:null!=(t=e.completed)&&t,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:l,title:e.title}}))||e.subtasks||[];return{...e,dateTime:void 0!==i.dateTime?i.dateTime:e.dateTime,deadline:(0,k.d$)(void 0!==i.deadline?i.deadline:e.deadline),description:null!=(a=i.description)?a:e.description,driverEmployeeId:(0,k.d$)(void 0!==i.driverEmployeeId?i.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==i.estimatedDuration?i.estimatedDuration:e.estimatedDuration,location:void 0!==i.location?i.location:e.location,notes:(0,k.d$)(void 0!==i.notes?i.notes:e.notes),priority:null!=(r=i.priority)?r:e.priority,requiredSkills:void 0!==i.requiredSkills?i.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==i.staffEmployeeId?i.staffEmployeeId:e.staffEmployeeId,status:null!=(d=i.status)?d:e.status,subtasks:o,updatedAt:n,vehicleId:(0,k.d$)(void 0!==i.vehicleId?i.vehicleId:e.vehicleId)}}return e})}),{previousTask:a,previousTasksList:r}},onSettled:(t,i,l)=>{e.invalidateQueries({queryKey:c.detail(l.id)}),e.invalidateQueries({queryKey:c.all})}})},g=()=>{let e=(0,a.jE)();return(0,r.n)({mutationFn:async e=>(await o.taskApiService.delete(e),e),onError:(t,i,l)=>{(null==l?void 0:l.previousTasksList)&&e.setQueryData(c.all,l.previousTasksList),console.error("Failed to delete task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:c.all}),await e.cancelQueries({queryKey:c.detail(t)});let i=e.getQueryData(c.all);return e.setQueryData(c.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==t)}),e.removeQueries({queryKey:c.detail(t)}),{previousTasksList:i}},onSettled:()=>{e.invalidateQueries({queryKey:c.all})}})}}}]);