'use client';

import { Edit, Gift, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import React from 'react';

import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { Separator } from '@/components/ui/separator';
import { useDeleteGift, useGift } from '@/lib/stores/queries/useGifts';
import { useRecipient } from '@/lib/stores/queries/useRecipients';

export default function GiftDetailPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations('gifts');
  const tCommon = useTranslations('common');
  const tForms = useTranslations('forms');
  
  const giftId = params.id as string;
  
  const { data: gift, isLoading, error } = useGift(giftId);
  const { data: recipient } = useRecipient(gift?.recipientId || null);
  const { mutateAsync: deleteGift, isPending: isDeleting } = useDeleteGift();

  const handleDelete = async () => {
    try {
      await deleteGift(giftId);
      router.push('/gifts');
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error deleting gift:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <AppBreadcrumb 
        homeHref="/" 
        homeLabel={tCommon('dashboard')}
        items={[
          { href: '/gifts', label: t('gifts') },
          { label: gift?.itemDescription || t('giftDetails') }
        ]}
      />
      
      <DataLoader
        data={gift}
        error={error}
        isLoading={isLoading}
        loadingComponent={<SkeletonLoader count={1} variant="card" />}
        emptyComponent={
          <div className="rounded-lg bg-card py-12 text-center shadow-md">
            <Gift className="mx-auto mb-6 size-16 text-muted-foreground" />
            <h3 className="mb-2 text-2xl font-semibold text-foreground">
              {t('giftNotFound')}
            </h3>
            <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
              {t('giftNotFoundDescription')}
            </p>
            <ActionButton asChild>
              <Link href="/gifts">{t('backToGifts')}</Link>
            </ActionButton>
          </div>
        }
      >
        {giftData => (
          <>
            <PageHeader
              description={t('giftDetailsDescription')}
              icon={Gift}
              title={giftData.itemDescription}
            >
              <div className="flex items-center gap-2">
                <ActionButton
                  actionType="secondary"
                  asChild
                  icon={<Edit className="size-4" />}
                >
                  <Link href={`/gifts/${giftId}/edit`}>
                    {tForms('buttons.edit')}
                  </Link>
                </ActionButton>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <ActionButton
                      actionType="destructive"
                      icon={<Trash2 className="size-4" />}
                    >
                      {tForms('buttons.delete')}
                    </ActionButton>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>{t('deleteGiftTitle')}</DialogTitle>
                      <DialogDescription>
                        {t('deleteGiftConfirmation', { item: giftData.itemDescription })}
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <ActionButton variant="outline">
                        {tCommon('cancel')}
                      </ActionButton>
                      <ActionButton
                        actionType="destructive"
                        isLoading={isDeleting}
                        onClick={handleDelete}
                      >
                        {tForms('buttons.delete')}
                      </ActionButton>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </PageHeader>

            <div className="grid gap-6 md:grid-cols-2">
              {/* Gift Information */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('giftInformation')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {tForms('labels.itemDescription')}
                    </label>
                    <p className="mt-1 text-sm">{giftData.itemDescription}</p>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {tForms('labels.dateSent')}
                    </label>
                    <p className="mt-1 text-sm">{formatDate(giftData.dateSent)}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {tForms('labels.senderName')}
                    </label>
                    <p className="mt-1 text-sm">{giftData.senderName}</p>
                  </div>
                  
                  {giftData.occasion && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {tForms('labels.occasion')}
                      </label>
                      <div className="mt-1">
                        <Badge variant="secondary">{giftData.occasion}</Badge>
                      </div>
                    </div>
                  )}
                  
                  {giftData.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {tForms('labels.notes')}
                      </label>
                      <p className="mt-1 text-sm">{giftData.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recipient Information */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('recipientInformation')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recipient ? (
                    <>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          {tForms('labels.name')}
                        </label>
                        <p className="mt-1 text-sm">
                          <Link 
                            href={`/recipients/${recipient.id}`}
                            className="text-primary hover:underline"
                          >
                            {recipient.name}
                          </Link>
                        </p>
                      </div>
                      
                      {recipient.email && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">
                            {tForms('labels.email')}
                          </label>
                          <p className="mt-1 text-sm">{recipient.email}</p>
                        </div>
                      )}
                      
                      {recipient.phone && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">
                            {tForms('labels.phone')}
                          </label>
                          <p className="mt-1 text-sm">{recipient.phone}</p>
                        </div>
                      )}
                      
                      {recipient.address && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">
                            {tForms('labels.address')}
                          </label>
                          <p className="mt-1 text-sm">{recipient.address}</p>
                        </div>
                      )}
                    </>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {t('recipientNotFound')}
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>{tCommon('metadata')}</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {tCommon('createdAt')}
                  </label>
                  <p className="mt-1 text-sm">{formatDate(giftData.createdAt)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {tCommon('updatedAt')}
                  </label>
                  <p className="mt-1 text-sm">{formatDate(giftData.updatedAt)}</p>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </DataLoader>
    </div>
  );
}
