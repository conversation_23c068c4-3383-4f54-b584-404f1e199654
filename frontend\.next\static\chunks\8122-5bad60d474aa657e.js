"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8122],{3235:(e,a,t)=>{t.d(a,{A:()=>l});let l=(0,t(40157).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},15300:(e,a,t)=>{t.d(a,{A:()=>l});let l=(0,t(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},17759:(e,a,t)=>{t.d(a,{C5:()=>b,MJ:()=>h,Rr:()=>y,eI:()=>p,lR:()=>x,lV:()=>d,zB:()=>m});var l=t(95155),r=t(12115),i=t(99708),s=t(62177),n=t(54036),o=t(85057);let d=s.Op,c=r.createContext({}),m=e=>{let{...a}=e;return(0,l.jsx)(c.Provider,{value:{name:a.name},children:(0,l.jsx)(s.xI,{...a})})},u=()=>{let e=r.useContext(c),a=r.useContext(f),{getFieldState:t,formState:l}=(0,s.xW)(),i=t(e.name,l);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=a;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...i}},f=r.createContext({}),p=r.forwardRef((e,a)=>{let{className:t,...i}=e,s=r.useId();return(0,l.jsx)(f.Provider,{value:{id:s},children:(0,l.jsx)("div",{ref:a,className:(0,n.cn)("space-y-2",t),...i})})});p.displayName="FormItem";let x=r.forwardRef((e,a)=>{let{className:t,...r}=e,{error:i,formItemId:s}=u();return(0,l.jsx)(o.J,{ref:a,className:(0,n.cn)(i&&"text-destructive",t),htmlFor:s,...r})});x.displayName="FormLabel";let h=r.forwardRef((e,a)=>{let{...t}=e,{error:r,formItemId:s,formDescriptionId:n,formMessageId:o}=u();return(0,l.jsx)(i.DX,{ref:a,id:s,"aria-describedby":r?"".concat(n," ").concat(o):"".concat(n),"aria-invalid":!!r,...t})});h.displayName="FormControl";let y=r.forwardRef((e,a)=>{let{className:t,...r}=e,{formDescriptionId:i}=u();return(0,l.jsx)("p",{ref:a,id:i,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});y.displayName="FormDescription";let b=r.forwardRef((e,a)=>{var t;let{className:r,children:i,...s}=e,{error:o,formMessageId:d}=u(),c=o?String(null!=(t=null==o?void 0:o.message)?t:""):i;return c?(0,l.jsx)("p",{ref:a,id:d,className:(0,n.cn)("text-sm font-medium text-destructive",r),...s,children:c}):null});b.displayName="FormMessage"},28328:(e,a,t)=>{t.d(a,{A:()=>l});let l=(0,t(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},30836:(e,a,t)=>{t.d(a,{x:()=>h});var l=t(95155);t(12115);var r=t(28328),i=t(51920),s=t(82733);let n=(0,t(40157).A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var o=t(3235),d=t(15300),c=t(75668),m=t(68801),u=t(71153);u.k5(["Active","Inactive","Maintenance","Out_of_Service"]),u.k5(["Gasoline","Diesel","Electric","Hybrid","CNG","LPG"]);let f=u.Ik({make:u.Yj().min(1,"Make is required"),model:u.Yj().min(1,"Model is required"),year:u.au.number().min(1900,"Year must be 1900 or later").max(new Date().getFullYear()+1,"Year cannot be more than ".concat(new Date().getFullYear()+1)),color:u.Yj().optional(),licensePlate:u.Yj().min(1,"License plate is required"),vin:u.Yj().optional().refine(e=>!e||/^[A-HJ-NPR-Z0-9]{17}$/.test(e),"VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)"),mileage:u.au.number().min(0,"Mileage cannot be negative").optional(),status:u.k5(["active","maintenance","inactive"]).default("active"),notes:u.Yj().optional(),ownerContact:u.Yj().optional(),ownerName:u.Yj().optional(),imageUrl:u.Yj().url("Invalid image URL").optional().or(u.eu("")),initialOdometer:u.au.number().min(0,"Odometer reading cannot be negative").optional()});var p=t(30285),x=t(66695);let h=e=>{let{initialData:a,isEditing:t=!1,isLoading:u=!1,onSubmit:h}=e;return(0,l.jsxs)(x.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,l.jsx)(x.aR,{children:(0,l.jsxs)(x.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(r.A,{className:"h-5 w-5"}),t?"Edit Vehicle":"Add New Vehicle"]})}),(0,l.jsx)(x.Wu,{children:(0,l.jsxs)(c.I,{defaultValues:{status:"active",...a},onSubmit:h,schema:f,className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Basic Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"Make",name:"make",placeholder:"e.g., Toyota, Ford, BMW",icon:r.A,required:!0}),(0,l.jsx)(m.z,{label:"Model",name:"model",placeholder:"e.g., Camry, F-150, X3",icon:r.A,required:!0})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"Year",name:"year",type:"number",placeholder:"e.g., 2023",icon:i.A,min:1900,max:new Date().getFullYear()+1,required:!0}),(0,l.jsx)(m.z,{label:"Color",name:"color",placeholder:"e.g., Red, Blue, Silver",icon:s.A})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Identification"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"License Plate",name:"licensePlate",placeholder:"e.g., ABC-1234",icon:n,required:!0}),(0,l.jsx)(m.z,{label:"VIN (Optional)",name:"vin",placeholder:"17-character VIN (auto-generated if empty)",icon:n,maxLength:17})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Additional Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"Mileage",name:"mileage",type:"number",placeholder:"Current mileage",icon:o.A,min:0}),(0,l.jsx)(m.z,{label:"Status",name:"status",type:"select",placeholder:"Select status",options:[{value:"active",label:"Active"},{value:"maintenance",label:"In Maintenance"},{value:"inactive",label:"Inactive"}],defaultValue:"active"})]}),(0,l.jsx)(m.z,{label:"Notes",name:"notes",type:"textarea",placeholder:"Additional notes about the vehicle...",icon:d.A,rows:3})]}),(0,l.jsx)("div",{className:"flex justify-end pt-6 border-t",children:(0,l.jsx)(p.$,{type:"submit",disabled:u,className:"min-w-[120px]",children:u?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),t?"Updating...":"Creating..."]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(r.A,{className:"h-4 w-4 mr-2"}),t?"Update Vehicle":"Create Vehicle"]})})})]})})]})}},42366:(e,a,t)=>{t.r(a),t.d(a,{useNotifications:()=>i,useWorkHubNotifications:()=>s});var l=t(12115),r=t(96016);let i=()=>{let e=(0,r.C)(e=>e.addNotification),a=(0,r.C)(e=>e.removeNotification),t=(0,r.C)(e=>e.clearAllNotifications),i=(0,r.C)(e=>e.unreadNotificationCount),s=(0,l.useCallback)(a=>{e({message:a,type:"success"})},[e]),n=(0,l.useCallback)(a=>{e({message:a,type:"error"})},[e]),o=(0,l.useCallback)(a=>{e({message:a,type:"warning"})},[e]),d=(0,l.useCallback)(a=>{e({message:a,type:"info"})},[e]),c=(0,l.useCallback)((e,a,t)=>{e?s(a):n(t)},[s,n]),m=(0,l.useCallback)(function(t,l){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:l,type:t}),setTimeout(()=>{let e=r.C.getState().notifications.at(-1);e&&e.message===l&&a(e.id)},i)},[e,a]),u=(0,l.useCallback)(function(){var a;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:t,type:"info"}),null==(a=r.C.getState().notifications.at(-1))?void 0:a.id},[e]),f=(0,l.useCallback)((e,t,l)=>{a(e),t?s(l):n(l)},[a,s,n]);return{clearAllNotifications:t,removeNotification:a,showApiResult:c,showError:n,showInfo:d,showLoading:u,showSuccess:s,showTemporary:m,showWarning:o,unreadCount:i,updateLoadingNotification:f}},s=()=>{let{clearAllNotifications:e,removeNotification:a,showError:t,showInfo:s,showSuccess:n,showWarning:o,unreadCount:d}=i(),c=(0,l.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"delegation",message:e,type:"delegation-update"})},[]),m=(0,l.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),u=(0,l.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:a,showDelegationUpdate:c,showEmployeeUpdate:(0,l.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"employee",message:e,type:"employee-update"})},[]),showError:t,showInfo:s,showSuccess:n,showTaskAssigned:u,showVehicleMaintenance:m,showWarning:o,unreadCount:d}}},51920:(e,a,t)=>{t.d(a,{A:()=>l});let l=(0,t(40157).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},59409:(e,a,t)=>{t.d(a,{bq:()=>u,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>m});var l=t(95155),r=t(31992),i=t(79556),s=t(77381),n=t(10518),o=t(12115),d=t(54036);let c=r.bL;r.YJ;let m=r.WT,u=o.forwardRef((e,a)=>{let{children:t,className:s,...n}=e;return(0,l.jsxs)(r.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),ref:a,...n,children:[t,(0,l.jsx)(r.In,{asChild:!0,children:(0,l.jsx)(i.A,{className:"size-4 opacity-50"})})]})});u.displayName=r.l9.displayName;let f=o.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,l.jsx)(r.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),ref:a,...i,children:(0,l.jsx)(s.A,{className:"size-4"})})});f.displayName=r.PP.displayName;let p=o.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)(r.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),ref:a,...s,children:(0,l.jsx)(i.A,{className:"size-4"})})});p.displayName=r.wn.displayName;let x=o.forwardRef((e,a)=>{let{children:t,className:i,position:s="popper",...n}=e;return(0,l.jsx)(r.ZL,{children:(0,l.jsxs)(r.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",i),position:s,ref:a,...n,children:[(0,l.jsx)(f,{}),(0,l.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,l.jsx)(p,{})]})})});x.displayName=r.UC.displayName,o.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,l.jsx)(r.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),ref:a,...i})}).displayName=r.JU.displayName;let h=o.memo(o.forwardRef((e,a)=>{let{children:t,className:i,...s}=e,c=o.useCallback(e=>{"function"==typeof a?a(e):a&&(a.current=e)},[a]);return(0,l.jsxs)(r.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",i),ref:c,...s,children:[(0,l.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,l.jsx)(r.VF,{children:(0,l.jsx)(n.A,{className:"size-4"})})}),(0,l.jsx)(r.p4,{children:t})]})}));h.displayName=r.q7.displayName,o.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,l.jsx)(r.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),ref:a,...i})}).displayName=r.wv.displayName},62523:(e,a,t)=>{t.d(a,{p:()=>s});var l=t(95155),r=t(12115),i=t(54036);let s=r.forwardRef((e,a)=>{let{className:t,type:r,...s}=e;return(0,l.jsx)("input",{className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,type:r,...s})});s.displayName="Input"},66695:(e,a,t)=>{t.d(a,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>s,aR:()=>n,wL:()=>m});var l=t(95155),r=t(12115),i=t(54036);let s=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),ref:a,...r})});s.displayName="Card";let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),ref:a,...r})});n.displayName="CardHeader";let o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),ref:a,...r})});o.displayName="CardTitle";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{className:(0,i.cn)("text-sm text-muted-foreground",t),ref:a,...r})});d.displayName="CardDescription";let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{className:(0,i.cn)("p-6 pt-0",t),ref:a,...r})});c.displayName="CardContent";let m=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{className:(0,i.cn)("flex items-center p-6 pt-0",t),ref:a,...r})});m.displayName="CardFooter"},68801:(e,a,t)=>{t.d(a,{z:()=>d});var l=t(95155);t(12115);var r=t(62177),i=t(17759),s=t(62523),n=t(88539),o=t(59409);let d=e=>{let{className:a="",disabled:t=!1,label:d,name:c,placeholder:m,render:u,type:f="text",options:p=[],defaultValue:x,icon:h,...y}=e,{control:b}=(0,r.xW)();return(0,l.jsxs)(i.eI,{className:a,children:[(0,l.jsx)(i.lR,{htmlFor:c,children:d}),(0,l.jsx)(r.xI,{control:b,name:c,render:u||(e=>{var a,r;let{field:u,fieldState:{error:b}}=e;return(0,l.jsx)(i.MJ,{children:"select"===f?(0,l.jsxs)(o.l6,{onValueChange:u.onChange,value:u.value||x||"",disabled:t,children:[(0,l.jsx)(o.bq,{className:b?"border-red-500":"",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[h&&(0,l.jsx)(h,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)(o.yv,{placeholder:m||"Select ".concat(d.toLowerCase())})]})}),(0,l.jsx)(o.gC,{children:p.map(e=>(0,l.jsx)(o.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===f?(0,l.jsxs)("div",{className:"relative",children:[h&&(0,l.jsx)(h,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,l.jsx)(n.T,{...u,...y,value:null!=(a=u.value)?a:"",className:"".concat(b?"border-red-500":""," ").concat(h?"pl-10":""),disabled:t,id:c,placeholder:m})]}):(0,l.jsxs)("div",{className:"relative",children:[h&&(0,l.jsx)(h,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,l.jsx)(s.p,{...u,...y,value:null!=(r=u.value)?r:"",className:"".concat(b?"border-red-500":""," ").concat(h?"pl-10":""),disabled:t,id:c,placeholder:m,type:f})]})})})}),(0,l.jsx)(i.C5,{})]})}},75668:(e,a,t)=>{t.d(a,{I:()=>n});var l=t(95155),r=t(90221),i=t(62177),s=t(17759);let n=e=>{let{children:a,defaultValues:t,onSubmit:n,schema:o,className:d="",ariaAttributes:c={}}=e,m=(0,i.mN)({...t&&{defaultValues:t},resolver:(0,r.u)(o)}),u=async e=>{await n(e)};return(0,l.jsx)(s.lV,{...m,children:(0,l.jsx)("form",{onSubmit:m.handleSubmit(u),className:d,...c,children:a})})}},80937:(e,a,t)=>{t.d(a,{NS:()=>p,T$:()=>c,W_:()=>m,Y1:()=>u,lR:()=>f});var l=t(26715),r=t(5041),i=t(90111),s=t(42366),n=t(99605),o=t(75908);let d={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,i.GK)([...d.all],async()=>(await o.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,a)=>{var t;return(0,i.GK)([...d.detail(e)],()=>o.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(t=null==a?void 0:a.enabled)||t),staleTime:3e5,...a})},u=()=>{let e=(0,l.jE)(),{showError:a,showSuccess:t}=(0,s.useNotifications)();return(0,r.n)({mutationFn:e=>{let a=n.M.toCreateRequest(e);return o.vehicleApiService.create(a)},onError:e=>{a("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:a=>{e.invalidateQueries({queryKey:d.all}),t('Vehicle "'.concat(a.licensePlate,'" has been created successfully!'))}})},f=()=>{let e=(0,l.jE)(),{showError:a,showSuccess:t}=(0,s.useNotifications)();return(0,r.n)({mutationFn:e=>{let{data:a,id:t}=e,l=n.M.toUpdateRequest(a);return o.vehicleApiService.update(t,l)},onError:e=>{a("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:a=>{e.invalidateQueries({queryKey:d.all}),e.invalidateQueries({queryKey:d.detail(a.id)}),t('Vehicle "'.concat(a.licensePlate,'" has been updated successfully!'))}})},p=()=>{let e=(0,l.jE)(),{showError:a,showSuccess:t}=(0,s.useNotifications)();return(0,r.n)({mutationFn:e=>o.vehicleApiService.delete(e),onError:e=>{a("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(a,l)=>{e.invalidateQueries({queryKey:d.all}),e.removeQueries({queryKey:d.detail(l)}),t("Vehicle has been deleted successfully!")}})}},82733:(e,a,t)=>{t.d(a,{A:()=>l});let l=(0,t(40157).A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},85057:(e,a,t)=>{t.d(a,{J:()=>d});var l=t(95155),r=t(12115),i=t(40968),s=t(74466),n=t(54036);let o=(0,s.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)(i.b,{ref:a,className:(0,n.cn)(o(),t),...r})});d.displayName=i.b.displayName},88539:(e,a,t)=>{t.d(a,{T:()=>s});var l=t(95155),r=t(12115),i=t(54036);let s=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,l.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...r})});s.displayName="Textarea"},96016:(e,a,t)=>{t.d(a,{C:()=>i});var l=t(65453),r=t(46786);let i=(0,l.v)()((0,r.lt)((0,r.Zr)((e,a)=>({addNotification:a=>e(e=>({notifications:[...e.notifications,{...a,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:a=>e(e=>({notifications:e.notifications.map(e=>e.id===a?{...e,read:!0}:e)})),notifications:[],removeNotification:a=>e(e=>({notifications:e.notifications.filter(e=>e.id!==a)})),setTheme:a=>{e({currentTheme:a})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=a();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))}}]);