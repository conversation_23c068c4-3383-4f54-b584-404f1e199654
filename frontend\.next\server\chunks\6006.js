"use strict";exports.id=6006,exports.ids=[6006],exports.modules={9989:(e,t,r)=>{r.d(t,{Kq:()=>F,UC:()=>U,bL:()=>z,l9:()=>S});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(31355),s=r(96963),c=r(55509),u=(r(25028),r(46059)),p=r(14163),d=r(8730),f=r(65551),x=r(69024),h=r(60687),[y,v]=(0,l.A)("Tooltip",[c.Bk]),g=(0,c.Bk)(),m="TooltipProvider",b="tooltip.open",[w,C]=y(m),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=n.useRef(!0),s=n.useRef(!1),c=n.useRef(0);return n.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(w,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(c.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};T.displayName=m;var k="Tooltip",[E,L]=y(k),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:u}=e,p=C(k,e.__scopeTooltip),d=g(t),[x,y]=n.useState(null),v=(0,s.B)(),m=n.useRef(0),w=a??p.disableHoverableContent,T=u??p.delayDuration,L=n.useRef(!1),[R,j]=(0,f.i)({prop:o,defaultProp:i??!1,onChange:e=>{e?(p.onOpen(),document.dispatchEvent(new CustomEvent(b))):p.onClose(),l?.(e)},caller:k}),M=n.useMemo(()=>R?L.current?"delayed-open":"instant-open":"closed",[R]),P=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,L.current=!1,j(!0)},[j]),_=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,j(!1)},[j]),D=n.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{L.current=!0,j(!0),m.current=0},T)},[T,j]);return n.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,h.jsx)(c.bL,{...d,children:(0,h.jsx)(E,{scope:t,contentId:v,open:R,stateAttribute:M,trigger:x,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{p.isOpenDelayedRef.current?D():P()},[p.isOpenDelayedRef,D,P]),onTriggerLeave:n.useCallback(()=>{w?_():(window.clearTimeout(m.current),m.current=0)},[_,w]),onOpen:P,onClose:_,disableHoverableContent:w,children:r})})};R.displayName=k;var j="TooltipTrigger",M=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=L(j,r),s=C(j,r),u=g(r),d=n.useRef(null),f=(0,i.s)(t,d,a.onTriggerChange),x=n.useRef(!1),y=n.useRef(!1),v=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,h.jsx)(c.Mz,{asChild:!0,...u,children:(0,h.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),x.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});M.displayName=j;var[P,_]=y("TooltipPortal",{forceMount:void 0}),D="TooltipContent",A=n.forwardRef((e,t)=>{let r=_(D,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=L(D,e.__scopeTooltip);return(0,h.jsx)(u.C,{present:n||l.open,children:l.disableHoverableContent?(0,h.jsx)(H,{side:o,...i,ref:t}):(0,h.jsx)(O,{side:o,...i,ref:t})})}),O=n.forwardRef((e,t)=>{let r=L(D,e.__scopeTooltip),o=C(D,e.__scopeTooltip),l=n.useRef(null),a=(0,i.s)(t,l),[s,c]=n.useState(null),{trigger:u,onClose:p}=r,d=l.current,{onPointerInTransitChange:f}=o,x=n.useCallback(()=>{c(null),f(!1)},[f]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(u&&d){let e=e=>y(e,d),t=e=>y(e,u);return u.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[u,d,y,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=u?.contains(t)||d?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,c=l.y,u=a.x,p=a.y;c>n!=p>n&&r<(u-s)*(n-c)/(p-c)+s&&(o=!o)}return o}(r,s);n?x():o&&(x(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,d,s,p,x]),(0,h.jsx)(H,{...e,ref:a})}),[B,I]=y(k,{isInside:!1}),N=(0,d.Dc)("TooltipContent"),H=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...u}=e,p=L(D,r),d=g(r),{onClose:f}=p;return n.useEffect(()=>(document.addEventListener(b,f),()=>document.removeEventListener(b,f)),[f]),n.useEffect(()=>{if(p.trigger){let e=e=>{let t=e.target;t?.contains(p.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[p.trigger,f]),(0,h.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,h.jsxs)(c.UC,{"data-state":p.stateAttribute,...d,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(N,{children:o}),(0,h.jsx)(B,{scope:r,isInside:!0,children:(0,h.jsx)(x.bL,{id:p.contentId,role:"tooltip",children:i||o})})]})})});A.displayName=D;var q="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=g(r);return I(q,r).isInside?null:(0,h.jsx)(c.i3,{...o,...n,ref:t})}).displayName=q;var F=T,z=R,S=M,U=A},69795:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},77368:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])}};