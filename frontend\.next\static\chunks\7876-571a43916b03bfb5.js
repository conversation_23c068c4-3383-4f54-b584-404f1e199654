"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7876],{10518:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},11275:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(12115),i=n(52712);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},29855:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function i(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function o(e,t,n){var i=r(e,t,"set");if(i.set)i.set.call(e,n);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=n}return n}n.d(t,{N:()=>d});var l,a=n(12115),f=n(46081),u=n(6101),c=n(99708),s=n(95155);function d(e){let t=e+"CollectionProvider",[n,r]=(0,f.A)(t),[i,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),o=a.useRef(new Map).current;return(0,s.jsx)(i,{scope:t,itemMap:o,collectionRef:r,children:n})};l.displayName=t;let d=e+"CollectionSlot",p=(0,c.TL)(d),h=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=o(d,n),l=(0,u.s)(t,i.collectionRef);return(0,s.jsx)(p,{ref:l,children:r})});h.displayName=d;let m=e+"CollectionItemSlot",g="data-radix-collection-item",y=(0,c.TL)(m),w=a.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,l=a.useRef(null),f=(0,u.s)(t,l),c=o(m,n);return a.useEffect(()=>(c.itemMap.set(l,{ref:l,...i}),()=>void c.itemMap.delete(l))),(0,s.jsx)(y,{...{[g]:""},ref:f,children:r})});return w.displayName=m,[{Provider:l,Slot:h,ItemSlot:w},function(t){let n=o(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function h(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),i=r>=0?r:n+r;return i<0||i>=n?-1:i}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap},35152:(e,t,n)=>{n.d(t,{Mz:()=>eJ,i3:()=>eQ,UC:()=>eK,bL:()=>eU,Bk:()=>eH});var r=n(12115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>s[e])}function v(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function A(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),f=g(a),u=p(t),c="y"===l,s=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,w=i[f]/2-o[f]/2;switch(u){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:s}=A(u,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:v}=await m({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,s=null!=y?y:s,p={...p,[o]:{...p[o],...w}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(u=!0===v.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):v.rects),{x:c,y:s}=A(u,d,f)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:s="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),g=a[p?"floating"===s?"reference":"floating":s],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:f})),w="floating"===s?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),A=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:v,strategy:f}):w);return{top:(y.top-R.top+m.top)/A.y,bottom:(R.bottom-y.bottom+m.bottom)/A.y,left:(y.left-R.left+m.left)/A.x,right:(R.right-y.right+m.right)/A.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return i.some(t=>e[t]>=0)}async function L(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),u=["left","top"].includes(l)?-1:1,c=o&&f?-1:1,s=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof s?{mainAxis:s,crossAxis:0,alignmentAxis:null}:{mainAxis:s.mainAxis||0,crossAxis:s.crossAxis||0,alignmentAxis:s.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),f?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function E(){return"undefined"!=typeof window}function O(e){return M(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(M(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function M(e){return!!E()&&(e instanceof Node||e instanceof P(e).Node)}function H(e){return!!E()&&(e instanceof Element||e instanceof P(e).Element)}function D(e){return!!E()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function N(e){return!!E()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function j(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=V(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function W(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=z(),n=H(e)?V(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(O(e))}function V(e){return P(e).getComputedStyle(e)}function I(e){return H(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||k(e);return N(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=_(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&j(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=P(i);if(o){let e=Y(l);return t.concat(l,l.visualViewport||[],j(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=V(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=D(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function G(e){return H(e)?e:e.contextElement}function $(e){let t=G(e);if(!D(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=q(t),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let U=u(0);function J(e){let t=P(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function K(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=G(e),a=u(1);t&&(r?H(r)&&(a=$(r)):a=$(e));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===P(l))&&i)?J(l):u(0),c=(o.left+f.x)/a.x,s=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=P(l),t=r&&H(r)?P(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=$(i),t=i.getBoundingClientRect(),r=V(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,p*=e.y,c+=o,s+=l,i=Y(n=P(i))}}return b({width:d,height:p,x:c,y:s})}function Q(e,t){let n=I(e).scrollLeft;return t?t.left+n:K(k(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=k(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let e=z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=I(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(e),f=-n.scrollTop;return"rtl"===V(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(k(e));else if(H(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=D(e)?$(e):u(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=J(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===V(e).position}function en(e,t){if(!D(e)||"fixed"===V(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(W(e))return n;if(!D(e)){let t=_(e);for(;t&&!B(t);){if(H(t)&&!et(t))return t;t=_(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(O(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!F(r)?n:r||function(e){let t=_(e);for(;D(t)&&!B(t);){if(F(t))return t;if(W(t))break;t=_(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),i=k(t),o="fixed"===n,l=K(e,!0,o,t),a={scrollLeft:0,scrollTop:0},f=u(0);if(r||!r&&!o)if(("body"!==O(t)||j(i))&&(a=I(t)),r){let e=K(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else i&&(f.x=Q(i));o&&!r&&i&&(f.x=Q(i));let c=!i||r||o?u(0):Z(i,a);return{x:l.left+a.scrollLeft-f.x-c.x,y:l.top+a.scrollTop-f.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=k(r),a=!!t&&W(t.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},c=u(1),s=u(0),d=D(r);if((d||!d&&!o)&&(("body"!==O(r)||j(l))&&(f=I(r)),D(r))){let e=K(r);c=$(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let p=!l||d||o?u(0):Z(l,f,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+s.x+p.x,y:n.y*c.y-f.scrollTop*c.y+s.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?W(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>H(e)&&"body"!==O(e)),i=null,o="fixed"===V(e).position,l=o?_(e):e;for(;H(l)&&!B(l);){let t=V(l),n=F(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||j(l)&&!n&&function e(t,n){let r=_(t);return!(r===n||!H(r)||B(r))&&("fixed"===V(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=_(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,f,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:$,isElement:H,isRTL:function(e){return"rtl"===V(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:f,elements:u,middlewareData:c}=t,{element:s,padding:p=0}=d(e,t)||{};if(null==s)return{};let w=x(p),v={x:n,y:r},b=m(y(i)),A=g(b),R=await f.getDimensions(s),C="y"===b,S=C?"clientHeight":"clientWidth",T=a.reference[A]+a.reference[b]-v[b]-a.floating[A],L=v[b]-a.reference[b],E=await (null==f.getOffsetParent?void 0:f.getOffsetParent(s)),O=E?E[S]:0;O&&await (null==f.isElement?void 0:f.isElement(E))||(O=u.floating[S]||a.floating[A]);let P=O/2-R[A]/2-1,k=o(w[C?"top":"left"],P),M=o(w[C?"bottom":"right"],P),H=O-R[A]-M,D=O/2-R[A]/2+(T/2-L/2),N=l(k,o(D,H)),j=!c.arrow&&null!=h(i)&&D!==N&&a.reference[A]/2-(D<k?k:M)-R[A]/2<0,W=j?D<k?D-k:D-H:0;return{[b]:v[b]+W,data:{[b]:N,centerOffset:D-N-W,...j&&{alignmentOffset:W}},reset:j}}}),ef=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var eu=n(47650),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function es(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!es(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!es(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await L(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),s={x:n,y:r},h=await C(t,c),g=y(p(i)),w=m(g),v=s[w],x=s[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,o(v,r))}if(f){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=u.fn({...t,[w]:v,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:f}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:u=!0}=d(e,t),c={x:n,y:r},s=y(i),h=m(s),g=c[h],w=c[s],v=d(a,t),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(f){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,A;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[s]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[s])||0)+(t?0:x.crossAxis),r=o.reference[s]+o.reference[e]+(t?0:(null==(A=l.offset)?void 0:A[s])||0)-(t?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[s]:w}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:u,initialPlacement:c,platform:s,elements:x}=t,{mainAxis:b=!0,crossAxis:A=!0,fallbackPlacements:R,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:L=!0,...E}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let O=p(a),P=y(c),k=p(c)===c,M=await (null==s.isRTL?void 0:s.isRTL(x.floating)),H=R||(k||!L?[v(c)]:function(e){let t=v(e);return[w(e),t,w(t)]}(c)),D="none"!==T;!R&&D&&H.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(c,L,T,M));let N=[c,...H],j=await C(t,E),W=[],F=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&W.push(j[O]),A){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=v(l)),[l,v(l)]}(a,u,M);W.push(j[e[0]],j[e[1]])}if(F=[...F,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(i=f.flip)?void 0:i.index)||0)+1,t=N[e];if(t&&("alignment"!==A||P===y(t)||F.every(e=>e.overflows[0]>0&&y(e.placement)===P)))return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(o=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=F.filter(e=>{if(D){let t=y(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:f,rects:u,platform:c,elements:s}=t,{apply:m=()=>{},...g}=d(e,t),w=await C(t,g),v=p(f),x=h(f),b="y"===y(f),{width:A,height:R}=u.floating;"top"===v||"bottom"===v?(i=v,a=x===(await (null==c.isRTL?void 0:c.isRTL(s.floating))?"start":"end")?"left":"right"):(a=v,i="end"===x?"top":"bottom");let S=R-w.top-w.bottom,T=A-w.left-w.right,L=o(R-w[i],S),E=o(A-w[a],T),O=!t.middlewareData.shift,P=L,k=E;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=S),O&&!x){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?k=A-2*(0!==e||0!==t?e+t:l(w.left,w.right)):P=R-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:k,availableHeight:P});let M=await c.getDimensions(s.floating);return A!==M.width||R!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=S(await C(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=S(await C(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}),eA=(e,t)=>({...em(e),options:[e,t]});var eR=n(63655),eC=n(95155),eS=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eC.jsx)(eR.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eT=n(6101),eL=n(46081),eE=n(39033),eO=n(52712),eP=n(11275),ek="Popper",[eM,eH]=(0,eL.A)(ek),[eD,eN]=eM(ek),ej=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eC.jsx)(eD,{scope:t,anchor:i,onAnchorChange:o,children:n})};ej.displayName=ek;var eW="PopperAnchor",eF=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eN(eW,n),a=r.useRef(null),f=(0,eT.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eC.jsx)(eR.sG.div,{...o,ref:f})});eF.displayName=eW;var ez="PopperContent",[eB,eV]=eM(ez),eI=r.forwardRef((e,t)=>{var n,i,a,u,c,s,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:v=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:A=0,sticky:R="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:T,...L}=e,E=eN(ez,h),[O,P]=r.useState(null),M=(0,eT.s)(t,e=>P(e)),[H,D]=r.useState(null),N=(0,eP.X)(H),j=null!=(d=null==N?void 0:N.width)?d:0,W=null!=(p=null==N?void 0:N.height)?p:0,F="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},z=Array.isArray(b)?b:[b],B=z.length>0,V={padding:F,boundary:z.filter(eq),altBoundary:B},{refs:I,floatingStyles:_,placement:Y,isPositioned:q,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:u,open:c}=e,[s,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);es(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),v=r.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),x=r.useCallback(e=>{e!==C.current&&(C.current=e,w(e))},[]),b=l||m,A=a||y,R=r.useRef(null),C=r.useRef(null),S=r.useRef(s),T=null!=u,L=eh(u),E=eh(o),O=eh(c),P=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};E.current&&(e.platform=E.current),ef(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};k.current&&!es(S.current,t)&&(S.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,E,O]);ec(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let k=r.useRef(!1);ec(()=>(k.current=!0,()=>{k.current=!1}),[]),ec(()=>{if(b&&(R.current=b),A&&(C.current=A),b&&A){if(L.current)return L.current(b,A,P);P()}},[b,A,P,L,T]);let M=r.useMemo(()=>({reference:R,floating:C,setReference:v,setFloating:x}),[v,x]),H=r.useMemo(()=>({reference:b,floating:A}),[b,A]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!H.floating)return e;let t=ep(H.floating,s.x),r=ep(H.floating,s.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(H.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,H.floating,s.x,s.y]);return r.useMemo(()=>({...s,update:P,refs:M,elements:H,floatingStyles:D}),[s,P,M,H,D])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=G(e),h=a||u?[...p?X(p):[],...X(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&s?function(e,t){let n,r=null,i=k(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||t(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),v={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...v,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,v)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let w=d?K(e):null;return d&&function t(){let r=K(e);w&&!el(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===S})},elements:{reference:E.anchor},middleware:[eg({mainAxis:g+W,alignmentAxis:w}),x&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ew():void 0,...V}),x&&ev({...V}),ex({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),H&&eA({element:H,padding:v}),eG({arrowWidth:j,arrowHeight:W}),C&&eb({strategy:"referenceHidden",...V})]}),[U,J]=e$(Y),Q=(0,eE.c)(T);(0,eO.N)(()=>{q&&(null==Q||Q())},[q,Q]);let Z=null==(n=$.arrow)?void 0:n.x,ee=null==(i=$.arrow)?void 0:i.y,et=(null==(a=$.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eO.N)(()=>{O&&er(window.getComputedStyle(O).zIndex)},[O]),(0,eC.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{..._,transform:q?_.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(u=$.transformOrigin)?void 0:u.x,null==(c=$.transformOrigin)?void 0:c.y].join(" "),...(null==(s=$.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eB,{scope:h,placedSide:U,onArrowChange:D,arrowX:Z,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(eR.sG.div,{"data-side":U,"data-align":J,...L,ref:M,style:{...L.style,animation:q?void 0:"none"}})})})});eI.displayName=ez;var e_="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eV(e_,n),o=eX[i.placedSide];return(0,eC.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eS,{...r,ref:t,style:{...r.style,display:"block"}})})});function eq(e){return null!==e}eY.displayName=e_;var eG=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,s=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=e$(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+s/2,y=(null!=(l=null==(i=u.arrow)?void 0:i.y)?l:0)+d/2,w="",v="";return"bottom"===p?(w=c?m:"".concat(g,"px"),v="".concat(-d,"px")):"top"===p?(w=c?m:"".concat(g,"px"),v="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),v=c?m:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),v=c?m:"".concat(y,"px")),{data:{x:w,y:v}}}});function e$(e){let[t,n="center"]=e.split("-");return[t,n]}var eU=ej,eJ=eF,eK=eI,eQ=eY},94315:(e,t,n)=>{n.d(t,{jH:()=>o});var r=n(12115);n(95155);var i=r.createContext(void 0);function o(e){let t=r.useContext(i);return e||t||"ltr"}}}]);