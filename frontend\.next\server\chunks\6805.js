"use strict";exports.id=6805,exports.ids=[6805],exports.modules={2093:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},14555:(e,t,r)=>{r.d(t,{C1:()=>H,bL:()=>N,q7:()=>q});var n=r(43210),o=r(70569),a=r(98599),i=r(11273),d=r(14163),l=r(72942),s=r(65551),u=r(43),c=r(18853),f=r(83721),p=r(46059),h=r(60687),m="Radio",[v,w]=(0,i.A)(m),[y,x]=v(m),b=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:l=!1,required:s,disabled:u,value:c="on",onCheck:f,form:p,...m}=e,[v,w]=n.useState(null),x=(0,a.s)(t,e=>w(e)),b=n.useRef(!1),g=!v||p||!!v.closest("form");return(0,h.jsxs)(y,{scope:r,checked:l,disabled:u,children:[(0,h.jsx)(d.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":S(l),"data-disabled":u?"":void 0,disabled:u,value:c,...m,ref:x,onClick:(0,o.m)(e.onClick,e=>{l||f?.(),g&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),g&&(0,h.jsx)(R,{control:v,bubbles:!b.current,name:i,value:c,checked:l,required:s,disabled:u,form:p,style:{transform:"translateX(-100%)"}})]})});b.displayName=m;var g="RadioIndicator",k=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,a=x(g,r);return(0,h.jsx)(p.C,{present:n||a.checked,children:(0,h.jsx)(d.sG.span,{"data-state":S(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t})})});k.displayName=g;var R=n.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:o=!0,...i},l)=>{let s=n.useRef(null),u=(0,a.s)(s,l),p=(0,f.Z)(r),m=(0,c.X)(t);return n.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[p,r,o]),(0,h.jsx)(d.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:u,style:{...i.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function S(e){return e?"checked":"unchecked"}R.displayName="RadioBubbleInput";var j=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],M="RadioGroup",[A,E]=(0,i.A)(M,[l.RG,w]),D=(0,l.RG)(),P=w(),[C,I]=A(M),G=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:a,required:i=!1,disabled:c=!1,orientation:f,dir:p,loop:m=!0,onValueChange:v,...w}=e,y=D(r),x=(0,u.jH)(p),[b,g]=(0,s.i)({prop:a,defaultProp:o??null,onChange:v,caller:M});return(0,h.jsx)(C,{scope:r,name:n,required:i,disabled:c,value:b,onValueChange:g,children:(0,h.jsx)(l.bL,{asChild:!0,...y,orientation:f,dir:x,loop:m,children:(0,h.jsx)(d.sG.div,{role:"radiogroup","aria-required":i,"aria-orientation":f,"data-disabled":c?"":void 0,dir:x,...w,ref:t})})})});G.displayName=M;var L="RadioGroupItem",_=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...d}=e,s=I(L,r),u=s.disabled||i,c=D(r),f=P(r),p=n.useRef(null),m=(0,a.s)(t,p),v=s.value===d.value,w=n.useRef(!1);return n.useEffect(()=>{let e=e=>{j.includes(e.key)&&(w.current=!0)},t=()=>w.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,h.jsx)(l.q7,{asChild:!0,...c,focusable:!u,active:v,children:(0,h.jsx)(b,{disabled:u,required:s.required,checked:v,...f,...d,name:s.name,ref:m,onCheck:()=>s.onValueChange(d.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(d.onFocus,()=>{w.current&&p.current?.click()})})})});_.displayName=L;var z=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=P(r);return(0,h.jsx)(k,{...o,...n,ref:t})});z.displayName="RadioGroupIndicator";var N=G,q=_,H=z},24851:(e,t,r)=>{r.d(t,{CC:()=>X,Q6:()=>T,bL:()=>U,zi:()=>V});var n=r(43210),o=r(67969),a=r(70569),i=r(98599),d=r(11273),l=r(65551),s=r(43),u=r(83721),c=r(18853),f=r(14163),p=r(9510),h=r(60687),m=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],w={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[x,b,g]=(0,p.N)(y),[k,R]=(0,d.A)(y,[g]),[S,j]=k(y),M=n.forwardRef((e,t)=>{let{name:r,min:i=0,max:d=100,step:s=1,orientation:u="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[i],value:w,onValueChange:y=()=>{},onValueCommit:b=()=>{},inverted:g=!1,form:k,...R}=e,j=n.useRef(new Set),M=n.useRef(0),A="horizontal"===u,[E=[],C]=(0,l.i)({prop:w,defaultProp:p,onChange:e=>{let t=[...j.current];t[M.current]?.focus(),y(e)}}),I=n.useRef(E);function G(e,t,{commit:r}={commit:!1}){let n=(String(s).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-i)/s)*s+i,n),l=(0,o.q)(a,[i,d]);C((e=[])=>{let n=function(e=[],t,r){let n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,l,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*s))return e;{M.current=n.indexOf(l);let t=String(n)!==String(e);return t&&r&&b(n),t?n:e}})}return(0,h.jsx)(S,{scope:e.__scopeSlider,name:r,disabled:c,min:i,max:d,valueIndexToChangeRef:M,thumbs:j.current,values:E,orientation:u,form:k,children:(0,h.jsx)(x.Provider,{scope:e.__scopeSlider,children:(0,h.jsx)(x.Slot,{scope:e.__scopeSlider,children:(0,h.jsx)(A?D:P,{"aria-disabled":c,"data-disabled":c?"":void 0,...R,ref:t,onPointerDown:(0,a.m)(R.onPointerDown,()=>{c||(I.current=E)}),min:i,max:d,inverted:g,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(E,e);G(e,t)},onSlideMove:c?void 0:function(e){G(e,M.current)},onSlideEnd:c?void 0:function(){let e=I.current[M.current];E[M.current]!==e&&b(E)},onHomeKeyDown:()=>!c&&G(i,0,{commit:!0}),onEndKeyDown:()=>!c&&G(d,E.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!c){let r=m.includes(e.key)||e.shiftKey&&v.includes(e.key),n=M.current;G(E[n]+s*(r?10:1)*t,n,{commit:!0})}}})})})})});M.displayName=y;var[A,E]=k(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),D=n.forwardRef((e,t)=>{let{min:r,max:o,dir:a,inverted:d,onSlideStart:l,onSlideMove:u,onSlideEnd:c,onStepKeyDown:f,...p}=e,[m,v]=n.useState(null),y=(0,i.s)(t,e=>v(e)),x=n.useRef(void 0),b=(0,s.jH)(a),g="ltr"===b,k=g&&!d||!g&&d;function R(e){let t=x.current||m.getBoundingClientRect(),n=O([0,t.width],k?[r,o]:[o,r]);return x.current=t,n(e-t.left)}return(0,h.jsx)(A,{scope:e.__scopeSlider,startEdge:k?"left":"right",endEdge:k?"right":"left",direction:k?1:-1,size:"width",children:(0,h.jsx)(C,{dir:b,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=R(e.clientX);l?.(t)},onSlideMove:e=>{let t=R(e.clientX);u?.(t)},onSlideEnd:()=>{x.current=void 0,c?.()},onStepKeyDown:e=>{let t=w[k?"from-left":"from-right"].includes(e.key);f?.({event:e,direction:t?-1:1})}})})}),P=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:a,onSlideStart:d,onSlideMove:l,onSlideEnd:s,onStepKeyDown:u,...c}=e,f=n.useRef(null),p=(0,i.s)(t,f),m=n.useRef(void 0),v=!a;function y(e){let t=m.current||f.current.getBoundingClientRect(),n=O([0,t.height],v?[o,r]:[r,o]);return m.current=t,n(e-t.top)}return(0,h.jsx)(A,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,h.jsx)(C,{"data-orientation":"vertical",...c,ref:p,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);d?.(t)},onSlideMove:e=>{let t=y(e.clientY);l?.(t)},onSlideEnd:()=>{m.current=void 0,s?.()},onStepKeyDown:e=>{let t=w[v?"from-bottom":"from-top"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),C=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:i,onHomeKeyDown:d,onEndKeyDown:l,onStepKeyDown:s,...u}=e,c=j(y,r);return(0,h.jsx)(f.sG.span,{...u,ref:t,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Home"===e.key?(d(e),e.preventDefault()):"End"===e.key?(l(e),e.preventDefault()):m.concat(v).includes(e.key)&&(s(e),e.preventDefault())}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),I="SliderTrack",G=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=j(I,r);return(0,h.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});G.displayName=I;var L="SliderRange",_=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,a=j(L,r),d=E(L,r),l=n.useRef(null),s=(0,i.s)(t,l),u=a.values.length,c=a.values.map(e=>K(e,a.min,a.max)),p=u>1?Math.min(...c):0,m=100-Math.max(...c);return(0,h.jsx)(f.sG.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...o,ref:s,style:{...e.style,[d.startEdge]:p+"%",[d.endEdge]:m+"%"}})});_.displayName=L;var z="SliderThumb",N=n.forwardRef((e,t)=>{let r=b(e.__scopeSlider),[o,a]=n.useState(null),d=(0,i.s)(t,e=>a(e)),l=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,h.jsx)(q,{...e,ref:d,index:l})}),q=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:d,...l}=e,s=j(z,r),u=E(z,r),[p,m]=n.useState(null),v=(0,i.s)(t,e=>m(e)),w=!p||s.form||!!p.closest("form"),y=(0,c.X)(p),b=s.values[o],g=void 0===b?0:K(b,s.min,s.max),k=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(o,s.values.length),R=y?.[u.size],S=R?function(e,t,r){let n=e/2,o=O([0,50],[0,n]);return(n-o(t)*r)*r}(R,g,u.direction):0;return n.useEffect(()=>{if(p)return s.thumbs.add(p),()=>{s.thumbs.delete(p)}},[p,s.thumbs]),(0,h.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:`calc(${g}% + ${S}px)`},children:[(0,h.jsx)(x.ItemSlot,{scope:e.__scopeSlider,children:(0,h.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||k,"aria-valuemin":s.min,"aria-valuenow":b,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...l,ref:v,style:void 0===b?{display:"none"}:e.style,onFocus:(0,a.m)(e.onFocus,()=>{s.valueIndexToChangeRef.current=o})})}),w&&(0,h.jsx)(H,{name:d??(s.name?s.name+(s.values.length>1?"[]":""):void 0),form:s.form,value:b},o)]})});N.displayName=z;var H=n.forwardRef(({__scopeSlider:e,value:t,...r},o)=>{let a=n.useRef(null),d=(0,i.s)(a,o),l=(0,u.Z)(t);return n.useEffect(()=>{let e=a.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(l!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[l,t]),(0,h.jsx)(f.sG.input,{style:{display:"none"},...r,ref:d,defaultValue:t})});function K(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function O(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}H.displayName="RadioBubbleInput";var U=M,X=G,T=_,V=N},36141:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},54608:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},93704:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])}};