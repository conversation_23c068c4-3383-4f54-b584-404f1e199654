/**
 * @file i18n Routing Configuration
 * @module i18n/routing
 *
 * Centralized routing configuration for next-intl.
 * Defines routing behavior, locale prefixes, and navigation settings.
 */

import { defineRouting } from 'next-intl/routing';

import { defaultLocale, locales } from './config';

/**
 * Routing configuration for next-intl
 * 
 * This configuration is shared between middleware and navigation APIs
 * to ensure consistent locale handling throughout the application.
 */
export const routing = defineRouting({
  // List of all supported locales
  locales,
  
  // Default locale used when no locale matches
  defaultLocale,
  
  // Always show locale prefix in URLs for consistency
  localePrefix: 'always',
});
