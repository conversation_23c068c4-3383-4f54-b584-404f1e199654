(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1569],{29797:(e,a,s)=>{"use strict";s.d(a,{$o:()=>j,Eb:()=>g,Iu:()=>m,WA:()=>h,cU:()=>x,dK:()=>c,n$:()=>u});var t=s(95155),l=s(965),r=s(73158),n=s(3561),i=s(12115),o=s(30285),d=s(54036);let c=i.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex justify-center",s),ref:a,...l})});c.displayName="Pagination";let m=i.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("ul",{className:(0,d.cn)("flex flex-row items-center gap-1",s),ref:a,...l})});m.displayName="PaginationContent";let x=i.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("li",{className:(0,d.cn)("",s),ref:a,...l})});x.displayName="PaginationItem";let u=i.forwardRef((e,a)=>{let{className:s,isActive:l,...r}=e;return(0,t.jsx)(o.$,{"aria-current":l?"page":void 0,className:(0,d.cn)("h-9 w-9",s),ref:a,size:"icon",variant:l?"outline":"ghost",...r})});u.displayName="PaginationLink";let g=i.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsxs)(o.$,{className:(0,d.cn)("h-9 w-9 gap-1",s),ref:a,size:"icon",variant:"ghost",...r,children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Previous page"})]})});g.displayName="PaginationPrevious";let h=i.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsxs)(o.$,{className:(0,d.cn)("h-9 w-9 gap-1",s),ref:a,size:"icon",variant:"ghost",...l,children:[(0,t.jsx)(r.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Next page"})]})});h.displayName="PaginationNext";let p=i.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",s),ref:a,...l,children:[(0,t.jsx)(n.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"More pages"})]})});function j(e){let{className:a,currentPage:s,onPageChange:l,totalPages:r}=e,n=(()=>{let e=[];e.push(1);let a=Math.max(2,s-1),t=Math.min(r-1,s+1);a>2&&e.push("ellipsis1");for(let s=a;s<=t;s++)e.push(s);return t<r-1&&e.push("ellipsis2"),r>1&&e.push(r),e})();return r<=1?null:(0,t.jsx)(c,{className:a,children:(0,t.jsxs)(m,{children:[(0,t.jsx)(x,{children:(0,t.jsx)(g,{"aria-disabled":1===s?"true":void 0,"aria-label":"Go to previous page",disabled:1===s,onClick:()=>l(s-1)})}),n.map((e,a)=>"ellipsis1"===e||"ellipsis2"===e?(0,t.jsx)(x,{children:(0,t.jsx)(p,{})},"ellipsis-".concat(a)):(0,t.jsx)(x,{children:(0,t.jsx)(u,{"aria-label":"Go to page ".concat(e),isActive:s===e,onClick:()=>l(e),children:e})},"page-".concat(e))),(0,t.jsx)(x,{children:(0,t.jsx)(h,{"aria-disabled":s===r?"true":void 0,"aria-label":"Go to next page",disabled:s===r,onClick:()=>l(s+1)})})]})})}p.displayName="PaginationEllipsis"},30940:()=>{},33271:(e,a,s)=>{"use strict";s.d(a,{k:()=>h});var t=s(95155),l=s(18018),r=s(50172),n=s(68718),i=s(15300),o=s(60679),d=s(12115),c=s(6560),m=s(44838),x=s(53712),u=s(54036),g=s(16146);function h(e){let{className:a,csvData:s,enableCsv:h=!1,entityId:p,fileName:j,reportContentId:b,reportType:N,tableId:f}=e,[v,y]=(0,d.useState)(!1),[w,C]=(0,d.useState)(!1),{showFormSuccess:k,showFormError:A}=(0,x.t6)(),D=async()=>{y(!0);try{let e="/api/reports/".concat(N).concat(p?"/".concat(p):""),a=document.createElement("a");a.href=e,a.download="".concat(j,".pdf"),a.target="_blank",document.body.append(a),a.click(),a.remove(),k({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),A("PDF download failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{y(!1)}},S=async()=>{if(h){C(!0);try{if((null==s?void 0:s.data)&&s.headers)(0,g.og)(s.data,s.headers,"".concat(j,".csv"));else if(f){let e=(0,g.tL)(f);(0,g.og)(e.data,e.headers,"".concat(j,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");k({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),A("CSV generation failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{C(!1)}}},E=v||w;return(0,t.jsxs)("div",{className:(0,u.cn)("flex items-center gap-2 no-print",a),children:[(0,t.jsx)(c.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,t.jsx)(l.A,{className:"size-4"})}),(0,t.jsxs)(m.rI,{children:[(0,t.jsx)(m.ty,{asChild:!0,children:(0,t.jsx)(c.r,{actionType:"secondary","aria-label":"Download report",disabled:E,size:"icon",title:"Download Report",children:E?(0,t.jsx)(r.A,{className:"size-4 animate-spin"}):(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsxs)(m.SQ,{align:"end",children:[(0,t.jsxs)(m._2,{disabled:v,onClick:D,children:[v?(0,t.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(i.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download PDF"})]}),h&&(0,t.jsxs)(m._2,{disabled:w,onClick:S,children:[w?(0,t.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(o.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download CSV"})]})]})]})]})}},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>m});var t=s(95155),l=s(12115),r=s(54036);let n=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),ref:a,...l})});n.displayName="Card";let i=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),ref:a,...l})});i.displayName="CardHeader";let o=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),ref:a,...l})});o.displayName="CardTitle";let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("text-sm text-muted-foreground",s),ref:a,...l})});d.displayName="CardDescription";let c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("p-6 pt-0",s),ref:a,...l})});c.displayName="CardContent";let m=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("flex items-center p-6 pt-0",s),ref:a,...l})});m.displayName="CardFooter"},77023:(e,a,s)=>{"use strict";s.d(a,{gO:()=>x,jt:()=>p,pp:()=>u});var t=s(95155),l=s(11133),r=s(50172);s(12115);var n=s(6560),i=s(55365),o=s(68856),d=s(54036);let c={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:a,className:s,data:l,emptyComponent:r,error:n,errorComponent:i,isLoading:o,loadingComponent:c,onRetry:m}=e;return o?c||(0,t.jsx)(h,{...s&&{className:s},text:"Loading..."}):n?i||(0,t.jsx)(g,{...s&&{className:s},message:n,...m&&{onRetry:m}}):!l||Array.isArray(l)&&0===l.length?r||(0,t.jsx)("div",{className:(0,d.cn)("text-center py-8",s),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:s,children:a(l)})}function u(e){let{className:a,description:s,icon:l,primaryAction:r,secondaryAction:i,title:o}=e;return(0,t.jsxs)("div",{className:(0,d.cn)("space-y-6 text-center py-12",a),children:[l&&(0,t.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,t.jsx)(l,{className:"h-10 w-10 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:o}),s&&(0,t.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[r&&(0,t.jsx)(n.r,{actionType:"primary",asChild:!!r.href,icon:r.icon,onClick:r.onClick,children:r.href?(0,t.jsx)("a",{href:r.href,children:r.label}):r.label}),i&&(0,t.jsx)(n.r,{actionType:"tertiary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,t.jsx)("a",{href:i.href,children:i.label}):i.label})]})]})}function g(e){let{className:a,message:s,onRetry:o}=e;return(0,t.jsxs)(i.Fc,{className:(0,d.cn)("my-4",a),variant:"destructive",children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsx)(i.XL,{children:"Error"}),(0,t.jsx)(i.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),o&&(0,t.jsx)(n.r,{actionType:"tertiary",icon:(0,t.jsx)(r.A,{className:"size-4"}),onClick:o,size:"sm",children:"Try Again"})]})})]})}function h(e){let{className:a,fullPage:s=!1,size:l="md",text:n}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",a),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(r.A,{className:(0,d.cn)("animate-spin text-primary",c[l])}),n&&(0,t.jsx)("span",{className:(0,d.cn)("mt-2 text-muted-foreground",m[l]),children:n})]})})}function p(e){let{className:a,count:s=1,testId:l="loading-skeleton",variant:r="default"}=e;return"card"===r?(0,t.jsx)("div",{className:(0,d.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",a),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,t.jsx)(o.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(o.E,{className:"mb-1 h-7 w-3/4"}),(0,t.jsx)(o.E,{className:"mb-3 h-4 w-1/2"}),(0,t.jsx)(o.E,{className:"my-3 h-px w-full"}),(0,t.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.E,{className:"mr-2.5 size-5 rounded-full"}),(0,t.jsx)(o.E,{className:"h-5 w-2/3"})]},a))})]})]},a))}):"table"===r?(0,t.jsxs)("div",{className:(0,d.cn)("space-y-3",a),"data-testid":l,children:[(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,a)=>(0,t.jsx)(o.E,{className:"h-8 flex-1"},a))}),Array(s).fill(0).map((e,a)=>(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,a)=>(0,t.jsx)(o.E,{className:"h-6 flex-1"},a))},a))]}):"list"===r?(0,t.jsx)("div",{className:(0,d.cn)("space-y-3",a),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(o.E,{className:"size-12 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)(o.E,{className:"h-4 w-1/3"}),(0,t.jsx)(o.E,{className:"h-4 w-full"})]})]},a))}):"stats"===r?(0,t.jsx)("div",{className:(0,d.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",a),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(o.E,{className:"h-5 w-1/3"}),(0,t.jsx)(o.E,{className:"size-5 rounded-full"})]}),(0,t.jsx)(o.E,{className:"mt-3 h-8 w-1/2"}),(0,t.jsx)(o.E,{className:"mt-2 h-4 w-2/3"})]},a))}):(0,t.jsx)("div",{className:(0,d.cn)("space-y-2",a),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsx)(o.E,{className:"h-5 w-full"},a))})}},80760:(e,a,s)=>{Promise.resolve().then(s.bind(s,99178))},83103:()=>{},99178:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>R});var t=s(95155),l=s(41784),r=s(83343);s(30940),s(83103);var n=s(77381),i=s(79556),o=s(75074),d=s(25318),c=s(98328),m=s(83662),x=s(50286),u=s(35695),g=s(12115),h=s(66695),p=s(54036);let j={Cancelled:{bg:"bg-gradient-to-br from-red-50 to-red-100",border:"border-red-200",text:"text-red-700"},Completed:{bg:"bg-gradient-to-br from-purple-50 to-purple-100",border:"border-purple-200",text:"text-purple-700"},Confirmed:{bg:"bg-gradient-to-br from-green-50 to-green-100",border:"border-green-200",text:"text-green-700"},In_Progress:{bg:"bg-gradient-to-br from-yellow-50 to-yellow-100",border:"border-yellow-200",text:"text-yellow-700"},No_details:{bg:"bg-gradient-to-br from-gray-50 to-gray-100",border:"border-gray-200",text:"text-gray-700"},Planned:{bg:"bg-gradient-to-br from-blue-50 to-blue-100",border:"border-blue-200",text:"text-blue-700"}},b=e=>e.replace("_"," ");function N(e){let{className:a,delegations:s}=e,l=s.length,r=s.reduce((e,a)=>{let s=a.status;return e[s]=(e[s]||0)+1,e},{}),n=s.reduce((e,a)=>{var s;return e+((null==(s=a.delegates)?void 0:s.length)||0)},0),i=Object.entries(r).sort((e,a)=>{let[,s]=e,[,t]=a;return t-s}).map(e=>{let[a]=e;return a});return(0,t.jsxs)("div",{className:(0,p.cn)("mt-6 mb-8",a),children:[(0,t.jsxs)("div",{className:"mb-6 grid grid-cols-2 gap-4 lg:grid-cols-4",children:[(0,t.jsx)(f,{className:"border-slate-200 bg-gradient-to-br from-slate-50 to-slate-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegations",textColor:"text-slate-700",value:l,valueColor:"text-slate-800"}),(0,t.jsx)(f,{className:"border-indigo-200 bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegates",textColor:"text-indigo-700",value:n,valueColor:"text-indigo-800"}),i.slice(0,2).map(e=>{let a=j[e];return(0,t.jsx)(f,{className:(0,p.cn)(a.bg,a.border,"shadow-sm hover:shadow-md transition-shadow"),label:b(e),textColor:a.text,value:r[e],valueColor:a.text},e)})]}),i.length>2&&(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-6",children:i.slice(2).map(e=>{let a=j[e];return(0,t.jsx)(f,{className:(0,p.cn)(a.bg,a.border,"shadow-sm hover:shadow-md transition-shadow"),compact:!0,label:b(e),textColor:a.text,value:r[e],valueColor:a.text},e)})})]})}function f(e){let{className:a,compact:s=!1,label:l,textColor:r="text-gray-600",value:n,valueColor:i="text-gray-800"}=e;return(0,t.jsx)(h.Zp,{className:(0,p.cn)("overflow-hidden border transition-all duration-200",a),children:(0,t.jsxs)(h.Wu,{className:(0,p.cn)("text-center",s?"p-3":"p-4"),children:[(0,t.jsx)("div",{className:(0,p.cn)("font-bold",s?"text-xl mb-1":"text-3xl mb-2",i),children:n.toLocaleString()}),(0,t.jsx)("div",{className:(0,p.cn)("font-medium",s?"text-xs":"text-sm",r),children:l})]})})}var v=s(33271),y=s(26126),w=s(30285),C=s(62523),k=s(77023),A=s(29797),D=s(59409),S=s(85127),E=s(17841),z=s(99673);let F=["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"],P=e=>{switch(e){case"Cancelled":return"bg-red-100 text-red-800 border-red-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}},T=e=>{if(!e)return"N/A";try{return(0,l.GP)((0,r.H)(e),"MMM d, yyyy")}catch(e){return"Invalid Date"}};function R(){return(0,t.jsx)(g.Suspense,{fallback:(0,t.jsx)("div",{className:"py-10 text-center",children:"Loading report..."}),children:(0,t.jsx)(L,{})})}function L(){let e=(0,u.useSearchParams)(),{data:a,error:s,isLoading:l,refetch:r}=(0,E.BD)(),j=(0,g.useMemo)(()=>a||[],[a]),[b,f]=(0,g.useState)(""),[R,L]=(0,g.useState)(""),[_,I]=(0,g.useState)("all"),[M,Z]=(0,g.useState)({}),[$,V]=(0,g.useState)(1),[H]=(0,g.useState)(10),[K,B]=(0,g.useState)("durationFrom"),[G,O]=(0,g.useState)("asc");(0,g.useEffect)(()=>{let a=(null==e?void 0:e.get("searchTerm"))||"",s=(null==e?void 0:e.get("status"))||"all";f(a),L(a),I(s)},[e]),(0,g.useEffect)(()=>{let e=setTimeout(()=>{L(b)},300);return()=>clearTimeout(e)},[b]),(0,g.useEffect)(()=>{document.title="Delegation List Report"},[]);let U=(0,g.useCallback)((e,a,s)=>[...e].sort((e,t)=>{let l,r;switch(a){case"delegates":var n,i;l=(null==(n=e.delegates)?void 0:n.length)||0,r=(null==(i=t.delegates)?void 0:i.length)||0;break;case"durationFrom":l=new Date(e.durationFrom).getTime(),r=new Date(t.durationFrom).getTime();break;case"eventName":l=e.eventName.toLowerCase(),r=t.eventName.toLowerCase();break;case"location":l=e.location.toLowerCase(),r=t.location.toLowerCase();break;case"status":l=e.status,r=t.status;break;default:l=e[a],r=t[a]}return null==l||null==r?0:l<r?"asc"===s?-1:1:l>r?"asc"===s?1:-1:0}),[]),W=(0,g.useMemo)(()=>{let e=[...j];if(R){let a=R.toLowerCase();e=e.filter(e=>{var s,t;return e.eventName.toLowerCase().includes(a)||e.location.toLowerCase().includes(a)||(null==(s=e.delegates)?void 0:s.some(e=>e.name.toLowerCase().includes(a)))||(null==(t=e.notes)?void 0:t.toLowerCase().includes(a))||e.status.toLowerCase().includes(a)})}return"all"!==_&&(e=e.filter(e=>e.status===_)),M.from&&(e=e.filter(e=>new Date(e.durationFrom)>=M.from)),M.to&&(e=e.filter(e=>new Date(e.durationFrom)<=M.to)),U(e,K,G)},[j,R,_,M,K,G,U]),q=(0,g.useCallback)(e=>{K===e?O("asc"===G?"desc":"asc"):(B(e),O("asc"))},[K,G]),X=(0,g.useCallback)(e=>K!==e?"none":"asc"===G?"ascending":"descending",[K,G]),Y=(0,g.useCallback)(()=>{f(""),L(""),I("all"),Z({}),V(1)},[]),Q=$*H,J=Q-H,ee=(0,g.useMemo)(()=>W.slice(J,Q),[W,J,Q]),ea=(0,g.useMemo)(()=>Math.ceil(W.length/H),[W.length,H]),es=(0,g.useCallback)(e=>{V(e)},[]),et=(0,g.useCallback)(e=>K!==e?null:"asc"===G?(0,t.jsx)(n.A,{className:"ml-1 inline-block size-4"}):(0,t.jsx)(i.A,{className:"ml-1 inline-block size-4"}),[K,G]);return l?(0,t.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,t.jsx)(k.jt,{count:5,variant:"table"})}):s?(0,t.jsxs)("div",{className:"mx-auto max-w-5xl p-4 text-red-500",children:["Error loading delegations: ",s.message,(0,t.jsx)(w.$,{className:"ml-2",onClick:()=>r(),children:"Retry"})]}):(0,t.jsxs)("div",{className:"delegation-report-container",children:[(0,t.jsx)("div",{className:"no-print mb-6 text-right",children:(0,t.jsx)(v.k,{enableCsv:W.length>0,fileName:"delegations-list-report-".concat(new Date().toISOString().split("T")[0]),reportContentId:"#delegations-list-report-content",reportType:"delegations",tableId:"#delegations-table"})}),(0,t.jsxs)("div",{className:"report-content",id:"delegations-list-report-content",children:[(0,t.jsxs)("header",{className:"delegation-report-header",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"delegation-report-title",children:"Delegation List Report"}),(0,t.jsx)("div",{className:"no-print mx-auto h-1 w-24 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600"})]}),(0,t.jsx)("p",{className:"delegation-report-subtitle",children:b||"all"!==_?"Filtered by: ".concat("all"===_?"":"Status - ".concat((0,z.fZ)(_))).concat(b?("all"===_?"":" | ")+'Search - "'.concat(b,'"'):""):"All Delegations"}),(0,t.jsxs)("p",{className:"delegation-report-date",children:["Generated: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,t.jsx)("div",{className:"no-print delegation-summary-grid",children:(0,t.jsx)(N,{delegations:W.map(e=>{var a;return{...e,delegates:e.delegates||[],escortEmployeeIds:(null==(a=e.escorts)?void 0:a.map(e=>e.employeeId.toString()))||[]}})})}),(0,t.jsxs)("div",{className:"print-only delegation-print-summary",children:[(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegations:"})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:W.length})]}),(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegates:"})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:W.reduce((e,a)=>{var s;return e+((null==(s=a.delegates)?void 0:s.length)||0)},0)})]}),F.map(e=>({count:W.filter(a=>a.status===e).length,status:e})).filter(e=>e.count>0).sort((e,a)=>a.count-e.count).slice(0,3).map(e=>{let{count:a,status:s}=e;return(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsxs)("span",{className:"delegation-print-summary-label",children:[(0,z.fZ)(s),":"]})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:a})]},s)}),"all"!==_&&(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Filtered by Status:"})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:(0,z.fZ)(_)})]}),b&&(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Search Term:"})," ",(0,t.jsxs)("span",{className:"delegation-print-summary-value",children:['"',b,'"']})]})]})]}),(0,t.jsx)("div",{className:"no-print mb-8",children:(0,t.jsx)(h.Zp,{className:"border-0 bg-gradient-to-r from-slate-50 to-gray-50 shadow-lg",children:(0,t.jsxs)(h.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"delegation-filters",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"status-filter",children:"Filter by Status"}),(0,t.jsxs)(D.l6,{"aria-label":"Filter by status",onValueChange:I,value:_,children:[(0,t.jsx)(D.bq,{className:"w-full border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500",children:(0,t.jsx)(D.yv,{placeholder:"All Statuses"})}),(0,t.jsxs)(D.gC,{children:[(0,t.jsx)(D.eb,{value:"all",children:"All Statuses"}),F.map(e=>(0,t.jsx)(D.eb,{value:e,children:(0,z.fZ)(e)},e))]})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"search-input",children:"Search Delegations"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(C.p,{"aria-label":"Search delegations",className:"border-gray-300 bg-white px-10 focus:border-blue-500 focus:ring-blue-500",id:"search-input",onChange:e=>f(e.target.value),placeholder:"Search by event, location, or delegate...",type:"text",value:b}),(0,t.jsx)(o.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-gray-400"}),b&&(0,t.jsxs)(w.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>f(""),size:"icon",variant:"ghost",children:[(0,t.jsx)(d.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Clear search"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"date-range",children:"Date Range"}),(0,t.jsx)(C.p,{"aria-label":"Date range filter (coming soon)",className:"bg-gray-100 opacity-50",disabled:!0,id:"date-range",placeholder:"Date range filter coming soon",type:"text"})]})]}),(b||"all"!==_)&&(0,t.jsxs)("div",{className:"mt-6 flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-semibold text-blue-800",children:"Active Filters:"}),b&&(0,t.jsxs)("span",{className:"ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-800",children:['Search: "',b,'"']}),"all"!==_&&(0,t.jsxs)("span",{className:"ml-2 rounded bg-green-100 px-2 py-1 text-xs text-green-800",children:["Status:"," ",(0,z.fZ)(_)]})]}),(0,t.jsx)(w.$,{"aria-label":"Reset all filters",className:"border-blue-300 text-blue-700 hover:bg-blue-100",onClick:Y,size:"sm",variant:"outline",children:"Reset Filters"})]})]})})}),0===W.length?(0,t.jsx)("div",{className:"rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-slate-100 py-16 text-center",children:(0,t.jsxs)("div",{className:"mx-auto max-w-md",children:[(0,t.jsx)("div",{className:"mb-4 text-gray-400",children:(0,t.jsx)(c.A,{className:"mx-auto mb-4 size-16"})}),(0,t.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-700",children:"No delegations found"}),(0,t.jsx)("p",{className:"mb-4 text-gray-500",children:"No delegations match the current filter criteria."}),(0,t.jsx)(w.$,{"aria-label":"Reset filters to show all delegations",className:"mt-2",onClick:Y,size:"lg",variant:"outline",children:"Reset Filters"})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"delegation-table-container",children:(0,t.jsxs)(S.XI,{className:"delegation-table",id:"delegations-table",children:[(0,t.jsx)(S.A0,{children:(0,t.jsxs)(S.Hj,{className:"border-b border-gray-200 bg-gradient-to-r from-slate-100 to-gray-100",children:[(0,t.jsxs)(S.nd,{"aria-label":"Sort by event name","aria-sort":X("eventName"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>q("eventName"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),q("eventName"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Event Name ",et("eventName")]}),(0,t.jsxs)(S.nd,{"aria-label":"Sort by location","aria-sort":X("location"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>q("location"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),q("location"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Location ",et("location")]}),(0,t.jsxs)(S.nd,{"aria-label":"Sort by duration","aria-sort":X("durationFrom"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>q("durationFrom"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),q("durationFrom"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Duration ",et("durationFrom")]}),(0,t.jsxs)(S.nd,{"aria-label":"Sort by status","aria-sort":X("status"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>q("status"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),q("status"))},role:"columnheader",style:{width:"10%"},tabIndex:0,children:["Status ",et("status")]}),(0,t.jsxs)(S.nd,{"aria-label":"Sort by number of delegates","aria-sort":X("delegates"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>q("delegates"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),q("delegates"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Delegates ",et("delegates")]})]})}),(0,t.jsx)(S.BF,{className:"no-print",children:ee.map((e,a)=>{var s,l,r,n,i,o,d;return(0,t.jsxs)(S.Hj,{className:(0,p.cn)("page-break-inside-avoid hover:bg-slate-50 transition-colors border-b border-gray-100",a%2==0?"bg-white":"bg-slate-50/30"),children:[(0,t.jsx)(S.nA,{className:"print-text-wrap p-4 font-medium",title:e.eventName,children:(0,t.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,t.jsx)(S.nA,{className:"print-text-wrap print-location-col p-4",title:e.location,children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(m.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,t.jsx)(S.nA,{className:"whitespace-nowrap p-4",children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{children:T(e.durationFrom)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",T(e.durationTo)]})]})]})}),(0,t.jsx)(S.nA,{className:"p-4",children:(0,t.jsx)(y.E,{className:(0,p.cn)("text-xs py-1 px-2 font-medium",P(e.status)),children:(0,z.fZ)(e.status)})}),(0,t.jsx)(S.nA,{className:"print-text-wrap max-w-xs p-4",title:null==(s=e.delegates)?void 0:s.map(e=>e.name).join(", "),children:((null==(l=e.delegates)?void 0:l.length)||0)>0?(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(x.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"no-print",children:3>=((null==(r=e.delegates)?void 0:r.length)||0)?(0,t.jsx)("div",{className:"space-y-1",children:null==(n=e.delegates)?void 0:n.map((e,a)=>(0,t.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||a))}):(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"mb-1 space-y-1",children:null==(i=e.delegates)?void 0:i.slice(0,2).map((e,a)=>(0,t.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||a))}),(0,t.jsxs)("span",{className:"rounded bg-gray-100 px-2 py-1 text-xs text-gray-500",children:["+",((null==(o=e.delegates)?void 0:o.length)||0)-2," ","more"]})]})}),(0,t.jsx)("span",{className:"print-only",children:(0,t.jsx)("div",{className:"space-y-1",children:null==(d=e.delegates)?void 0:d.map((e,a)=>(0,t.jsx)("div",{className:"text-sm",children:e.name},e.id||a))})})]})]}):(0,t.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id)})}),(0,t.jsx)(S.BF,{className:"print-only",children:W.map((e,a)=>{var s,l,r;return(0,t.jsxs)(S.Hj,{className:(0,p.cn)("page-break-inside-avoid",a%2==0?"bg-white":"bg-slate-50/30"),children:[(0,t.jsx)(S.nA,{className:"print-text-wrap font-medium",title:e.eventName,children:(0,t.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,t.jsx)(S.nA,{className:"print-text-wrap print-location-col",title:e.location,children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(m.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,t.jsx)(S.nA,{className:"",children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{children:T(e.durationFrom)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",T(e.durationTo)]})]})]})}),(0,t.jsx)(S.nA,{className:"",children:(0,t.jsx)(y.E,{className:(0,p.cn)("text-xs py-1 px-2 font-medium",P(e.status)),children:(0,z.fZ)(e.status)})}),(0,t.jsx)(S.nA,{className:"print-text-wrap",title:null==(s=e.delegates)?void 0:s.map(e=>e.name).join(", "),children:((null==(l=e.delegates)?void 0:l.length)||0)>0?(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(x.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,t.jsx)("div",{children:(0,t.jsx)("div",{className:"space-y-1",children:null==(r=e.delegates)?void 0:r.map((e,a)=>(0,t.jsx)("div",{className:"text-sm",children:e.name},e.id||a))})})]}):(0,t.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id)})})]})}),W.length>H&&(0,t.jsx)("div",{className:"no-print mt-8 flex justify-center",children:(0,t.jsx)(A.$o,{currentPage:$,onPageChange:es,totalPages:ea})})]}),(0,t.jsx)("footer",{className:"delegation-report-footer",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("p",{className:"font-medium",children:["Report generated on: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,t.jsx)("p",{className:"text-gray-400",children:"WorkHub - Delegation Management"}),(0,t.jsx)("p",{className:"print-only text-xs",children:"Confidential - For internal use only"})]})})]})]})}},99673:(e,a,s)=>{"use strict";function t(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function l(e){var a,s;if(null==(a=e.fullName)?void 0:a.trim())return e.fullName.trim();if(null==(s=e.name)?void 0:s.trim()){let a=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(a.toLowerCase())||a.includes("_")){let e=a.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return a}if(e.role){let a=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(a," (Role)")}return"Unknown Employee"}function r(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function n(e){return e.replaceAll("_"," ")}s.d(a,{DV:()=>l,fZ:()=>t,s:()=>r,vq:()=>n})}},e=>{var a=a=>e(e.s=a);e.O(0,[5866,6476,7047,6897,3860,9664,375,7876,1859,5247,7998,4036,4767,8950,3712,3615,7841,8441,1684,7358],()=>a(80760)),_N_E=e.O()}]);