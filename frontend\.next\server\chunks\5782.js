"use strict";exports.id=5782,exports.ids=[5782],exports.modules={3940:(e,r,t)=>{t.d(r,{O_:()=>l,t6:()=>a});var s=t(43210),i=t(49278);function a(){let e=(0,s.useCallback)((e,r)=>i.JP.success(e,r),[]),r=(0,s.useCallback)((e,r)=>i.JP.error(e,r),[]),t=(0,s.useCallback)((e,r)=>i.JP.info(e,r),[]),a=(0,s.useCallback)(r=>e(r?.successTitle||"Success",r?.successDescription||"Operation completed successfully"),[e]),l=(0,s.useCallback)((e,t)=>{let s=e instanceof Error?e.message:e;return r(t?.errorTitle||"Error",t?.errorDescription||s||"An unexpected error occurred")},[r]);return{showSuccess:e,showError:r,showInfo:t,showFormSuccess:a,showFormError:l}}function l(e){let r;switch(e){case"employee":r=t(49278).Ok;break;case"vehicle":r=t(49278).G7;break;case"task":r=t(49278).z0;break;case"delegation":r=t(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,r){let{showFormSuccess:t,showFormError:l}=a(),c=r||(e?(0,i.iw)(e):null),d=(0,s.useCallback)(e=>c?c.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[c,t]),n=(0,s.useCallback)(e=>c?c.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[c,t]),o=(0,s.useCallback)(e=>c?c.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[c,t]),u=(0,s.useCallback)(e=>{if(c){let r=e instanceof Error?e.message:e;return c.entityCreationError(r)}return l(e,{errorTitle:"Creation Failed"})},[c,l]);return{showEntityCreated:d,showEntityUpdated:n,showEntityDeleted:o,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(c){let r=e instanceof Error?e.message:e;return c.entityUpdateError(r)}return l(e,{errorTitle:"Update Failed"})},[c,l]),showEntityDeletionError:(0,s.useCallback)(e=>{if(c){let r=e instanceof Error?e.message:e;return c.entityDeletionError(r)}return l(e,{errorTitle:"Deletion Failed"})},[c,l]),showFormSuccess:t,showFormError:l}}(void 0,r)}},49278:(e,r,t)=>{t.d(r,{G7:()=>m,Gb:()=>d,JP:()=>n,Ok:()=>o,Qu:()=>u,iw:()=>c,oz:()=>p,z0:()=>h});var s=t(3389);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,r){return this.show({title:e,description:r,variant:"default"})}error(e,r){return this.show({title:e,description:r,variant:"destructive"})}info(e,r){return this.show({title:e,description:r,variant:"default"})}}class a extends i{constructor(e){super(),this.config=e}entityCreated(e){let r=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(r))}entityUpdated(e){let r=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(r))}entityDeleted(e){let r=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(r))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class l extends i{serviceRecordCreated(e,r){return this.success("Service Record Added",`${r} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,r){return this.success("Service Record Updated",`${r} service for "${e}" has been updated.`)}serviceRecordDeleted(e,r){return this.success("Service Record Deleted",`${r} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function c(e){return new a(e)}function d(e,r){return new a({entityName:e,getDisplayName:r,messages:{created:{title:`${e} Created`,description:r=>`The ${e.toLowerCase()} "${r}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:r=>r||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:r=>r||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:r=>r||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let n=new i,o=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),m=new a({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),p=new l},52027:(e,r,t)=>{t.d(r,{gO:()=>m,jt:()=>f,pp:()=>h});var s=t(60687),i=t(72963),a=t(11516);t(43210);var l=t(68752),c=t(91821),d=t(85726),n=t(22482);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},u={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function m({children:e,className:r,data:t,emptyComponent:i,error:a,errorComponent:l,isLoading:c,loadingComponent:d,onRetry:o}){return c?d||(0,s.jsx)(x,{...r&&{className:r},text:"Loading..."}):a?l||(0,s.jsx)(p,{...r&&{className:r},message:a,...o&&{onRetry:o}}):!t||Array.isArray(t)&&0===t.length?i||(0,s.jsx)("div",{className:(0,n.cn)("text-center py-8",r),children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,s.jsx)("div",{className:r,children:e(t)})}function h({className:e,description:r,icon:t,primaryAction:i,secondaryAction:a,title:c}){return(0,s.jsxs)("div",{className:(0,n.cn)("space-y-6 text-center py-12",e),children:[t&&(0,s.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,s.jsx)(t,{className:"h-10 w-10 text-muted-foreground"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:c}),r&&(0,s.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:r})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[i&&(0,s.jsx)(l.r,{actionType:"primary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,s.jsx)("a",{href:i.href,children:i.label}):i.label}),a&&(0,s.jsx)(l.r,{actionType:"tertiary",asChild:!!a.href,icon:a.icon,onClick:a.onClick,children:a.href?(0,s.jsx)("a",{href:a.href,children:a.label}):a.label})]})]})}function p({className:e,message:r,onRetry:t}){return(0,s.jsxs)(c.Fc,{className:(0,n.cn)("my-4",e),variant:"destructive",children:[(0,s.jsx)(i.A,{className:"size-4"}),(0,s.jsx)(c.XL,{children:"Error"}),(0,s.jsx)(c.TN,{children:(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:r}),t&&(0,s.jsx)(l.r,{actionType:"tertiary",icon:(0,s.jsx)(a.A,{className:"size-4"}),onClick:t,size:"sm",children:"Try Again"})]})})]})}function x({className:e,fullPage:r=!1,size:t="md",text:i}){return(0,s.jsx)("div",{className:(0,n.cn)("flex items-center justify-center",r&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",e),children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(a.A,{className:(0,n.cn)("animate-spin text-primary",o[t])}),i&&(0,s.jsx)("span",{className:(0,n.cn)("mt-2 text-muted-foreground",u[t]),children:i})]})})}function f({className:e,count:r=1,testId:t="loading-skeleton",variant:i="default"}){return"card"===i?(0,s.jsx)("div",{className:(0,n.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",e),"data-testid":t,children:Array(r).fill(0).map((e,r)=>(0,s.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,s.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,s.jsxs)("div",{className:"p-5",children:[(0,s.jsx)(d.E,{className:"mb-1 h-7 w-3/4"}),(0,s.jsx)(d.E,{className:"mb-3 h-4 w-1/2"}),(0,s.jsx)(d.E,{className:"my-3 h-px w-full"}),(0,s.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.E,{className:"mr-2.5 size-5 rounded-full"}),(0,s.jsx)(d.E,{className:"h-5 w-2/3"})]},r))})]})]},r))}):"table"===i?(0,s.jsxs)("div",{className:(0,n.cn)("space-y-3",e),"data-testid":t,children:[(0,s.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,r)=>(0,s.jsx)(d.E,{className:"h-8 flex-1"},r))}),Array(r).fill(0).map((e,r)=>(0,s.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,r)=>(0,s.jsx)(d.E,{className:"h-6 flex-1"},r))},r))]}):"list"===i?(0,s.jsx)("div",{className:(0,n.cn)("space-y-3",e),"data-testid":t,children:Array(r).fill(0).map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(d.E,{className:"size-12 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(d.E,{className:"h-4 w-1/3"}),(0,s.jsx)(d.E,{className:"h-4 w-full"})]})]},r))}):"stats"===i?(0,s.jsx)("div",{className:(0,n.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",e),"data-testid":t,children:Array(r).fill(0).map((e,r)=>(0,s.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(d.E,{className:"h-5 w-1/3"}),(0,s.jsx)(d.E,{className:"size-5 rounded-full"})]}),(0,s.jsx)(d.E,{className:"mt-3 h-8 w-1/2"}),(0,s.jsx)(d.E,{className:"mt-2 h-4 w-2/3"})]},r))}):(0,s.jsx)("div",{className:(0,n.cn)("space-y-2",e),"data-testid":t,children:Array(r).fill(0).map((e,r)=>(0,s.jsx)(d.E,{className:"h-5 w-full"},r))})}},68752:(e,r,t)=>{t.d(r,{r:()=>n});var s=t(60687),i=t(11516),a=t(43210),l=t.n(a),c=t(29523),d=t(22482);let n=l().forwardRef(({actionType:e="primary",asChild:r=!1,children:t,className:a,disabled:l,icon:n,isLoading:o=!1,loadingText:u,...m},h)=>{let{className:p,variant:x}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,s.jsx)(c.$,{asChild:r,className:(0,d.cn)(p,a),disabled:o||l,ref:h,variant:x,...m,children:o?(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,s.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}),u||t]}):(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",n&&(0,s.jsx)("span",{className:"mr-2",children:n}),t]})})});n.displayName="ActionButton"},85726:(e,r,t)=>{t.d(r,{E:()=>a});var s=t(60687),i=t(22482);function a({className:e,...r}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...r})}}};