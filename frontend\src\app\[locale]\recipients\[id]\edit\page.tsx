'use client';

import { Users } from 'lucide-react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import type { UpdateRecipientData } from '@/lib/types/domain';

import { RecipientForm } from '@/components/features/recipients';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import {
  useRecipient,
  useUpdateRecipient,
} from '@/lib/stores/queries/useRecipients';

export default function EditRecipientPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations('recipients');
  const tCommon = useTranslations('common');

  const recipientId = params.id as string;

  const { data: recipient, isLoading, error } = useRecipient(recipientId);
  const {
    mutateAsync: updateRecipient,
    isPending,
    error: updateError,
  } = useUpdateRecipient();

  const handleSubmit = async (data: UpdateRecipientData) => {
    try {
      await updateRecipient({ id: recipientId, data });
      router.push(`/recipients/${recipientId}`);
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error updating recipient:', error);
    }
  };

  return (
    <div className="space-y-6">
      <AppBreadcrumb homeHref="/" homeLabel={tCommon('dashboard')} />

      <DataLoader
        data={recipient}
        error={error?.message || null}
        isLoading={isLoading}
        loadingComponent={<SkeletonLoader count={1} variant="card" />}
        emptyComponent={
          <div className="rounded-lg bg-card py-12 text-center shadow-md">
            <Users className="mx-auto mb-6 size-16 text-muted-foreground" />
            <h3 className="mb-2 text-2xl font-semibold text-foreground">
              {t('recipientNotFound')}
            </h3>
            <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
              {t('recipientNotFoundDescription')}
            </p>
          </div>
        }
      >
        {recipientData => (
          <>
            <PageHeader
              description={t('editRecipientDescription')}
              icon={Users}
              title={t('editRecipient')}
            />

            {updateError && (
              <div className="rounded-md bg-red-100 p-3 text-red-500">
                {tCommon('error')}: {updateError.message}
              </div>
            )}

            <RecipientForm
              initialData={recipientData}
              isEditing={true}
              isLoading={isPending}
              onSubmit={handleSubmit}
            />
          </>
        )}
      </DataLoader>
    </div>
  );
}
