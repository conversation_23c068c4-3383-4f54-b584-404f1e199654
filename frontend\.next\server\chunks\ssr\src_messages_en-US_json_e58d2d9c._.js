module.exports = {

"[project]/src/messages/en-US.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"metadata\":{\"title\":\"WorkHub - Comprehensive Management System\",\"description\":\"Streamline your operations with our comprehensive management platform\",\"keywords\":\"workforce management, employee tracking, performance monitoring, productivity optimization, HR management, business operations, team management, organizational efficiency\"},\"NotFoundPage\":{\"title\":\"Page Not Found\",\"description\":\"The page you are looking for does not exist.\",\"backHome\":\"Back to Home\"},\"common\":{\"loading\":\"Loading...\",\"error\":\"An error occurred\",\"retry\":\"Retry\",\"cancel\":\"Cancel\",\"save\":\"Save\",\"edit\":\"Edit\",\"delete\":\"Delete\",\"add\":\"Add\",\"search\":\"Search\",\"filter\":\"Filter\",\"clear\":\"Clear\",\"submit\":\"Submit\",\"back\":\"Back\",\"next\":\"Next\",\"previous\":\"Previous\",\"close\":\"Close\",\"confirm\":\"Confirm\",\"yes\":\"Yes\",\"no\":\"No\"},\"navigation\":{\"dashboard\":\"Dashboard\",\"delegations\":\"Delegations\",\"employees\":\"Employees\",\"reports\":\"Reports\",\"settings\":\"Settings\",\"profile\":\"Profile\",\"logout\":\"Logout\",\"assets\":\"Assets\",\"fleet\":\"Fleet\",\"maintenance\":\"Maintenance\",\"serviceRecords\":\"Service Records\",\"projects\":\"Projects\",\"tasks\":\"Tasks\",\"team\":\"Team\",\"teamMembers\":\"Team Members\",\"analytics\":\"Analytics\",\"reliability\":\"Reliability\",\"monitoring\":\"Monitoring\",\"admin\":\"Admin\",\"administration\":\"Administration\",\"allSettings\":\"All Settings\",\"gifts\":\"Gifts\",\"recipients\":\"Recipients\",\"sections\":{\"overview\":\"Overview\",\"operations\":\"Operations\",\"workforce\":\"Workforce\",\"system\":\"System\"},\"badges\":{\"new\":\"New\"},\"fontSize\":{\"small\":\"Small\",\"medium\":\"Medium\",\"large\":\"Large\"},\"search\":{\"placeholder\":\"Search navigation...\"}},\"auth\":{\"login\":\"Login\",\"logout\":\"Logout\",\"email\":\"Email\",\"password\":\"Password\",\"forgotPassword\":\"Forgot Password?\",\"rememberMe\":\"Remember Me\",\"loginError\":\"Invalid email or password\",\"loginSuccess\":\"Login successful\",\"welcomeBack\":\"Welcome back\",\"signInToAccount\":\"Sign in to your WorkHub account\",\"emailAddress\":\"Email address\",\"signIn\":\"Sign in\",\"signingIn\":\"Signing in...\",\"noInternetConnection\":\"No internet connection\",\"offlineWarning\":\"You're currently offline. Please check your internet connection to sign in.\",\"showPassword\":\"Show password\",\"hidePassword\":\"Hide password\",\"validation\":{\"emailRequired\":\"Email is required\",\"emailInvalid\":\"Please enter a valid email address\",\"passwordRequired\":\"Password is required\",\"passwordTooShort\":\"Password must be at least 6 characters\"},\"errors\":{\"authenticationFailed\":\"Failed to initialize authentication\",\"invalidCredentials\":\"Invalid email or password\",\"networkError\":\"Network error. Please try again.\",\"unknownError\":\"An unexpected error occurred\"}},\"dashboard\":{\"welcomeBack\":\"Welcome back\",\"welcomeToWorkHub\":\"Welcome to WorkHub\",\"signInToAccess\":\"Please sign in to access your dashboard\",\"signIn\":\"Sign In\",\"operationsToday\":\"Here's what's happening with your operations today.\",\"keyMetrics\":\"Key Metrics\",\"stats\":{\"totalVehicles\":\"Total Vehicles\",\"activeFleetAssets\":\"Active fleet assets\",\"activeProjects\":\"Active Projects\",\"inProgressDelegations\":\"In progress delegations\",\"pendingTasks\":\"Pending Tasks\",\"awaitingCompletion\":\"Awaiting completion\",\"maintenanceDue\":\"Maintenance Due\",\"requiresAttention\":\"Requires attention\"},\"recentActivity\":\"Recent Activity\",\"viewAll\":\"View All\",\"quickActions\":\"Quick Actions\",\"addVehicle\":\"Add Vehicle\",\"createDelegation\":\"Create Delegation\",\"assignTask\":\"Assign Task\",\"scheduleService\":\"Schedule Service\",\"fleetPerformance\":\"Fleet Performance\",\"fleetEfficiencyOverview\":\"Overview of fleet efficiency and maintenance status\",\"fleetUtilization\":\"Fleet Utilization\",\"maintenanceUpToDate\":\"Maintenance Up-to-date\",\"fuelEfficiency\":\"Fuel Efficiency\",\"taskCompletionRate\":\"Task Completion Rate\",\"latestUpdates\":\"Latest updates from your operations\",\"addNewVehicle\":\"Add New Vehicle\",\"registerNewAsset\":\"Register a new asset to your fleet\",\"scheduleMaintenance\":\"Schedule Maintenance\",\"planUpcomingService\":\"Plan upcoming service appointments\",\"viewAnalytics\":\"View Analytics\",\"seeDetailedReports\":\"See detailed reports and insights\",\"createAndDelegate\":\"Create and delegate new tasks\",\"activity\":{\"maintenanceCompleted\":\"maintenance completed\",\"newTaskAssigned\":\"New task assigned\",\"milestoneCompleted\":\"milestone completed\",\"oilChangeFinished\":\"Oil change and tire rotation finished\",\"safetyInspectionDue\":\"Quarterly safety inspection due next week\",\"deliverablesSubmitted\":\"Phase 2 deliverables submitted\"},\"timeAgo\":{\"hoursAgo\":\"{hours} hours ago\",\"dayAgo\":\"{days} day ago\",\"daysAgo\":\"{days} days ago\"}},\"reliability\":{\"dashboard\":\"Reliability Dashboard\",\"systemMonitoring\":\"System monitoring\",\"connectionStatus\":{\"connected\":\"Connected\",\"connecting\":\"Connecting\",\"reconnecting\":\"Reconnecting\",\"disconnected\":\"Disconnected\",\"error\":\"Error\",\"polling\":\"Polling\",\"realTimeActive\":\"Real-time connection active\",\"establishingConnection\":\"Establishing connection...\",\"attemptingReconnect\":\"Attempting to reconnect...\",\"connectionError\":\"Connection error occurred\",\"pollingFallback\":\"Using polling fallback (WebSocket requires authentication)\",\"connectionLost\":\"Real-time connection lost\"},\"widgets\":{\"systemHealth\":\"System Health\",\"systemHealthDesc\":\"Overall system status and health indicators\",\"healthStatusIndicators\":\"Health Status Indicators\",\"healthStatusDesc\":\"Real-time component health status indicators\",\"dependencyStatus\":\"Dependency Status\",\"dependencyStatusDesc\":\"External dependency health monitoring\",\"healthTrends\":\"Health Trends\",\"healthTrendsDesc\":\"Historical health trend visualization\",\"systemResources\":\"System Resources\",\"systemResourcesDesc\":\"CPU, memory, and connection monitoring\",\"performanceOverview\":\"Performance Overview\",\"performanceOverviewDesc\":\"Comprehensive performance metrics and scoring\",\"systemMetrics\":\"System Performance\",\"systemMetricsDesc\":\"Performance-focused system metrics monitoring\",\"httpMetrics\":\"HTTP Request Metrics\",\"httpMetricsDesc\":\"Request performance and throughput analysis\",\"deduplicationMetrics\":\"Deduplication Metrics\",\"deduplicationMetricsDesc\":\"Cache efficiency and request deduplication analysis\"}},\"forms\":{\"validation\":{\"required\":\"This field is required\",\"invalidEmail\":\"Please enter a valid email address\",\"invalidPhone\":\"Please enter a valid phone number\",\"invalidDate\":\"Please enter a valid date\",\"minLength\":\"Must be at least {min} characters\",\"maxLength\":\"Must be no more than {max} characters\",\"invalidFormat\":\"Invalid format\"},\"placeholders\":{\"enterEmail\":\"Enter your email address\",\"enterPassword\":\"Enter your password\",\"enterName\":\"Enter name\",\"enterDescription\":\"Enter description\",\"enterPhone\":\"Enter phone number\",\"enterAddress\":\"Enter address\",\"enterNotes\":\"Enter notes\",\"selectOption\":\"Select an option\",\"selectRecipient\":\"Select a recipient\",\"searchPlaceholder\":\"Search...\",\"searchGifts\":\"Search gifts...\",\"searchRecipients\":\"Search recipients...\"},\"labels\":{\"name\":\"Name\",\"email\":\"Email\",\"phone\":\"Phone\",\"address\":\"Address\",\"notes\":\"Notes\",\"recipient\":\"Recipient\",\"dateSent\":\"Date Sent\",\"itemDescription\":\"Item Description\",\"occasion\":\"Occasion\",\"senderName\":\"Sender Name\"},\"buttons\":{\"submit\":\"Submit\",\"save\":\"Save\",\"cancel\":\"Cancel\",\"edit\":\"Edit\",\"delete\":\"Delete\",\"add\":\"Add\",\"update\":\"Update\",\"create\":\"Create\",\"goBack\":\"Go Back\"},\"titles\":{\"addRecipient\":\"Add New Recipient\",\"editRecipient\":\"Edit Recipient\",\"addGift\":\"Add New Gift\",\"editGift\":\"Edit Gift\"}},\"gifts\":{\"pageTitle\":\"Gift Management\",\"pageDescription\":\"Track and manage gifts sent to recipients\",\"addGift\":\"Add Gift\",\"editGift\":\"Edit Gift\",\"addGiftDescription\":\"Add a new gift to the tracking system\",\"editGiftDescription\":\"Update gift information\",\"giftDetails\":\"Gift Details\",\"giftDetailsDescription\":\"View detailed gift information\",\"giftInformation\":\"Gift Information\",\"giftSettings\":\"Gift Settings\",\"giftSettingsDescription\":\"Configure gift tracking preferences\",\"recipientInformation\":\"Recipient Information\",\"giftHistory\":\"Gift History\",\"totalGifts\":\"Total Gifts\",\"recentGifts\":\"Recent Gifts\",\"viewAllGifts\":\"View All Gifts\",\"noGiftsYet\":\"No gifts tracked yet\",\"noGiftsMatch\":\"No gifts match your search\",\"getStartedWithGifts\":\"Start tracking gifts by adding your first gift record\",\"tryAdjustingFilters\":\"Try adjusting your search or filters\",\"addFirstGift\":\"Add Your First Gift\",\"backToGifts\":\"Back to Gifts\",\"giftNotFound\":\"Gift Not Found\",\"giftNotFoundDescription\":\"The gift you're looking for doesn't exist or has been removed\",\"deleteGiftTitle\":\"Delete Gift\",\"deleteGiftConfirmation\":\"Are you sure you want to delete '{item}'? This action cannot be undone.\",\"gifts\":\"Gifts\"},\"recipients\":{\"pageTitle\":\"Recipient Management\",\"pageDescription\":\"Manage gift recipients and their information\",\"addRecipient\":\"Add Recipient\",\"editRecipient\":\"Edit Recipient\",\"addRecipientDescription\":\"Add a new recipient to the system\",\"editRecipientDescription\":\"Update recipient information\",\"recipientDetails\":\"Recipient Details\",\"recipientDetailsDescription\":\"View detailed recipient information\",\"recipientInformation\":\"Recipient Information\",\"recipientSettings\":\"Recipient Settings\",\"recipientSettingsDescription\":\"Configure recipient management preferences\",\"noRecipientsYet\":\"No recipients added yet\",\"noRecipientsMatch\":\"No recipients match your search\",\"getStartedWithRecipients\":\"Start by adding your first recipient\",\"tryAdjustingFilters\":\"Try adjusting your search or filters\",\"addFirstRecipient\":\"Add Your First Recipient\",\"backToRecipients\":\"Back to Recipients\",\"recipientNotFound\":\"Recipient Not Found\",\"recipientNotFoundDescription\":\"The recipient you're looking for doesn't exist or has been removed\",\"deleteRecipientTitle\":\"Delete Recipient\",\"deleteRecipientConfirmation\":\"Are you sure you want to delete '{name}'? This action cannot be undone.\",\"recipients\":\"Recipients\"}}"));}}),

};