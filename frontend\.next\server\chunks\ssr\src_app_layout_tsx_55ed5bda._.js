module.exports = {

"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Root Layout - i18n Routing Entry Point
 * @module app/layout
 *
 * Root layout that handles locale routing and redirects.
 * This layout only handles the root level routing - actual content
 * is handled by the locale-specific layouts.
 */ __turbopack_context__.s({
    "default": (()=>RootLayout)
});
;
function RootLayout({ children }) {
    return children;
}
}}),

};

//# sourceMappingURL=src_app_layout_tsx_55ed5bda._.js.map