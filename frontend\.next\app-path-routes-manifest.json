{"/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/page": "/", "/[locale]/auth-test/page": "/[locale]/auth-test", "/[locale]/delegations/[id]/page": "/[locale]/delegations/[id]", "/[locale]/delegations/[id]/edit/page": "/[locale]/delegations/[id]/edit", "/[locale]/add-vehicle/page": "/[locale]/add-vehicle", "/[locale]/delegations/page": "/[locale]/delegations", "/[locale]/delegations/add/page": "/[locale]/delegations/add", "/[locale]/employees/[id]/edit/page": "/[locale]/employees/[id]/edit", "/[locale]/employees/[id]/page": "/[locale]/employees/[id]", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/employees/add/page": "/[locale]/employees/add", "/[locale]/font-size-demo/page": "/[locale]/font-size-demo", "/[locale]/employees/page": "/[locale]/employees", "/[locale]/employees/new/page": "/[locale]/employees/new", "/[locale]/login/page": "/[locale]/login", "/[locale]/service-history/page": "/[locale]/service-history", "/[locale]/page": "/[locale]", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/service-records/[id]/edit/page": "/[locale]/service-records/[id]/edit", "/[locale]/service-records/[id]/page": "/[locale]/service-records/[id]", "/[locale]/tasks/[id]/edit/page": "/[locale]/tasks/[id]/edit", "/[locale]/tasks/add/page": "/[locale]/tasks/add", "/[locale]/tasks/[id]/page": "/[locale]/tasks/[id]", "/[locale]/vehicles/[id]/page": "/[locale]/vehicles/[id]", "/[locale]/tasks/page": "/[locale]/tasks", "/[locale]/vehicles/edit/[id]/page": "/[locale]/vehicles/edit/[id]", "/[locale]/zustand-test/page": "/[locale]/zustand-test", "/[locale]/vehicles/new/page": "/[locale]/vehicles/new", "/[locale]/vehicles/page": "/[locale]/vehicles", "/[locale]/delegations/[id]/report/page": "/[locale]/delegations/[id]/report", "/[locale]/admin/page": "/[locale]/admin", "/[locale]/delegations/report/list/page": "/[locale]/delegations/report/list", "/[locale]/tasks/report/page": "/[locale]/tasks/report", "/[locale]/vehicles/[id]/report/service-history/page": "/[locale]/vehicles/[id]/report/service-history", "/[locale]/vehicles/[id]/report/page": "/[locale]/vehicles/[id]/report", "/[locale]/reports/analytics/page": "/[locale]/reports/analytics", "/[locale]/reliability/page": "/[locale]/reliability", "/[locale]/reports/data/page": "/[locale]/reports/data", "/[locale]/reports/page": "/[locale]/reports"}