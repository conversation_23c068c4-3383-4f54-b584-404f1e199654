{"/_not-found/page": "/_not-found", "/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/page": "/", "/[locale]/add-vehicle/page": "/[locale]/add-vehicle", "/[locale]/delegations/[id]/page": "/[locale]/delegations/[id]", "/[locale]/delegations/add/page": "/[locale]/delegations/add", "/[locale]/delegations/[id]/edit/page": "/[locale]/delegations/[id]/edit", "/[locale]/auth-test/page": "/[locale]/auth-test", "/[locale]/employees/add/page": "/[locale]/employees/add", "/[locale]/employees/[id]/page": "/[locale]/employees/[id]", "/[locale]/page": "/[locale]", "/[locale]/login/page": "/[locale]/login", "/[locale]/font-size-demo/page": "/[locale]/font-size-demo", "/[locale]/delegations/page": "/[locale]/delegations", "/[locale]/employees/[id]/edit/page": "/[locale]/employees/[id]/edit", "/[locale]/employees/new/page": "/[locale]/employees/new", "/[locale]/employees/page": "/[locale]/employees", "/[locale]/service-records/[id]/edit/page": "/[locale]/service-records/[id]/edit", "/[locale]/service-history/page": "/[locale]/service-history", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/tasks/[id]/edit/page": "/[locale]/tasks/[id]/edit", "/[locale]/service-records/[id]/page": "/[locale]/service-records/[id]", "/[locale]/tasks/add/page": "/[locale]/tasks/add", "/[locale]/tasks/[id]/page": "/[locale]/tasks/[id]", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/tasks/page": "/[locale]/tasks", "/[locale]/vehicles/new/page": "/[locale]/vehicles/new", "/[locale]/vehicles/edit/[id]/page": "/[locale]/vehicles/edit/[id]", "/[locale]/vehicles/page": "/[locale]/vehicles", "/[locale]/vehicles/[id]/page": "/[locale]/vehicles/[id]", "/[locale]/zustand-test/page": "/[locale]/zustand-test", "/[locale]/delegations/report/list/page": "/[locale]/delegations/report/list", "/[locale]/delegations/[id]/report/page": "/[locale]/delegations/[id]/report", "/[locale]/admin/page": "/[locale]/admin", "/[locale]/vehicles/[id]/report/service-history/page": "/[locale]/vehicles/[id]/report/service-history", "/[locale]/vehicles/[id]/report/page": "/[locale]/vehicles/[id]/report", "/[locale]/tasks/report/page": "/[locale]/tasks/report", "/[locale]/reliability/page": "/[locale]/reliability", "/[locale]/reports/data/page": "/[locale]/reports/data", "/[locale]/reports/page": "/[locale]/reports", "/[locale]/reports/analytics/page": "/[locale]/reports/analytics"}