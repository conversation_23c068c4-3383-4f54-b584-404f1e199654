"use strict";exports.id=6813,exports.ids=[6813],exports.modules={2775:(e,t,a)=>{a.d(t,{Ln:()=>f,WV:()=>h,fs:()=>p,kI:()=>y,xH:()=>m,xT:()=>v});var i=a(8693),r=a(54050),l=a(77312),s=a(46349),n=a(8342);let c={fromApi(e){let t={cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId};return e.vehicleMake||e.vehicleModel||e.vehicleYear?{...t,vehicleMake:e.vehicleMake||"Unknown",vehicleModel:e.vehicleModel||"Unknown",vehicleYear:e.vehicleYear||new Date().getFullYear(),licensePlate:e.licensePlate||null,employeeName:e.employeeName||null}:t},toApi:e=>e};class o extends l.v{constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/servicerecords",this.transformer=c}async getById(e){return this.executeWithInfrastructure(`${this.endpoint}:${e}`,async()=>{let t=await this.apiClient.get(`${this.endpoint}/${e}`);return this.transformer.fromApi?this.transformer.fromApi(t):t})}async updateRecord(e,t,a){return this.executeWithInfrastructure(null,async()=>{let{vehicleId:i,...r}=a,l=await this.apiClient.put(`/vehicles/${t}/servicerecords/${e}`,r);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:${e}`)),this.cache.invalidatePattern(RegExp(`^vehicles:${t}:`)),this.transformer.fromApi?this.transformer.fromApi(l):l})}async deleteRecord(e,t){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete(`/vehicles/${t}/servicerecords/${e}`),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:${e}`)),this.cache.invalidatePattern(RegExp(`^vehicles:${t}:`))})}async getVehicleServiceRecords(e){return this.executeWithInfrastructure(`vehicles:${e}:servicerecords`,async()=>await this.apiClient.get(`/vehicles/${e}/servicerecords`))}async createVehicleServiceRecord(e,t){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.post(`/vehicles/${e}/servicerecords`,t);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^vehicles:${e}:`)),this.transformer.fromApi?this.transformer.fromApi(a):a})}async getAllEnriched(){return this.executeWithInfrastructure(`${this.endpoint}:enriched`,async()=>(await this.apiClient.get(`${this.endpoint}/enriched`)).map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e))}}let d=new o(n.uE),u="serviceRecords",h=(e,t)=>(0,s.GK)([u,e],()=>d.getById(e),"serviceRecord",{enabled:t?.enabled??!!e,staleTime:3e5}),p=e=>(0,s.GK)([u,"allEnriched"],()=>d.getAllEnriched(),"serviceRecord",{enabled:e?.enabled??!0,staleTime:3e5}),m=(e,t)=>(0,s.GK)([u,"forVehicle",e],()=>d.getVehicleServiceRecords(e),"serviceRecord",{enabled:t?.enabled??!0,staleTime:3e5}),y=()=>{let e=(0,i.jE)();return(0,r.n)({mutationFn:async e=>{let{vehicleId:t}=e;return d.createVehicleServiceRecord(t,e)},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,"forVehicle",a.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",a.vehicleId]})}})},f=()=>{let e=(0,i.jE)();return(0,r.n)({mutationFn:async({id:e,vehicleId:t,data:a})=>d.updateRecord(e,t,a),onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,a.id]}),e.invalidateQueries({queryKey:[u,"forVehicle",a.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",a.vehicleId]})}})},v=()=>{let e=(0,i.jE)();return(0,r.n)({mutationFn:async({id:e,vehicleId:t})=>d.deleteRecord(e,t),onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,a.id]}),e.invalidateQueries({queryKey:[u,"forVehicle"]}),e.invalidateQueries({queryKey:["vehicle"]})}})}},38765:(e,t,a)=>{a.d(t,{A:()=>i});let i=(0,a(82614).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},55817:(e,t,a)=>{a.d(t,{A:()=>i});let i=(0,a(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},55925:(e,t,a)=>{a.d(t,{Lt:()=>D,Rx:()=>W,Zr:()=>U,EO:()=>Q,$v:()=>T,ck:()=>F,wd:()=>V,r7:()=>M,tv:()=>q});var i=a(60687),r=a(43210),l=a(11273),s=a(98599),n=a(26134),c=a(70569),o=a(8730),d="AlertDialog",[u,h]=(0,l.A)(d,[n.Hs]),p=(0,n.Hs)(),m=e=>{let{__scopeAlertDialog:t,...a}=e,r=p(t);return(0,i.jsx)(n.bL,{...r,...a,modal:!0})};m.displayName=d;var y=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,i.jsx)(n.l9,{...l,...r,ref:t})});y.displayName="AlertDialogTrigger";var f=e=>{let{__scopeAlertDialog:t,...a}=e,r=p(t);return(0,i.jsx)(n.ZL,{...r,...a})};f.displayName="AlertDialogPortal";var v=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,i.jsx)(n.hJ,{...l,...r,ref:t})});v.displayName="AlertDialogOverlay";var g="AlertDialogContent",[x,A]=u(g),N=(0,o.Dc)("AlertDialogContent"),b=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:l,...o}=e,d=p(a),u=r.useRef(null),h=(0,s.s)(t,u),m=r.useRef(null);return(0,i.jsx)(n.G$,{contentName:g,titleName:k,docsSlug:"alert-dialog",children:(0,i.jsx)(x,{scope:a,cancelRef:m,children:(0,i.jsxs)(n.UC,{role:"alertdialog",...d,...o,ref:h,onOpenAutoFocus:(0,c.m)(o.onOpenAutoFocus,e=>{e.preventDefault(),m.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,i.jsx)(N,{children:l}),(0,i.jsx)(S,{contentRef:u})]})})})});b.displayName=g;var k="AlertDialogTitle",w=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,i.jsx)(n.hE,{...l,...r,ref:t})});w.displayName=k;var R="AlertDialogDescription",C=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,i.jsx)(n.VY,{...l,...r,ref:t})});C.displayName=R;var $=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,i.jsx)(n.bm,{...l,...r,ref:t})});$.displayName="AlertDialogAction";var j="AlertDialogCancel",E=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:l}=A(j,a),c=p(a),o=(0,s.s)(t,l);return(0,i.jsx)(n.bm,{...c,...r,ref:o})});E.displayName=j;var S=({contentRef:e})=>{let t=`\`${g}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${g}\` by passing a \`${R}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${g}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return r.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},I=a(29523),K=a(22482);let D=m,q=y,P=r.forwardRef(({className:e,...t},a)=>(0,i.jsx)(v,{className:(0,K.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:a}));P.displayName=v.displayName;let Q=r.forwardRef(({className:e,...t},a)=>(0,i.jsxs)(f,{children:[(0,i.jsx)(P,{}),(0,i.jsx)(b,{className:(0,K.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),ref:a,...t})]}));Q.displayName=b.displayName;let V=({className:e,...t})=>(0,i.jsx)("div",{className:(0,K.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});V.displayName="AlertDialogHeader";let F=({className:e,...t})=>(0,i.jsx)("div",{className:(0,K.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});F.displayName="AlertDialogFooter";let M=r.forwardRef(({className:e,...t},a)=>(0,i.jsx)(w,{className:(0,K.cn)("text-lg font-semibold",e),ref:a,...t}));M.displayName=w.displayName;let T=r.forwardRef(({className:e,...t},a)=>(0,i.jsx)(C,{className:(0,K.cn)("text-sm text-muted-foreground",e),ref:a,...t}));T.displayName=C.displayName;let W=r.forwardRef(({className:e,...t},a)=>(0,i.jsx)($,{className:(0,K.cn)((0,I.r)(),e),ref:a,...t}));W.displayName=$.displayName;let U=r.forwardRef(({className:e,...t},a)=>(0,i.jsx)(E,{className:(0,K.cn)((0,I.r)({variant:"outline"}),"mt-2 sm:mt-0",e),ref:a,...t}));U.displayName=E.displayName},57207:(e,t,a)=>{a.d(t,{A:()=>i});let i=(0,a(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},68012:(e,t,a)=>{a.d(t,{g:()=>r});var i=a(72273);function r(e,t){let{data:a,error:r,isLoading:l,refetch:s}=(0,i.W_)(e??0,{enabled:(t?.enabled??!0)&&!!e});return{error:r,isLoading:l,refetch:s,vehicleInfo:a?{color:a.color??null,id:a.id,licensePlate:a.licensePlate,make:a.make,model:a.model,vin:a.vin,year:a.year}:null}}},72273:(e,t,a)=>{a.d(t,{NS:()=>m,T$:()=>d,W_:()=>u,Y1:()=>h,lR:()=>p});var i=a(8693),r=a(54050),l=a(46349),s=a(87676),n=a(48839),c=a(49603);let o={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,l.GK)([...o.all],async()=>(await c.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>(0,l.GK)([...o.detail(e)],()=>c.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(t?.enabled??!0),staleTime:3e5,...t}),h=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:a}=(0,s.useNotifications)();return(0,r.n)({mutationFn:e=>{let t=n.M.toCreateRequest(e);return c.vehicleApiService.create(t)},onError:e=>{t(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),a(`Vehicle "${t.licensePlate}" has been created successfully!`)}})},p=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:a}=(0,s.useNotifications)();return(0,r.n)({mutationFn:({data:e,id:t})=>{let a=n.M.toUpdateRequest(e);return c.vehicleApiService.update(t,a)},onError:e=>{t(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(t.id)}),a(`Vehicle "${t.licensePlate}" has been updated successfully!`)}})},m=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:a}=(0,s.useNotifications)();return(0,r.n)({mutationFn:e=>c.vehicleApiService.delete(e),onError:e=>{t(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(t,i)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(i)}),a("Vehicle has been deleted successfully!")}})}},85726:(e,t,a)=>{a.d(t,{E:()=>l});var i=a(60687),r=a(22482);function l({className:e,...t}){return(0,i.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...t})}},87676:(e,t,a)=>{a.r(t),a.d(t,{useNotifications:()=>l,useWorkHubNotifications:()=>s});var i=a(43210),r=a(94538);let l=()=>{let e=(0,r.C)(e=>e.addNotification),t=(0,r.C)(e=>e.removeNotification),a=(0,r.C)(e=>e.clearAllNotifications),l=(0,r.C)(e=>e.unreadNotificationCount),s=(0,i.useCallback)(t=>{e({message:t,type:"success"})},[e]),n=(0,i.useCallback)(t=>{e({message:t,type:"error"})},[e]),c=(0,i.useCallback)(t=>{e({message:t,type:"warning"})},[e]),o=(0,i.useCallback)(t=>{e({message:t,type:"info"})},[e]),d=(0,i.useCallback)((e,t,a)=>{e?s(t):n(a)},[s,n]),u=(0,i.useCallback)((a,i,l=5e3)=>{e({message:i,type:a}),setTimeout(()=>{let e=r.C.getState().notifications.at(-1);e&&e.message===i&&t(e.id)},l)},[e,t]),h=(0,i.useCallback)((t="Loading...")=>{e({message:t,type:"info"});let a=r.C.getState().notifications;return a.at(-1)?.id},[e]),p=(0,i.useCallback)((e,a,i)=>{t(e),a?s(i):n(i)},[t,s,n]);return{clearAllNotifications:a,removeNotification:t,showApiResult:d,showError:n,showInfo:o,showLoading:h,showSuccess:s,showTemporary:u,showWarning:c,unreadCount:l,updateLoadingNotification:p}},s=()=>{let{clearAllNotifications:e,removeNotification:t,showError:a,showInfo:s,showSuccess:n,showWarning:c,unreadCount:o}=l(),d=(0,i.useCallback)((e,t)=>{(0,r.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,i.useCallback)((e,t)=>{(0,r.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),h=(0,i.useCallback)((e,t)=>{(0,r.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:d,showEmployeeUpdate:(0,i.useCallback)((e,t)=>{(0,r.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:a,showInfo:s,showSuccess:n,showTaskAssigned:h,showVehicleMaintenance:u,showWarning:c,unreadCount:o}}}};