(()=>{var e={};e.id=1666,e.ids=[1666],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3707:(e,s,t)=>{"use strict";t.d(s,{DashboardSettings:()=>a});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardSettings() from the server but DashboardSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\DashboardSettings.tsx","DashboardSettings");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\components\\\\reliability\\\\dashboard\\\\DashboardSettings.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\DashboardSettings.tsx","default")},3940:(e,s,t)=>{"use strict";t.d(s,{O_:()=>l,t6:()=>i});var r=t(43210),a=t(49278);function i(){let e=(0,r.useCallback)((e,s)=>a.JP.success(e,s),[]),s=(0,r.useCallback)((e,s)=>a.JP.error(e,s),[]),t=(0,r.useCallback)((e,s)=>a.JP.info(e,s),[]),i=(0,r.useCallback)(s=>e(s?.successTitle||"Success",s?.successDescription||"Operation completed successfully"),[e]),l=(0,r.useCallback)((e,t)=>{let r=e instanceof Error?e.message:e;return s(t?.errorTitle||"Error",t?.errorDescription||r||"An unexpected error occurred")},[s]);return{showSuccess:e,showError:s,showInfo:t,showFormSuccess:i,showFormError:l}}function l(e){let s;switch(e){case"employee":s=t(49278).Ok;break;case"vehicle":s=t(49278).G7;break;case"task":s=t(49278).z0;break;case"delegation":s=t(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,s){let{showFormSuccess:t,showFormError:l}=i(),n=s||(e?(0,a.iw)(e):null),c=(0,r.useCallback)(e=>n?n.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,t]),d=(0,r.useCallback)(e=>n?n.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,t]),o=(0,r.useCallback)(e=>n?n.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,t]),m=(0,r.useCallback)(e=>{if(n){let s=e instanceof Error?e.message:e;return n.entityCreationError(s)}return l(e,{errorTitle:"Creation Failed"})},[n,l]);return{showEntityCreated:c,showEntityUpdated:d,showEntityDeleted:o,showEntityCreationError:m,showEntityUpdateError:(0,r.useCallback)(e=>{if(n){let s=e instanceof Error?e.message:e;return n.entityUpdateError(s)}return l(e,{errorTitle:"Update Failed"})},[n,l]),showEntityDeletionError:(0,r.useCallback)(e=>{if(n){let s=e instanceof Error?e.message:e;return n.entityDeletionError(s)}return l(e,{errorTitle:"Deletion Failed"})},[n,l]),showFormSuccess:t,showFormError:l}}(void 0,s)}},5856:(e,s,t)=>{Promise.resolve().then(t.bind(t,20508)),Promise.resolve().then(t.bind(t,72756)),Promise.resolve().then(t.bind(t,48897)),Promise.resolve().then(t.bind(t,30612)),Promise.resolve().then(t.bind(t,27491)),Promise.resolve().then(t.bind(t,3707)),Promise.resolve().then(t.bind(t,70390)),Promise.resolve().then(t.bind(t,89889)),Promise.resolve().then(t.bind(t,87432)),Promise.resolve().then(t.bind(t,29131))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10963:(e,s,t)=>{"use strict";t.d(s,{ConnectionStatusIndicator:()=>m});var r=t(60687),a=t(3662),i=t(11516),l=t(85777);t(43210);var n=t(96834),c=t(76242),d=t(658),o=t(22482);let m=({className:e="",showText:s=!0,size:t="md"})=>{let{connectionState:m,isConnected:x}=(0,d.gv)(),h=(()=>{switch(m){case"connected":return{bgColor:"bg-green-100 dark:bg-green-900/20",borderColor:"border-green-200 dark:border-green-800",color:"text-green-600 dark:text-green-400",description:"Real-time connection active",icon:a.A,text:"Connected",variant:"default"};case"connecting":case"reconnecting":return{animate:!0,bgColor:"bg-yellow-100 dark:bg-yellow-900/20",borderColor:"border-yellow-200 dark:border-yellow-800",color:"text-yellow-600 dark:text-yellow-400",description:"connecting"===m?"Establishing connection...":"Attempting to reconnect...",icon:i.A,text:"connecting"===m?"Connecting":"Reconnecting",variant:"secondary"};default:return{bgColor:"bg-red-100 dark:bg-red-900/20",borderColor:"border-red-200 dark:border-red-800",color:"text-red-600 dark:text-red-400",description:"error"===m?"Connection error occurred":"Real-time connection lost",icon:l.A,text:"error"===m?"Error":"Disconnected",variant:"destructive"}}})(),u=h.icon,p=(()=>{switch(t){case"lg":return{badge:"text-sm px-3 py-1.5",icon:"h-5 w-5",text:"text-sm"};case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};default:return{badge:"text-xs px-2.5 py-1",icon:"h-4 w-4",text:"text-sm"}}})();return(0,r.jsx)(c.Bc,{children:(0,r.jsxs)(c.m_,{children:[(0,r.jsx)(c.k$,{asChild:!0,children:(0,r.jsxs)("div",{"aria-label":`Connection status: ${h.text}`,className:(0,o.cn)("flex items-center gap-2",e),role:"status",children:[(0,r.jsx)("div",{className:(0,o.cn)("flex items-center justify-center rounded-full p-1",h.bgColor,h.borderColor,"border"),children:(0,r.jsx)(u,{"aria-hidden":"true",className:(0,o.cn)(p.icon,h.color,h.animate&&"animate-spin")})}),s&&(0,r.jsx)(n.E,{className:(0,o.cn)(p.badge,"font-medium"),variant:h.variant,children:h.text})]})}),(0,r.jsxs)(c.ZI,{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:h.text}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:h.description})]})]})})}},11997:e=>{"use strict";e.exports=require("punycode")},15975:(e,s,t)=>{"use strict";t.d(s,{ReliabilityDashboard:()=>p});var r=t(60687),a=t(43210),i=t(30594),l=t(90445),n=t(19060),c=t(95668),d=t(91821),o=t(11516),m=t(22482);let x=({className:e,fullPage:s=!1,size:t="md",text:a})=>(0,r.jsxs)("div",{className:(0,m.cn)("flex flex-col items-center justify-center",s?"fixed inset-0 bg-background/80 backdrop-blur-sm z-50":"",e),children:[(0,r.jsx)(o.A,{className:(0,m.cn)("animate-spin text-primary",{lg:"h-12 w-12",md:"h-8 w-8",sm:"h-4 w-4",xl:"h-16 w-16"}[t])}),a&&(0,r.jsx)("p",{className:(0,m.cn)("mt-2 text-muted-foreground",{lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"}[t]),children:a})]});var h=t(95594),u=t(79986);let p=({className:e="",showSettings:s=!1})=>{let t=(0,h.XD)(e=>e.monitoring.isEnabled),o=(0,h.XD)(e=>e.monitoring.connectionStatus),m=(0,h.XD)(e=>e.ui.activeTab),p=(0,h.XD)(e=>e.setActiveTab),{data:g,isLoading:j,error:f}=(0,u.u_)(),v={data:g?.systemHealth},y={data:g?.circuitBreakers};return((0,a.useEffect)(()=>{m||p("overview")},[m,p]),t)?!j||v.data||y.data?!f||v.data||y.data?(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:`space-y-6 ${e}`,role:"main","aria-label":"Reliability monitoring dashboard",children:[(0,r.jsx)(l.DashboardHeader,{}),"disconnected"===o&&(0,r.jsxs)(d.Fc,{variant:"destructive",className:"mx-auto max-w-4xl",children:[(0,r.jsx)(d.XL,{children:"Real-time Connection Lost"}),(0,r.jsx)(d.TN,{children:"The real-time connection to the monitoring system has been lost. Data may not be current. The system will automatically attempt to reconnect."})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(i.DashboardGrid,{}),s&&(0,r.jsx)(n.DashboardSettings,{})]})]})}):(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:`space-y-6 ${e}`,children:[(0,r.jsx)(l.DashboardHeader,{}),(0,r.jsxs)(d.Fc,{variant:"destructive",className:"mx-auto max-w-2xl",children:[(0,r.jsx)(d.XL,{children:"Dashboard Error"}),(0,r.jsxs)(d.TN,{children:["Failed to load reliability dashboard data. Please check your connection and try again.",f&&(0,r.jsxs)("details",{className:"mt-2",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-sm",children:"Error details"}),(0,r.jsx)("pre",{className:"mt-1 text-xs",children:f.message})]})]})]})]})}):(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:`space-y-6 ${e}`,children:[(0,r.jsx)(l.DashboardHeader,{}),(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x,{size:"lg"}),(0,r.jsx)("p",{className:"mt-4 text-sm text-muted-foreground",children:"Loading reliability dashboard..."})]})})]})}):(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:`space-y-6 ${e}`,children:[(0,r.jsx)(l.DashboardHeader,{}),(0,r.jsxs)(d.Fc,{className:"mx-auto max-w-2xl",children:[(0,r.jsx)(d.XL,{children:"Monitoring Disabled"}),(0,r.jsx)(d.TN,{children:"Reliability monitoring is currently disabled. Enable monitoring in the dashboard settings to view real-time system health data."})]}),(0,r.jsx)(n.DashboardSettings,{})]})})}},18116:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(60687),a=t(24851),i=t(43210),l=t(22482);let n=i.forwardRef(({className:e,...s},t)=>(0,r.jsxs)(a.bL,{className:(0,l.cn)("relative flex w-full touch-none select-none items-center",e),ref:t,...s,children:[(0,r.jsx)(a.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,r.jsx)(a.Q6,{className:"absolute h-full bg-primary"})}),(0,r.jsx)(a.zi,{className:"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));n.displayName=a.bL.displayName},19060:(e,s,t)=>{"use strict";t.d(s,{DashboardSettings:()=>v});var r=t(60687),a=t(58369),i=t(36141),l=t(93704);let n=(0,t(82614).A)("SquareStack",[["path",{d:"M4 10c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2",key:"4i38lg"}],["path",{d:"M10 16c-1.1 0-2-.9-2-2v-4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2",key:"mlte4a"}],["rect",{width:"8",height:"8",x:"14",y:"14",rx:"2",key:"1fa9i4"}]]);t(43210);var c=t(68752),d=t(96834),o=t(44493),m=t(80013),x=t(50812),h=t(15079),u=t(35950),p=t(18116),g=t(54987),j=t(85763),f=t(95594);let v=({className:e=""})=>{let s=(0,f.XD)(e=>e.preferences),t=(0,f.XD)(e=>e.setDashboardLayout),v=(0,f.XD)(e=>e.setGridColumns),y=(0,f.XD)(e=>e.setRefreshInterval),b=(0,f.XD)(e=>e.setNotificationPreferences),N=(0,f.XD)(e=>e.toggleWidget),w=(0,f.XD)(e=>e.resetPreferencesToDefaults),k=(0,f.XD)(e=>e.monitoring.isEnabled),C=(0,f.XD)(e=>e.setMonitoringEnabled),A=[{value:5e3,label:"5 seconds"},{value:1e4,label:"10 seconds"},{value:15e3,label:"15 seconds"},{value:3e4,label:"30 seconds"},{value:6e4,label:"1 minute"},{value:3e5,label:"5 minutes"}],S=(e,s)=>{y(e,parseInt(s))},M=(e,s)=>{b({[e]:s})};return(0,r.jsxs)(o.Zp,{className:e,children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(a.A,{className:"h-5 w-5"}),"Dashboard Settings"]}),(0,r.jsx)(o.BT,{children:"Customize your reliability monitoring dashboard"})]}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsxs)(j.tU,{defaultValue:"layout",className:"w-full",children:[(0,r.jsxs)(j.j7,{className:"grid w-full grid-cols-4",children:[(0,r.jsx)(j.Xi,{value:"layout",children:"Layout"}),(0,r.jsx)(j.Xi,{value:"widgets",children:"Widgets"}),(0,r.jsx)(j.Xi,{value:"refresh",children:"Refresh"}),(0,r.jsx)(j.Xi,{value:"notifications",children:"Alerts"})]}),(0,r.jsx)(j.av,{value:"layout",className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"text-base font-medium",children:"Dashboard Layout"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose how widgets are arranged on the dashboard"})]}),(0,r.jsxs)(x.z,{value:s.dashboardLayout.layout,onValueChange:e=>{t(e)},className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 rounded-lg border p-4",children:[(0,r.jsx)(x.C,{value:"grid",id:"grid"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),(0,r.jsx)(m.J,{htmlFor:"grid",children:"Grid"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 rounded-lg border p-4",children:[(0,r.jsx)(x.C,{value:"list",id:"list"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)(m.J,{htmlFor:"list",children:"List"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 rounded-lg border p-4",children:[(0,r.jsx)(x.C,{value:"compact",id:"compact"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(n,{className:"h-4 w-4"}),(0,r.jsx)(m.J,{htmlFor:"compact",children:"Compact"})]})]})]}),"grid"===s.dashboardLayout.layout&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(m.J,{className:"text-sm font-medium",children:["Grid Columns: ",s.dashboardLayout.gridColumns]}),(0,r.jsx)(p.A,{value:[s.dashboardLayout.gridColumns],onValueChange:e=>{let s=e[0];void 0!==s&&v(s)},max:6,min:1,step:1,className:"w-full"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,r.jsx)("span",{children:"1 column"}),(0,r.jsx)("span",{children:"6 columns"})]})]})]})}),(0,r.jsx)(j.av,{value:"widgets",className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"text-base font-medium",children:"Widget Visibility"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose which widgets to display on the dashboard"})]}),(0,r.jsx)("div",{className:"space-y-3",children:[{id:"system-health",name:"System Health",description:"Overall system status"},{id:"health-status-indicators",name:"Health Status Indicators",description:"Real-time component health status"},{id:"circuit-breakers",name:"Circuit Breakers Overview",description:"Circuit breaker status and failure protection"},{id:"circuit-breaker-metrics",name:"Circuit Breaker Metrics",description:"Performance metrics and failure rate charts"},{id:"performance-overview",name:"Performance Overview",description:"Comprehensive performance metrics and scoring"},{id:"system-metrics",name:"System Performance",description:"Performance-focused system metrics monitoring"},{id:"http-metrics",name:"HTTP Request Metrics",description:"Request performance and throughput analysis"},{id:"performance-metrics",name:"Performance Metrics",description:"System performance monitoring and analysis"},{id:"deduplication-metrics",name:"Deduplication Metrics",description:"Cache efficiency and request deduplication analysis"},{id:"active-alerts",name:"Active Alerts",description:"Current system alerts and notifications"},{id:"alert-statistics",name:"Alert Statistics",description:"Alert trends and statistical analysis"},{id:"dependency-status",name:"Dependency Status",description:"External dependency health monitoring"},{id:"dependency-health",name:"Dependency Health",description:"External service and dependency status"},{id:"health-trends",name:"Health Trends",description:"Historical health trend visualization"}].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-3",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.J,{className:"font-medium",children:e.name}),s.dashboardLayout.visibleWidgets.has(e.id)&&(0,r.jsx)(d.E,{variant:"secondary",className:"text-xs",children:"Visible"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,r.jsx)(g.d,{checked:s.dashboardLayout.visibleWidgets.has(e.id),onCheckedChange:()=>N(e.id)})]},e.id))})]})}),(0,r.jsx)(j.av,{value:"refresh",className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"text-base font-medium",children:"Refresh Intervals"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure how often data is refreshed for different components"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Health Monitoring"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"System health checks"})]}),(0,r.jsxs)(h.l6,{value:s.refreshIntervals.health.toString(),onValueChange:e=>S("health",e),children:[(0,r.jsx)(h.bq,{className:"w-32",children:(0,r.jsx)(h.yv,{})}),(0,r.jsx)(h.gC,{children:A.map(e=>(0,r.jsx)(h.eb,{value:e.value.toString(),children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Circuit Breakers"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Circuit breaker status"})]}),(0,r.jsxs)(h.l6,{value:s.refreshIntervals["circuit-breakers"].toString(),onValueChange:e=>S("circuit-breakers",e),children:[(0,r.jsx)(h.bq,{className:"w-32",children:(0,r.jsx)(h.yv,{})}),(0,r.jsx)(h.gC,{children:A.map(e=>(0,r.jsx)(h.eb,{value:e.value.toString(),children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Performance Metrics"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"System performance data"})]}),(0,r.jsxs)(h.l6,{value:s.refreshIntervals.metrics.toString(),onValueChange:e=>S("metrics",e),children:[(0,r.jsx)(h.bq,{className:"w-32",children:(0,r.jsx)(h.yv,{})}),(0,r.jsx)(h.gC,{children:A.map(e=>(0,r.jsx)(h.eb,{value:e.value.toString(),children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Alerts"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Alert notifications"})]}),(0,r.jsxs)(h.l6,{value:s.refreshIntervals.alerts.toString(),onValueChange:e=>S("alerts",e),children:[(0,r.jsx)(h.bq,{className:"w-32",children:(0,r.jsx)(h.yv,{})}),(0,r.jsx)(h.gC,{children:A.map(e=>(0,r.jsx)(h.eb,{value:e.value.toString(),children:e.label},e.value))})]})]})]})]})}),(0,r.jsx)(j.av,{value:"notifications",className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"text-base font-medium",children:"Alert Notifications"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure how you receive alert notifications"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Sound Notifications"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Play sound for new alerts"})]}),(0,r.jsx)(g.d,{checked:s.notifications.soundEnabled,onCheckedChange:e=>M("soundEnabled",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Desktop Notifications"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Show browser notifications"})]}),(0,r.jsx)(g.d,{checked:s.notifications.desktopEnabled,onCheckedChange:e=>M("desktopEnabled",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Minimum Severity"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Only notify for alerts above this level"})]}),(0,r.jsxs)(h.l6,{value:s.notifications.minimumSeverity,onValueChange:e=>M("minimumSeverity",e),children:[(0,r.jsx)(h.bq,{className:"w-32",children:(0,r.jsx)(h.yv,{})}),(0,r.jsxs)(h.gC,{children:[(0,r.jsx)(h.eb,{value:"low",children:"Low"}),(0,r.jsx)(h.eb,{value:"medium",children:"Medium"}),(0,r.jsx)(h.eb,{value:"high",children:"High"}),(0,r.jsx)(h.eb,{value:"critical",children:"Critical"})]})]})]})]})]})})]}),(0,r.jsx)(u.w,{className:"my-6"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(m.J,{className:"font-medium",children:"Monitoring Status"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:k?"Real-time monitoring active":"Monitoring paused"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(g.d,{checked:k,onCheckedChange:C}),(0,r.jsx)(c.r,{actionType:"secondary",size:"sm",onClick:w,children:"Reset to Defaults"})]})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20508:(e,s,t)=>{"use strict";t.d(s,{LoginForm:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\auth\\loginForm.tsx","LoginForm")},21820:e=>{"use strict";e.exports=require("os")},27491:(e,s,t)=>{"use strict";t.d(s,{DashboardHeader:()=>a});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardHeader() from the server but DashboardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\DashboardHeader.tsx","DashboardHeader");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\components\\\\reliability\\\\dashboard\\\\DashboardHeader.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\DashboardHeader.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29131:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>a,useAuthContext:()=>i});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthContext() from the server but useAuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\contexts\\AuthContext.tsx","useAuthContext");(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\contexts\\AuthContext.tsx","AuthContext")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30594:(e,s,t)=>{"use strict";t.d(s,{DashboardGrid:()=>s$});var r=t(60687),a=t(43210),i=t.n(a),l=t(61278),n=t(91821),c=t(29523),d=t(21724),o=t(53597),m=t(82614);let x=(0,m.A)("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);var h=t(14975),u=t(15036),p=t(3662),g=t(81950),j=t(96834),f=t(44493),v=t(23562),y=t(79986),b=t(22482);let N=({className:e="",showDetails:s=!0})=>{let{data:t,isLoading:a,error:i}=(0,y.Hs)(),l=e=>{switch(e){case"CLOSED":return{icon:d.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",description:"Operating normally"};case"HALF_OPEN":return{icon:o.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Testing",description:"Testing recovery"};case"OPEN":return{icon:x,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Failed",description:"Blocking requests"}}},n=()=>{if(!t?.summary)return 0;let{total:e,closed:s}=t.summary;return e>0?Math.round(s/e*100):100};if(a)return(0,r.jsx)("div",{className:(0,b.cn)("space-y-4",e),children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)(f.Zp,{className:"animate-pulse",children:[(0,r.jsx)(f.aR,{className:"pb-2",children:(0,r.jsx)("div",{className:"h-4 w-20 bg-muted rounded"})}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"h-8 w-12 bg-muted rounded mb-2"}),(0,r.jsx)("div",{className:"h-3 w-16 bg-muted rounded"})]})]},s))})});if(i||!t)return(0,r.jsx)("div",{className:(0,b.cn)("flex items-center justify-center py-8",e),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker data"})]})});let{summary:c,circuitBreakers:m}=t,N=n(),w=(()=>{let e=n();return e>=90?"healthy":e>=70?"degraded":"unhealthy"})();return(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Total"}),(0,r.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:c.total}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Circuit breakers"})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Healthy"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:c.closed}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Operating normally"})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Testing"}),(0,r.jsx)(u.A,{className:"h-4 w-4 text-yellow-600"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:c.halfOpen}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Testing recovery"})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Failed"}),(0,r.jsx)(x,{className:"h-4 w-4 text-red-600"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:c.open}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Blocking requests"})]})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:(0,b.cn)("h-5 w-5","healthy"===w?"text-green-600":"degraded"===w?"text-yellow-600":"text-red-600")}),"Overall Health",(0,r.jsxs)(j.E,{variant:"healthy"===w?"default":"degraded"===w?"secondary":"destructive",children:[N,"%"]})]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(v.k,{value:N,className:"h-2","aria-label":`Circuit breaker health: ${N}%`}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-muted-foreground",children:[c.closed," of ",c.total," circuit breakers healthy"]}),c.open>0&&(0,r.jsxs)("span",{className:"flex items-center gap-1 text-red-600",children:[(0,r.jsx)(g.A,{className:"h-3 w-3"}),c.open," failing"]})]})]})})]}),s&&m.length>0&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Circuit Breaker Status"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[m.slice(0,5).map(e=>{let s=l(e.state),t=s.icon;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:(0,b.cn)("p-2 rounded-full",s.bgColor),children:(0,r.jsx)(t,{className:(0,b.cn)("h-4 w-4",s.color)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:s.description})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)(j.E,{variant:"CLOSED"===e.state?"default":"HALF_OPEN"===e.state?"secondary":"destructive",children:s.label}),e.failureCount>0&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[e.failureCount," failures"]})]})]},e.name)}),m.length>5&&(0,r.jsx)("div",{className:"text-center pt-2",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["+",m.length-5," more circuit breakers"]})})]})})]})]})};var w=t(77368);let k=(0,m.A)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var C=t(89667),A=t(15079),S=t(6211),M=t(76242);let E=({className:e="",maxItems:s=10})=>{let{data:t,isLoading:l,error:n,refetch:m}=(0,y.Hs)(),[p,g]=(0,a.useState)(""),[v,N]=(0,a.useState)("all"),E=e=>{switch(e){case"CLOSED":return{icon:d.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",variant:"default"};case"HALF_OPEN":return{icon:o.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Testing",variant:"secondary"};case"OPEN":return{icon:x,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Failed",variant:"destructive"}}},R=e=>{if(!e)return"Never";let s=new Date,t=new Date(e),r=Math.floor((s.getTime()-t.getTime())/6e4),a=Math.floor(r/60),i=Math.floor(a/24);return i>0?`${i}d ago`:a>0?`${a}h ago`:r>0?`${r}m ago`:"Just now"},D=e=>{if(!e)return null;let s=new Date,t=new Date(e).getTime()-s.getTime();if(t<=0)return"Ready to test";let r=Math.floor(t/6e4),a=Math.floor(t%6e4/1e3);return r>0?`${r}m ${a}s`:`${a}s`},T=i().useMemo(()=>t?.circuitBreakers?t.circuitBreakers.filter(e=>{let s=e.name.toLowerCase().includes(p.toLowerCase()),t="all"===v||e.state===v;return s&&t}).slice(0,s):[],[t?.circuitBreakers,p,v,s]);return l?(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Circuit Breakers"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:5}).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-4 animate-pulse",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-muted rounded-full"}),(0,r.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded w-1/3"}),(0,r.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]}),(0,r.jsx)("div",{className:"h-6 w-16 bg-muted rounded"})]},s))})})]}):n||!t?(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Circuit Breakers"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Failed to load circuit breaker data"}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>m(),children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Retry"]})]})})})]}):(0,r.jsx)(M.Bc,{children:(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsxs)(f.aR,{children:[(0,r.jsx)(f.ZB,{children:"Circuit Breakers"}),(0,r.jsxs)("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(C.p,{placeholder:"Search circuit breakers...",value:p,onChange:e=>g(e.target.value),className:"w-64"}),(0,r.jsxs)(A.l6,{value:v,onValueChange:e=>N(e),children:[(0,r.jsx)(A.bq,{className:"w-32",children:(0,r.jsx)(A.yv,{})}),(0,r.jsxs)(A.gC,{children:[(0,r.jsx)(A.eb,{value:"all",children:"All States"}),(0,r.jsx)(A.eb,{value:"CLOSED",children:"Healthy"}),(0,r.jsx)(A.eb,{value:"HALF_OPEN",children:"Testing"}),(0,r.jsx)(A.eb,{value:"OPEN",children:"Failed"})]})]})]}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>m(),children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,r.jsxs)(f.Wu,{children:[0===T.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(o.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:p||"all"!==v?"No circuit breakers match your filters":"No circuit breakers found"})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)(S.XI,{children:[(0,r.jsx)(S.A0,{children:(0,r.jsxs)(S.Hj,{children:[(0,r.jsx)(S.nd,{children:"Name"}),(0,r.jsx)(S.nd,{children:"Status"}),(0,r.jsx)(S.nd,{children:"Failures"}),(0,r.jsx)(S.nd,{children:"Last Failure"}),(0,r.jsx)(S.nd,{children:"Next Attempt"})]})}),(0,r.jsx)(S.BF,{children:T.map(e=>{let s=E(e.state),t=s.icon,a=D(e.nextAttempt);return(0,r.jsxs)(S.Hj,{children:[(0,r.jsx)(S.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:(0,b.cn)("p-2 rounded-full",s.bgColor),children:(0,r.jsx)(t,{className:(0,b.cn)("h-4 w-4",s.color)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),e.timeout&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Timeout: ",e.timeout,"ms"]})]})]})}),(0,r.jsx)(S.nA,{children:(0,r.jsx)(j.E,{variant:s.variant,children:s.label})}),(0,r.jsx)(S.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:(0,b.cn)("font-medium",e.failureCount>0?"text-red-600":"text-muted-foreground"),children:e.failureCount}),void 0!==e.successCount&&(0,r.jsxs)(M.m_,{children:[(0,r.jsx)(M.k$,{children:(0,r.jsxs)("span",{className:"text-xs text-green-600",children:["+",e.successCount]})}),(0,r.jsxs)(M.ZI,{children:[e.successCount," successful requests"]})]})]})}),(0,r.jsx)(S.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,r.jsx)(u.A,{className:"h-3 w-3"}),R(e.lastFailureTime)]})}),(0,r.jsx)(S.nA,{children:a?(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,r.jsx)(k,{className:"h-3 w-3 text-yellow-600"}),(0,r.jsx)("span",{className:"text-yellow-600",children:a})]}):(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"—"})})]},e.name)})})]})}),t.circuitBreakers.length>s&&(0,r.jsx)("div",{className:"text-center pt-4 border-t",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",Math.min(T.length,s)," of ",t.circuitBreakers.length," circuit breakers"]})})]})]})})};var R=t(8751),D=t(27629),T=t(22853),P=t(48482),L=t(53892),B=t(69206),F=t(25679),O=t(38246),$=t(57359);let H={dark:".dark",light:""},z=a.createContext(null);function W(){let e=a.useContext(z);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let Z=a.forwardRef(({children:e,className:s,config:t,id:i,...l},n)=>{let c=a.useId(),d=`chart-${i||c.replaceAll(":","")}`;return(0,r.jsx)(z.Provider,{value:{config:t},children:(0,r.jsxs)("div",{className:(0,b.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",s),"data-chart":d,ref:n,...l,children:[(0,r.jsx)(q,{config:t,id:d}),(0,r.jsx)(P.u,{children:e})]})})});Z.displayName="Chart";let q=({config:e,id:s})=>{let t=Object.entries(e).filter(([,e])=>e.theme||e.color);return 0===t.length?null:(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(H).map(([e,r])=>`
${r} [data-chart=${s}] {
${t.map(([s,t])=>{let r=t.theme?.[e]||t.color;return r?`  --color-${s}: ${r};`:null}).join("\n")}
}
`).join("\n")}})},I=O.m,U=a.forwardRef(({active:e,className:s,color:t,formatter:i,hideIndicator:l=!1,hideLabel:n=!1,indicator:c="dot",label:d,labelClassName:o,labelFormatter:m,labelKey:x,nameKey:h,payload:u},p)=>{let{config:g}=W(),j=a.useMemo(()=>{if(n||!u?.length)return null;let[e]=u,s=`${x||e?.dataKey||e?.name||"value"}`,t=_(g,e,s),a=x||"string"!=typeof d?t?.label:g[d]?.label||d;return m?(0,r.jsx)("div",{className:(0,b.cn)("font-medium",o),children:m(a,u)}):a?(0,r.jsx)("div",{className:(0,b.cn)("font-medium",o),children:a}):null},[d,m,u,n,o,g,x]);if(!e||!u?.length)return null;let f=1===u.length&&"dot"!==c;return(0,r.jsxs)("div",{className:(0,b.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",s),ref:p,children:[f?null:j,(0,r.jsx)("div",{className:"grid gap-1.5",children:u.map((e,s)=>{let a=`${h||e.name||e.dataKey||"value"}`,n=_(g,e,a),d=t||e.payload.fill||e.color;return(0,r.jsx)("div",{className:(0,b.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===c&&"items-center"),children:i&&e?.value!==void 0&&e.name?i(e.value,e.name,e,s,e.payload):(0,r.jsxs)(r.Fragment,{children:[n?.icon?(0,r.jsx)(n.icon,{}):!l&&(0,r.jsx)("div",{className:(0,b.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===c,"my-0.5":f&&"dashed"===c,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===c,"w-1":"line"===c}),style:{"--color-bg":d,"--color-border":d}}),(0,r.jsxs)("div",{className:(0,b.cn)("flex flex-1 justify-between leading-none",f?"items-end":"items-center"),children:[(0,r.jsxs)("div",{className:"grid gap-1.5",children:[f?j:null,(0,r.jsx)("span",{className:"text-muted-foreground",children:n?.label||e.name})]}),e.value&&(0,r.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});function _(e,s,t){if("object"!=typeof s||null===s)return;let r="payload"in s&&"object"==typeof s.payload&&null!==s.payload?s.payload:void 0,a=t;return t in s&&"string"==typeof s[t]?a=s[t]:r&&t in r&&"string"==typeof r[t]&&(a=r[t]),a in e?e[a]:e[t]}U.displayName="ChartTooltip",$.s,a.forwardRef(({className:e,hideIcon:s=!1,nameKey:t,payload:a,verticalAlign:i="bottom"},l)=>{let{config:n}=W();return a?.length?(0,r.jsx)("div",{className:(0,b.cn)("flex items-center justify-center gap-4","top"===i?"pb-3":"pt-3",e),ref:l,children:a.map(e=>{let a=`${t||e.dataKey||"value"}`,i=_(n,e,a);return(0,r.jsxs)("div",{className:(0,b.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[i?.icon&&!s?(0,r.jsx)(i.icon,{}):(0,r.jsx)("div",{className:"size-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),i?.label]},e.value)})}):null}).displayName="ChartLegend";let V=({className:e="",showCharts:s=!0})=>{let{data:t,isLoading:a,error:i}=(0,y.Hs)();if(a)return(0,r.jsx)("div",{className:(0,b.cn)("space-y-4",e),children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)(f.Zp,{className:"animate-pulse",children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)("div",{className:"h-4 w-24 bg-muted rounded"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"h-20 bg-muted rounded"})})]},s))})});if(i||!t)return(0,r.jsx)("div",{className:(0,b.cn)("flex items-center justify-center py-8",e),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker metrics"})]})});let l=(()=>{if(!t?.summary)return[];let{closed:e,halfOpen:s,open:r}=t.summary;return[{name:"Healthy",value:e,color:"#10b981",percentage:t.summary.total>0?Math.round(e/t.summary.total*100):0},{name:"Testing",value:s,color:"#f59e0b",percentage:t.summary.total>0?Math.round(s/t.summary.total*100):0},{name:"Failed",value:r,color:"#ef4444",percentage:t.summary.total>0?Math.round(r/t.summary.total*100):0}].filter(e=>e.value>0)})(),n=(()=>{if(!t?.circuitBreakers)return{totalFailures:0,totalSuccesses:0,averageFailureRate:0,healthScore:100};let e=t.circuitBreakers.reduce((e,s)=>e+s.failureCount,0),s=t.circuitBreakers.reduce((e,s)=>e+(s.successCount||0),0),r=e+s;return{totalFailures:e,totalSuccesses:s,averageFailureRate:r>0?e/r*100:0,healthScore:t.summary.total>0?Math.round(t.summary.closed/t.summary.total*100):100}})();return(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Health Score"}),(0,r.jsx)(R.A,{className:(0,b.cn)("h-4 w-4",n.healthScore>=90?"text-green-600":n.healthScore>=70?"text-yellow-600":"text-red-600")})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsxs)("div",{className:(0,b.cn)("text-2xl font-bold",n.healthScore>=90?"text-green-600":n.healthScore>=70?"text-yellow-600":"text-red-600"),children:[n.healthScore,"%"]}),(0,r.jsx)(v.k,{value:n.healthScore,className:"mt-2 h-2","aria-label":`Health score: ${n.healthScore}%`})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Failure Rate"}),(0,r.jsx)(g.A,{className:"h-4 w-4 text-red-600"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[n.averageFailureRate.toFixed(1),"%"]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[n.totalFailures," failures"]})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Successes"}),(0,r.jsx)(R.A,{className:"h-4 w-4 text-green-600"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:n.totalSuccesses.toLocaleString()}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Successful requests"})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(f.ZB,{className:"text-sm font-medium",children:"Active"}),(0,r.jsx)(D.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.summary.total}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Circuit breakers"})]})]})]}),s&&l.length>0&&(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-2",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(T.A,{className:"h-5 w-5"}),"State Distribution"]})}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)(Z,{config:{healthy:{label:"Healthy",color:"#10b981"},testing:{label:"Testing",color:"#f59e0b"},failed:{label:"Failed",color:"#ef4444"}},className:"h-[200px]",children:(0,r.jsx)(P.u,{width:"100%",height:"100%",children:(0,r.jsxs)(L.r,{children:[(0,r.jsx)(B.F,{data:l,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:2,dataKey:"value",children:l.map((e,s)=>(0,r.jsx)(F.f,{fill:e.color},`cell-${s}`))}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{formatter:(e,s)=>[`${e} (${l.find(e=>e.name===s)?.percentage}%)`,s]})})]})})}),(0,r.jsx)("div",{className:"flex justify-center gap-4 mt-4",children:l.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.name," (",e.percentage,"%)"]})]},e.name))})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(D.A,{className:"h-5 w-5"}),"Performance Summary"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Success Rate"}),(0,r.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:[(100-n.averageFailureRate).toFixed(1),"%"]})]}),(0,r.jsx)(v.k,{value:100-n.averageFailureRate,className:"h-2","aria-label":`Success rate: ${(100-n.averageFailureRate).toFixed(1)}%`})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Failure Rate"}),(0,r.jsxs)("span",{className:"text-sm text-red-600 font-medium",children:[n.averageFailureRate.toFixed(1),"%"]})]}),(0,r.jsx)(v.k,{value:n.averageFailureRate,className:"h-2","aria-label":`Failure rate: ${n.averageFailureRate.toFixed(1)}%`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-2 border-t",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-green-600",children:n.totalSuccesses.toLocaleString()}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Successes"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-red-600",children:n.totalFailures.toLocaleString()}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Failures"})]})]})]})})]})]})]})};var X=t(44610),G=t(26622);let K=e=>{let s=new Date,t=[],r="1h"===e?1:"6h"===e?6:"24h"===e?24:"7d"===e?168:720,a=Math.min(r/2,20);for(let e=0;e<a;e++){let a=new Date(s.getTime()-Math.random()*r*36e5),i=["CLOSED","OPEN","HALF_OPEN"],l=i[Math.floor(Math.random()*i.length)],n=i[Math.floor(Math.random()*i.length)];l!==n&&t.push({id:`event-${e}`,circuitBreakerName:`service-${Math.floor(5*Math.random())+1}`,fromState:l,toState:n,timestamp:a.toISOString(),reason:"CLOSED"===l&&"OPEN"===n?"Failure threshold exceeded":"OPEN"===l&&"HALF_OPEN"===n?"Timeout period elapsed":"HALF_OPEN"===l&&"CLOSED"===n?"Test request succeeded":"HALF_OPEN"===l&&"OPEN"===n?"Test request failed":"State transition",failureCount:Math.floor(10*Math.random())})}return t.sort((e,s)=>new Date(s.timestamp).getTime()-new Date(e.timestamp).getTime())},J=({className:e="",timeRange:s="24h"})=>{let{data:t,isLoading:i,error:l}=(0,y.Hs)(),[n,c]=(0,a.useState)(s),m=e=>{switch(e){case"CLOSED":return{icon:d.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",variant:"default"};case"HALF_OPEN":return{icon:o.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Testing",variant:"secondary"};case"OPEN":return{icon:x,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Failed",variant:"destructive"}}},p=e=>{let s=new Date,t=new Date(e),r=Math.floor((s.getTime()-t.getTime())/6e4),a=Math.floor(r/60),i=Math.floor(a/24);return i>0?`${i}d ago`:a>0?`${a}h ago`:r>0?`${r}m ago`:"Just now"},g=(e,s)=>"CLOSED"===e&&"OPEN"===s?"failure":"OPEN"===e&&"HALF_OPEN"===s?"recovery-attempt":"HALF_OPEN"===e&&"CLOSED"===s?"recovery-success":"HALF_OPEN"===e&&"OPEN"===s?"recovery-failure":"neutral",v=K(n);return i?(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Circuit Breaker History"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-4 animate-pulse",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-muted rounded-full"}),(0,r.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded w-2/3"}),(0,r.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]}),(0,r.jsx)("div",{className:"h-6 w-16 bg-muted rounded"})]},s))})})]}):l||!t?(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Circuit Breaker History"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker history"})]})})})]}):(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(X.A,{className:"h-5 w-5"}),"Circuit Breaker History"]}),(0,r.jsxs)(A.l6,{value:n,onValueChange:e=>c(e),children:[(0,r.jsx)(A.bq,{className:"w-32",children:(0,r.jsx)(A.yv,{})}),(0,r.jsxs)(A.gC,{children:[(0,r.jsx)(A.eb,{value:"1h",children:"Last Hour"}),(0,r.jsx)(A.eb,{value:"6h",children:"Last 6 Hours"}),(0,r.jsx)(A.eb,{value:"24h",children:"Last 24 Hours"}),(0,r.jsx)(A.eb,{value:"7d",children:"Last 7 Days"}),(0,r.jsx)(A.eb,{value:"30d",children:"Last 30 Days"})]})]})]})}),(0,r.jsx)(f.Wu,{children:0===v.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(G.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"No state changes in the selected time range"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"This indicates stable circuit breaker operation"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3 mb-6",children:[(0,r.jsxs)("div",{className:"text-center p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)("div",{className:"text-lg font-bold",children:v.length}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"State Changes"})]}),(0,r.jsxs)("div",{className:"text-center p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-red-600",children:v.filter(e=>e.fromState&&e.toState&&"failure"===g(e.fromState,e.toState)).length}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Failures"})]}),(0,r.jsxs)("div",{className:"text-center p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-green-600",children:v.filter(e=>e.fromState&&e.toState&&"recovery-success"===g(e.fromState,e.toState)).length}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Recoveries"})]})]}),(0,r.jsx)("div",{className:"space-y-3",children:v.map((e,s)=>{if(!e.fromState||!e.toState)return null;let t=m(e.fromState),a=m(e.toState),i=g(e.fromState,e.toState),l=t.icon,n=a.icon;return(0,r.jsxs)("div",{className:"flex items-center gap-4 p-3 rounded-lg border bg-card",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:(0,b.cn)("w-3 h-3 rounded-full","failure"===i?"bg-red-500":"recovery-success"===i?"bg-green-500":"recovery-attempt"===i?"bg-yellow-500":"bg-muted-foreground")}),s<v.length-1&&(0,r.jsx)("div",{className:"w-px h-8 bg-border mt-2"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:e.circuitBreakerName}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:(0,b.cn)("p-1 rounded",t.bgColor),children:(0,r.jsx)(l,{className:(0,b.cn)("h-3 w-3",t.color)})}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"→"}),(0,r.jsx)("div",{className:(0,b.cn)("p-1 rounded",a.bgColor),children:(0,r.jsx)(n,{className:(0,b.cn)("h-3 w-3",a.color)})})]}),(0,r.jsx)(j.E,{variant:a.variant,className:"text-xs",children:a.label})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.reason,e.failureCount>0&&(0,r.jsxs)("span",{className:"ml-2 text-red-600",children:["(",e.failureCount," failures)"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,r.jsx)(u.A,{className:"h-3 w-3"}),p(e.timestamp)]})]})]})]},e.id)})})]})})]})};var Q=t(64181);let Y=(0,m.A)("BellOff",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]]),ee=({className:e="",maxAlerts:s=10})=>{let{data:t,isLoading:l,error:n}=(0,y.Zz)(),[d,m]=(0,a.useState)("all"),g=e=>{switch(e){case"critical":return{icon:x,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",variant:"destructive",label:"Critical"};case"high":return{icon:h.A,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-100 dark:bg-orange-900/20",variant:"destructive",label:"High"};case"medium":return{icon:o.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",variant:"secondary",label:"Medium"};case"low":return{icon:Q.A,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20",variant:"outline",label:"Low"}}},v=e=>{let s=new Date,t=new Date(e),r=Math.floor((s.getTime()-t.getTime())/6e4),a=Math.floor(r/60),i=Math.floor(a/24);return i>0?`${i}d ago`:a>0?`${a}h ago`:r>0?`${r}m ago`:"Just now"},N=i().useMemo(()=>t?t.filter(e=>{let s="circuit-breaker"===e.type||e.source?.includes("circuit")||e.message.toLowerCase().includes("circuit"),t="all"===d||e.severity===d,r="active"===e.status;return s&&t&&r}).slice(0,s):[],[t,d,s]),w=async e=>{console.log("Acknowledging alert:",e)},k=async e=>{console.log("Resolving alert:",e)};return l?(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Circuit Breaker Alerts"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-4 animate-pulse",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-muted rounded-full"}),(0,r.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded w-2/3"}),(0,r.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]}),(0,r.jsx)("div",{className:"h-6 w-16 bg-muted rounded"})]},s))})})]}):n||!t?(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Circuit Breaker Alerts"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker alerts"})]})})})]}):(0,r.jsx)(M.Bc,{children:(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(Q.A,{className:"h-5 w-5"}),"Circuit Breaker Alerts",N.length>0&&(0,r.jsx)(j.E,{variant:"destructive",className:"ml-2",children:N.length})]}),(0,r.jsxs)(A.l6,{value:d,onValueChange:e=>m(e),children:[(0,r.jsx)(A.bq,{className:"w-32",children:(0,r.jsx)(A.yv,{})}),(0,r.jsxs)(A.gC,{children:[(0,r.jsx)(A.eb,{value:"all",children:"All Levels"}),(0,r.jsx)(A.eb,{value:"critical",children:"Critical"}),(0,r.jsx)(A.eb,{value:"high",children:"High"}),(0,r.jsx)(A.eb,{value:"medium",children:"Medium"}),(0,r.jsx)(A.eb,{value:"low",children:"Low"})]})]})]})}),(0,r.jsx)(f.Wu,{children:0===N.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(p.A,{className:"mx-auto h-8 w-8 text-green-600 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"No active circuit breaker alerts"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"All circuit breakers are operating normally"})]}):(0,r.jsxs)("div",{className:"space-y-3",children:[N.map(e=>{let s=g(e.severity),t=s.icon;return(0,r.jsxs)("div",{className:(0,b.cn)("flex items-start gap-3 p-3 rounded-lg border",s.bgColor),children:[(0,r.jsx)("div",{className:(0,b.cn)("p-2 rounded-full bg-background/50"),children:(0,r.jsx)(t,{className:(0,b.cn)("h-4 w-4",s.color)})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.message}),e.source&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]})]}),(0,r.jsx)(j.E,{variant:s.variant,className:"text-xs",children:s.label})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,r.jsx)(u.A,{className:"h-3 w-3"}),v(e.timestamp)]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsxs)(M.m_,{children:[(0,r.jsx)(M.k$,{asChild:!0,children:(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>w(e.id),className:"h-7 w-7 p-0",children:(0,r.jsx)(Y,{className:"h-3 w-3"})})}),(0,r.jsx)(M.ZI,{children:"Acknowledge alert"})]}),(0,r.jsxs)(M.m_,{children:[(0,r.jsx)(M.k$,{asChild:!0,children:(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>k(e.id),className:"h-7 w-7 p-0",children:(0,r.jsx)(p.A,{className:"h-3 w-3"})})}),(0,r.jsx)(M.ZI,{children:"Resolve alert"})]})]})]})]})]},e.id)}),t.filter(e=>("circuit-breaker"===e.type||e.source?.includes("circuit"))&&"active"===e.status).length>s&&(0,r.jsxs)("div",{className:"text-center pt-3 border-t",children:[(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["+",t.filter(e=>("circuit-breaker"===e.type||e.source?.includes("circuit"))&&"active"===e.status).length-s," ","more alerts"]}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",className:"mt-2",children:"View All Alerts"})]})]})})]})})};var es=t(97025);let et=(0,m.A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),er=(0,m.A)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);var ea=t(8563),ei=t(27805),el=t(81801);let en=({className:e="",showDetails:s=!0,showDependencies:t=!0})=>{let{data:a,isLoading:i,error:l}=(0,y.n8)(),{data:n}=(0,y.Rt)(),c=e=>{switch(e){case"healthy":return{icon:p.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",variant:"default"};case"degraded":return{icon:h.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Degraded",variant:"secondary"};case"unhealthy":return{icon:es.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Unhealthy",variant:"destructive"};default:return{icon:u.A,color:"text-gray-600 dark:text-gray-400",bgColor:"bg-gray-100 dark:bg-gray-900/20",label:"Unknown",variant:"outline"}}};if(i)return(0,r.jsx)("div",{className:(0,b.cn)("space-y-4",e),children:(0,r.jsxs)(f.Zp,{className:"animate-pulse",children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)("div",{className:"h-6 w-32 bg-muted rounded"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"h-20 bg-muted rounded"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)("div",{className:"h-16 bg-muted rounded"}),(0,r.jsx)("div",{className:"h-16 bg-muted rounded"})]})]})})]})});if(l||!a)return(0,r.jsx)("div",{className:(0,b.cn)("flex items-center justify-center py-8",e),children:(0,r.jsx)(f.Zp,{className:"w-full",children:(0,r.jsx)(f.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load system health data"})]})})})});let d=c(a.status),o=d.icon,m=(()=>{if(!n?.checks)return 0;let e=n.checks,s=Object.values(e).filter(e=>"healthy"===e.status).length;return Object.keys(e).length>0?Math.round(s/Object.keys(e).length*100):100})();return(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(et,{className:"h-5 w-5 text-red-500"}),"System Health",(0,r.jsx)(j.E,{variant:d.variant,children:d.label})]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:(0,b.cn)("flex items-center gap-4 p-4 rounded-lg",d.bgColor),children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o,{className:(0,b.cn)("h-8 w-8",d.color)})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("h3",{className:"font-semibold text-lg",children:["System is ",d.label]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"All systems operational"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:(0,b.cn)("text-2xl font-bold",d.color),children:[m,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Health Score"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Overall Health"}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[m,"%"]})]}),(0,r.jsx)(v.k,{value:m,className:"h-2","aria-label":`System health score: ${m}%`})]}),s&&(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Uptime"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:(e=>{if(!e)return"Unknown";let s=Math.floor(e/864e5),t=Math.floor(e%864e5/36e5),r=Math.floor(e%36e5/6e4);return s>0?`${s}d ${t}h`:t>0?`${t}h ${r}m`:`${r}m`})(a.uptime)})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)(er,{className:"h-5 w-5 text-purple-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Version"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:a.version||"Unknown"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)(ea.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Environment"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:a.environment||"Unknown"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)(ei.A,{className:"h-5 w-5 text-orange-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Last Check"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:a.timestamp?new Date(a.timestamp).toLocaleTimeString():"Unknown"})]})]})]})]})})]}),t&&n?.checks&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(el.A,{className:"h-5 w-5"}),"System Components"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(n.checks).map(([e,s])=>{let t=c(s.status),a=t.icon;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:(0,b.cn)("p-2 rounded-full",t.bgColor),children:(0,r.jsx)(a,{className:(0,b.cn)("h-4 w-4",t.color)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium capitalize",children:e.replace(/([A-Z])/g," $1").trim()}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:t.label})]})]}),(0,r.jsx)(j.E,{variant:t.variant,children:t.label})]},e)})})})]})]})};var ec=t(85726);let ed=e=>{switch(e){case"healthy":return"text-green-600 bg-green-50 border-green-200";case"degraded":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"unhealthy":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},eo=e=>({className:s})=>{switch(e){case"healthy":return(0,r.jsx)(p.A,{className:s});case"degraded":return(0,r.jsx)(h.A,{className:s});case"unhealthy":return(0,r.jsx)(es.A,{className:s});default:return(0,r.jsx)(u.A,{className:s})}},em=e=>e?e<1e3?`${e}ms`:`${(e/1e3).toFixed(2)}s`:"N/A",ex=({className:e="",showDetails:s=!0,showResponseTimes:t=!0,compact:a=!1})=>{let{data:l,isLoading:n,error:c}=(0,y.n8)(),{data:d,isLoading:m,error:x}=(0,y.Rt)(),h=c||x,u=i().useMemo(()=>d?.checks?[{id:"database",label:"Database",status:d.checks.database.status,icon:({className:e})=>(0,r.jsx)(el.A,{className:e}),responseTime:d.checks.database.responseTime||0,details:"PostgreSQL connection and query performance"},{id:"supabase",label:"Supabase",status:d.checks.supabase.status,icon:({className:e})=>(0,r.jsx)(ea.A,{className:e}),responseTime:d.checks.supabase.responseTime||0,details:"Supabase API and real-time connections"},{id:"cache",label:"Cache",status:d.checks.cache.status,icon:({className:e})=>(0,r.jsx)(er,{className:e}),responseTime:d.checks.cache.responseTime||0,details:d.checks.cache.details?.redis?.status||"Cache system status"},{id:"circuitBreakers",label:"Circuit Breakers",status:d.checks.circuitBreakers.status,icon:({className:e})=>(0,r.jsx)(o.A,{className:e}),responseTime:d.checks.circuitBreakers.responseTime||0,details:`${d.checks.circuitBreakers.details?.openBreakers||0} open breakers`},{id:"systemResources",label:"System Resources",status:d.checks.systemResources.status,icon:({className:e})=>(0,r.jsx)(ei.A,{className:e}),responseTime:d.checks.systemResources.responseTime||0,details:`${d.checks.systemResources.details?.memory?.usagePercent||0}% memory usage`},{id:"businessLogic",label:"Business Logic",status:d.checks.businessLogic.status,icon:({className:e})=>(0,r.jsx)(et,{className:e}),responseTime:d.checks.businessLogic.responseTime||0,details:"Core application services and workflows"}]:[],[d]);return n||m?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-32"}),(0,r.jsx)(ec.E,{className:"h-5 w-20"})]}),(0,r.jsx)("div",{className:"grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",children:Array.from({length:6}).map((e,s)=>(0,r.jsx)(ec.E,{className:"h-16 w-full"},s))})]}):h?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(es.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load health status"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:h.message||"Unable to retrieve system health information"})]}):(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(et,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"font-semibold text-sm",children:"System Health Status"})]}),l&&(0,r.jsx)(j.E,{variant:"outline",className:(0,b.cn)("font-medium",ed(l.status)),children:l.status.toUpperCase()})]}),(0,r.jsx)("div",{className:(0,b.cn)("grid gap-3",a?"grid-cols-2 sm:grid-cols-3":"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"),children:u.map(e=>{let i=eo(e.status),l=e.icon;return(0,r.jsx)(f.Zp,{className:(0,b.cn)("transition-all duration-200 hover:shadow-md",ed(e.status),a?"p-3":""),children:(0,r.jsxs)(f.Wu,{className:(0,b.cn)("p-4",a&&"p-3"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l,{className:(0,b.cn)("h-4 w-4",a?"h-3 w-3":"h-4 w-4")}),(0,r.jsx)("span",{className:(0,b.cn)("font-medium",a?"text-xs":"text-sm"),children:e.label})]}),(0,r.jsx)(i,{className:(0,b.cn)(a?"h-3 w-3":"h-4 w-4")})]}),t&&e.responseTime&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("p",{className:(0,b.cn)("text-muted-foreground","text-xs"),children:["Response: ",em(e.responseTime)]})}),s&&e.details&&!a&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.details})})]})},e.id)})}),d?.summary&&!a&&(0,r.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 pt-2",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-green-600",children:d.summary.healthyChecks}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Healthy"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-yellow-600",children:d.summary.degradedChecks}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Degraded"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-red-600",children:d.summary.unhealthyChecks}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Unhealthy"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-blue-600",children:d.summary.totalChecks}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total"})]})]})]})};var eh=t(67857),eu=t(2093);let ep=e=>{switch(e){case"healthy":return"text-green-600 bg-green-50 border-green-200";case"degraded":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"unhealthy":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},eg=e=>({className:s})=>{switch(e){case"healthy":return(0,r.jsx)(p.A,{className:s});case"degraded":return(0,r.jsx)(h.A,{className:s});case"unhealthy":return(0,r.jsx)(es.A,{className:s});default:return(0,r.jsx)(u.A,{className:s})}},ej=e=>e?e<1e3?`${e}ms`:`${(e/1e3).toFixed(2)}s`:"N/A",ef=e=>void 0===e?"N/A":`${e.toFixed(2)}%`,ev=e=>e?e>=99.9?"text-green-600":e>=99?"text-yellow-600":"text-red-600":"text-gray-500",ey=({Icon:e,className:s})=>(0,r.jsx)(e,{className:s}),eb=({className:e="",showResponseTimes:s=!0,showAvailability:t=!0,compact:a=!1})=>{let{data:l,isLoading:n,error:c}=(0,y.vW)(),d=i().useCallback(e=>{let s=e.toLowerCase();return s.includes("database")||s.includes("db")?e=>(0,r.jsx)(ey,{Icon:el.A,...e}):s.includes("api")||s.includes("service")?e=>(0,r.jsx)(ey,{Icon:er,...e}):s.includes("cache")||s.includes("redis")?e=>(0,r.jsx)(ey,{Icon:eh.A}):s.includes("auth")||s.includes("jwt")?e=>(0,r.jsx)(ey,{Icon:eu.A,...e}):e=>(0,r.jsx)(ey,{Icon:er,...e})},[]),o=i().useCallback(e=>{let s=e.toLowerCase();return s.includes("database")?"Primary database connection":s.includes("supabase")?"Supabase backend services":s.includes("redis")?"Cache and session storage":s.includes("websocket")?"Real-time communication":s.includes("auth")?"Authentication services":"External service dependency"},[]),m=i().useMemo(()=>l?.dependencies?(Array.isArray(l.dependencies)?l.dependencies:Object.entries(l.dependencies).map(([e,s])=>({name:e,status:s.status,responseTime:s.responseTime,lastChecked:s.lastChecked||new Date().toISOString(),error:s.error}))).map(e=>({id:e.name.toLowerCase().replace(/\s+/g,"-"),name:e.name,status:e.status,responseTime:e.responseTime||0,availability:10*Math.random()+90,lastChecked:e.lastChecked,icon:d(e.name),description:o(e.name)})):[],[l,d,o]);return n?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-40"}),(0,r.jsx)(ec.E,{className:"h-5 w-24"})]}),(0,r.jsx)("div",{className:"grid gap-3 grid-cols-1 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,r.jsx)(ec.E,{className:"h-24 w-full"},s))})]}):c?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(es.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load dependency status"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:c.message||"Unable to retrieve dependency health information"})]}):m.length?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(ea.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"font-semibold text-sm",children:"External Dependencies"})]}),l&&(0,r.jsx)(j.E,{variant:"outline",className:(0,b.cn)("font-medium",ep(l.summary.healthy>l.summary.unhealthy?"healthy":"unhealthy")),children:l.summary.healthy>l.summary.unhealthy?"HEALTHY":"UNHEALTHY"})]}),(0,r.jsx)("div",{className:(0,b.cn)("grid gap-3",a?"grid-cols-1":"grid-cols-1 sm:grid-cols-2"),children:m.map(e=>{let i=eg(e.status),l=e.icon;return(0,r.jsx)(f.Zp,{className:(0,b.cn)("transition-all duration-200 hover:shadow-md",ep(e.status)),children:(0,r.jsxs)(f.Wu,{className:(0,b.cn)("p-4",a&&"p-3"),children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l,{className:"h-4 w-4 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:(0,b.cn)("font-medium",a?"text-xs":"text-sm"),children:e.name}),!a&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]}),(0,r.jsx)(i,{className:"h-4 w-4 flex-shrink-0"})]}),(0,r.jsxs)("div",{className:"mt-3 space-y-2",children:[s&&e.responseTime&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Response Time:"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:ej(e.responseTime)})]}),t&&void 0!==e.availability&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Availability:"}),(0,r.jsx)("span",{className:(0,b.cn)("text-xs font-medium",ev(e.availability)),children:ef(e.availability)})]}),e.lastChecked&&!a&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Last Checked:"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:new Date(e.lastChecked).toLocaleTimeString()})]})]})]})},e.id)})}),l?.summary&&!a&&(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-2",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-green-600",children:l.summary.healthy}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Healthy"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-yellow-600",children:l.summary.degraded}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Degraded"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-red-600",children:l.summary.unhealthy}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Unhealthy"})]})]})]}):(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(er,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"No dependencies configured"})]})};var eN=t(61678),ew=t(27747),ek=t(9920),eC=t(50326),eA=t(2041),eS=t(90812),eM=t(92491),eE=t(49384),eR=t(93492),eD=t(5231),eT=t.n(eD),eP=t(90453),eL=t.n(eP),eB=t(37456),eF=t.n(eB),eO=t(77822),e$=t.n(eO),eH=t(71967),ez=t.n(eH),eW=t(81888),eZ=t(95530),eq=t(98986),eI=t(98845),eU=t(20237),e_=t(22989),eV=t(30087),eX=t(54186),eG=["layout","type","stroke","connectNulls","isRange","ref"],eK=["key"];function eJ(e){return(eJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eQ(e,s){if(null==e)return{};var t,r,a=function(e,s){if(null==e)return{};var t={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(s.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,s);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)t=i[r],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}function eY(){return(eY=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function e0(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);s&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function e2(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?e0(Object(t),!0).forEach(function(s){e3(e,s,t[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):e0(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function e1(e,s){for(var t=0;t<s.length;t++){var r=s[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,e8(r.key),r)}}function e4(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e4=function(){return!!e})()}function e6(e){return(e6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function e5(e,s){return(e5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,s){return e.__proto__=s,e})(e,s)}function e3(e,s,t){return(s=e8(s))in e?Object.defineProperty(e,s,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[s]=t,e}function e8(e){var s=function(e,s){if("object"!=eJ(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,s||"default");if("object"!=eJ(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==eJ(s)?s:s+""}var e9=function(e){var s,t;function r(){var e,s,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var a=arguments.length,i=Array(a),l=0;l<a;l++)i[l]=arguments[l];return s=r,t=[].concat(i),s=e6(s),e3(e=function(e,s){if(s&&("object"===eJ(s)||"function"==typeof s))return s;if(void 0!==s)throw TypeError("Derived constructors may only return object or undefined");var t=e;if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(this,e4()?Reflect.construct(s,t||[],e6(this).constructor):s.apply(this,t)),"state",{isAnimationFinished:!0}),e3(e,"id",(0,e_.NF)("recharts-area-")),e3(e,"handleAnimationEnd",function(){var s=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),eT()(s)&&s()}),e3(e,"handleAnimationStart",function(){var s=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),eT()(s)&&s()}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&e5(r,e),s=[{key:"renderDots",value:function(e,s,t){var a=this.props.isAnimationActive,l=this.state.isAnimationFinished;if(a&&!l)return null;var n=this.props,c=n.dot,d=n.points,o=n.dataKey,m=(0,eX.J9)(this.props,!1),x=(0,eX.J9)(c,!0),h=d.map(function(e,s){var t=e2(e2(e2({key:"dot-".concat(s),r:3},m),x),{},{index:s,cx:e.x,cy:e.y,dataKey:o,value:e.value,payload:e.payload,points:d});return r.renderDotItem(c,t)}),u={clipPath:e?"url(#clipPath-".concat(s?"":"dots-").concat(t,")"):null};return i().createElement(eq.W,eY({className:"recharts-area-dots"},u),h)}},{key:"renderHorizontalRect",value:function(e){var s=this.props,t=s.baseLine,r=s.points,a=s.strokeWidth,l=r[0].x,n=r[r.length-1].x,c=e*Math.abs(l-n),d=eL()(r.map(function(e){return e.y||0}));return((0,e_.Et)(t)&&"number"==typeof t?d=Math.max(t,d):t&&Array.isArray(t)&&t.length&&(d=Math.max(eL()(t.map(function(e){return e.y||0})),d)),(0,e_.Et)(d))?i().createElement("rect",{x:l<n?l:l-c,y:0,width:c,height:Math.floor(d+(a?parseInt("".concat(a),10):1))}):null}},{key:"renderVerticalRect",value:function(e){var s=this.props,t=s.baseLine,r=s.points,a=s.strokeWidth,l=r[0].y,n=r[r.length-1].y,c=e*Math.abs(l-n),d=eL()(r.map(function(e){return e.x||0}));return((0,e_.Et)(t)&&"number"==typeof t?d=Math.max(t,d):t&&Array.isArray(t)&&t.length&&(d=Math.max(eL()(t.map(function(e){return e.x||0})),d)),(0,e_.Et)(d))?i().createElement("rect",{x:0,y:l<n?l:l-c,width:d+(a?parseInt("".concat(a),10):1),height:Math.floor(c)}):null}},{key:"renderClipRect",value:function(e){return"vertical"===this.props.layout?this.renderVerticalRect(e):this.renderHorizontalRect(e)}},{key:"renderAreaStatically",value:function(e,s,t,r){var a=this.props,l=a.layout,n=a.type,c=a.stroke,d=a.connectNulls,o=a.isRange,m=(a.ref,eQ(a,eG));return i().createElement(eq.W,{clipPath:t?"url(#clipPath-".concat(r,")"):null},i().createElement(eW.I,eY({},(0,eX.J9)(m,!0),{points:e,connectNulls:d,type:n,baseLine:s,layout:l,stroke:"none",className:"recharts-area-area"})),"none"!==c&&i().createElement(eW.I,eY({},(0,eX.J9)(this.props,!1),{className:"recharts-area-curve",layout:l,type:n,connectNulls:d,fill:"none",points:e})),"none"!==c&&o&&i().createElement(eW.I,eY({},(0,eX.J9)(this.props,!1),{className:"recharts-area-curve",layout:l,type:n,connectNulls:d,fill:"none",points:s})))}},{key:"renderAreaWithAnimation",value:function(e,s){var t=this,r=this.props,a=r.points,l=r.baseLine,n=r.isAnimationActive,c=r.animationBegin,d=r.animationDuration,o=r.animationEasing,m=r.animationId,x=this.state,h=x.prevPoints,u=x.prevBaseLine;return i().createElement(eR.Ay,{begin:c,duration:d,isActive:n,easing:o,from:{t:0},to:{t:1},key:"area-".concat(m),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(r){var n=r.t;if(h){var c,d=h.length/a.length,o=a.map(function(e,s){var t=Math.floor(s*d);if(h[t]){var r=h[t],a=(0,e_.Dj)(r.x,e.x),i=(0,e_.Dj)(r.y,e.y);return e2(e2({},e),{},{x:a(n),y:i(n)})}return e});return c=(0,e_.Et)(l)&&"number"==typeof l?(0,e_.Dj)(u,l)(n):eF()(l)||e$()(l)?(0,e_.Dj)(u,0)(n):l.map(function(e,s){var t=Math.floor(s*d);if(u[t]){var r=u[t],a=(0,e_.Dj)(r.x,e.x),i=(0,e_.Dj)(r.y,e.y);return e2(e2({},e),{},{x:a(n),y:i(n)})}return e}),t.renderAreaStatically(o,c,e,s)}return i().createElement(eq.W,null,i().createElement("defs",null,i().createElement("clipPath",{id:"animationClipPath-".concat(s)},t.renderClipRect(n))),i().createElement(eq.W,{clipPath:"url(#animationClipPath-".concat(s,")")},t.renderAreaStatically(a,l,e,s)))})}},{key:"renderArea",value:function(e,s){var t=this.props,r=t.points,a=t.baseLine,i=t.isAnimationActive,l=this.state,n=l.prevPoints,c=l.prevBaseLine,d=l.totalLength;return i&&r&&r.length&&(!n&&d>0||!ez()(n,r)||!ez()(c,a))?this.renderAreaWithAnimation(e,s):this.renderAreaStatically(r,a,e,s)}},{key:"render",value:function(){var e,s=this.props,t=s.hide,r=s.dot,a=s.points,l=s.className,n=s.top,c=s.left,d=s.xAxis,o=s.yAxis,m=s.width,x=s.height,h=s.isAnimationActive,u=s.id;if(t||!a||!a.length)return null;var p=this.state.isAnimationFinished,g=1===a.length,j=(0,eE.A)("recharts-area",l),f=d&&d.allowDataOverflow,v=o&&o.allowDataOverflow,y=f||v,b=eF()(u)?this.id:u,N=null!=(e=(0,eX.J9)(r,!1))?e:{r:3,strokeWidth:2},w=N.r,k=N.strokeWidth,C=((0,eX.sT)(r)?r:{}).clipDot,A=void 0===C||C,S=2*(void 0===w?3:w)+(void 0===k?2:k);return i().createElement(eq.W,{className:j},f||v?i().createElement("defs",null,i().createElement("clipPath",{id:"clipPath-".concat(b)},i().createElement("rect",{x:f?c:c-m/2,y:v?n:n-x/2,width:f?m:2*m,height:v?x:2*x})),!A&&i().createElement("clipPath",{id:"clipPath-dots-".concat(b)},i().createElement("rect",{x:c-S/2,y:n-S/2,width:m+S,height:x+S}))):null,g?null:this.renderArea(y,b),(r||g)&&this.renderDots(y,A,b),(!h||p)&&eI.Z.renderCallByParent(this.props,a))}}],t=[{key:"getDerivedStateFromProps",value:function(e,s){return e.animationId!==s.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,curBaseLine:e.baseLine,prevPoints:s.curPoints,prevBaseLine:s.curBaseLine}:e.points!==s.curPoints||e.baseLine!==s.curBaseLine?{curPoints:e.points,curBaseLine:e.baseLine}:null}}],s&&e1(r.prototype,s),t&&e1(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a.PureComponent);e3(e9,"displayName","Area"),e3(e9,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!eU.m.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),e3(e9,"getBaseValue",function(e,s,t,r){var a=e.layout,i=e.baseValue,l=s.props.baseValue,n=null!=l?l:i;if((0,e_.Et)(n)&&"number"==typeof n)return n;var c="horizontal"===a?r:t,d=c.scale.domain();if("number"===c.type){var o=Math.max(d[0],d[1]),m=Math.min(d[0],d[1]);return"dataMin"===n?m:"dataMax"===n||o<0?o:Math.max(Math.min(d[0],d[1]),0)}return"dataMin"===n?d[0]:"dataMax"===n?d[1]:d[0]}),e3(e9,"getComposedData",function(e){var s,t=e.props,r=e.item,a=e.xAxis,i=e.yAxis,l=e.xAxisTicks,n=e.yAxisTicks,c=e.bandSize,d=e.dataKey,o=e.stackedData,m=e.dataStartIndex,x=e.displayedData,h=e.offset,u=t.layout,p=o&&o.length,g=e9.getBaseValue(t,r,a,i),j="horizontal"===u,f=!1,v=x.map(function(e,s){p?t=o[m+s]:Array.isArray(t=(0,eV.kr)(e,d))?f=!0:t=[g,t];var t,r=null==t[1]||p&&null==(0,eV.kr)(e,d);return j?{x:(0,eV.nb)({axis:a,ticks:l,bandSize:c,entry:e,index:s}),y:r?null:i.scale(t[1]),value:t,payload:e}:{x:r?null:a.scale(t[1]),y:(0,eV.nb)({axis:i,ticks:n,bandSize:c,entry:e,index:s}),value:t,payload:e}});return s=p||f?v.map(function(e){var s=Array.isArray(e.value)?e.value[0]:null;return j?{x:e.x,y:null!=s&&null!=e.y?i.scale(s):null}:{x:null!=s?a.scale(s):null,y:e.y}}):j?i.scale(g):a.scale(g),e2({points:v,baseLine:s,layout:u,isRange:f},h)}),e3(e9,"renderDotItem",function(e,s){var t;if(i().isValidElement(e))t=i().cloneElement(e,s);else if(eT()(e))t=e(s);else{var r=(0,eE.A)("recharts-area-dot","boolean"!=typeof e?e.className:""),a=s.key,l=eQ(s,eK);t=i().createElement(eZ.c,eY({},l,{key:a,className:r}))}return t});var e7=t(84629),se=(0,eM.gu)({chartName:"AreaChart",GraphicalChild:e9,axisComponents:[{axisType:"xAxis",AxisComp:ew.W},{axisType:"yAxis",AxisComp:ek.h}],formatAxisMap:e7.pr});let ss=[{value:"1h",label:"1 Hour"},{value:"6h",label:"6 Hours"},{value:"24h",label:"24 Hours"},{value:"7d",label:"7 Days"}],st=(e,s)=>{switch(s){case"1h":case"6h":case"24h":return e.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"});case"7d":return e.toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit"});default:return e.toLocaleTimeString()}},sr=({className:e="",chartType:s="area",showTimeRangeSelector:t=!0,height:a=300,defaultTimeframe:l="24h"})=>{let[n,d]=i().useState(l),{data:o,isLoading:m,error:x}=(0,y.Gz)(n),h=i().useMemo(()=>o?.dataPoints?o.dataPoints.map(e=>({timestamp:e.timestamp,time:st(new Date(e.timestamp),n),healthy:e.summary.healthyChecks,degraded:e.summary.degradedChecks,unhealthy:e.summary.unhealthyChecks,overallScore:Math.round(e.summary.healthyChecks/e.summary.totalChecks*100)})):[],[o,n]),u=i().useMemo(()=>{if(h.length<2)return"stable";let e=h.slice(-3),s=h.slice(-6,-3),t=e.reduce((e,s)=>e+s.overallScore,0)/e.length-s.reduce((e,s)=>e+s.overallScore,0)/s.length;return t>2?"improving":t<-2?"declining":"stable"},[h]),p={healthy:{label:"Healthy",color:"#10b981"},degraded:{label:"Degraded",color:"#f59e0b"},unhealthy:{label:"Unhealthy",color:"#ef4444"},overallScore:{label:"Overall Score",color:"#3b82f6"}};return m?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-32"}),(0,r.jsx)(ec.E,{className:"h-8 w-24"})]}),(0,r.jsx)(ec.E,{className:"w-full",style:{height:`${a}px`}})]}):x?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(D.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load health trends"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:x.message||"Unable to retrieve health trend data"})]}):(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"font-semibold text-sm",children:"Health Trends"}),"stable"!==u&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:["improving"===u?(0,r.jsx)(R.A,{className:"h-4 w-4 text-green-600"}):(0,r.jsx)(g.A,{className:"h-4 w-4 text-red-600"}),(0,r.jsx)("span",{className:(0,b.cn)("text-xs font-medium","improving"===u?"text-green-600":"text-red-600"),children:u})]})]}),t&&(0,r.jsx)("div",{className:"flex items-center gap-1",children:ss.map(e=>(0,r.jsx)(c.$,{variant:n===e.value?"default":"ghost",size:"sm",className:"h-7 px-2 text-xs",onClick:()=>d(e.value),children:e.label},e.value))})]}),(0,r.jsx)(Z,{config:p,className:"w-full",style:{height:`${a}px`},children:(0,r.jsx)(P.u,{width:"100%",height:"100%",children:(()=>{let e={data:h,margin:{top:5,right:30,left:20,bottom:5}};switch(s){case"line":return(0,r.jsxs)(eN.b,{...e,children:[(0,r.jsx)(ew.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(ek.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{})}),(0,r.jsx)(eC.N,{type:"monotone",dataKey:"overallScore",stroke:p.overallScore.color,strokeWidth:2,dot:!1})]});case"bar":return(0,r.jsxs)(eA.E,{...e,children:[(0,r.jsx)(ew.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(ek.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{})}),(0,r.jsx)(eS.y,{dataKey:"healthy",stackId:"a",fill:p.healthy.color}),(0,r.jsx)(eS.y,{dataKey:"degraded",stackId:"a",fill:p.degraded.color}),(0,r.jsx)(eS.y,{dataKey:"unhealthy",stackId:"a",fill:p.unhealthy.color})]});default:return(0,r.jsxs)(se,{...e,children:[(0,r.jsx)(ew.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(ek.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{})}),(0,r.jsx)(e9,{type:"monotone",dataKey:"healthy",stackId:"1",stroke:p.healthy.color,fill:p.healthy.color,fillOpacity:.6}),(0,r.jsx)(e9,{type:"monotone",dataKey:"degraded",stackId:"1",stroke:p.degraded.color,fill:p.degraded.color,fillOpacity:.6}),(0,r.jsx)(e9,{type:"monotone",dataKey:"unhealthy",stackId:"1",stroke:p.unhealthy.color,fill:p.unhealthy.color,fillOpacity:.6})]})}})()})})]})},sa=(0,m.A)("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),si=(0,m.A)("MemoryStick",[["path",{d:"M6 19v-3",key:"1nvgqn"}],["path",{d:"M10 19v-3",key:"iu8nkm"}],["path",{d:"M14 19v-3",key:"kcehxu"}],["path",{d:"M18 19v-3",key:"1vh91z"}],["path",{d:"M8 11V9",key:"63erz4"}],["path",{d:"M16 11V9",key:"fru6f3"}],["path",{d:"M12 11V9",key:"ha00sb"}],["path",{d:"M2 15h20",key:"16ne18"}],["path",{d:"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1.1a2 2 0 0 0 0 3.837V17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5.1a2 2 0 0 0 0-3.837Z",key:"lhddv3"}]]);var sl=t(5600);let sn=(e,s,t)=>e>=t?"text-red-600":e>=s?"text-yellow-600":"text-green-600",sc=e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return`${parseFloat((e/Math.pow(1024,s)).toFixed(1))} ${["B","KB","MB","GB","TB"][s]}`},sd=({className:e="",showDetails:s=!0,compact:t=!1,showConnections:a=!0})=>{let{data:l,isLoading:n,error:c}=(0,y.BB)(),d=i().useMemo(()=>{if(!l)return[];let e=[];return l.systemMetrics?.cpu?.loadAverage?.[0]!==void 0&&e.push({id:"cpu-load",label:"CPU Load",value:100*l.systemMetrics.cpu.loadAverage[0],unit:"%",threshold:{warning:70,critical:90},icon:({className:e})=>(0,r.jsx)(sa,{className:e}),description:"1-minute load average"}),l.systemMetrics?.cpu?.usage!==void 0&&e.push({id:"cpu-usage",label:"CPU Usage",value:l.systemMetrics.cpu.usage,unit:"%",threshold:{warning:70,critical:90},icon:({className:e})=>(0,r.jsx)(sa,{className:e}),description:"Current CPU utilization"}),l.systemMetrics?.memory?.usagePercent!==void 0&&e.push({id:"memory-usage",label:"Memory Usage",value:l.systemMetrics.memory.usagePercent,unit:"%",threshold:{warning:80,critical:95},icon:({className:e})=>(0,r.jsx)(si,{className:e}),description:`${sc(l.systemMetrics.memory.used)} / ${sc(l.systemMetrics.memory.total)}`}),e},[l]),o=i().useMemo(()=>l?.systemMetrics?.connections?{activeConnections:l.systemMetrics.connections.active||0,requestsPerSecond:0,averageResponseTime:0}:null,[l]);return n?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-32"}),(0,r.jsx)(ec.E,{className:"h-5 w-20"})]}),(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(ec.E,{className:"h-4 w-20"}),(0,r.jsx)(ec.E,{className:"h-4 w-12"})]}),(0,r.jsx)(ec.E,{className:"h-2 w-full"})]},s))})]}):c?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(sl.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load system metrics"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:c.message||"Unable to retrieve system resource information"})]}):(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(ei.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"font-semibold text-sm",children:"System Resources"})]}),(0,r.jsx)(j.E,{variant:"outline",className:"font-medium",children:"System Monitor"})]}),(0,r.jsx)("div",{className:"space-y-3",children:d.map(e=>{let a=e.icon,i=sn(e.value,e.threshold.warning,e.threshold.critical);return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(a,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:(0,b.cn)("font-medium",t?"text-xs":"text-sm"),children:e.label})]}),(0,r.jsxs)("span",{className:(0,b.cn)("font-semibold",t?"text-xs":"text-sm",i),children:[e.value.toFixed(1),e.unit]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(v.k,{value:Math.min(e.value,100),className:"h-2"}),s&&!t&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.id)})}),a&&o&&(0,r.jsxs)("div",{className:"pt-2 border-t",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)(eh.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("h4",{className:"font-medium text-sm",children:"Connections"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:(0,b.cn)("font-semibold text-blue-600",t?"text-sm":"text-lg"),children:o.activeConnections}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:(0,b.cn)("font-semibold text-green-600",t?"text-sm":"text-lg"),children:o.requestsPerSecond.toFixed(1)}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Req/sec"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{className:(0,b.cn)("font-semibold text-purple-600",t?"text-sm":"text-lg"),children:[o.averageResponseTime.toFixed(0),"ms"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Avg Response"})]})]})]}),l&&s&&!t&&(0,r.jsxs)("div",{className:"pt-2 border-t",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)(er,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("h4",{className:"font-medium text-sm",children:"System Info"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"CPU Cores:"}),(0,r.jsx)("p",{className:"font-medium",children:l.systemMetrics?.cpu?.loadAverage?.length||"Unknown"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Memory Total:"}),(0,r.jsx)("p",{className:"font-medium",children:l.systemMetrics?.memory?.total?sc(l.systemMetrics.memory.total):"Unknown"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Connections:"}),(0,r.jsx)("p",{className:"font-medium",children:l.systemMetrics?.connections?.total||"Unknown"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Status:"}),(0,r.jsx)("p",{className:"font-medium",children:"Monitoring"})]})]})]})]})},so=e=>{let s=100,t=e.systemMetrics?.cpu?.usage||0;t>90?s-=30:t>70?s-=20:t>50&&(s-=10);let r=e.systemMetrics?.memory?.usagePercent||0;r>95?s-=25:r>80?s-=15:r>60&&(s-=5);let a=e.deduplicationMetrics?.hitRate||0;return a<40?s-=25:a<60?s-=15:a<80&&(s-=5),0===(e.httpRequestMetrics?.values?.length||0)&&(s-=10),Math.max(0,Math.min(100,s))},sm=e=>e>=90?"excellent":e>=75?"good":e>=50?"warning":"poor",sx=e=>{switch(e){case"poor":return"text-red-600 bg-red-50 border-red-200";case"excellent":return"text-green-600 bg-green-50 border-green-200";case"good":return"text-blue-600 bg-blue-50 border-blue-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},sh=e=>{switch(e){case"down":return({className:e})=>(0,r.jsx)(g.A,{className:e});case"up":return({className:e})=>(0,r.jsx)(R.A,{className:e});default:return({className:e})=>(0,r.jsx)(ei.A,{className:e})}},su=({className:e="",compact:s=!1,showDetails:t=!0,showTrends:a=!0})=>{let{data:l,error:n,isLoading:c}=(0,y.BB)(),d=i().useMemo(()=>{if(!l)return[];let e=[],s=l.httpRequestMetrics?.values?.length?Math.round(1e3*(l.httpRequestMetrics.values.reduce((e,s)=>e+s.value,0)/l.httpRequestMetrics.values.length)):200;e.push({description:"Average API response time",icon:({className:e})=>(0,r.jsx)(u.A,{className:e}),id:"response-time",label:"Avg Response Time",recommendation:s<300?"Excellent":s<500?"Good":s<1e3?"Warning":"Critical",status:s<200?"excellent":s<500?"good":s<1e3?"warning":"poor",trend:s<300?"stable":"up",unit:"ms",value:s});let t=l.systemMetrics?.connections?.active||0;e.push({description:"Request processing throughput",icon:({className:e})=>(0,r.jsx)(eu.A,{className:e}),id:"throughput",label:"Throughput",recommendation:t>50?"Excellent":t>20?"Good":t>5?"Warning":"Critical",status:t>50?"excellent":t>20?"good":t>5?"warning":"poor",trend:"stable",unit:"req/min",value:10*t});let a=l.deduplicationMetrics?.hitRate||0;e.push({description:"Request deduplication efficiency",icon:({className:e})=>(0,r.jsx)(D.A,{className:e}),id:"cache-hit-rate",label:"Cache Hit Rate",recommendation:a>80?"Excellent":a>60?"Good":a>40?"Warning":"Critical",status:a>80?"excellent":a>60?"good":a>40?"warning":"poor",trend:a>70?"up":"down",unit:"%",value:a});let i=(()=>{if(!l.httpRequestMetrics?.values?.length)return 0;let e=l.httpRequestMetrics.values.reduce((e,s)=>e+s.value,0),s=l.httpRequestMetrics.values.filter(e=>parseInt(e.labels.statusCode)>=400).reduce((e,s)=>e+s.value,0);return e>0?s/e*100:0})();return e.push({description:"Request error percentage",icon:({className:e})=>(0,r.jsx)(h.A,{className:e}),id:"error-rate",label:"Error Rate",recommendation:i<1?"Excellent":i<3?"Good":i<5?"Warning":"Critical",status:i<1?"excellent":i<3?"good":i<5?"warning":"poor",trend:i<2?"down":"up",unit:"%",value:i}),e},[l]),o=i().useMemo(()=>l?so(l):0,[l]),m=sm(o);return c?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-40"}),(0,r.jsx)(ec.E,{className:"h-8 w-24"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,r.jsx)(ec.E,{className:"h-20 w-full"},s))}),(0,r.jsx)(ec.E,{className:"h-16 w-full"})]}):n?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(h.A,{className:"mx-auto mb-4 size-12 text-red-500"}),(0,r.jsx)("p",{className:"text-sm font-medium text-red-600",children:"Failed to load performance metrics"}),(0,r.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:n.message||"Unable to retrieve performance data"})]}):(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(ei.A,{className:"size-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"text-sm font-semibold",children:"Performance Overview"})]}),(0,r.jsxs)(j.E,{className:(0,b.cn)("font-medium",sx(m)),variant:"outline",children:["Score: ",o]})]}),!s&&(0,r.jsxs)(f.Zp,{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Overall Performance"}),(0,r.jsx)("span",{className:(0,b.cn)("text-sm font-semibold","excellent"===m?"text-green-600":"good"===m?"text-blue-600":"warning"===m?"text-yellow-600":"text-red-600"),children:m.toUpperCase()})]}),(0,r.jsx)(v.k,{className:"h-3",value:o}),(0,r.jsx)("p",{className:"mt-2 text-xs text-muted-foreground",children:"Performance score based on CPU, memory, cache efficiency, and response times"})]}),(0,r.jsx)("div",{className:(0,b.cn)("grid gap-3",s?"grid-cols-2":"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"),children:d.map(e=>{let i=sh(e.trend);return(0,r.jsx)(f.Zp,{className:(0,b.cn)("transition-all duration-200 hover:shadow-md",sx(e.status)),children:(0,r.jsxs)(f.Wu,{className:(0,b.cn)("p-4",s&&"p-3"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,r.jsx)(e.icon,{className:"size-4"}),(0,r.jsx)("span",{className:(0,b.cn)("font-medium",s?"text-xs":"text-sm"),children:e.label})]}),a&&e.trend&&(0,r.jsx)(i,{className:(0,b.cn)(s?"h-3 w-3":"h-4 w-4","up"===e.trend?"text-green-600":"down"===e.trend?"text-red-600":"text-gray-600")})]}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("p",{className:(0,b.cn)("font-semibold",s?"text-sm":"text-lg"),children:[e.value.toFixed(+("%"===e.unit)),e.unit]}),t&&!s&&(0,r.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:e.description})]})]})},e.id)})})]})},sp=(e,s)=>{let t=[];for(let r=11;r>=0;r--){let a=Math.max(0,Math.min(100,e+5*Math.sin(r/12*Math.PI*2)));t.push({time:`${5*r}m ago`,[s]:a,timestamp:Date.now()-5*r*6e4})}return t},sg=e=>{switch(e){case"low":return"text-green-600";case"medium":return"text-yellow-600";case"high":return"text-red-600";default:return"text-gray-600"}},sj=(e,s,t)=>e>=t?"text-red-600 bg-red-50 border-red-200":e>=s?"text-yellow-600 bg-yellow-50 border-yellow-200":"text-green-600 bg-green-50 border-green-200",sf=e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return`${parseFloat((e/Math.pow(1024,s)).toFixed(1))} ${["B","KB","MB","GB","TB"][s]}`},sv=({className:e="",showCharts:s=!0,showRecommendations:t=!0,compact:a=!1})=>{let{data:l,isLoading:n,error:c}=(0,y.BB)(),d=i().useMemo(()=>{if(!l)return[];let e=[];if(l.systemMetrics?.cpu?.usage!==void 0){let s=l.systemMetrics.cpu.usage,t={id:"cpu-performance",label:"CPU Performance",value:s,unit:"%",threshold:{warning:70,critical:90},performanceImpact:s>80?"high":s>60?"medium":"low",icon:({className:e})=>(0,r.jsx)(sa,{className:e}),description:"CPU utilization impact on response times"};s>80&&(t.recommendation="Consider scaling or optimizing CPU-intensive operations"),e.push(t)}if(l.systemMetrics?.memory?.usagePercent!==void 0){let s=l.systemMetrics.memory.usagePercent,t={id:"memory-performance",label:"Memory Efficiency",value:s,unit:"%",threshold:{warning:80,critical:95},performanceImpact:s>85?"high":s>70?"medium":"low",icon:({className:e})=>(0,r.jsx)(si,{className:e}),description:"Memory usage impact on application performance"};s>85&&(t.recommendation="Memory optimization needed - check for memory leaks"),e.push(t)}if(l.systemMetrics?.connections){let{active:s,total:t}=l.systemMetrics.connections,a=t>0?s/t*100:0;e.push({id:"connection-performance",label:"Connection Pool",value:a,unit:"%",threshold:{warning:80,critical:95},performanceImpact:a>90?"high":a>70?"medium":"low",icon:({className:e})=>(0,r.jsx)(eh.A,{className:e}),description:"Connection pool utilization and efficiency",recommendation:a>90?"Excellent connection performance":a>70?"Good connection performance":a>40?"Consider optimizing connection pooling":"Critical: Review connection management"})}if(l.systemMetrics?.cpu?.loadAverage?.[0]!==void 0){let s=Math.min(100,100*l.systemMetrics.cpu.loadAverage[0]);e.push({id:"load-performance",label:"System Load",value:s,unit:"%",threshold:{warning:70,critical:90},performanceImpact:s>80?"high":s>60?"medium":"low",icon:({className:e})=>(0,r.jsx)(ei.A,{className:e}),description:"1-minute load average performance impact",recommendation:s>80?"Excellent load handling":s>60?"Good load performance":s>40?"Consider load balancing optimization":"Critical: Review load distribution"})}return e},[l]),o=i().useMemo(()=>{if(!d.length)return[];let e=d.find(e=>"cpu-performance"===e.id);return e?sp(e.value,"CPU"):[]},[d]),m={CPU:{label:"CPU Usage",color:"#3b82f6"}};return n?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-40"}),(0,r.jsx)(ec.E,{className:"h-5 w-24"})]}),(0,r.jsx)("div",{className:"grid gap-3 grid-cols-1 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,r.jsx)(ec.E,{className:"h-24 w-full"},s))}),s&&(0,r.jsx)(ec.E,{className:"h-64 w-full"})]}):c?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(sl.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load system metrics"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:c.message||"Unable to retrieve system performance data"})]}):(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(er,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"font-semibold text-sm",children:"System Performance"})]}),(0,r.jsx)(j.E,{variant:"outline",className:"font-medium",children:"Real-time"})]}),(0,r.jsx)("div",{className:(0,b.cn)("grid gap-3",a?"grid-cols-1":"grid-cols-1 sm:grid-cols-2"),children:d.map(e=>{let s=e.icon,i=sj(e.value,e.threshold.warning,e.threshold.critical),l=sg(e.performanceImpact);return(0,r.jsx)(f.Zp,{className:(0,b.cn)("transition-all duration-200 hover:shadow-md",i),children:(0,r.jsxs)(f.Wu,{className:(0,b.cn)("p-4",a&&"p-3"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(s,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:(0,b.cn)("font-medium",a?"text-xs":"text-sm"),children:e.label})]}),(0,r.jsxs)(j.E,{variant:"outline",className:(0,b.cn)("text-xs",l),children:[e.performanceImpact," impact"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:(0,b.cn)("font-semibold",a?"text-sm":"text-lg"),children:[e.value.toFixed(1),e.unit]}),(0,r.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Warning: ",e.threshold.warning,e.unit]})]}),(0,r.jsx)(v.k,{value:Math.min(e.value,100),className:"h-2"}),!a&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),t&&e.recommendation&&!a&&(0,r.jsx)("div",{className:"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded",children:(0,r.jsxs)("p",{className:"text-xs text-yellow-800",children:["\uD83D\uDCA1 ",e.recommendation]})})]})]})},e.id)})}),s&&o.length>0&&!a&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(R.A,{className:"h-5 w-5"}),"Performance Trends"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)(Z,{config:m,className:"w-full",style:{height:"256px"},children:(0,r.jsx)(P.u,{width:"100%",height:"100%",children:(0,r.jsxs)(se,{data:o,children:[(0,r.jsx)(ew.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(ek.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1,domain:[0,100]}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{})}),(0,r.jsx)(e9,{type:"monotone",dataKey:"CPU",stroke:m.CPU.color,fill:m.CPU.color,fillOpacity:.3,strokeWidth:2})]})})})})]}),l&&!a&&(0,r.jsx)(f.Zp,{children:(0,r.jsx)(f.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 text-xs",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Total Memory:"}),(0,r.jsx)("p",{className:"font-medium",children:l.systemMetrics?.memory?.total?sf(l.systemMetrics.memory.total):"Unknown"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Used Memory:"}),(0,r.jsx)("p",{className:"font-medium",children:l.systemMetrics?.memory?.used?sf(l.systemMetrics.memory.used):"Unknown"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Active Connections:"}),(0,r.jsx)("p",{className:"font-medium",children:l.systemMetrics?.connections?.active||0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Load Average:"}),(0,r.jsx)("p",{className:"font-medium",children:l.systemMetrics?.cpu?.loadAverage?.[0]?.toFixed(2)||"Unknown"})]})]})})})]})},sy=()=>[{range:"0-100ms",count:45,percentage:45},{range:"100-200ms",count:30,percentage:30},{range:"200-500ms",count:15,percentage:15},{range:"500ms-1s",count:7,percentage:7},{range:"1s+",count:3,percentage:3}],sb=()=>{let e=[],s=new Date;for(let t=11;t>=0;t--){let r=new Date(s.getTime()-5*t*6e4),a=100+50*Math.random();e.push({time:r.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),requests:Math.round(a),responseTime:Math.round(150+100*Math.random()),timestamp:r.getTime()})}return e},sN=e=>{switch(e){case"excellent":return"text-green-600 bg-green-50 border-green-200";case"good":return"text-blue-600 bg-blue-50 border-blue-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"critical":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},sw=({className:e="",showCharts:s=!0,showEndpoints:t=!0,compact:a=!1})=>{let{data:l,isLoading:n,error:c}=(0,y._r)(),d=i().useMemo(()=>{if(!l?.values?.length)return{totalRequests:0,averageResponseTime:0,errorRate:0,throughput:0,slowestEndpoint:"N/A",fastestEndpoint:"N/A"};let e=l.values,s=e.reduce((e,s)=>e+s.value,0),t=e.reduce((e,s)=>e+1e3*s.value,0),r=e.filter(e=>parseInt(e.labels.statusCode)>=400).reduce((e,s)=>e+s.value,0),a=new Map;e.forEach(e=>{let s=e.labels.route||"unknown",t=1e3*e.value;a.has(s)||a.set(s,[]),a.get(s).push(t)});let i="N/A",n="N/A",c=0,d=1/0;return a.forEach((e,s)=>{let t=e.reduce((e,s)=>e+s,0)/e.length;t>c&&(c=t,i=s),t<d&&(d=t,n=s)}),{totalRequests:s,averageResponseTime:Math.round(s>0?t/s:0),errorRate:Math.round(10*(s>0?r/s*100:0))/10,throughput:Math.round(12*s),slowestEndpoint:i,fastestEndpoint:n}},[l]),o=i().useMemo(()=>{if(!l?.values?.length)return[];let e=new Map;return l.values.forEach(s=>{let t=s.labels.route||"unknown",r=s.labels.method||"GET",a=parseInt(s.labels.statusCode)||200,i=1e3*s.value;e.has(t)||e.set(t,{requests:0,methods:new Set,statusCodes:[],responseTimes:[]});let l=e.get(t);l.requests+=s.value,l.methods.add(r),l.statusCodes.push(a),l.responseTimes.push(i)}),Array.from(e.entries()).map(([e,s])=>{let t=s.statusCodes.filter(e=>e>=400).length,r=s.statusCodes.length>0?t/s.statusCodes.length*100:0,a=s.responseTimes.length>0?s.responseTimes.reduce((e,s)=>e+s,0)/s.responseTimes.length:0,i="excellent";return a>500||r>5?i="critical":a>300||r>2?i="warning":(a>200||r>1)&&(i="good"),{endpoint:e,method:Array.from(s.methods).join(", "),requests:s.requests,avgResponseTime:Math.round(a),errorRate:Math.round(10*r)/10,status:i}}).sort((e,s)=>s.requests-e.requests).slice(0,5)},[l]),m=i().useMemo(()=>{if(!l?.values?.length)return sy();let e=l.values.map(e=>1e3*e.value);return[{range:"0-100ms",min:0,max:100},{range:"100-200ms",min:100,max:200},{range:"200-500ms",min:200,max:500},{range:"500ms-1s",min:500,max:1e3},{range:"1s+",min:1e3,max:1/0}].map(({range:s,min:t,max:r})=>{let a=e.filter(e=>e>=t&&e<r).length,i=e.length>0?a/e.length*100:0;return{range:s,count:a,percentage:Math.round(i)}})},[l]),x=i().useMemo(()=>{if(!l?.values?.length)return sb();let e=new Date,s=[],t=Math.max(1,Math.floor(l.values.reduce((e,s)=>e+s.value,0)/12));for(let r=11;r>=0;r--){let a=new Date(e.getTime()-5*r*6e4),i=Math.max(1,Math.floor(t*(1+.3*Math.sin(r/12*Math.PI*2)))),n=l.values.length>0?Math.round(l.values.reduce((e,s)=>e+s.value,0)/l.values.length*1e3):150;s.push({time:a.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),requests:i,responseTime:n,timestamp:a.getTime()})}return s},[l]),h={requests:{label:"Requests",color:"#3b82f6"},responseTime:{label:"Response Time",color:"#10b981"},count:{label:"Count",color:"#8b5cf6"}};return n?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-40"}),(0,r.jsx)(ec.E,{className:"h-5 w-24"})]}),(0,r.jsx)("div",{className:"grid gap-4 grid-cols-2 sm:grid-cols-4",children:Array.from({length:4}).map((e,s)=>(0,r.jsx)(ec.E,{className:"h-16 w-full"},s))}),s&&(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 lg:grid-cols-2",children:[(0,r.jsx)(ec.E,{className:"h-64 w-full"}),(0,r.jsx)(ec.E,{className:"h-64 w-full"})]})]}):c?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(ea.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load HTTP metrics"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:c.message||"Unable to retrieve HTTP request data"})]}):(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(ea.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"font-semibold text-sm",children:"HTTP Request Metrics"})]}),(0,r.jsx)(j.E,{variant:"outline",className:"font-medium",children:"Live Data"})]}),(0,r.jsxs)("div",{className:"grid gap-3 grid-cols-2 sm:grid-cols-4",children:[(0,r.jsxs)(f.Zp,{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D.A,{className:"h-4 w-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"Total Requests"})]}),(0,r.jsx)("p",{className:"text-lg font-semibold mt-1",children:d.totalRequests.toLocaleString()})]}),(0,r.jsxs)(f.Zp,{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"Avg Response"})]}),(0,r.jsxs)("p",{className:"text-lg font-semibold mt-1",children:[d.averageResponseTime,"ms"]})]}),(0,r.jsxs)(f.Zp,{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(eu.A,{className:"h-4 w-4 text-purple-600"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"Throughput"})]}),(0,r.jsxs)("p",{className:"text-lg font-semibold mt-1",children:[d.throughput,"/min"]})]}),(0,r.jsxs)(f.Zp,{className:(0,b.cn)("p-3",d.errorRate>5?"bg-red-50 border-red-200":d.errorRate>2?"bg-yellow-50 border-yellow-200":""),children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-red-600"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"Error Rate"})]}),(0,r.jsxs)("p",{className:"text-lg font-semibold mt-1",children:[d.errorRate,"%"]})]})]}),s&&!a&&(0,r.jsxs)("div",{className:"grid gap-4 grid-cols-1 lg:grid-cols-2",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(R.A,{className:"h-5 w-5"}),"Request Volume"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)(Z,{config:h,className:"w-full",style:{height:"192px"},children:(0,r.jsx)(P.u,{width:"100%",height:"100%",children:(0,r.jsxs)(eN.b,{data:x,children:[(0,r.jsx)(ew.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(ek.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{})}),(0,r.jsx)(eC.N,{type:"monotone",dataKey:"requests",stroke:h.requests.color,strokeWidth:2,dot:!1})]})})})})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(D.A,{className:"h-5 w-5"}),"Response Time Distribution"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)(Z,{config:h,className:"w-full",style:{height:"192px"},children:(0,r.jsx)(P.u,{width:"100%",height:"100%",children:(0,r.jsxs)(eA.E,{data:m,children:[(0,r.jsx)(ew.W,{dataKey:"range",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(ek.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{})}),(0,r.jsx)(eS.y,{dataKey:"percentage",fill:h.count.color,radius:[4,4,0,0]})]})})})})]})]}),t&&o.length>0&&!a&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Top Endpoints"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:o.map((e,s)=>(0,r.jsxs)("div",{className:(0,b.cn)("flex items-center justify-between p-3 rounded-lg border",sN(e.status)),children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"font-mono text-sm font-medium",children:e.endpoint}),(0,r.jsx)(j.E,{variant:"outline",className:"text-xs",children:e.method})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-1 text-xs text-muted-foreground",children:[(0,r.jsxs)("span",{children:[e.requests," requests"]}),(0,r.jsxs)("span",{children:[e.avgResponseTime,"ms avg"]}),(0,r.jsxs)("span",{children:[e.errorRate,"% errors"]})]})]}),(0,r.jsx)(j.E,{variant:"outline",className:(0,b.cn)("ml-2","excellent"===e.status?"text-green-600":"good"===e.status?"text-blue-600":"warning"===e.status?"text-yellow-600":"text-red-600"),children:e.status})]},e.endpoint))})})]})]})};var sk=t(54608);let sC=(0,m.A)("Layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]),sA=e=>{let s=[];for(let t=11;t>=0;t--){let r=new Date(Date.now()-5*t*6e4),a=Math.max(0,Math.min(100,e+3*Math.sin(t/12*Math.PI*2))),i=100-a;s.push({hits:a,misses:i,time:r.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),timestamp:r.getTime()})}return s},sS=e=>[{color:"#10b981",name:"Cache Hits",value:e},{color:"#ef4444",name:"Cache Misses",value:100-e}],sM=e=>e>=90?"excellent":e>=75?"good":e>=50?"warning":"poor",sE=e=>{switch(e){case"excellent":return"text-green-600 bg-green-50 border-green-200";case"good":return"text-blue-600 bg-blue-50 border-blue-200";case"poor":return"text-red-600 bg-red-50 border-red-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},sR=e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return`${Number.parseFloat((e/Math.pow(1024,s)).toFixed(1))} ${["B","KB","MB","GB","TB"][s]}`},sD=({className:e="",compact:s=!1,showCharts:t=!0,showOptimizations:a=!0})=>{let{data:l,error:n,isLoading:c}=(0,y.BB)(),d=i().useMemo(()=>{if(!l?.deduplicationMetrics)return[];let e=l.deduplicationMetrics,s=[],t=e.hitRate||0,a=sM(t);s.push({description:"Percentage of requests served from cache",icon:({className:e})=>(0,r.jsx)(el.A,{className:e}),id:"hit-rate",label:"Cache Hit Rate",optimization:t<75?"Consider increasing cache TTL or improving cache keys":void 0,status:a,unit:"%",value:t});let i=Math.min(100,1.2*t);s.push({description:"Overall cache performance and optimization",icon:({className:e})=>(0,r.jsx)(eu.A,{className:e}),id:"efficiency",label:"Cache Efficiency",optimization:i<80?"Optimize cache strategy and key patterns":void 0,status:sM(i),unit:"%",value:i});let n=e.cacheHits&&e.totalRequests?e.cacheHits/e.totalRequests*100:0;s.push({description:"Memory saved through request deduplication",icon:({className:e})=>(0,r.jsx)(sk.A,{className:e}),id:"memory-savings",label:"Memory Savings",optimization:n<50?"Review cache size and eviction policies":void 0,status:n>60?"excellent":n>40?"good":n>20?"warning":"poor",unit:"%",value:n});let c=e.totalRequests>0?(e.totalRequests-e.cacheMisses)/e.totalRequests*100:0;return s.push({description:"Percentage of duplicate requests eliminated",icon:({className:e})=>(0,r.jsx)(sC,{className:e}),id:"dedup-rate",label:"Deduplication Rate",optimization:c<70?"Improve request fingerprinting algorithm":void 0,status:sM(c),unit:"%",value:c}),s},[l]),o=l?.deduplicationMetrics?.hitRate||0,m=sA(o),x=sS(o),h={hits:{color:"#10b981",label:"Cache Hits"},misses:{color:"#ef4444",label:"Cache Misses"}};return c?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(ec.E,{className:"h-6 w-40"}),(0,r.jsx)(ec.E,{className:"h-5 w-24"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,r.jsx)(ec.E,{className:"h-24 w-full"},s))}),t&&(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-2",children:[(0,r.jsx)(ec.E,{className:"h-64 w-full"}),(0,r.jsx)(ec.E,{className:"h-64 w-full"})]})]}):n?(0,r.jsxs)("div",{className:(0,b.cn)("text-center py-8",e),children:[(0,r.jsx)(el.A,{className:"mx-auto mb-4 size-12 text-red-500"}),(0,r.jsx)("p",{className:"text-sm font-medium text-red-600",children:"Failed to load deduplication metrics"}),(0,r.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:n.message||"Unable to retrieve cache performance data"})]}):(0,r.jsxs)("div",{className:(0,b.cn)("space-y-4",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(el.A,{className:"size-5 text-muted-foreground"}),(0,r.jsx)("h3",{className:"text-sm font-semibold",children:"Cache & Deduplication"})]}),(0,r.jsxs)(j.E,{className:(0,b.cn)("font-medium",sE(sM(o))),variant:"outline",children:[o.toFixed(1),"% Hit Rate"]})]}),(0,r.jsx)("div",{className:(0,b.cn)("grid gap-3",s?"grid-cols-1":"grid-cols-1 sm:grid-cols-2"),children:d.map(e=>{let t=e.icon,i=sE(e.status);return(0,r.jsx)(f.Zp,{className:(0,b.cn)("transition-all duration-200 hover:shadow-md",i),children:(0,r.jsxs)(f.Wu,{className:(0,b.cn)("p-4",s&&"p-3"),children:[(0,r.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(t,{className:"size-4"}),(0,r.jsx)("span",{className:(0,b.cn)("font-medium",s?"text-xs":"text-sm"),children:e.label})]}),(0,r.jsx)(j.E,{className:(0,b.cn)("text-xs","excellent"===e.status?"text-green-600":"good"===e.status?"text-blue-600":"warning"===e.status?"text-yellow-600":"text-red-600"),variant:"outline",children:e.status})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("span",{className:(0,b.cn)("font-semibold",s?"text-sm":"text-lg"),children:[e.value.toFixed(1),e.unit]})}),(0,r.jsx)(v.k,{className:"h-2",value:Math.min(e.value,100)}),!s&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),a&&e.optimization&&!s&&(0,r.jsx)("div",{className:"mt-2 rounded border border-blue-200 bg-blue-50 p-2",children:(0,r.jsxs)("p",{className:"text-xs text-blue-800",children:["\uD83D\uDCA1 ",e.optimization]})})]})]})},e.id)})}),t&&!s&&(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-2",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(R.A,{className:"size-5"}),"Cache Performance Trends"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)(Z,{className:"h-48",config:h,children:(0,r.jsx)(P.u,{height:"100%",width:"100%",children:(0,r.jsxs)(se,{data:m,children:[(0,r.jsx)(ew.W,{axisLine:!1,dataKey:"time",tick:{fontSize:12},tickLine:!1}),(0,r.jsx)(ek.h,{axisLine:!1,domain:[0,100],tick:{fontSize:12},tickLine:!1}),(0,r.jsx)(I,{content:(0,r.jsx)(U,{})}),(0,r.jsx)(e9,{dataKey:"hits",fill:h.hits.color,fillOpacity:.6,stackId:"1",stroke:h.hits.color,type:"monotone"}),(0,r.jsx)(e9,{dataKey:"misses",fill:h.misses.color,fillOpacity:.6,stackId:"1",stroke:h.misses.color,type:"monotone"})]})})})})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(T.A,{className:"size-5"}),"Cache Distribution"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)(Z,{className:"h-48",config:h,children:(0,r.jsx)(P.u,{height:"100%",width:"100%",children:(0,r.jsxs)(L.r,{children:[(0,r.jsx)(B.F,{cx:"50%",cy:"50%",data:x,dataKey:"value",innerRadius:40,outerRadius:80,paddingAngle:5,children:x.map((e,s)=>(0,r.jsx)(F.f,{fill:e.color},`cell-${s}`))}),(0,r.jsx)(I,{content:({active:e,payload:s})=>{if(e&&s?.length&&s[0]?.payload){let e=s[0].payload;return(0,r.jsxs)("div",{className:"rounded border bg-white p-2 shadow",children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.value.toFixed(1),"%"]})]})}return null}})]})})})})]})]}),l&&!s&&(0,r.jsx)(f.Zp,{children:(0,r.jsx)(f.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs sm:grid-cols-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Total Requests:"}),(0,r.jsx)("p",{className:"font-medium",children:(l.deduplicationMetrics?.totalRequests||0).toLocaleString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Cache Hits:"}),(0,r.jsx)("p",{className:"font-medium text-green-600",children:Math.round((l.deduplicationMetrics?.totalRequests||0)*o/100).toLocaleString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Cache Misses:"}),(0,r.jsx)("p",{className:"font-medium text-red-600",children:Math.round((l.deduplicationMetrics?.totalRequests||0)*(100-o)/100).toLocaleString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Memory Saved:"}),(0,r.jsx)("p",{className:"font-medium text-blue-600",children:sR((l.deduplicationMetrics?.totalRequests||0)*o*1048.576)})]})]})})})]})},sT=({className:e="",maxAlerts:s=10})=>{let{data:t,isLoading:a,error:i}=(0,y.Zz)(),l=e=>{switch(e){case"critical":return{icon:es.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",variant:"destructive"};case"high":return{icon:h.A,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-100 dark:bg-orange-900/20",variant:"destructive"};case"medium":return{icon:h.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",variant:"secondary"};case"low":return{icon:u.A,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20",variant:"outline"}}},n=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/6e4);return t<1?"Just now":t<60?`${t}m ago`:t<1440?`${Math.floor(t/60)}h ago`:s.toLocaleDateString()};if(a)return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Active Alerts"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"h-16 bg-muted rounded"})},s))})})]});if(i)return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Active Alerts"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"text-center py-4",children:[(0,r.jsx)(h.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load alerts"})]})})]});let c=t?.slice(0,s)||[],d=t?.filter(e=>"critical"===e.severity).length||0;return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Active Alerts"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(j.E,{variant:d>0?"destructive":"default",children:[t?.length||0," total"]}),d>0&&(0,r.jsxs)(j.E,{variant:"destructive",children:[d," critical"]})]})]})}),(0,r.jsx)(f.Wu,{children:0===c.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(p.A,{className:"mx-auto h-8 w-8 text-green-600 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"No active alerts"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"All systems operating normally"})]}):(0,r.jsxs)("div",{className:"space-y-3",children:[c.map(e=>{let s=l(e.severity),t=s.icon;return(0,r.jsxs)("div",{className:(0,b.cn)("flex items-start gap-3 p-3 rounded-lg border",s.bgColor),children:[(0,r.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,r.jsx)(t,{className:(0,b.cn)("h-4 w-4",s.color)})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.message}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[e.type," • ",e.source]})]}),(0,r.jsx)(j.E,{variant:s.variant,className:"flex-shrink-0",children:e.severity})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:n(e.timestamp)}),(0,r.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Status: ",e.status]})]})]})]},e.id)}),t&&t.length>s&&(0,r.jsx)("div",{className:"text-center pt-2",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["+",t.length-s," more alerts"]})})]})})]})},sP=({className:e=""})=>{let{data:s,isLoading:t,error:a}=(0,y.TR)(),i=e=>{switch(e){case"critical":return"bg-red-500";case"high":return"bg-orange-500";case"medium":return"bg-yellow-500";case"low":return"bg-blue-500";default:return"bg-gray-500"}};if(t)return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(D.A,{className:"h-5 w-5"}),"Alert Statistics"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded mb-2"}),(0,r.jsx)("div",{className:"h-2 bg-muted rounded"})]},s))})})]});if(a||!s)return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(D.A,{className:"h-5 w-5"}),"Alert Statistics"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"text-center py-4",children:[(0,r.jsx)(D.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load statistics"})]})})]});let{total:l,active:n,resolved:c,bySeverity:d,averageResolutionTime:o,recentTrends:m}=s,x=l>0?Math.round(c/l*100):0;return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(D.A,{className:"h-5 w-5"}),"Alert Statistics"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:l}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total Alerts"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:c}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Resolved"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:n}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[x,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Resolution Rate"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Resolution Rate"}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[x,"%"]})]}),(0,r.jsx)(v.k,{value:x,className:"h-2"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-3",children:"By Severity"}),(0,r.jsx)("div",{className:"space-y-2",children:Object.entries(d).map(([e,s])=>{let t=l>0?Math.round(s/l*100):0;return(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:(0,b.cn)("w-3 h-3 rounded-full",i(e))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm capitalize",children:e}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[s," (",t,"%)"]})]}),(0,r.jsx)(v.k,{value:t,className:"h-1 mt-1"})]})]},e)})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Avg. Resolution Time"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:(e=>e<60?`${Math.round(e)}m`:e<1440?`${Math.round(e/60)}h`:`${Math.round(e/1440)}d`)(o)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Recent Trends"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Last 24 Hours"}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:m.last24Hours}),m.last24Hours>m.last7Days/7?(0,r.jsx)(R.A,{className:"h-3 w-3 text-red-500"}):(0,r.jsx)(g.A,{className:"h-3 w-3 text-green-500"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Last 7 Days"}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:m.last7Days}),m.last7Days>m.last30Days/4?(0,r.jsx)(R.A,{className:"h-3 w-3 text-red-500"}):(0,r.jsx)(g.A,{className:"h-3 w-3 text-green-500"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Last 30 Days"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:m.last30Days})]})]})]})]})})]})},sL=({className:e=""})=>{let{data:s,error:t,isLoading:a}=(0,y.vW)(),i=e=>{switch(e){case"degraded":return{bgColor:"bg-yellow-100 dark:bg-yellow-900/20",color:"text-yellow-600 dark:text-yellow-400",icon:h.A,variant:"secondary"};case"healthy":return{bgColor:"bg-green-100 dark:bg-green-900/20",color:"text-green-600 dark:text-green-400",icon:p.A,variant:"default"};case"unhealthy":return{bgColor:"bg-red-100 dark:bg-red-900/20",color:"text-red-600 dark:text-red-400",icon:es.A,variant:"destructive"};default:return{bgColor:"bg-gray-100 dark:bg-gray-900/20",color:"text-gray-600 dark:text-gray-400",icon:u.A,variant:"outline"}}},l=e=>{let s=e.toLowerCase();return s.includes("database")||s.includes("db")?el.A:s.includes("api")||s.includes("service")?er:s.includes("network")||s.includes("connection")?eh.A:ea.A};if(a)return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(ea.A,{className:"size-5"}),"Dependency Health"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"h-16 rounded bg-muted"})},s))})})]});if(t||!s)return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(ea.A,{className:"size-5"}),"Dependency Health"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"py-4 text-center",children:[(0,r.jsx)(h.A,{className:"mx-auto mb-2 size-8 text-muted-foreground"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load dependency health"})]})})]});let n=(()=>{if(!s?.summary)return 0;let{healthy:e,total:t}=s.summary;return t>0?Math.round(e/t*100):100})(),c=(()=>{if(!s?.summary)return"unhealthy";let{degraded:e,healthy:t,unhealthy:r}=s.summary;return r>0?"unhealthy":e>0?"degraded":"healthy"})(),d=i(c);return(0,r.jsxs)(f.Zp,{className:e,children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(ea.A,{className:"size-5"}),"Dependency Health"]}),(0,r.jsxs)(j.E,{variant:d.variant,children:[n,"%"]})]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Overall Health"}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[n,"%"]})]}),(0,r.jsx)(v.k,{className:"h-2",value:n})]}),(0,r.jsx)("div",{className:"space-y-3",children:(s.dependencies||[]).map(e=>{let s=i(e.status),t=s.icon,a=l(e.name);return(0,r.jsxs)("div",{className:(0,b.cn)("flex items-center justify-between p-3 rounded-lg border",s.bgColor),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(a,{className:"size-4 text-muted-foreground"}),(0,r.jsx)(t,{className:(0,b.cn)("h-4 w-4",s.color)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium capitalize",children:e.name.replaceAll(/([A-Z])/g," $1").trim()}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"healthy"===e.status?"Operating normally":"degraded"===e.status?"Performance issues":"Service unavailable"})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)(j.E,{variant:s.variant,children:e.status}),e.responseTime&&(0,r.jsxs)("p",{className:"mt-1 text-xs text-muted-foreground",children:[e.responseTime,"ms"]})]})]},e.name)})}),(0,r.jsxs)("div",{className:(0,b.cn)("p-3 rounded-lg",d.bgColor),children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.icon,{className:(0,b.cn)("h-4 w-4",d.color)}),(0,r.jsx)("span",{className:"font-medium",children:"healthy"===c?"All dependencies healthy":"degraded"===c?"Some dependencies degraded":"Critical dependencies failing"})]}),(0,r.jsxs)("p",{className:"mt-1 text-sm text-muted-foreground",children:["Last checked: ",new Date().toLocaleTimeString()]})]})]})})]})};var sB=t(95594),sF=t(77618);let sO=({widgetId:e,title:s,description:t})=>(0,r.jsx)("div",{className:"flex h-64 items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/10",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"font-medium text-muted-foreground",children:s}),(0,r.jsx)("p",{className:"mt-1 text-sm text-muted-foreground/75",children:t}),(0,r.jsxs)("p",{className:"mt-2 text-xs text-muted-foreground/50",children:["Widget ID: ",e]})]})}),s$=({className:e=""})=>{let s=(0,sF.c3)("reliability.widgets"),t=(0,sB.XD)(e=>e.preferences.dashboardLayout.layout),a=(0,sB.XD)(e=>e.preferences.dashboardLayout.gridColumns),i=(0,sB.XD)(e=>e.getVisibleWidgets),d=(0,sB.XD)(e=>e.ui.activeTab),o=(0,sB.XD)(e=>e.toggleWidget),m=i(),x={"system-health":{title:s("systemHealth"),description:s("systemHealthDesc"),component:en},"health-status-indicators":{title:s("healthStatusIndicators"),description:s("healthStatusDesc"),component:ex},"dependency-status":{title:s("dependencyStatus"),description:s("dependencyStatusDesc"),component:eb},"health-trends":{title:s("healthTrends"),description:s("healthTrendsDesc"),component:sr},"system-resources":{title:s("systemResources"),description:s("systemResourcesDesc"),component:sd},"performance-overview":{title:s("performanceOverview"),description:s("performanceOverviewDesc"),component:su},"system-metrics":{title:s("systemMetrics"),description:s("systemMetricsDesc"),component:sv},"http-metrics":{title:s("httpMetrics"),description:s("httpMetricsDesc"),component:sw},"deduplication-metrics":{title:s("deduplicationMetrics"),description:s("deduplicationMetricsDesc"),component:sD},"performance-metrics":{title:"Performance Metrics",description:"System performance monitoring and analysis",component:sv},"circuit-breakers":{title:"Circuit Breakers Overview",description:"Circuit breaker status and failure protection",component:N},"circuit-breaker-list":{title:"Circuit Breaker Details",description:"Detailed circuit breaker list with status",component:E},"circuit-breaker-metrics":{title:"Circuit Breaker Metrics",description:"Performance metrics and failure rate charts",component:V},"circuit-breaker-history":{title:"Circuit Breaker History",description:"Historical state changes and recovery patterns",component:J},"circuit-breaker-alerts":{title:"Circuit Breaker Alerts",description:"Active alerts related to circuit breaker failures",component:ee},"active-alerts":{title:"Active Alerts",description:"Current system alerts and notifications",component:sT},"alert-statistics":{title:"Alert Statistics",description:"Alert trends and statistical analysis",component:sP},"dependency-health":{title:"Dependency Health",description:"External service and dependency status",component:sL}},h=(()=>{switch(d){case"health":return m.filter(e=>["system-health","dependency-health","circuit-breakers","circuit-breaker-list"].includes(e));case"metrics":return m.filter(e=>["performance-overview","system-metrics","http-metrics","deduplication-metrics","performance-metrics","circuit-breaker-metrics"].includes(e));case"alerts":return m.filter(e=>["active-alerts","alert-statistics","circuit-breaker-alerts"].includes(e));case"history":return m.filter(e=>["alert-statistics","performance-metrics","circuit-breaker-history"].includes(e));default:return m}})(),u=e=>{let s=x[e];if(!s)return(0,r.jsx)(sO,{widgetId:e,title:"Unknown Widget",description:"Widget configuration not found"});if(s.component){let e=s.component;return(0,r.jsx)(e,{})}return(0,r.jsx)(sO,{widgetId:e,title:s.title,description:s.description})};return 0===h.length?(0,r.jsxs)("div",{className:(0,b.cn)("space-y-6",e),children:[(0,r.jsx)(n.Fc,{children:(0,r.jsxs)(n.TN,{children:["No widgets are currently visible for the ",d," tab. You can enable widgets in the dashboard settings."]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(c.$,{variant:"outline",onClick:()=>{(({overview:["system-health","health-status-indicators","circuit-breakers","active-alerts"],health:["health-status-indicators","dependency-status","system-resources","health-trends"],metrics:["performance-overview","system-metrics","http-metrics","deduplication-metrics","performance-metrics","circuit-breaker-metrics"],alerts:["active-alerts","circuit-breaker-alerts","alert-statistics"],history:["health-trends","circuit-breaker-history","alert-statistics"]})[d]||["system-health"]).forEach(e=>o(e))},children:"Enable Default Widgets"})})]}):(0,r.jsx)("div",{className:(0,b.cn)((()=>{if("list"===t)return"grid grid-cols-1 gap-6";if("compact"===t)return"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";let e={1:"grid-cols-1",2:"grid-cols-1 lg:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",5:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",6:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"};return`grid gap-6 ${e[a]||e[3]}`})(),e),role:"grid","aria-label":`Dashboard widgets in ${t} layout`,children:h.map(e=>(0,r.jsx)(l.WidgetContainer,{widgetId:e,title:x[e]?.title||e,className:"min-h-[200px]",children:u(e)},e))})}},30612:(e,s,t)=>{"use strict";t.d(s,{DashboardGrid:()=>a});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardGrid() from the server but DashboardGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\DashboardGrid.tsx","DashboardGrid");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\components\\\\reliability\\\\dashboard\\\\DashboardGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\DashboardGrid.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35232:(e,s,t)=>{Promise.resolve().then(t.bind(t,55387)),Promise.resolve().then(t.bind(t,34570)),Promise.resolve().then(t.bind(t,10963)),Promise.resolve().then(t.bind(t,30594)),Promise.resolve().then(t.bind(t,90445)),Promise.resolve().then(t.bind(t,19060)),Promise.resolve().then(t.bind(t,15975)),Promise.resolve().then(t.bind(t,61278)),Promise.resolve().then(t.bind(t,12662)),Promise.resolve().then(t.bind(t,63213))},37301:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>a});var r=t(37413);t(61120);let a={title:{template:"%s | Reliability | WorkHub",default:"Reliability | WorkHub"},description:"System reliability monitoring and management for WorkHub"};function i({children:e}){return(0,r.jsx)("div",{className:"min-h-screen bg-background",children:(0,r.jsx)("main",{className:"flex-1",children:e})})}},48897:(e,s,t)=>{"use strict";t.d(s,{ConnectionStatusIndicator:()=>a});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ConnectionStatusIndicator() from the server but ConnectionStatusIndicator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\ConnectionStatusIndicator.tsx","ConnectionStatusIndicator");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\components\\\\reliability\\\\dashboard\\\\ConnectionStatusIndicator.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\ConnectionStatusIndicator.tsx","default")},49278:(e,s,t)=>{"use strict";t.d(s,{G7:()=>x,Gb:()=>c,JP:()=>d,Ok:()=>o,Qu:()=>m,iw:()=>n,oz:()=>u,z0:()=>h});var r=t(3389);class a{show(e){return(0,r.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,s){return this.show({title:e,description:s,variant:"default"})}error(e,s){return this.show({title:e,description:s,variant:"destructive"})}info(e,s){return this.show({title:e,description:s,variant:"default"})}}class i extends a{constructor(e){super(),this.config=e}entityCreated(e){let s=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(s))}entityUpdated(e){let s=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(s))}entityDeleted(e){let s=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(s))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class l extends a{serviceRecordCreated(e,s){return this.success("Service Record Added",`${s} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,s){return this.success("Service Record Updated",`${s} service for "${e}" has been updated.`)}serviceRecordDeleted(e,s){return this.success("Service Record Deleted",`${s} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new i(e)}function c(e,s){return new i({entityName:e,getDisplayName:s,messages:{created:{title:`${e} Created`,description:s=>`The ${e.toLowerCase()} "${s}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:s=>s||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:s=>s||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:s=>s||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let d=new a,o=new i({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),m=new i({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),x=new i({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new i({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),u=new l},50812:(e,s,t)=>{"use strict";t.d(s,{C:()=>d,z:()=>c});var r=t(60687),a=t(14555),i=t(73256),l=t(43210),n=t(22482);let c=l.forwardRef(({className:e,...s},t)=>(0,r.jsx)(a.bL,{className:(0,n.cn)("grid gap-2",e),...s,ref:t}));c.displayName=a.bL.displayName;let d=l.forwardRef(({className:e,...s},t)=>(0,r.jsx)(a.q7,{className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s,children:(0,r.jsx)(a.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"size-2.5 fill-current text-current"})})}));d.displayName=a.q7.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61278:(e,s,t)=>{"use strict";t.d(s,{WidgetContainer:()=>j});var r=t(60687),a=t(77368);let i=(0,t(82614).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var l=t(89743),n=t(61662),c=t(11003),d=t(43210),o=t(68752),m=t(29523),x=t(44493),h=t(21342),u=t(76242),p=t(95594),g=t(22482);let j=({widgetId:e,title:s,subtitle:t,children:j,className:f="",refreshable:v=!1,onRefresh:y,isLoading:b=!1,error:N=null,actions:w})=>{let k=(0,p.XD)(e=>e.preferences.dashboardLayout.expandedWidgets),C=(0,p.XD)(e=>e.setWidgetExpanded),A=(0,p.XD)(e=>e.toggleWidget),[S,M]=(0,d.useState)(!1),E=k.has(e),R=async()=>{if(y&&!S){M(!0);try{await y()}catch(s){console.error(`Failed to refresh widget ${e}:`,s)}finally{M(!1)}}};return(0,r.jsx)(u.Bc,{children:(0,r.jsxs)(x.Zp,{className:(0,g.cn)("shadow-md transition-all duration-200",E?"row-span-2":"",N?"border-destructive/50":"",f),role:"region","aria-labelledby":`widget-title-${e}`,"aria-expanded":E,children:[(0,r.jsxs)(x.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(x.ZB,{id:`widget-title-${e}`,className:"text-base font-semibold",children:s}),t&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[w,v&&(0,r.jsxs)(u.m_,{children:[(0,r.jsx)(u.k$,{asChild:!0,children:(0,r.jsx)(m.$,{variant:"ghost",size:"sm",onClick:R,disabled:S||b,"aria-label":`Refresh ${s}`,children:(0,r.jsx)(a.A,{className:(0,g.cn)("h-4 w-4",(S||b)&&"animate-spin")})})}),(0,r.jsx)(u.ZI,{children:"Refresh widget data"})]}),(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsx)(m.$,{variant:"ghost",size:"sm","aria-label":`${s} widget options`,children:(0,r.jsx)(i,{className:"h-4 w-4"})})}),(0,r.jsxs)(h.SQ,{align:"end",className:"w-48",children:[(0,r.jsx)(h._2,{onClick:()=>{C(e,!E)},children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Collapse Widget"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Expand Widget"]})}),v&&(0,r.jsxs)(h._2,{onClick:R,disabled:S||b,children:[(0,r.jsx)(a.A,{className:"mr-2 h-4 w-4"}),"Refresh Data"]}),(0,r.jsx)(h.mB,{}),(0,r.jsxs)(h._2,{onClick:()=>{A(e)},className:"text-destructive focus:text-destructive",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Hide Widget"]})]})]})]})]}),(0,r.jsx)(x.Wu,{className:"pt-0",children:N?(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-destructive font-medium",children:"Failed to load widget data"}),(0,r.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:N}),v&&(0,r.jsx)(o.r,{actionType:"secondary",size:"sm",className:"mt-3",onClick:R,isLoading:S,children:"Retry"})]})}):(0,r.jsx)("div",{className:(0,g.cn)("transition-all duration-200",E?"min-h-[400px]":"min-h-[200px]"),children:j})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68752:(e,s,t)=>{"use strict";t.d(s,{r:()=>d});var r=t(60687),a=t(11516),i=t(43210),l=t.n(i),n=t(29523),c=t(22482);let d=l().forwardRef(({actionType:e="primary",asChild:s=!1,children:t,className:i,disabled:l,icon:d,isLoading:o=!1,loadingText:m,...x},h)=>{let{className:u,variant:p}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,r.jsx)(n.$,{asChild:s,className:(0,c.cn)(u,i),disabled:o||l,ref:h,variant:p,...x,children:o?(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,r.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}),m||t]}):(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",d&&(0,r.jsx)("span",{className:"mr-2",children:d}),t]})})});d.displayName="ActionButton"},70390:(e,s,t)=>{"use strict";t.d(s,{ReliabilityDashboard:()=>a});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ReliabilityDashboard() from the server but ReliabilityDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\ReliabilityDashboard.tsx","ReliabilityDashboard");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\components\\\\reliability\\\\dashboard\\\\ReliabilityDashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\ReliabilityDashboard.tsx","default")},72756:(e,s,t)=>{"use strict";t.d(s,{ProtectedRoute:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\auth\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},76242:(e,s,t)=>{"use strict";t.d(s,{Bc:()=>n,ZI:()=>o,k$:()=>d,m_:()=>c});var r=t(60687),a=t(9989),i=t(43210),l=t(22482);let n=a.Kq,c=a.bL,d=a.l9,o=i.forwardRef(({className:e,sideOffset:s=4,...t},i)=>(0,r.jsx)(a.UC,{className:(0,l.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),ref:i,sideOffset:s,...t}));o.displayName=a.UC.displayName},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79649:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["[locale]",{children:["reliability",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80724)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\reliability\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,37301)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\reliability\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\reliability\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/reliability/page",pathname:"/[locale]/reliability",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79986:(e,s,t)=>{"use strict";t.d(s,{BB:()=>o,Gz:()=>m,Hs:()=>d,Rt:()=>n,TR:()=>u,Zz:()=>h,_r:()=>x,n8:()=>l,u_:()=>p,vW:()=>c}),t(87676);var r=t(46349),a=t(49603);let i={all:["reliability"],health:()=>[...i.all,"health"],healthDetailed:()=>[...i.health(),"detailed"],healthDependencies:()=>[...i.health(),"dependencies"],healthTrends:()=>[...i.health(),"trends"],circuitBreakers:()=>[...i.all,"circuit-breakers"],circuitBreakerHistory:()=>[...i.circuitBreakers(),"history"],httpRequestMetrics:()=>[...i.metrics(),"http-requests"],metrics:()=>[...i.all,"metrics"],deduplication:()=>[...i.metrics(),"deduplication"],alerts:()=>[...i.all,"alerts"],alertsHistory:(e,s)=>[...i.alerts(),"history",e,s],alertsStatistics:()=>[...i.alerts(),"statistics"]},l=e=>(0,r.ol)([...i.health()],()=>a.reliabilityApiService.getSystemHealth(),"health",e),n=e=>(0,r.ol)([...i.healthDetailed()],()=>a.reliabilityApiService.getDetailedHealth(),"health",e),c=e=>(0,r.ol)([...i.healthDependencies()],()=>a.reliabilityApiService.getDependencyHealth(),"health",e),d=e=>(0,r.ol)([...i.circuitBreakers()],()=>a.reliabilityApiService.getCircuitBreakerStatus(),"circuit-breakers",e),o=e=>(0,r.ol)([...i.metrics()],()=>a.reliabilityApiService.getMetrics(),"metrics",e),m=(e="24h",s)=>(0,r.ol)([...i.healthTrends(),e],()=>a.reliabilityApiService.getHealthTrends(e),"health",{...s,staleTime:3e5,gcTime:6e5}),x=e=>(0,r.ol)([...i.httpRequestMetrics()],()=>a.reliabilityApiService.getHttpRequestMetrics(),"metrics",{...e,staleTime:6e4,gcTime:18e4}),h=e=>(0,r.ol)([...i.alerts()],()=>a.reliabilityApiService.getActiveAlerts(),"alerts",e),u=e=>(0,r.ol)([...i.alertsStatistics()],()=>a.reliabilityApiService.getAlertStatistics(),"alerts",e),p=e=>(0,r.ol)([...i.all,"dashboard"],()=>a.reliabilityApiService.getReliabilityDashboardData(),"metrics",{staleTime:3e4,...e})},80724:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>n});var r=t(37413);t(61120),t(29131),t(20508);var a=t(72756),i=t(70390);t(27491),t(30612),t(89889),t(48897),t(3707);var l=t(87432);let n={description:"Real-time system monitoring and reliability insights for WorkHub",keywords:["reliability","monitoring","dashboard","system health","alerts"],title:"Reliability Dashboard | WorkHub"};function c(){return(0,r.jsx)(a.ProtectedRoute,{children:(0,r.jsxs)("div",{className:"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8",children:[(0,r.jsx)(l.AppBreadcrumb,{}),(0,r.jsx)(i.ReliabilityDashboard,{})]})})}},81630:e=>{"use strict";e.exports=require("http")},81801:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var r=t(60687),a=t(22482);function i({className:e,...s}){return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...s})}},89889:(e,s,t)=>{"use strict";t.d(s,{WidgetContainer:()=>a});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call WidgetContainer() from the server but WidgetContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\WidgetContainer.tsx","WidgetContainer");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\components\\\\reliability\\\\dashboard\\\\WidgetContainer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\reliability\\dashboard\\WidgetContainer.tsx","default")},90445:(e,s,t)=>{"use strict";t.d(s,{DashboardHeader:()=>A});var r=t(60687),a=t(16032),i=t(19422),l=t(58369),n=t(53597),c=t(27629),d=t(24026),o=t(27805),m=t(14975),x=t(44610),h=t(43210),u=t(77618),p=t(68752),g=t(96834),j=t(29523),f=t(63503),v=t(21342),y=t(85763),b=t(76242),N=t(95594),w=t(79986),k=t(10963),C=t(19060);let A=({className:e=""})=>{let s=(0,u.c3)("reliability"),[t,A]=(0,h.useState)(!1),S=(0,N.XD)(e=>e.ui.activeTab),M=(0,N.XD)(e=>e.setActiveTab),E=(0,N.XD)(e=>e.monitoring.isEnabled),R=(0,N.XD)(e=>e.setMonitoringEnabled),D=(0,N.XD)(e=>e.toggleFilterPanel),T=(0,N.XD)(e=>e.ui.isFilterPanelOpen),{data:P}=(0,w.Zz)(),{data:L}=(0,w.TR)(),B=P?.filter(e=>"active"===e.status).length||0,F=P?.filter(e=>"active"===e.status&&"critical"===e.severity).length||0;return(0,r.jsx)(b.Bc,{children:(0,r.jsxs)("header",{"aria-label":"Reliability dashboard header",className:`space-y-2 ${e}`,role:"banner",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-primary",children:s("dashboard")}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:s("systemMonitoring")})]}),(0,r.jsx)(k.ConnectionStatusIndicator,{})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(b.m_,{children:[(0,r.jsx)(b.k$,{asChild:!0,children:(0,r.jsx)(p.r,{actionType:E?"secondary":"primary","aria-label":E?"Pause monitoring":"Resume monitoring",icon:E?(0,r.jsx)(a.A,{className:"size-4"}):(0,r.jsx)(i.A,{className:"size-4"}),onClick:()=>{R(!E)},size:"sm",children:E?"Pause":"Resume"})}),(0,r.jsx)(b.ZI,{children:E?"Pause real-time monitoring":"Resume real-time monitoring"})]}),(0,r.jsxs)(v.rI,{children:[(0,r.jsx)(v.ty,{asChild:!0,children:(0,r.jsxs)(j.$,{size:"sm",variant:"outline",children:[(0,r.jsx)(l.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Dashboard settings"})]})}),(0,r.jsxs)(v.SQ,{align:"end",className:"w-48",children:[(0,r.jsxs)(v._2,{onClick:()=>{A(!0)},children:[(0,r.jsx)(l.A,{className:"mr-2 size-4"}),"Dashboard Settings"]}),(0,r.jsxs)(v._2,{onClick:D,children:[(0,r.jsx)(n.A,{className:"mr-2 size-4"}),T?"Hide Filters":"Show Filters"]}),(0,r.jsx)(v.mB,{}),(0,r.jsxs)(v._2,{children:[(0,r.jsx)(c.A,{className:"mr-2 size-4"}),"Export Data"]})]})]})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto border-b",children:(0,r.jsx)(y.tU,{className:"w-full",onValueChange:e=>M(e),value:S,children:(0,r.jsxs)(y.j7,{className:"inline-flex w-full min-w-fit lg:grid lg:w-full lg:grid-cols-5",children:[(0,r.jsxs)(y.Xi,{"aria-label":"Overview dashboard",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"overview",children:[(0,r.jsx)(d.A,{className:"size-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Overview"})]}),(0,r.jsxs)(y.Xi,{"aria-label":"System health monitoring",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"health",children:[(0,r.jsx)(o.A,{className:"size-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Health"})]}),(0,r.jsxs)(y.Xi,{"aria-label":"Performance metrics",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"metrics",children:[(0,r.jsx)(c.A,{className:"size-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Metrics"})]}),(0,r.jsxs)(y.Xi,{"aria-label":"Active alerts and notifications",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"alerts",children:[(0,r.jsx)(m.A,{className:"size-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Alerts"}),B>0&&(0,r.jsx)(g.E,{className:"ml-1 h-5 min-w-[20px] shrink-0 text-xs",variant:F>0?"destructive":"secondary",children:B})]}),(0,r.jsxs)(y.Xi,{"aria-label":"Historical data and trends",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"history",children:[(0,r.jsx)(x.A,{className:"size-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"History"})]})]})})}),B>0&&(0,r.jsx)("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-950",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"size-4 text-orange-600 dark:text-orange-400"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-orange-800 dark:text-orange-200",children:[B," active alert",1===B?"":"s",F>0&&(0,r.jsxs)("span",{className:"ml-1 text-red-600 dark:text-red-400",children:["(",F," critical)"]})]})]}),(0,r.jsx)(j.$,{className:"text-orange-700 hover:text-orange-800 dark:text-orange-300 dark:hover:text-orange-200",onClick:()=>M("alerts"),size:"sm",variant:"outline",children:"View Alerts"})]})}),(0,r.jsx)(f.lG,{onOpenChange:A,open:t,children:(0,r.jsxs)(f.Cf,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[(0,r.jsx)(f.c7,{children:(0,r.jsx)(f.L3,{children:"Dashboard Settings"})}),(0,r.jsx)(C.DashboardSettings,{})]})})]})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,4825,625,9851,8390,2670,9275,9211,6805,6006,2482,9794,6413,3439,6906],()=>t(79649));module.exports=r})();