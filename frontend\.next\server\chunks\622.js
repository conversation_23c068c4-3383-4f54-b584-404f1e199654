"use strict";exports.id=622,exports.ids=[622],exports.modules={3940:(e,t,r)=>{r.d(t,{O_:()=>a,t6:()=>n});var s=r(43210),i=r(49278);function n(){let e=(0,s.useCallback)((e,t)=>i.JP.success(e,t),[]),t=(0,s.useCallback)((e,t)=>i.JP.error(e,t),[]),r=(0,s.useCallback)((e,t)=>i.JP.info(e,t),[]),n=(0,s.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),a=(0,s.useCallback)((e,r)=>{let s=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||s||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:n,showFormError:a}}function a(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:a}=n(),o=t||(e?(0,i.iw)(e):null),c=(0,s.useCallback)(e=>o?o.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[o,r]),d=(0,s.useCallback)(e=>o?o.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[o,r]),l=(0,s.useCallback)(e=>o?o.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[o,r]),u=(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityCreationError(t)}return a(e,{errorTitle:"Creation Failed"})},[o,a]);return{showEntityCreated:c,showEntityUpdated:d,showEntityDeleted:l,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityUpdateError(t)}return a(e,{errorTitle:"Update Failed"})},[o,a]),showEntityDeletionError:(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityDeletionError(t)}return a(e,{errorTitle:"Deletion Failed"})},[o,a]),showFormSuccess:r,showFormError:a}}(void 0,t)}},12662:(e,t,r)=>{r.d(t,{AppBreadcrumb:()=>u});var s=r(60687),i=r(85814),n=r.n(i),a=r(16189),o=r(43210),c=r.n(o),d=r(70640),l=r(22482);function u({className:e,homeHref:t="/",homeLabel:r="Dashboard",showContainer:i=!0}){let o=(0,a.usePathname)(),u=o?o.split("/").filter(Boolean):[],h=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let t={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return t[e]?t[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=u.map((e,t)=>{let r="/"+u.slice(0,t+1).join("/"),i=t===u.length-1,a=h(e);return(0,s.jsxs)(c().Fragment,{children:[(0,s.jsx)(d.BreadcrumbItem,{children:i?(0,s.jsx)(d.BreadcrumbPage,{className:"font-medium text-foreground",children:a}):(0,s.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:r,children:a})})}),!i&&(0,s.jsx)(d.BreadcrumbSeparator,{})]},r)}),m=(0,s.jsx)(d.Breadcrumb,{className:(0,l.cn)("text-sm",e),children:(0,s.jsxs)(d.BreadcrumbList,{className:"flex-wrap",children:[(0,s.jsx)(d.BreadcrumbItem,{children:(0,s.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:r})})}),u.length>0&&(0,s.jsx)(d.BreadcrumbSeparator,{}),p]})});return i?(0,s.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,s.jsx)("div",{className:"flex items-center",children:m})}):m}},28149:(e,t,r)=>{function s(e,t){return e&&"string"==typeof e&&""!==e.trim()?e:t}function i(e,t=400,r=250){return`https://picsum.photos/seed/${e}/${t}/${r}`}function n(e,t,r="card"){let{width:a,height:o}={card:{width:400,height:250},detail:{width:600,height:375},report:{width:600,height:375}}[r];return s(e,i(t,a,o))}function a(e,t){return s(e,i(t,600,375))}r.d(t,{_x:()=>n,aI:()=>a})},48041:(e,t,r)=>{r.d(t,{z:()=>i});var s=r(60687);function i({children:e,description:t,icon:r,title:i}){return(0,s.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,s.jsx)(r,{className:"size-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:i})]}),t&&(0,s.jsx)("p",{className:"mt-1 text-muted-foreground",children:t})]}),e&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:e})]})}r(43210)},49278:(e,t,r)=>{r.d(t,{G7:()=>h,Gb:()=>c,JP:()=>d,Ok:()=>l,Qu:()=>u,iw:()=>o,oz:()=>m,z0:()=>p});var s=r(3389);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class n extends i{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class a extends i{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function o(e){return new n(e)}function c(e,t){return new n({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let d=new i,l=new n({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new n({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),h=new n({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),p=new n({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),m=new a},54050:(e,t,r)=>{r.d(t,{n:()=>l});var s=r(43210),i=r(65406),n=r(33465),a=r(35536),o=r(31212),c=class extends a.Q{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#i(),this.#n()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#i(){let e=this.#r?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},d=r(8693);function l(e,t){let r=(0,d.jE)(t),[i]=s.useState(()=>new c(r,e));s.useEffect(()=>{i.setOptions(e)},[i,e]);let a=s.useSyncExternalStore(s.useCallback(e=>i.subscribe(n.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),l=s.useCallback((e,t)=>{i.mutate(e,t).catch(o.lQ)},[i]);if(a.error&&(0,o.GU)(i.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:l,mutateAsync:a.mutate}}},70640:(e,t,r)=>{r.d(t,{Breadcrumb:()=>c,BreadcrumbItem:()=>l,BreadcrumbLink:()=>u,BreadcrumbList:()=>d,BreadcrumbPage:()=>h,BreadcrumbSeparator:()=>p});var s=r(60687),i=r(8730),n=r(74158),a=(r(69795),r(43210)),o=r(22482);let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("nav",{"aria-label":"breadcrumb",className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:r,...t}));c.displayName="Breadcrumb";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("ol",{className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:r,...t}));d.displayName="BreadcrumbList";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("li",{className:(0,o.cn)("inline-flex items-center gap-1.5",e),ref:r,...t}));l.displayName="BreadcrumbItem";let u=a.forwardRef(({asChild:e,className:t,...r},n)=>{let a=e?i.DX:"a";return(0,s.jsx)(a,{className:(0,o.cn)("transition-colors hover:text-foreground",t),ref:n,...r})});u.displayName="BreadcrumbLink";let h=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,o.cn)("font-normal text-foreground",e),ref:r,role:"link",...t}));h.displayName="BreadcrumbPage";let p=({children:e,className:t,...r})=>(0,s.jsx)("span",{"aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",t),role:"presentation",...r,children:e??(0,s.jsx)(n.A,{className:"size-4"})});p.displayName="BreadcrumbSeparator"}};