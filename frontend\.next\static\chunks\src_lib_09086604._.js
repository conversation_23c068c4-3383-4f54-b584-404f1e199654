(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/transformers/giftTransformer.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Data transformer for Gift domain models.
 * @module transformers/giftTransformer
 */ __turbopack_context__.s({
    "GiftTransformer": (()=>GiftTransformer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/dateUtils.ts [app-client] (ecmascript)");
;
const GiftTransformer = {
    /**
   * Converts an API Gift response into a frontend Gift domain model.
   * @param apiData - The data received from the API.
   * @returns The Gift domain model.
   */ fromApi (apiData) {
        return {
            id: apiData.id,
            itemDescription: apiData.itemDescription,
            recipientId: apiData.recipientId,
            dateSent: apiData.dateSent,
            senderName: apiData.senderName,
            occasion: apiData.occasion || null,
            notes: apiData.notes || null,
            createdAt: apiData.createdAt,
            updatedAt: apiData.updatedAt,
            ...apiData.recipient && {
                recipient: {
                    id: apiData.recipient.id,
                    name: apiData.recipient.name,
                    email: apiData.recipient.email || null,
                    phone: apiData.recipient.phone || null,
                    address: apiData.recipient.address || null,
                    notes: apiData.recipient.notes || null,
                    createdAt: apiData.recipient.createdAt,
                    updatedAt: apiData.recipient.updatedAt
                }
            }
        };
    },
    /**
   * Converts frontend CreateGiftData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */ toCreateRequest (domainData) {
        return {
            itemDescription: domainData.itemDescription,
            recipientId: domainData.recipientId,
            dateSent: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDateForApi"])(domainData.dateSent),
            senderName: domainData.senderName,
            occasion: domainData.occasion ?? null,
            notes: domainData.notes ?? null
        };
    },
    /**
   * Converts frontend UpdateGiftData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */ toUpdateRequest (domainData) {
        const request = {};
        if (domainData.itemDescription !== undefined) {
            request.itemDescription = domainData.itemDescription;
        }
        if (domainData.recipientId !== undefined) {
            request.recipientId = domainData.recipientId;
        }
        if (domainData.dateSent !== undefined) {
            request.dateSent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDateForApi"])(domainData.dateSent);
        }
        if (domainData.senderName !== undefined) {
            request.senderName = domainData.senderName;
        }
        if (domainData.occasion !== undefined) {
            request.occasion = domainData.occasion;
        }
        if (domainData.notes !== undefined) {
            request.notes = domainData.notes;
        }
        return request;
    },
    /**
   * Transforms an array of API Gift responses into domain models.
   * @param apiDataArray - Array of API responses.
   * @returns Array of Gift domain models.
   */ fromApiArray (apiDataArray) {
        return apiDataArray.map(this.fromApi);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/domain/giftApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "GiftApiService": (()=>GiftApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$giftTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/giftTransformer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-client] (ecmascript)");
;
;
const GiftApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$giftTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GiftTransformer"].fromApi(data),
    toApi: (data)=>data
};
class GiftApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/gifts';
    transformer = GiftApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    /**
   * Get gifts sent within a date range
   * @param startDate - Start date (ISO string)
   * @param endDate - End date (ISO string)
   * @returns Promise resolving to array of gifts
   */ async getByDateRange(startDate, endDate) {
        const result = await this.getAll({
            endDate,
            startDate
        });
        return result.data;
    }
    /**
   * Get gifts by occasion
   * @param occasion - The occasion to filter by
   * @returns Promise resolving to array of gifts
   */ async getByOccasion(occasion) {
        const result = await this.getAll({
            occasion
        });
        return result.data;
    }
    /**
   * Get gifts by recipient ID
   * @param recipientId - The recipient ID to filter by
   * @returns Promise resolving to array of gifts
   */ async getByRecipient(recipientId) {
        const result = await this.getAll({
            recipientId
        });
        return result.data;
    }
    /**
   * Get gifts by sender name
   * @param senderName - The sender name to filter by
   * @returns Promise resolving to array of gifts
   */ async getBySender(senderName) {
        const result = await this.getAll({
            senderName
        });
        return result.data;
    }
    /**
   * Get recent gifts (last 30 days)
   * @returns Promise resolving to array of recent gifts
   */ async getRecent() {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return this.getByDateRange(thirtyDaysAgo.toISOString(), new Date().toISOString());
    }
    /**
   * Get gift statistics
   * @returns Promise resolving to gift statistics
   */ async getStatistics() {
        return this.executeWithInfrastructure('getStatistics', async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/statistics`);
            return {
                giftsThisMonth: response.giftsThisMonth,
                giftsThisYear: response.giftsThisYear,
                popularOccasions: response.popularOccasions,
                recentActivity: response.recentActivity,
                topRecipients: response.topRecipients,
                totalGifts: response.totalGifts
            };
        });
    }
    /**
   * Search gifts by item description
   * @param searchTerm - The search term to filter by
   * @returns Promise resolving to array of gifts
   */ async searchByDescription(searchTerm) {
        const result = await this.getAll({
            search: searchTerm
        });
        return result.data;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/transformers/recipientTransformer.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Data transformer for Recipient domain models.
 * @module transformers/recipientTransformer
 */ __turbopack_context__.s({
    "RecipientTransformer": (()=>RecipientTransformer)
});
const RecipientTransformer = {
    /**
   * Converts an API Recipient response into a frontend Recipient domain model.
   * @param apiData - The data received from the API.
   * @returns The Recipient domain model.
   */ fromApi (apiData) {
        return {
            id: apiData.id,
            name: apiData.name,
            email: apiData.email || null,
            phone: apiData.phone || null,
            address: apiData.address || null,
            notes: apiData.notes || null,
            createdAt: apiData.createdAt,
            updatedAt: apiData.updatedAt,
            ...apiData.gifts && {
                gifts: apiData.gifts.map((gift)=>({
                        id: gift.id,
                        itemDescription: gift.itemDescription,
                        recipientId: gift.recipientId,
                        dateSent: gift.dateSent,
                        senderName: gift.senderName,
                        occasion: gift.occasion || null,
                        notes: gift.notes || null,
                        createdAt: gift.createdAt,
                        updatedAt: gift.updatedAt
                    }))
            }
        };
    },
    /**
   * Converts frontend CreateRecipientData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */ toCreateRequest (domainData) {
        return {
            name: domainData.name,
            email: domainData.email ?? null,
            phone: domainData.phone ?? null,
            address: domainData.address ?? null,
            notes: domainData.notes ?? null
        };
    },
    /**
   * Converts frontend UpdateRecipientData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */ toUpdateRequest (domainData) {
        const request = {};
        if (domainData.name !== undefined) {
            request.name = domainData.name;
        }
        if (domainData.email !== undefined) {
            request.email = domainData.email;
        }
        if (domainData.phone !== undefined) {
            request.phone = domainData.phone;
        }
        if (domainData.address !== undefined) {
            request.address = domainData.address;
        }
        if (domainData.notes !== undefined) {
            request.notes = domainData.notes;
        }
        return request;
    },
    /**
   * Transforms an array of API Recipient responses into domain models.
   * @param apiDataArray - Array of API responses.
   * @returns Array of Recipient domain models.
   */ fromApiArray (apiDataArray) {
        return apiDataArray.map(this.fromApi);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/domain/recipientApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "RecipientApiService": (()=>RecipientApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$recipientTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/recipientTransformer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-client] (ecmascript)");
;
;
const RecipientApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$recipientTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RecipientTransformer"].fromApi(data),
    toApi: (data)=>data
};
class RecipientApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/recipients';
    transformer = RecipientApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 10 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    /**
   * Search recipients by name
   * @param searchTerm - The search term to filter by
   * @returns Promise resolving to array of recipients
   */ async searchByName(searchTerm) {
        const result = await this.getAll({
            search: searchTerm
        });
        return result.data;
    }
    /**
   * Get recipients with gift counts
   * @returns Promise resolving to array of recipients with gift counts
   */ async getWithGiftCounts() {
        return this.executeWithInfrastructure('getWithGiftCounts', async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/with-gift-counts`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$recipientTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RecipientTransformer"].fromApiArray(response);
        });
    }
    /**
   * Get recipient statistics
   * @returns Promise resolving to recipient statistics
   */ async getStatistics() {
        return this.executeWithInfrastructure('getStatistics', async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/statistics`);
            return {
                totalRecipients: response.totalRecipients,
                recipientsThisMonth: response.recipientsThisMonth,
                recipientsThisYear: response.recipientsThisYear,
                mostGifted: response.mostGifted
            };
        });
    }
    /**
   * Get recipients by email domain
   * @param domain - The email domain to filter by (e.g., 'gmail.com')
   * @returns Promise resolving to array of recipients
   */ async getByEmailDomain(domain) {
        const result = await this.getAll({
            emailDomain: domain
        });
        return result.data;
    }
    /**
   * Get recipients who have received gifts
   * @returns Promise resolving to array of recipients with gifts
   */ async getWithGifts() {
        const result = await this.getAll({
            hasGifts: true
        });
        return result.data;
    }
    /**
   * Get recipients who haven't received gifts
   * @returns Promise resolving to array of recipients without gifts
   */ async getWithoutGifts() {
        const result = await this.getAll({
            hasGifts: false
        });
        return result.data;
    }
    /**
   * Get recipients for autocomplete/dropdown (limited results)
   * @param searchTerm - The search term to filter by
   * @returns Promise resolving to array of recipients (limited to 10)
   */ async getForAutocomplete(searchTerm) {
        return this.executeWithInfrastructure(`autocomplete:${searchTerm}`, async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/search?q=${encodeURIComponent(searchTerm)}&limit=10`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$recipientTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RecipientTransformer"].fromApiArray(response);
        });
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/domain/reliabilityApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReliabilityApiService": (()=>ReliabilityApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-client] (ecmascript)");
;
const ReliabilityTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class ReliabilityApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/reliability';
    transformer = ReliabilityTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 1 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    async acknowledgeAlert(alertId, note, acknowledgedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/acknowledge`, {
                acknowledgedBy,
                note
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async getActiveAlerts() {
        return this.executeWithInfrastructure('alerts:active', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/alerts');
                return apiResponse?.alerts || [];
            } catch (error) {
                console.error('Failed to get active alerts:', error);
                return [];
            }
        });
    }
    async getAlertHistory(page = 1, limit = 50) {
        return this.executeWithInfrastructure(`alerts:history:${page}:${limit}`, async ()=>{
            const queryParams = new URLSearchParams({
                limit: limit.toString(),
                page: page.toString()
            });
            const response = await this.apiClient.get(`/alerts/history?${queryParams.toString()}`);
            return response;
        });
    }
    async getAlertStatistics() {
        return this.executeWithInfrastructure('alerts:statistics', async ()=>{
            try {
                const response = await this.apiClient.get('/alerts/statistics');
                return response;
            } catch (error) {
                console.error('Failed to get alert statistics:', error);
                return {
                    acknowledged: 0,
                    active: 0,
                    averageResolutionTime: 0,
                    bySeverity: {
                        critical: 0,
                        high: 0,
                        low: 0,
                        medium: 0
                    },
                    recentTrends: {
                        last7Days: 0,
                        last24Hours: 0,
                        last30Days: 0
                    },
                    resolved: 0,
                    total: 0
                };
            }
        });
    }
    async getCircuitBreakerHistory(timeframe = '24h', breakerName) {
        return this.executeWithInfrastructure(`circuit-breakers:history:${timeframe}:${breakerName || 'all'}`, async ()=>{
            const params = new URLSearchParams({
                timeframe
            });
            if (breakerName) {
                params.append('breakerName', breakerName);
            }
            const response = await this.apiClient.get(`/monitoring/circuit-breakers/history?${params.toString()}`);
            return response;
        });
    }
    async getCircuitBreakerStatus() {
        return this.executeWithInfrastructure('monitoring:circuit-breakers', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/circuit-breakers');
                const circuitBreakers = apiResponse?.circuitBreakers || [];
                return {
                    circuitBreakers: circuitBreakers || [],
                    summary: {
                        closed: circuitBreakers?.filter((cb)=>cb.state === 'CLOSED').length || 0,
                        halfOpen: circuitBreakers?.filter((cb)=>cb.state === 'HALF_OPEN').length || 0,
                        open: circuitBreakers?.filter((cb)=>cb.state === 'OPEN').length || 0,
                        total: circuitBreakers?.length || 0
                    }
                };
            } catch (error) {
                console.error('Failed to get circuit breaker status:', error);
                return {
                    circuitBreakers: [],
                    summary: {
                        closed: 0,
                        halfOpen: 0,
                        open: 0,
                        total: 0
                    }
                };
            }
        });
    }
    async getCriticalAlertCount() {
        try {
            const statistics = await this.getAlertStatistics();
            return statistics.bySeverity.critical;
        } catch  {
            return 0;
        }
    }
    async getDeduplicationMetrics() {
        return this.executeWithInfrastructure('monitoring:deduplication', async ()=>{
            const response = await this.apiClient.get('/monitoring/deduplication');
            return response;
        });
    }
    async getDependencyHealth() {
        return this.executeWithInfrastructure('health:dependencies', async ()=>{
            const response = await this.apiClient.get('/health/dependencies');
            return response;
        });
    }
    async getDetailedHealth() {
        return this.executeWithInfrastructure('health:detailed', async ()=>{
            const response = await this.apiClient.get('/health/detailed');
            return response;
        });
    }
    async getHealthTrends(timeframe = '24h') {
        return this.executeWithInfrastructure(`health:trends:${timeframe}`, async ()=>{
            const response = await this.apiClient.get(`/health/trends?timeframe=${timeframe}`);
            return response;
        });
    }
    async getHttpRequestMetrics() {
        return this.executeWithInfrastructure('http:metrics', async ()=>{
            const response = await this.apiClient.get('/http-request-metrics');
            return response;
        });
    }
    async getMetrics() {
        return this.executeWithInfrastructure('metrics:system', async ()=>{
            const response = await this.apiClient.get('/metrics', {
                headers: {
                    Accept: 'application/json'
                }
            });
            return response;
        });
    }
    async getReliabilityDashboardData() {
        const [systemHealth, detailedHealth, circuitBreakers, metrics, activeAlerts, alertStatistics] = await Promise.all([
            this.getSystemHealth(),
            this.getDetailedHealth(),
            this.getCircuitBreakerStatus(),
            this.getMetrics(),
            this.getActiveAlerts(),
            this.getAlertStatistics()
        ]);
        return {
            activeAlerts,
            alertStatistics,
            circuitBreakers,
            detailedHealth,
            metrics,
            systemHealth
        };
    }
    async getSystemHealth() {
        return this.executeWithInfrastructure('health:system', async ()=>{
            const response = await this.apiClient.get('/health');
            return response;
        });
    }
    async isSystemHealthy() {
        try {
            const health = await this.getSystemHealth();
            return health.status === 'healthy';
        } catch  {
            return false;
        }
    }
    async resolveAlert(alertId, reason, resolvedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/resolve`, {
                reason,
                resolvedBy
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async testAlerts() {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post('/alerts/test');
            return {
                message: response?.message || 'Test alert triggered',
                success: response?.status === 'success',
                testAlertId: response?.data?.id
            };
        });
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Factory for creating and managing API service instances.
 * @module api/services/apiServiceFactory
 */ __turbopack_context__.s({
    "ApiServiceFactory": (()=>ApiServiceFactory),
    "apiServiceFactory": (()=>apiServiceFactory),
    "delegationApiService": (()=>delegationApiService),
    "employeeApiService": (()=>employeeApiService),
    "reliabilityApiService": (()=>reliabilityApiService),
    "setFactoryAuthTokenProvider": (()=>setFactoryAuthTokenProvider),
    "taskApiService": (()=>taskApiService),
    "vehicleApiService": (()=>vehicleApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$giftApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/giftApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$recipientApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/recipientApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/reliabilityApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-client] (ecmascript)");
// Import secure auth token provider
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
;
;
/**
 * Get the current auth token from the secure provider
 * Uses the single source of truth for authentication tokens
 */ function getSecureAuthToken() {
    const provider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSecureAuthTokenProvider"])();
    if (!provider) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');
        }
        return null;
    }
    try {
        return provider();
    } catch (error) {
        console.error('❌ Factory: Error getting auth token from secure provider:', error);
        return null;
    }
}
function setFactoryAuthTokenProvider(provider) {
    console.warn('⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.');
// This function is now a no-op since we use the secure provider
// The warning guides developers to use the correct function
}
class ApiServiceFactory {
    apiClient;
    delegationService;
    employeeService;
    giftService;
    recipientService;
    reliabilityService;
    taskService;
    vehicleService;
    /**
   * Creates an instance of ApiServiceFactory.
   * @param config - Configuration for the API services.
   */ constructor(config){
        this.apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiClient"]({
            ...config,
            getAuthToken: getSecureAuthToken
        });
    }
    /**
   * Gets the underlying ApiClient instance.
   * @returns The ApiClient instance.
   */ getApiClient() {
        return this.apiClient;
    }
    /**
   * Gets the Delegation API service instance.
   * @returns The DelegationApiService instance.
   */ getDelegationService() {
        if (!this.delegationService) {
            this.delegationService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DelegationApiService"](this.apiClient);
        }
        return this.delegationService;
    }
    /**
   * Gets the Employee API service instance.
   * @returns The EmployeeApiService instance.
   */ getEmployeeService() {
        if (!this.employeeService) {
            this.employeeService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmployeeApiService"](this.apiClient);
        }
        return this.employeeService;
    }
    /**
   * Gets the Reliability API service instance.
   * @returns The ReliabilityApiService instance.
   */ getReliabilityService() {
        if (!this.reliabilityService) {
            this.reliabilityService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReliabilityApiService"](this.apiClient);
        }
        return this.reliabilityService;
    }
    /**
   * Gets the Task API service instance.
   * @returns The TaskApiService instance.
   */ getTaskService() {
        if (!this.taskService) {
            this.taskService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskApiService"](this.apiClient);
        }
        return this.taskService;
    }
    /**
   * Gets the Vehicle API service instance.
   * @returns The VehicleApiService instance.
   */ getVehicleService() {
        if (!this.vehicleService) {
            this.vehicleService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VehicleApiService"](this.apiClient);
        }
        return this.vehicleService;
    }
    /**
   * Gets the Gift API service instance.
   * @returns The GiftApiService instance.
   */ getGiftService() {
        if (!this.giftService) {
            this.giftService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$giftApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GiftApiService"](this.apiClient);
        }
        return this.giftService;
    }
    /**
   * Gets the Recipient API service instance.
   * @returns The RecipientApiService instance.
   */ getRecipientService() {
        if (!this.recipientService) {
            this.recipientService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$recipientApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RecipientApiService"](this.apiClient);
        }
        return this.recipientService;
    }
}
// Create a default factory instance for the application with environment-aware configuration
const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
const defaultConfig = {
    baseURL: envConfig.apiBaseUrl,
    headers: {
        'Content-Type': 'application/json'
    },
    retryAttempts: 3,
    timeout: 10_000
};
const apiServiceFactory = new ApiServiceFactory(defaultConfig);
const vehicleApiService = apiServiceFactory.getVehicleService();
const delegationApiService = apiServiceFactory.getDelegationService();
const taskApiService = apiServiceFactory.getTaskService();
const employeeApiService = apiServiceFactory.getEmployeeService();
const reliabilityApiService = apiServiceFactory.getReliabilityService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file API Service Factory - Backward Compatibility Export
 * @module api/services/apiServiceFactory
 *
 * This file provides backward compatibility for imports that expect
 * apiServiceFactory.ts instead of factory.ts
 */ // Re-export everything from the factory module
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
// Re-export secure auth provider for convenience
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setFactoryAuthTokenProvider"]),
    "setSecureAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setSecureAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["setFactoryAuthTokenProvider"]),
    "setSecureAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["setSecureAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <exports>");
}}),
}]);

//# sourceMappingURL=src_lib_09086604._.js.map