"use strict";exports.id=3224,exports.ids=[3224],exports.modules={3940:(e,t,s)=>{s.d(t,{O_:()=>n,t6:()=>i});var r=s(43210),a=s(49278);function i(){let e=(0,r.useCallback)((e,t)=>a.JP.success(e,t),[]),t=(0,r.useCallback)((e,t)=>a.JP.error(e,t),[]),s=(0,r.useCallback)((e,t)=>a.JP.info(e,t),[]),i=(0,r.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),n=(0,r.useCallback)((e,s)=>{let r=e instanceof Error?e.message:e;return t(s?.errorTitle||"Error",s?.errorDescription||r||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:s,showFormSuccess:i,showFormError:n}}function n(e){let t;switch(e){case"employee":t=s(49278).Ok;break;case"vehicle":t=s(49278).G7;break;case"task":t=s(49278).z0;break;case"delegation":t=s(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:s,showFormError:n}=i(),o=t||(e?(0,a.iw)(e):null),l=(0,r.useCallback)(e=>o?o.entityCreated(e):s({successTitle:"Created",successDescription:"Item has been created successfully"}),[o,s]),u=(0,r.useCallback)(e=>o?o.entityUpdated(e):s({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[o,s]),c=(0,r.useCallback)(e=>o?o.entityDeleted(e):s({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[o,s]),d=(0,r.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityCreationError(t)}return n(e,{errorTitle:"Creation Failed"})},[o,n]);return{showEntityCreated:l,showEntityUpdated:u,showEntityDeleted:c,showEntityCreationError:d,showEntityUpdateError:(0,r.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityUpdateError(t)}return n(e,{errorTitle:"Update Failed"})},[o,n]),showEntityDeletionError:(0,r.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityDeletionError(t)}return n(e,{errorTitle:"Deletion Failed"})},[o,n]),showFormSuccess:s,showFormError:n}}(void 0,t)}},54050:(e,t,s)=>{s.d(t,{n:()=>c});var r=s(43210),a=s(65406),i=s(33465),n=s(35536),o=s(31212),l=class extends n.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#a()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#a(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#a(),this.#i()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#a(){let e=this.#s?.state??(0,a.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},u=s(8693);function c(e,t){let s=(0,u.jE)(t),[a]=r.useState(()=>new l(s,e));r.useEffect(()=>{a.setOptions(e)},[a,e]);let n=r.useSyncExternalStore(r.useCallback(e=>a.subscribe(i.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),c=r.useCallback((e,t)=>{a.mutate(e,t).catch(o.lQ)},[a]);if(n.error&&(0,o.GU)(a.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:c,mutateAsync:n.mutate}}},55925:(e,t,s)=>{s.d(t,{Lt:()=>S,Rx:()=>F,Zr:()=>G,EO:()=>I,$v:()=>z,ck:()=>P,wd:()=>L,r7:()=>$,tv:()=>U});var r=s(60687),a=s(43210),i=s(11273),n=s(98599),o=s(26134),l=s(70569),u=s(8730),c="AlertDialog",[d,m]=(0,i.A)(c,[o.Hs]),p=(0,o.Hs)(),f=e=>{let{__scopeAlertDialog:t,...s}=e,a=p(t);return(0,r.jsx)(o.bL,{...a,...s,modal:!0})};f.displayName=c;var h=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,i=p(s);return(0,r.jsx)(o.l9,{...i,...a,ref:t})});h.displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:t,...s}=e,a=p(t);return(0,r.jsx)(o.ZL,{...a,...s})};y.displayName="AlertDialogPortal";var b=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,i=p(s);return(0,r.jsx)(o.hJ,{...i,...a,ref:t})});b.displayName="AlertDialogOverlay";var x="AlertDialogContent",[g,v]=d(x),N=(0,u.Dc)("AlertDialogContent"),w=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,children:i,...u}=e,c=p(s),d=a.useRef(null),m=(0,n.s)(t,d),f=a.useRef(null);return(0,r.jsx)(o.G$,{contentName:x,titleName:j,docsSlug:"alert-dialog",children:(0,r.jsx)(g,{scope:s,cancelRef:f,children:(0,r.jsxs)(o.UC,{role:"alertdialog",...c,...u,ref:m,onOpenAutoFocus:(0,l.m)(u.onOpenAutoFocus,e=>{e.preventDefault(),f.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(N,{children:i}),(0,r.jsx)(A,{contentRef:d})]})})})});w.displayName=x;var j="AlertDialogTitle",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,i=p(s);return(0,r.jsx)(o.hE,{...i,...a,ref:t})});R.displayName=j;var k="AlertDialogDescription",D=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,i=p(s);return(0,r.jsx)(o.VY,{...i,...a,ref:t})});D.displayName=k;var C=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,i=p(s);return(0,r.jsx)(o.bm,{...i,...a,ref:t})});C.displayName="AlertDialogAction";var E="AlertDialogCancel",O=a.forwardRef((e,t)=>{let{__scopeAlertDialog:s,...a}=e,{cancelRef:i}=v(E,s),l=p(s),u=(0,n.s)(t,i);return(0,r.jsx)(o.bm,{...l,...a,ref:u})});O.displayName=E;var A=({contentRef:e})=>{let t=`\`${x}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${x}\` by passing a \`${k}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${x}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return a.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},M=s(29523),B=s(22482);let S=f,U=h,T=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(b,{className:(0,B.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:s}));T.displayName=b.displayName;let I=a.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(y,{children:[(0,r.jsx)(T,{}),(0,r.jsx)(w,{className:(0,B.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),ref:s,...t})]}));I.displayName=w.displayName;let L=({className:e,...t})=>(0,r.jsx)("div",{className:(0,B.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});L.displayName="AlertDialogHeader";let P=({className:e,...t})=>(0,r.jsx)("div",{className:(0,B.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});P.displayName="AlertDialogFooter";let $=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(R,{className:(0,B.cn)("text-lg font-semibold",e),ref:s,...t}));$.displayName=R.displayName;let z=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(D,{className:(0,B.cn)("text-sm text-muted-foreground",e),ref:s,...t}));z.displayName=D.displayName;let F=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(C,{className:(0,B.cn)((0,M.r)(),e),ref:s,...t}));F.displayName=C.displayName;let G=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(O,{className:(0,B.cn)((0,M.r)({variant:"outline"}),"mt-2 sm:mt-0",e),ref:s,...t}));G.displayName=O.displayName},70640:(e,t,s)=>{s.d(t,{Breadcrumb:()=>l,BreadcrumbItem:()=>c,BreadcrumbLink:()=>d,BreadcrumbList:()=>u,BreadcrumbPage:()=>m,BreadcrumbSeparator:()=>p});var r=s(60687),a=s(8730),i=s(74158),n=(s(69795),s(43210)),o=s(22482);let l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("nav",{"aria-label":"breadcrumb",className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:s,...t}));l.displayName="Breadcrumb";let u=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("ol",{className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:s,...t}));u.displayName="BreadcrumbList";let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("li",{className:(0,o.cn)("inline-flex items-center gap-1.5",e),ref:s,...t}));c.displayName="BreadcrumbItem";let d=n.forwardRef(({asChild:e,className:t,...s},i)=>{let n=e?a.DX:"a";return(0,r.jsx)(n,{className:(0,o.cn)("transition-colors hover:text-foreground",t),ref:i,...s})});d.displayName="BreadcrumbLink";let m=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,o.cn)("font-normal text-foreground",e),ref:s,role:"link",...t}));m.displayName="BreadcrumbPage";let p=({children:e,className:t,...s})=>(0,r.jsx)("span",{"aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",t),role:"presentation",...s,children:e??(0,r.jsx)(i.A,{className:"size-4"})});p.displayName="BreadcrumbSeparator"},71032:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(82614).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},85726:(e,t,s)=>{s.d(t,{E:()=>i});var r=s(60687),a=s(22482);function i({className:e,...t}){return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}}};