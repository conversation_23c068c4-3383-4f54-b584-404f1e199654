'use client';

import { Edit, Gift, Trash2, Users } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import React from 'react';

import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { Separator } from '@/components/ui/separator';
import { useGiftsByRecipient } from '@/lib/stores/queries/useGifts';
import { useDeleteRecipient, useRecipient } from '@/lib/stores/queries/useRecipients';

export default function RecipientDetailPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations('recipients');
  const tCommon = useTranslations('common');
  const tForms = useTranslations('forms');
  const tGifts = useTranslations('gifts');
  
  const recipientId = params.id as string;
  
  const { data: recipient, isLoading, error } = useRecipient(recipientId);
  const { data: gifts = [] } = useGiftsByRecipient(recipientId);
  const { mutateAsync: deleteRecipient, isPending: isDeleting } = useDeleteRecipient();

  const handleDelete = async () => {
    try {
      await deleteRecipient(recipientId);
      router.push('/recipients');
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error deleting recipient:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <AppBreadcrumb 
        homeHref="/" 
        homeLabel={tCommon('dashboard')}
        items={[
          { href: '/recipients', label: t('recipients') },
          { label: recipient?.name || t('recipientDetails') }
        ]}
      />
      
      <DataLoader
        data={recipient}
        error={error}
        isLoading={isLoading}
        loadingComponent={<SkeletonLoader count={1} variant="card" />}
        emptyComponent={
          <div className="rounded-lg bg-card py-12 text-center shadow-md">
            <Users className="mx-auto mb-6 size-16 text-muted-foreground" />
            <h3 className="mb-2 text-2xl font-semibold text-foreground">
              {t('recipientNotFound')}
            </h3>
            <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
              {t('recipientNotFoundDescription')}
            </p>
            <ActionButton asChild>
              <Link href="/recipients">{t('backToRecipients')}</Link>
            </ActionButton>
          </div>
        }
      >
        {recipientData => (
          <>
            <PageHeader
              description={t('recipientDetailsDescription')}
              icon={Users}
              title={recipientData.name}
            >
              <div className="flex items-center gap-2">
                <ActionButton
                  actionType="secondary"
                  asChild
                  icon={<Edit className="size-4" />}
                >
                  <Link href={`/recipients/${recipientId}/edit`}>
                    {tForms('buttons.edit')}
                  </Link>
                </ActionButton>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <ActionButton
                      actionType="destructive"
                      icon={<Trash2 className="size-4" />}
                    >
                      {tForms('buttons.delete')}
                    </ActionButton>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>{t('deleteRecipientTitle')}</DialogTitle>
                      <DialogDescription>
                        {t('deleteRecipientConfirmation', { name: recipientData.name })}
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <ActionButton variant="outline">
                        {tCommon('cancel')}
                      </ActionButton>
                      <ActionButton
                        actionType="destructive"
                        isLoading={isDeleting}
                        onClick={handleDelete}
                      >
                        {tForms('buttons.delete')}
                      </ActionButton>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </PageHeader>

            <div className="grid gap-6 md:grid-cols-2">
              {/* Recipient Information */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('recipientInformation')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {tForms('labels.name')}
                    </label>
                    <p className="mt-1 text-sm">{recipientData.name}</p>
                  </div>
                  
                  <Separator />
                  
                  {recipientData.email && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {tForms('labels.email')}
                      </label>
                      <p className="mt-1 text-sm">
                        <a 
                          href={`mailto:${recipientData.email}`}
                          className="text-primary hover:underline"
                        >
                          {recipientData.email}
                        </a>
                      </p>
                    </div>
                  )}
                  
                  {recipientData.phone && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {tForms('labels.phone')}
                      </label>
                      <p className="mt-1 text-sm">
                        <a 
                          href={`tel:${recipientData.phone}`}
                          className="text-primary hover:underline"
                        >
                          {recipientData.phone}
                        </a>
                      </p>
                    </div>
                  )}
                  
                  {recipientData.address && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {tForms('labels.address')}
                      </label>
                      <p className="mt-1 text-sm">{recipientData.address}</p>
                    </div>
                  )}
                  
                  {recipientData.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {tForms('labels.notes')}
                      </label>
                      <p className="mt-1 text-sm">{recipientData.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Gift Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Gift className="size-4" />
                    {tGifts('giftHistory')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary">
                      {gifts.length}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {tGifts('totalGifts')}
                    </p>
                  </div>
                  
                  {gifts.length > 0 && (
                    <>
                      <Separator />
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">{tGifts('recentGifts')}</h4>
                        {gifts.slice(0, 3).map((gift) => (
                          <div key={gift.id} className="flex justify-between text-sm">
                            <Link 
                              href={`/gifts/${gift.id}`}
                              className="text-primary hover:underline truncate"
                            >
                              {gift.itemDescription}
                            </Link>
                            <span className="text-muted-foreground">
                              {formatDate(gift.dateSent)}
                            </span>
                          </div>
                        ))}
                        {gifts.length > 3 && (
                          <Link 
                            href={`/gifts?recipientId=${recipientId}`}
                            className="text-sm text-primary hover:underline"
                          >
                            {tGifts('viewAllGifts')} ({gifts.length - 3} {tCommon('more')})
                          </Link>
                        )}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>{tCommon('metadata')}</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {tCommon('createdAt')}
                  </label>
                  <p className="mt-1 text-sm">{formatDate(recipientData.createdAt)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {tCommon('updatedAt')}
                  </label>
                  <p className="mt-1 text-sm">{formatDate(recipientData.updatedAt)}</p>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </DataLoader>
    </div>
  );
}
