/**
 * @file Centralized Form Validation Hook
 * @description Provides consistent form validation patterns with Zod schema integration
 */

import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useMemo } from 'react';
import {
  type FieldErrors,
  type FieldValues,
  type UseFormReturn,
  useForm,
  type DefaultValues,
  type Path,
} from 'react-hook-form';
import { type ZodSchema, ZodError } from 'zod';
import { useTranslations } from 'next-intl';

import { useToast } from '@/hooks/utils/use-toast';

/**
 * Configuration options for form validation
 */
export interface FormValidationOptions<T extends FieldValues> {
  /** Zod schema for validation */
  schema: ZodSchema<T>;
  /** Default values for the form */
  defaultValues?: Partial<T>;
  /** Validation mode */
  mode?: 'onChange' | 'onBlur' | 'onSubmit' | 'onTouched' | 'all';
  /** Whether to revalidate on change */
  reValidateMode?: 'onChange' | 'onBlur' | 'onSubmit';
  /** Whether to show validation errors immediately */
  showErrorsImmediately?: boolean;
  /** Custom error messages */
  customErrorMessages?: Record<string, string>;
}

/**
 * Enhanced form validation result
 */
export interface FormValidationResult<T extends FieldValues> {
  /** React Hook Form instance */
  form: UseFormReturn<T>;
  /** Whether the form is valid */
  isValid: boolean;
  /** Whether the form has been touched */
  isDirty: boolean;
  /** Current form errors */
  errors: FieldErrors<T>;
  /** Validate specific field */
  validateField: (fieldName: keyof T) => Promise<boolean>;
  /** Validate entire form */
  validateForm: () => Promise<boolean>;
  /** Clear all errors */
  clearErrors: () => void;
  /** Clear specific field error */
  clearFieldError: (fieldName: keyof T) => void;
  /** Get formatted error message for field */
  getFieldError: (fieldName: keyof T) => string | undefined;
  /** Check if specific field has error */
  hasFieldError: (fieldName: keyof T) => boolean;
  /** Reset form to default values */
  resetForm: () => void;
}

/**
 * Centralized form validation hook with Zod schema integration
 *
 * @example
 * ```typescript
 * const { form, isValid, validateForm, getFieldError } = useFormValidation({
 *   schema: UserSchema,
 *   defaultValues: { name: '', email: '' },
 *   mode: 'onChange',
 *   showErrorsImmediately: true,
 * });
 * ```
 */
export const useFormValidation = <T extends FieldValues>(
  options: FormValidationOptions<T>
): FormValidationResult<T> => {
  const {
    schema,
    defaultValues,
    mode = 'onChange',
    reValidateMode = 'onChange',
    showErrorsImmediately = false,
    customErrorMessages = {},
  } = options;

  const t = useTranslations('forms.validation');

  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as DefaultValues<T>,
    mode,
    reValidateMode,
    criteriaMode: 'all', // Show all validation errors
  });

  const {
    formState: { errors, isValid, isDirty },
    trigger,
    clearErrors: clearAllErrors,
    setError,
    reset,
  } = form;

  // Validate specific field
  const validateField = useCallback(
    async (fieldName: keyof T): Promise<boolean> => {
      const result = await trigger(fieldName as Path<T>);
      return result;
    },
    [trigger]
  );

  // Validate entire form
  const validateForm = useCallback(async (): Promise<boolean> => {
    const result = await trigger();
    return result;
  }, [trigger]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    clearAllErrors();
  }, [clearAllErrors]);

  // Clear specific field error
  const clearFieldError = useCallback(
    (fieldName: keyof T) => {
      clearAllErrors(fieldName as Path<T>);
    },
    [clearAllErrors]
  );

  // Get formatted error message for field
  const getFieldError = useCallback(
    (fieldName: keyof T): string | undefined => {
      const fieldError = errors[fieldName];
      if (!fieldError) return undefined;

      // Check for custom error message first
      const customMessage = customErrorMessages[fieldName as string];
      if (customMessage) return customMessage;

      // Return the error message from validation
      if (typeof fieldError.message === 'string') {
        return fieldError.message;
      }

      // Fallback for complex error structures
      return 'This field is invalid';
    },
    [errors, customErrorMessages]
  );

  // Check if specific field has error
  const hasFieldError = useCallback(
    (fieldName: keyof T): boolean => {
      return !!errors[fieldName];
    },
    [errors]
  );

  // Reset form to default values
  const resetForm = useCallback(() => {
    reset(defaultValues as DefaultValues<T>);
  }, [reset, defaultValues]);

  const result: FormValidationResult<T> = useMemo(
    () => ({
      form,
      isValid,
      isDirty,
      errors,
      validateField,
      validateForm,
      clearErrors,
      clearFieldError,
      getFieldError,
      hasFieldError,
      resetForm,
    }),
    [
      form,
      isValid,
      isDirty,
      errors,
      validateField,
      validateForm,
      clearErrors,
      clearFieldError,
      getFieldError,
      hasFieldError,
      resetForm,
    ]
  );

  return result;
};

/**
 * Hook for validating form data without a form instance
 * Useful for validating data before submission or in other contexts
 */
export const useSchemaValidation = <T>(schema: ZodSchema<T>) => {
  const validateData = useCallback(
    (data: unknown): { isValid: boolean; errors?: ZodError; validData?: T } => {
      try {
        const validData = schema.parse(data);
        return { isValid: true, validData };
      } catch (error) {
        if (error instanceof ZodError) {
          return { isValid: false, errors: error };
        }
        throw error;
      }
    },
    [schema]
  );

  const validateAsync = useCallback(
    async (
      data: unknown
    ): Promise<{ isValid: boolean; errors?: ZodError; validData?: T }> => {
      try {
        const validData = await schema.parseAsync(data);
        return { isValid: true, validData };
      } catch (error) {
        if (error instanceof ZodError) {
          return { isValid: false, errors: error };
        }
        throw error;
      }
    },
    [schema]
  );

  const getFieldErrors = useCallback(
    (zodError: ZodError): Record<string, string> => {
      const fieldErrors: Record<string, string> = {};

      zodError.errors.forEach(error => {
        const fieldPath = error.path.join('.');
        fieldErrors[fieldPath] = error.message;
      });

      return fieldErrors;
    },
    []
  );

  return {
    validateData,
    validateAsync,
    getFieldErrors,
  };
};

/**
 * Hook for conditional validation based on form state
 * Useful for complex forms with conditional fields
 */
export const useConditionalValidation = <T extends FieldValues>(
  baseSchema: ZodSchema<T>,
  conditions: Array<{
    condition: (data: T) => boolean;
    schema: ZodSchema<T>;
  }>
) => {
  const getDynamicSchema = useCallback(
    (data: T): ZodSchema<T> => {
      // Find the first matching condition
      const matchingCondition = conditions.find(({ condition }) =>
        condition(data)
      );

      // Return the conditional schema or base schema
      return matchingCondition ? matchingCondition.schema : baseSchema;
    },
    [baseSchema, conditions]
  );

  const validateWithConditions = useCallback(
    (data: T): { isValid: boolean; errors?: ZodError; validData?: T } => {
      const dynamicSchema = getDynamicSchema(data);

      try {
        const validData = dynamicSchema.parse(data);
        return { isValid: true, validData };
      } catch (error) {
        if (error instanceof ZodError) {
          return { isValid: false, errors: error };
        }
        throw error;
      }
    },
    [getDynamicSchema]
  );

  return {
    getDynamicSchema,
    validateWithConditions,
  };
};
