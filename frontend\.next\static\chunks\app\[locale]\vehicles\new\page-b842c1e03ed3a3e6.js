(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8782],{20317:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var s=r(83940);class i{static showSuccessToast(e,t,r){if(!e.showSuccessToast)return;let{entityType:i,entity:a,successMessage:n}=e;try{switch(i){case"employee":a?s.Ok.entityCreated(a):s.JP.success("Employee Created",n);break;case"vehicle":a?s.G7.entityCreated(a):s.JP.success("Vehicle Added",n);break;case"task":a?s.z0.entityCreated(a):s.JP.success("Task Created",n);break;case"delegation":a?s.Qu.entityCreated(a):s.JP.success("Delegation Created",n);break;case"serviceRecord":a&&r?s.oz.serviceRecordCreated(a.vehicleName||"Vehicle",a.serviceType||"Service"):s.JP.success("Service Record Added",n);break;default:s.JP.success("Success",n||"Operation completed successfully")}}catch(e){s.JP.success("Success",n||"Operation completed successfully")}}static showErrorToast(e,t,r){if(!e.showErrorToast)return;let{entityType:i,errorMessage:a}=e,n=t.message||a||"An unexpected error occurred";try{switch(i){case"employee":s.Ok.entityCreationError(n);break;case"vehicle":s.G7.entityCreationError(n);break;case"task":s.z0.entityCreationError(n);break;case"delegation":s.Qu.entityCreationError(n);break;case"serviceRecord":s.oz.serviceRecordCreationError(n);break;default:s.JP.error("Error",n)}}catch(e){s.JP.error("Error",n)}}static showUpdateSuccessToast(e,t,r){if(!e.showSuccessToast)return;let{entityType:i,entity:a,successMessage:n}=e;try{switch(i){case"employee":a?s.Ok.entityUpdated(a):s.JP.success("Employee Updated",n);break;case"vehicle":a?s.G7.entityUpdated(a):s.JP.success("Vehicle Updated",n);break;case"task":a?s.z0.entityUpdated(a):s.JP.success("Task Updated",n);break;case"delegation":a?s.Qu.entityUpdated(a):s.JP.success("Delegation Updated",n);break;case"serviceRecord":a&&r?s.oz.serviceRecordUpdated(a.vehicleName||"Vehicle",a.serviceType||"Service"):s.JP.success("Service Record Updated",n);break;default:s.JP.success("Success",n||"Update completed successfully")}}catch(e){s.JP.success("Success",n||"Update completed successfully")}}static showUpdateErrorToast(e,t,r){if(!e.showErrorToast)return;let{entityType:i,errorMessage:a}=e,n=t.message||a||"An unexpected error occurred";try{switch(i){case"employee":s.Ok.entityUpdateError(n);break;case"vehicle":s.G7.entityUpdateError(n);break;case"task":s.z0.entityUpdateError(n);break;case"delegation":s.Qu.entityUpdateError(n);break;case"serviceRecord":s.oz.serviceRecordUpdateError(n);break;default:s.JP.error("Update Failed",n)}}catch(e){s.JP.error("Update Failed",n)}}static createCustomEntityToastService(e,t){return(0,s.Gb)(e,t)}}},39097:(e,t,r)=>{"use strict";r.d(t,{k:()=>l});var s=r(12115),i=r(94141),a=r(20317);class n{announceStatus(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"polite";if(!this.config.announceStatus||!this.config.screenReaderAnnouncements)return;let r=document.getElementById("form-submission-announcements");r||((r=document.createElement("div")).id="form-submission-announcements",r.setAttribute("aria-live",t),r.setAttribute("aria-atomic","true"),r.className="sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden",document.body.appendChild(r)),r.textContent=e,setTimeout(()=>{r&&r.textContent===e&&(r.textContent="")},1e3)}generateAriaAttributes(e,t,r){return{"aria-busy":e,"aria-invalid":t,"aria-describedby":this.config.errorDescribedBy||(t?"form-error":void 0),"aria-live":"submitting"===r||"validating"===r?"polite":"off"}}manageFocus(e,t){if("none"!==this.config.focusManagement)switch(e){case"error":"first-error"===this.config.focusManagement&&t&&t("first-error");break;case"success":if("success-message"===this.config.focusManagement){let e=document.getElementById("form-success-message");e&&e.focus()}else"next-field"===this.config.focusManagement&&t&&t("next-field");break;case"retry":t&&t("retry-button")}}createErrorMessage(e){let t=document.createElement("div");return t.id=this.config.errorDescribedBy||"form-error",t.setAttribute("role","alert"),t.setAttribute("aria-live","assertive"),t.className="sr-only",t.textContent=e,t}updateErrorMessage(e){let t=this.config.errorDescribedBy||"form-error",r=document.getElementById(t);e?r?r.textContent=e:(r=this.createErrorMessage(e),document.body.appendChild(r)):r&&r.remove()}getStatusMessage(e,t,r){switch(e){case"validating":return"Validating form data...";case"submitting":return"Submitting form...";case"retrying":return"Retrying submission... (Attempt ".concat(t||1,"/").concat(r||3,")");case"success":return"Form submitted successfully";case"error":return"Form submission failed";default:return""}}setupKeyboardNavigation(){let e=e=>{if("Escape"===e.key){let e=document.querySelector("[data-form-cancel]");e&&e.click()}if((e.ctrlKey||e.metaKey)&&"Enter"===e.key){let e=document.querySelector('[type="submit"]');e&&!e.disabled&&e.click()}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}announceProgress(e,t,r){if(!this.config.screenReaderAnnouncements)return;let s="Step ".concat(e," of ").concat(t,": ").concat(r);this.announceStatus(s,"polite")}cleanup(){let e=document.getElementById("form-submission-announcements");e&&e.remove(),this.updateErrorMessage(null)}constructor(e){this.config=e}}class c{shouldRetry(e){return this.currentAttempt<this.config.maxAttempts&&(!this.config.retryCondition||this.config.retryCondition(e))}getRetryDelay(){let e=this.config.delay;return this.config.exponentialBackoff?e*Math.pow(2,this.currentAttempt):e}incrementAttempt(){return this.currentAttempt+=1,this.currentAttempt}resetAttempts(){this.currentAttempt=0}getCurrentAttempt(){return this.currentAttempt}getMaxAttempts(){return this.config.maxAttempts}async sleep(e){return new Promise(t=>setTimeout(t,e))}async executeRetry(e){if(!this.shouldRetry(Error("Manual retry")))throw Error("Maximum retry attempts exceeded");let t=this.getRetryDelay();return this.incrementAttempt(),await this.sleep(t),e()}getRetryStatus(){return{currentAttempt:this.currentAttempt,maxAttempts:this.config.maxAttempts,hasRetriesLeft:this.currentAttempt<this.config.maxAttempts,nextDelay:this.getRetryDelay()}}withConfig(e){return new c({...this.config,...e})}constructor(e){this.currentAttempt=0,this.config=e}}class o{startTiming(){this.submissionStartTime=Date.now()}endTiming(e){if(!this.submissionStartTime)return 0;let t=Date.now()-this.submissionStartTime;return this.updateMetrics(e,t),this.submissionStartTime=null,t}updateMetrics(e,t){let r=this.metrics.totalSubmissions+1,s=e?this.metrics.successfulSubmissions+1:this.metrics.successfulSubmissions,i=e?this.metrics.failedSubmissions:this.metrics.failedSubmissions+1,a=this.metrics.averageDuration*this.metrics.totalSubmissions+t;this.metrics={totalSubmissions:r,successfulSubmissions:s,failedSubmissions:i,averageDuration:a/r}}getMetrics(){return{...this.metrics}}resetMetrics(){this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}debounce(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.config.debounceMs;var r=this;return function(){for(var s=arguments.length,i=Array(s),a=0;a<s;a++)i[a]=arguments[a];r.debounceTimer&&clearTimeout(r.debounceTimer),r.debounceTimer=setTimeout(()=>{e(...i)},t)}}clearDebounce(){this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null)}createTimeoutPromise(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.timeoutMs;return new Promise((t,r)=>{setTimeout(()=>{r(Error("Request timeout after ".concat(e,"ms")))},e)})}async withTimeout(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.config.timeoutMs;return Promise.race([e,this.createTimeoutPromise(t)])}getSuccessRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.successfulSubmissions/this.metrics.totalSubmissions*100}getFailureRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.failedSubmissions/this.metrics.totalSubmissions*100}isPerformanceAcceptable(e){let t={maxAverageDuration:5e3,minSuccessRate:95,...e};return this.metrics.averageDuration<=t.maxAverageDuration&&this.getSuccessRate()>=t.minSuccessRate}generateReport(){let e=this.getSuccessRate(),t=this.getFailureRate(),r=this.isPerformanceAcceptable(),s=[];return this.metrics.averageDuration>3e3&&s.push("Consider optimizing form validation or submission logic"),e<90&&s.push("High failure rate detected - review error handling"),this.metrics.totalSubmissions>100&&this.metrics.averageDuration>1e3&&s.push("Consider implementing caching for better performance"),{metrics:this.getMetrics(),successRate:e,failureRate:t,isAcceptable:r,recommendations:s}}cleanup(){this.clearDebounce(),this.submissionStartTime=null}constructor(e){this.submissionStartTime=null,this.debounceTimer=null,this.config=e,this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}}let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=i.SY.mergeRetryConfig(t.retry),l=i.SY.mergeAccessibilityConfig(t.accessibility),u=i.SY.mergePerformanceConfig(t.performance),d=i.SY.mergeToastConfig(t.toast),m=(0,s.useRef)(new n(l)).current,h=(0,s.useRef)(new c(r)).current,p=(0,s.useRef)(new o(u)).current,[g,f]=(0,s.useState)("idle"),[y,b]=(0,s.useState)(null),[v,S]=(0,s.useState)(null),[x,w]=(0,s.useState)(null),[E,A]=(0,s.useState)(null),[T,k]=(0,s.useState)(null),[C,D]=(0,s.useState)(null),R=(0,s.useRef)(null),M="submitting"===g||"validating"===g,N="success"===g,P="validating"===g,U="retrying"===g,F=h.getCurrentAttempt();(0,s.useEffect)(()=>()=>{R.current&&R.current.abort(),p.cleanup(),m.cleanup()},[p,m]);let j=(0,s.useCallback)(()=>{b(null),S(null),m.updateErrorMessage(null),"error"===g&&f("idle")},[g,m]),O=(0,s.useCallback)(()=>{f("idle"),b(null),S(null),w(null),A(null),k(null),D(null),h.resetAttempts(),p.resetMetrics(),m.updateErrorMessage(null)},[h,p,m]),V=(0,s.useCallback)(()=>{R.current&&R.current.abort(),f("idle"),m.announceStatus("Form submission cancelled")},[m]),J=(0,s.useCallback)(async function(s){let i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{p.startTiming(),R.current=new AbortController;let n=i?"retrying":"submitting";f(n);let c=m.getStatusMessage(n,F,r.maxAttempts);if(m.announceStatus(c),t.onSubmitStart&&await t.onSubmitStart(s),t.preSubmitValidation&&(f("validating"),m.announceStatus("Validating form data..."),!await t.preSubmitValidation(s)))throw Error("Validation failed");f(n);let o=s;t.transformData&&(o=await t.transformData(s));let l=e(o),u=await p.withTimeout(l),g=u;if(t.transformResult&&(g=await t.transformResult(u)),t.postSubmitValidation&&!await t.postSubmitValidation(g))throw Error("Post-submission validation failed");let y=p.endTiming(!0);f("success"),k(g),A(Date.now()),w(s),D(y),h.resetAttempts(),a.P.showSuccessToast(d,s,g),m.announceStatus("Form submitted successfully","assertive"),m.manageFocus("success",t.formFocus),t.resetOnSuccess&&t.formReset&&t.formReset(),t.onSuccess&&await t.onSuccess(s,g),t.onSubmitComplete&&await t.onSubmitComplete(s,!0)}catch(o){let e=o instanceof Error?o:Error(String(o)),n=p.endTiming(!1);if(!i&&h.shouldRetry(e)){f("retrying");let e=h.getRetryDelay();return h.incrementAttempt(),m.announceStatus("Retrying in ".concat(e,"ms... (Attempt ").concat(h.getCurrentAttempt(),"/").concat(r.maxAttempts,")")),await h.sleep(e),J(s,!0)}f("error");let c=e.message||d.errorMessage||"An unexpected error occurred";b(c),S(e),D(n),a.P.showErrorToast(d,e,s),m.updateErrorMessage(c),m.announceStatus("Error: ".concat(c),"assertive"),m.manageFocus("error",t.formFocus),t.onError&&await t.onError(e,s),t.onSubmitComplete&&await t.onSubmitComplete(s,!1)}},[e,t,h,p,m,d,r.maxAttempts,F]),_=(0,s.useCallback)(async(e,t)=>{t&&t.preventDefault(),p.debounce(()=>J(e),u.debounceMs)()},[J,p,u.debounceMs]),z=(0,s.useCallback)(async()=>{x&&(h.resetAttempts(),await J(x))},[x,J,h]),I=m.generateAriaAttributes(M,!!y,g);return{isLoading:M,state:g,error:y,errorObject:v,isSuccess:N,isValidating:P,isRetrying:U,lastSubmittedData:x,lastSubmitted:E,lastResult:T,retryAttempt:F,handleSubmit:_,clearError:j,reset:O,retry:z,cancel:V,ariaAttributes:I,submissionDuration:C,metrics:p.getMetrics()}}},40136:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(95155),i=r(28328),a=r(35695);r(12115);var n=r(95647),c=r(30836),o=r(39097),l=r(80937);let u=()=>{let e=(0,a.useRouter)(),{mutateAsync:t}=(0,l.Y1)(),{handleSubmit:r,isLoading:u,error:d,clearError:m,state:h,retry:p}=(0,o.k)(async r=>{let s=await t(r);return e.push("/vehicles"),s},{toast:{entityType:"vehicle",entity:{make:"",model:""},successMessage:"Vehicle added to fleet! \uD83D\uDE97"},preSubmitValidation:e=>{var t,r,s;if(!(null==(t=e.make)?void 0:t.trim()))throw Error("Make is required");if(!(null==(r=e.model)?void 0:r.trim()))throw Error("Model is required");if(!(null==(s=e.licensePlate)?void 0:s.trim()))throw Error("License Plate is required");return!0},transformData:e=>{var t,r,s,i;return{...e,year:"string"==typeof e.year?parseInt(e.year,10):e.year,make:(null==(t=e.make)?void 0:t.trim())||"",model:(null==(r=e.model)?void 0:r.trim())||"",licensePlate:(null==(i=e.licensePlate)||null==(s=i.trim())?void 0:s.toUpperCase())||""}},retry:{maxAttempts:3,exponentialBackoff:!0,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")}});return(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(n.z,{description:"Enter the details for your new vehicle.",icon:i.A,title:"Add New Vehicle"}),d&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 border border-red-200 p-4",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,s.jsx)("p",{className:"text-sm text-red-700 mt-1",children:d})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:["error"===h&&(0,s.jsx)("button",{onClick:p,className:"text-red-700 hover:text-red-900 text-sm font-medium",children:"Retry"}),(0,s.jsx)("button",{onClick:m,className:"text-red-700 hover:text-red-900 text-sm",children:"✕"})]})]})}),(0,s.jsx)(c.x,{isEditing:!1,isLoading:u,onSubmit:r})]})}},40879:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m,oR:()=>d});var s=r(12115);let i=0,a=new Map,n=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),u({toastId:e,type:"REMOVE_TOAST"})},1e6);a.set(e,t)},c=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:r}=t;if(r)n(r);else for(let t of e.toasts)n(t.id);return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},o=[],l={toasts:[]};function u(e){for(let t of(l=c(l,e),o))t(l)}function d(e){let{...t}=e,r=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>u({toastId:r,type:"DISMISS_TOAST"});return u({toast:{...t,id:r,onOpenChange:e=>{e||s()},open:!0},type:"ADD_TOAST"}),{dismiss:s,id:r,update:e=>u({toast:{...e,id:r},type:"UPDATE_TOAST"})}}function m(){let[e,t]=s.useState(l);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);-1!==e&&o.splice(e,1)}),[e]),{...e,dismiss:e=>u({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:d}}},59866:(e,t,r)=>{Promise.resolve().then(r.bind(r,40136))},83940:(e,t,r)=>{"use strict";r.d(t,{G7:()=>m,Gb:()=>o,JP:()=>l,Ok:()=>u,Qu:()=>d,iw:()=>c,oz:()=>p,z0:()=>h});var s=r(40879);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends i{entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}constructor(e){super(),this.config=e}}class n extends i{serviceRecordCreated(e,t){return this.success("Service Record Added","".concat(t,' service for "').concat(e,'" has been successfully logged.'))}serviceRecordUpdated(e,t){return this.success("Service Record Updated","".concat(t,' service for "').concat(e,'" has been updated.'))}serviceRecordDeleted(e,t){return this.success("Service Record Deleted","".concat(t,' service record for "').concat(e,'" has been permanently removed.'))}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function c(e){return new a(e)}function o(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:"".concat(e," Created"),description:t=>"The ".concat(e.toLowerCase(),' "').concat(t,'" has been successfully created.')},updated:{title:"".concat(e," Updated Successfully"),description:e=>"".concat(e," has been updated.")},deleted:{title:"".concat(e," Deleted Successfully"),description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create ".concat(e),description:t=>t||"An unexpected error occurred while creating the ".concat(e.toLowerCase(),".")},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the ".concat(e.toLowerCase(),".")},deletionError:{title:"Failed to Delete ".concat(e),description:t=>t||"An unexpected error occurred while deleting the ".concat(e.toLowerCase(),".")}}})}let l=new i,u=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>'The employee "'.concat(e,'" has been successfully created.')},updated:{title:"Employee Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Employee Deleted Successfully",description:e=>"".concat(e," has been permanently removed from the system.")},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),d=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>'The delegation "'.concat(e,'" has been successfully created.')},updated:{title:"Delegation Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Delegation Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),m=new a({entityName:"Vehicle",getDisplayName:e=>"".concat(e.make," ").concat(e.model),messages:{created:{title:"Vehicle Added",description:e=>'The vehicle "'.concat(e,'" has been successfully created.')},updated:{title:"Vehicle Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Vehicle Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>'The task "'.concat(e,'" has been successfully created.')},updated:{title:"Task Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Task Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),p=new n},94141:(e,t,r)=>{"use strict";r.d(t,{SY:()=>c});let s={maxAttempts:3,delay:1e3,exponentialBackoff:!0,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")||e.message.includes("502")||e.message.includes("503")||e.message.includes("504")},i={announceStatus:!0,focusManagement:"first-error",screenReaderAnnouncements:!0},a={debounceMs:300,enableDeduplication:!0,cacheResults:!1,timeoutMs:3e4},n={showSuccessToast:!0,showErrorToast:!0,successMessage:"Operation completed successfully",errorMessage:"An unexpected error occurred",entityType:"generic"};class c{static mergeRetryConfig(e){return{...s,...e}}static mergeAccessibilityConfig(e){return{...i,...e}}static mergePerformanceConfig(e){return{...a,...e}}static mergeToastConfig(e){return{...n,...e}}}},95647:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(95155);function i(e){let{children:t,description:r,icon:i,title:a}=e;return(0,s.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[i&&(0,s.jsx)(i,{className:"size-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:a})]}),r&&(0,s.jsx)("p",{className:"mt-1 text-muted-foreground",children:r})]}),t&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:t})]})}r(12115)}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,5669,4629,4036,4767,8950,8122,8441,1684,7358],()=>t(59866)),_N_E=e.O()}]);