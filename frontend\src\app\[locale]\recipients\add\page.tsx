'use client';

import { Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import type { CreateRecipientData } from '@/lib/types/domain';

import { RecipientForm } from '@/components/features/recipients';
import { PageHeader } from '@/components/ui/PageHeader';
import { useCreateRecipient } from '@/lib/stores/queries/useRecipients';

export default function AddRecipientPage() {
  const router = useRouter();
  const t = useTranslations('recipients');
  const tCommon = useTranslations('common');
  
  const { mutateAsync: createRecipient, isPending, error } = useCreateRecipient();

  const handleSubmit = async (data: CreateRecipientData) => {
    try {
      await createRecipient(data);
      router.push('/recipients');
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error creating recipient:', error);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        description={t('addRecipientDescription')}
        icon={Users}
        title={t('addRecipient')}
      />
      
      {error && (
        <div className="rounded-md bg-red-100 p-3 text-red-500">
          {tCommon('error')}: {error.message}
        </div>
      )}
      
      <RecipientForm
        isEditing={false}
        isLoading={isPending}
        onSubmit={handleSubmit}
      />
    </div>
  );
}
