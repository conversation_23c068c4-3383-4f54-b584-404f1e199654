{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/tslib/tslib.es6.mjs"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;8EAa8E,GAC9E,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC;IAC/B,gBAAgB,OAAO,cAAc,IAChC,CAAA;QAAE,WAAW,EAAE;IAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;IAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;QAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IACpG,OAAO,cAAc,GAAG;AAC1B;AAEO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC5B,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;IAC7D,cAAc,GAAG;IACjB,SAAS;QAAO,IAAI,CAAC,WAAW,GAAG;IAAG;IACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;AACrF;AAEO,IAAI,WAAW;IACpB,WAAW,OAAO,MAAM,IAAI,SAAS,SAAS,CAAC;QAC3C,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACT;AAEO,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;IACtD,IAAI,IAAI,UAAU,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,wBAAwB,CAAC,QAAQ,OAAO,MAAM;IAC3H,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,KAAK;SACpH,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK,IAAI,IAAI,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,IAAI,KAAK;IAChJ,OAAO,IAAI,KAAK,KAAK,OAAO,cAAc,CAAC,QAAQ,KAAK,IAAI;AAC9D;AAEO,SAAS,QAAQ,UAAU,EAAE,SAAS;IAC3C,OAAO,SAAU,MAAM,EAAE,GAAG;QAAI,UAAU,QAAQ,KAAK;IAAa;AACtE;AAEO,SAAS,aAAa,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB;IACrG,SAAS,OAAO,CAAC;QAAI,IAAI,MAAM,KAAK,KAAK,OAAO,MAAM,YAAY,MAAM,IAAI,UAAU;QAAsB,OAAO;IAAG;IACtH,IAAI,OAAO,UAAU,IAAI,EAAE,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;IACzF,IAAI,SAAS,CAAC,gBAAgB,OAAO,SAAS,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG;IACnF,IAAI,aAAa,gBAAgB,CAAC,SAAS,OAAO,wBAAwB,CAAC,QAAQ,UAAU,IAAI,IAAI,CAAC,CAAC;IACvG,IAAI,GAAG,OAAO;IACd,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC7C,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,KAAK,UAAW,OAAO,CAAC,EAAE,GAAG,MAAM,WAAW,CAAC,IAAI,SAAS,CAAC,EAAE;QACxE,IAAK,IAAI,KAAK,UAAU,MAAM,CAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,EAAE;QACvE,QAAQ,cAAc,GAAG,SAAU,CAAC;YAAI,IAAI,MAAM,MAAM,IAAI,UAAU;YAA2D,kBAAkB,IAAI,CAAC,OAAO,KAAK;QAAQ;QAC5K,IAAI,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,SAAS,aAAa;YAAE,KAAK,WAAW,GAAG;YAAE,KAAK,WAAW,GAAG;QAAC,IAAI,UAAU,CAAC,IAAI,EAAE;QACtH,IAAI,SAAS,YAAY;YACrB,IAAI,WAAW,KAAK,GAAG;YACvB,IAAI,WAAW,QAAQ,OAAO,WAAW,UAAU,MAAM,IAAI,UAAU;YACvE,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,IAAI,GAAG,aAAa,OAAO,CAAC;QACtD,OACK,IAAI,IAAI,OAAO,SAAS;YACzB,IAAI,SAAS,SAAS,aAAa,OAAO,CAAC;iBACtC,UAAU,CAAC,IAAI,GAAG;QAC3B;IACJ;IACA,IAAI,QAAQ,OAAO,cAAc,CAAC,QAAQ,UAAU,IAAI,EAAE;IAC1D,OAAO;AACT;;AAEO,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,KAAK;IAC5D,IAAI,WAAW,UAAU,MAAM,GAAG;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC1C,QAAQ,WAAW,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;IACnF;IACA,OAAO,WAAW,QAAQ,KAAK;AACjC;;AAEO,SAAS,UAAU,CAAC;IACzB,OAAO,OAAO,MAAM,WAAW,IAAI,GAAG,MAAM,CAAC;AAC/C;;AAEO,SAAS,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,WAAW,EAAE,OAAO;IAC5F,OAAO,OAAO,cAAc,CAAC,GAAG,QAAQ;QAAE,cAAc;QAAM,OAAO,SAAS,GAAG,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAAK;AACpH;;AAEO,SAAS,WAAW,WAAW,EAAE,aAAa;IACnD,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,OAAO,QAAQ,QAAQ,CAAC,aAAa;AAClH;AAEO,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACzD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACF;AAEO,SAAS,YAAY,OAAO,EAAE,IAAI;IACvC,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,aAAa,aAAa,WAAW,MAAM,EAAE,SAAS;IAC/L,OAAO,EAAE,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IAC1J,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACF;AAEO,IAAI,kBAAkB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAChE,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QAC/E,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAChE;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AAC/B,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACxB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AACd;AAEO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC/B,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,gBAAgB,GAAG,GAAG;AAC7G;AAEO,SAAS,SAAS,CAAC;IACxB,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACtD;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,KAAK,EAAE,EAAE,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAC3C,KAAK,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE;IACtC,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,MAAM,EAAE,IAAI,IAAI,IAAK,KAAK,SAAS,CAAC,EAAE,CAAC,MAAM;IACnF,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IACzC,IAAK,IAAI,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAC1D,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI;IAC1C,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACpD;AAEO,SAAS,QAAQ,CAAC;IACvB,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AACpE;AAEO,SAAS,iBAAiB,OAAO,EAAE,UAAU,EAAE,SAAS;IAC7D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,kBAAkB,aAAa,gBAAgB,MAAM,EAAE,SAAS,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACtN,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACnF;AAEO,SAAS,iBAAiB,CAAC;IAChC,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACvI;AAEO,SAAS,cAAc,CAAC;IAC7B,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC7H;AAEO,SAAS,qBAAqB,MAAM,EAAE,GAAG;IAC9C,IAAI,OAAO,cAAc,EAAE;QAAE,OAAO,cAAc,CAAC,QAAQ,OAAO;YAAE,OAAO;QAAI;IAAI,OAAO;QAAE,OAAO,GAAG,GAAG;IAAK;IAC9G,OAAO;AACT;;AAEA,IAAI,qBAAqB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC;IACrD,OAAO,cAAc,CAAC,GAAG,WAAW;QAAE,YAAY;QAAM,OAAO;IAAE;AACnE,IAAK,SAAS,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,UAAU,GAAG;AACjB;AAEA,IAAI,UAAU,SAAS,CAAC;IACtB,UAAU,OAAO,mBAAmB,IAAI,SAAU,CAAC;QACjD,IAAI,KAAK,EAAE;QACX,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG;QACjF,OAAO;IACT;IACA,OAAO,QAAQ;AACjB;AAEO,SAAS,aAAa,GAAG;IAC9B,IAAI,OAAO,IAAI,UAAU,EAAE,OAAO;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,MAAM;QAAA,IAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,KAAK,WAAW,gBAAgB,QAAQ,KAAK,CAAC,CAAC,EAAE;IAAC;IAChI,mBAAmB,QAAQ;IAC3B,OAAO;AACT;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,SAAS;IAAI;AACxD;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC7D,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACtF;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpE,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACtG;AAEO,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IACnD,IAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa,YAAa,MAAM,IAAI,UAAU;IAC/G,OAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,GAAG,CAAC;AACtE;AAEO,SAAS,wBAAwB,GAAG,EAAE,KAAK,EAAE,KAAK;IACvD,IAAI,UAAU,QAAQ,UAAU,KAAK,GAAG;QACtC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,MAAM,IAAI,UAAU;QAClF,IAAI,SAAS;QACb,IAAI,OAAO;YACT,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,IAAI,UAAU;YAC9C,UAAU,KAAK,CAAC,OAAO,YAAY,CAAC;QACtC;QACA,IAAI,YAAY,KAAK,GAAG;YACtB,IAAI,CAAC,OAAO,OAAO,EAAE,MAAM,IAAI,UAAU;YACzC,UAAU,KAAK,CAAC,OAAO,OAAO,CAAC;YAC/B,IAAI,OAAO,QAAQ;QACrB;QACA,IAAI,OAAO,YAAY,YAAY,MAAM,IAAI,UAAU;QACvD,IAAI,OAAO,UAAU;YAAa,IAAI;gBAAE,MAAM,IAAI,CAAC,IAAI;YAAG,EAAE,OAAO,GAAG;gBAAE,OAAO,QAAQ,MAAM,CAAC;YAAI;QAAE;QACpG,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;YAAO,SAAS;YAAS,OAAO;QAAM;IAChE,OACK,IAAI,OAAO;QACd,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;QAAK;IAC/B;IACA,OAAO;AACT;AAEA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IACnH,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AAEO,SAAS,mBAAmB,GAAG;IACpC,SAAS,KAAK,CAAC;QACb,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,IAAI,iBAAiB,GAAG,IAAI,KAAK,EAAE,8CAA8C;QAC5G,IAAI,QAAQ,GAAG;IACjB;IACA,IAAI,GAAG,IAAI;IACX,SAAS;QACP,MAAO,IAAI,IAAI,KAAK,CAAC,GAAG,GAAI;YAC1B,IAAI;gBACF,IAAI,CAAC,EAAE,KAAK,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,OAAO,GAAG,IAAI,CAAC;gBACjF,IAAI,EAAE,OAAO,EAAE;oBACb,IAAI,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK;oBACnC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC;wBAAI,KAAK;wBAAI,OAAO;oBAAQ;gBACvG,OACK,KAAK;YACZ,EACA,OAAO,GAAG;gBACR,KAAK;YACP;QACF;QACA,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,GAAG,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI,QAAQ,OAAO;QAC9E,IAAI,IAAI,QAAQ,EAAE,MAAM,IAAI,KAAK;IACnC;IACA,OAAO;AACT;AAEO,SAAS,iCAAiC,IAAI,EAAE,WAAW;IAChE,IAAI,OAAO,SAAS,YAAY,WAAW,IAAI,CAAC,OAAO;QACnD,OAAO,KAAK,OAAO,CAAC,oDAAoD,SAAU,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YAChG,OAAO,MAAM,cAAc,SAAS,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,MAAM,GAAG,WAAW,KAAK;QAC7G;IACJ;IACA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/fast-memoize/lib/index.js"], "sourcesContent": ["//\n// Main\n//\nexport function memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nexport var strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n"], "names": [], "mappings": "AAAA,EAAE;AACF,OAAO;AACP,EAAE;;;;;AACK,SAAS,QAAQ,EAAE,EAAE,OAAO;IAC/B,IAAI,QAAQ,WAAW,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;IACvD,IAAI,aAAa,WAAW,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG;IACtE,IAAI,WAAW,WAAW,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG;IAChE,OAAO,SAAS,IAAI;QAChB,OAAO;QACP,YAAY;IAChB;AACJ;AACA,EAAE;AACF,WAAW;AACX,EAAE;AACF,SAAS,YAAY,KAAK;IACtB,OAAQ,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,gEAAgE;AACvJ;AACA,SAAS,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IACvC,IAAI,WAAW,YAAY,OAAO,MAAM,WAAW;IACnD,IAAI,gBAAgB,MAAM,GAAG,CAAC;IAC9B,IAAI,OAAO,kBAAkB,aAAa;QACtC,gBAAgB,GAAG,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,GAAG,CAAC,UAAU;IACxB;IACA,OAAO;AACX;AACA,SAAS,SAAS,EAAE,EAAE,KAAK,EAAE,UAAU;IACnC,IAAI,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACjD,IAAI,WAAW,WAAW;IAC1B,IAAI,gBAAgB,MAAM,GAAG,CAAC;IAC9B,IAAI,OAAO,kBAAkB,aAAa;QACtC,gBAAgB,GAAG,KAAK,CAAC,IAAI,EAAE;QAC/B,MAAM,GAAG,CAAC,UAAU;IACxB;IACA,OAAO;AACX;AACA,SAAS,SAAS,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS;IACrD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,OAAO;AAC7C;AACA,SAAS,gBAAgB,EAAE,EAAE,OAAO;IAChC,IAAI,WAAW,GAAG,MAAM,KAAK,IAAI,UAAU;IAC3C,OAAO,SAAS,IAAI,IAAI,EAAE,UAAU,QAAQ,KAAK,CAAC,MAAM,IAAI,QAAQ,UAAU;AAClF;AACA,SAAS,iBAAiB,EAAE,EAAE,OAAO;IACjC,OAAO,SAAS,IAAI,IAAI,EAAE,UAAU,QAAQ,KAAK,CAAC,MAAM,IAAI,QAAQ,UAAU;AAClF;AACA,SAAS,gBAAgB,EAAE,EAAE,OAAO;IAChC,OAAO,SAAS,IAAI,IAAI,EAAE,SAAS,QAAQ,KAAK,CAAC,MAAM,IAAI,QAAQ,UAAU;AACjF;AACA,EAAE;AACF,aAAa;AACb,EAAE;AACF,IAAI,oBAAoB;IACpB,OAAO,KAAK,SAAS,CAAC;AAC1B;AACA,EAAE;AACF,QAAQ;AACR,EAAE;AACF,IAAI,8BAA6C;IAC7C,SAAS;QACL,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC;IAC/B;IACA,4BAA4B,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IAC1B;IACA,4BAA4B,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG,EAAE,KAAK;QAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACtB;IACA,OAAO;AACX;AACA,IAAI,eAAe;IACf,QAAQ,SAAS;QACb,OAAO,IAAI;IACf;AACJ;AACO,IAAI,aAAa;IACpB,UAAU;IACV,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/error.js"], "sourcesContent": ["export var ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n"], "names": [], "mappings": ";;;AAAO,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,qCAAqC,GACrC,SAAS,CAAC,SAAS,CAAC,gCAAgC,GAAG,EAAE,GAAG;IAC5D,mCAAmC,GACnC,SAAS,CAAC,SAAS,CAAC,iBAAiB,GAAG,EAAE,GAAG;IAC7C,2CAA2C,GAC3C,SAAS,CAAC,SAAS,CAAC,qBAAqB,GAAG,EAAE,GAAG;IACjD,4CAA4C,GAC5C,SAAS,CAAC,SAAS,CAAC,uBAAuB,GAAG,EAAE,GAAG;IACnD,iDAAiD,GACjD,SAAS,CAAC,SAAS,CAAC,wBAAwB,GAAG,EAAE,GAAG;IACpD,sDAAsD,GACtD,SAAS,CAAC,SAAS,CAAC,wBAAwB,GAAG,EAAE,GAAG;IACpD,oCAAoC,GACpC,SAAS,CAAC,SAAS,CAAC,0BAA0B,GAAG,EAAE,GAAG;IACtD,uCAAuC,GACvC,SAAS,CAAC,SAAS,CAAC,6BAA6B,GAAG,EAAE,GAAG;IACzD,2EAA2E,GAC3E,SAAS,CAAC,SAAS,CAAC,yBAAyB,GAAG,EAAE,GAAG;IACrD,4EAA4E,GAC5E,SAAS,CAAC,SAAS,CAAC,4BAA4B,GAAG,GAAG,GAAG;IACzD,6EAA6E,GAC7E,SAAS,CAAC,SAAS,CAAC,mCAAmC,GAAG,GAAG,GAAG;IAChE,2DAA2D,GAC3D,SAAS,CAAC,SAAS,CAAC,iCAAiC,GAAG,GAAG,GAAG;IAC9D,oGAAoG,GACpG,SAAS,CAAC,SAAS,CAAC,sCAAsC,GAAG,GAAG,GAAG;IACnE,6FAA6F,GAC7F,SAAS,CAAC,SAAS,CAAC,uCAAuC,GAAG,GAAG,GAAG;IACpE,oEAAoE,GACpE,SAAS,CAAC,SAAS,CAAC,kCAAkC,GAAG,GAAG,GAAG;IAC/D,uFAAuF,GACvF,SAAS,CAAC,SAAS,CAAC,kCAAkC,GAAG,GAAG,GAAG;IAC/D,2FAA2F,GAC3F,SAAS,CAAC,SAAS,CAAC,2CAA2C,GAAG,GAAG,GAAG;IACxE;;;KAGC,GACD,SAAS,CAAC,SAAS,CAAC,2CAA2C,GAAG,GAAG,GAAG;IACxE,wFAAwF,GACxF,SAAS,CAAC,SAAS,CAAC,mCAAmC,GAAG,GAAG,GAAG;IAChE;;;KAGC,GACD,SAAS,CAAC,SAAS,CAAC,qCAAqC,GAAG,GAAG,GAAG;IAClE;;KAEC,GACD,SAAS,CAAC,SAAS,CAAC,qCAAqC,GAAG,GAAG,GAAG;IAClE,+DAA+D,GAC/D,SAAS,CAAC,SAAS,CAAC,uBAAuB,GAAG,GAAG,GAAG;IACpD,qDAAqD,GACrD,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,GAAG,GAAG;IAC3C,qDAAqD,GACrD,SAAS,CAAC,SAAS,CAAC,mBAAmB,GAAG,GAAG,GAAG;IAChD,gFAAgF,GAChF,SAAS,CAAC,SAAS,CAAC,wBAAwB,GAAG,GAAG,GAAG;IACrD,kEAAkE,GAClE,SAAS,CAAC,SAAS,CAAC,eAAe,GAAG,GAAG,GAAG;AAChD,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/types.js"], "sourcesContent": ["export var TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nexport var SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nexport function isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexport function isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexport function isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexport function isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexport function isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexport function isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexport function isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexport function isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexport function isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexport function isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexport function isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexport function createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexport function createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAO,IAAI;AACX,CAAC,SAAU,IAAI;IACX;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,GAAG;IAC5B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,GAAG;IAC7B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG;IAC3B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;IACzB;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;IACzB;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG;IAC3B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG;IAC3B;;;KAGC,GACD,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG;AAC5B,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACd,IAAI;AACX,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE,GAAG;IAC7C,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,EAAE,GAAG;AACnD,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AAIhC,SAAS,iBAAiB,EAAE;IAC/B,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO;AACnC;AACO,SAAS,kBAAkB,EAAE;IAChC,OAAO,GAAG,IAAI,KAAK,KAAK,QAAQ;AACpC;AACO,SAAS,gBAAgB,EAAE;IAC9B,OAAO,GAAG,IAAI,KAAK,KAAK,MAAM;AAClC;AACO,SAAS,cAAc,EAAE;IAC5B,OAAO,GAAG,IAAI,KAAK,KAAK,IAAI;AAChC;AACO,SAAS,cAAc,EAAE;IAC5B,OAAO,GAAG,IAAI,KAAK,KAAK,IAAI;AAChC;AACO,SAAS,gBAAgB,EAAE;IAC9B,OAAO,GAAG,IAAI,KAAK,KAAK,MAAM;AAClC;AACO,SAAS,gBAAgB,EAAE;IAC9B,OAAO,GAAG,IAAI,KAAK,KAAK,MAAM;AAClC;AACO,SAAS,eAAe,EAAE;IAC7B,OAAO,GAAG,IAAI,KAAK,KAAK,KAAK;AACjC;AACO,SAAS,aAAa,EAAE;IAC3B,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B;AACO,SAAS,iBAAiB,EAAE;IAC/B,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,OAAO,YAAY,GAAG,IAAI,KAAK,cAAc,MAAM;AAC9E;AACO,SAAS,mBAAmB,EAAE;IACjC,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,OAAO,YAAY,GAAG,IAAI,KAAK,cAAc,QAAQ;AAChF;AACO,SAAS,qBAAqB,KAAK;IACtC,OAAO;QACH,MAAM,KAAK,OAAO;QAClB,OAAO;IACX;AACJ;AACO,SAAS,oBAAoB,KAAK,EAAE,KAAK;IAC5C,OAAO;QACH,MAAM,KAAK,MAAM;QACjB,OAAO;QACP,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/regex.generated.js"], "sourcesContent": ["// @generated from regex-gen.ts\nexport var SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;AACxB,IAAI,wBAAwB;AAC5B,IAAI,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-skeleton-parser/lib/date-time.js"], "sourcesContent": ["/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD,IAAI,kBAAkB;AAOf,SAAS,sBAAsB,QAAQ;IAC1C,IAAI,SAAS,CAAC;IACd,SAAS,OAAO,CAAC,iBAAiB,SAAU,KAAK;QAC7C,IAAI,MAAM,MAAM,MAAM;QACtB,OAAQ,KAAK,CAAC,EAAE;YACZ,MAAM;YACN,KAAK;gBACD,OAAO,GAAG,GAAG,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;gBACzD;YACJ,OAAO;YACP,KAAK;gBACD,OAAO,IAAI,GAAG,QAAQ,IAAI,YAAY;gBACtC;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,UAAU;YACV,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,QAAQ;YACR,KAAK;YACL,KAAK;gBACD,OAAO,KAAK,GAAG;oBAAC;oBAAW;oBAAW;oBAAS;oBAAQ;iBAAS,CAAC,MAAM,EAAE;gBACzE;YACJ,OAAO;YACP,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,KAAK;gBACD,OAAO,GAAG,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC5C;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,UAAU;YACV,KAAK;gBACD,OAAO,OAAO,GAAG,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;gBAC7D;YACJ,KAAK;gBACD,IAAI,MAAM,GAAG;oBACT,MAAM,IAAI,WAAW;gBACzB;gBACA,OAAO,OAAO,GAAG;oBAAC;oBAAS;oBAAQ;oBAAU;iBAAQ,CAAC,MAAM,EAAE;gBAC9D;YACJ,KAAK;gBACD,IAAI,MAAM,GAAG;oBACT,MAAM,IAAI,WAAW;gBACzB;gBACA,OAAO,OAAO,GAAG;oBAAC;oBAAS;oBAAQ;oBAAU;iBAAQ,CAAC,MAAM,EAAE;gBAC9D;YACJ,SAAS;YACT,KAAK;gBACD,OAAO,MAAM,GAAG;gBAChB;YACJ,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,OAAO;YACP,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,SAAS;YACT,KAAK;gBACD,OAAO,MAAM,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC/C;YACJ,SAAS;YACT,KAAK;gBACD,OAAO,MAAM,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC/C;YACJ,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,OAAO;YACP,KAAK;gBACD,OAAO,YAAY,GAAG,MAAM,IAAI,UAAU;gBAC1C;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;QAC7B;QACA,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-skeleton-parser/lib/regex.generated.js"], "sourcesContent": ["// @generated from regex-gen.ts\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;AACxB,IAAI,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-skeleton-parser/lib/number.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { WHITE_SPACE_REGEX } from './regex.generated';\nexport function parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nexport function parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = __assign(__assign(__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = __assign(__assign(__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = __assign(__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = __assign(__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = __assign(__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,8BAA8B,QAAQ;IAClD,IAAI,SAAS,MAAM,KAAK,GAAG;QACvB,MAAM,IAAI,MAAM;IACpB;IACA,qBAAqB;IACrB,IAAI,eAAe,SACd,KAAK,CAAC,oLAAA,CAAA,oBAAiB,EACvB,MAAM,CAAC,SAAU,CAAC;QAAI,OAAO,EAAE,MAAM,GAAG;IAAG;IAChD,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,KAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,MAAM,EAAE,KAAM;QAC9E,IAAI,cAAc,cAAc,CAAC,GAAG;QACpC,IAAI,iBAAiB,YAAY,KAAK,CAAC;QACvC,IAAI,eAAe,MAAM,KAAK,GAAG;YAC7B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,OAAO,cAAc,CAAC,EAAE,EAAE,UAAU,eAAe,KAAK,CAAC;QAC7D,IAAK,IAAI,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,MAAM,EAAE,KAAM;YAC/D,IAAI,SAAS,SAAS,CAAC,GAAG;YAC1B,IAAI,OAAO,MAAM,KAAK,GAAG;gBACrB,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,OAAO,IAAI,CAAC;YAAE,MAAM;YAAM,SAAS;QAAQ;IAC/C;IACA,OAAO;AACX;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,KAAK,OAAO,CAAC,WAAW;AACnC;AACA,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAClC,IAAI,sBAAsB;AAC1B,IAAI,8BAA8B;AAClC,SAAS,0BAA0B,GAAG;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,KAAK;QAC7B,OAAO,gBAAgB,GAAG;IAC9B,OACK,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,KAAK;QAClC,OAAO,gBAAgB,GAAG;IAC9B;IACA,IAAI,OAAO,CAAC,6BAA6B,SAAU,CAAC,EAAE,EAAE,EAAE,EAAE;QACxD,WAAW;QACX,IAAI,OAAO,OAAO,UAAU;YACxB,OAAO,wBAAwB,GAAG,GAAG,MAAM;YAC3C,OAAO,wBAAwB,GAAG,GAAG,MAAM;QAC/C,OAEK,IAAI,OAAO,KAAK;YACjB,OAAO,wBAAwB,GAAG,GAAG,MAAM;QAC/C,OAEK,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK;YACpB,OAAO,wBAAwB,GAAG,GAAG,MAAM;QAC/C,OAEK;YACD,OAAO,wBAAwB,GAAG,GAAG,MAAM;YAC3C,OAAO,wBAAwB,GAC3B,GAAG,MAAM,GAAG,CAAC,OAAO,OAAO,WAAW,GAAG,MAAM,GAAG,CAAC;QAC3D;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,UAAU,GAAG;IAClB,OAAQ;QACJ,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,cAAc;YAClB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;gBACb,cAAc;YAClB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;gBACb,cAAc;YAClB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;IACR;AACJ;AACA,SAAS,yCAAyC,IAAI;IAClD,cAAc;IACd,IAAI;IACJ,IAAI,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK;QACpC,SAAS;YACL,UAAU;QACd;QACA,OAAO,KAAK,KAAK,CAAC;IACtB,OACK,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;QACtB,SAAS;YACL,UAAU;QACd;QACA,OAAO,KAAK,KAAK,CAAC;IACtB;IACA,IAAI,QAAQ;QACR,IAAI,cAAc,KAAK,KAAK,CAAC,GAAG;QAChC,IAAI,gBAAgB,MAAM;YACtB,OAAO,WAAW,GAAG;YACrB,OAAO,KAAK,KAAK,CAAC;QACtB,OACK,IAAI,gBAAgB,MAAM;YAC3B,OAAO,WAAW,GAAG;YACrB,OAAO,KAAK,KAAK,CAAC;QACtB;QACA,IAAI,CAAC,4BAA4B,IAAI,CAAC,OAAO;YACzC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,oBAAoB,GAAG,KAAK,MAAM;IAC7C;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,GAAG;IAC7B,IAAI,SAAS,CAAC;IACd,IAAI,WAAW,UAAU;IACzB,IAAI,UAAU;QACV,OAAO;IACX;IACA,OAAO;AACX;AAIO,SAAS,oBAAoB,MAAM;IACtC,IAAI,SAAS,CAAC;IACd,IAAK,IAAI,KAAK,GAAG,WAAW,QAAQ,KAAK,SAAS,MAAM,EAAE,KAAM;QAC5D,IAAI,QAAQ,QAAQ,CAAC,GAAG;QACxB,OAAQ,MAAM,IAAI;YACd,KAAK;YACL,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf;YACJ,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf,OAAO,KAAK,GAAG;gBACf;YACJ,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf,OAAO,QAAQ,GAAG,MAAM,OAAO,CAAC,EAAE;gBAClC;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,qBAAqB,GAAG;gBAC/B;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf,OAAO,IAAI,GAAG,cAAc,MAAM,OAAO,CAAC,EAAE;gBAC5C;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,QAAQ,GAAG;gBAClB,OAAO,cAAc,GAAG;gBACxB;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,QAAQ,GAAG;gBAClB,OAAO,cAAc,GAAG;gBACxB;YACJ,KAAK;gBACD,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;oBAAE,UAAU;gBAAa,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;oBAAI,OAAQ,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,qBAAqB;gBAAQ,GAAG,CAAC;gBAC9L;YACJ,KAAK;gBACD,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;oBAAE,UAAU;gBAAc,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;oBAAI,OAAQ,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,qBAAqB;gBAAQ,GAAG,CAAC;gBAC/L;YACJ,KAAK;gBACD,OAAO,QAAQ,GAAG;gBAClB;YACJ,8FAA8F;YAC9F,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB;YACJ,KAAK;gBACD,OAAO,KAAK,GAAG,WAAW,MAAM,OAAO,CAAC,EAAE;gBAC1C;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,gGAAgG;YAChG,KAAK;gBACD,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;oBAC1B,MAAM,IAAI,WAAW;gBACzB;gBACA,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,qBAAqB,SAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;oBACzE,IAAI,IAAI;wBACJ,OAAO,oBAAoB,GAAG,GAAG,MAAM;oBAC3C,OACK,IAAI,MAAM,IAAI;wBACf,MAAM,IAAI,MAAM;oBACpB,OACK,IAAI,IAAI;wBACT,MAAM,IAAI,MAAM;oBACpB;oBACA,OAAO;gBACX;gBACA;QACR;QACA,gGAAgG;QAChG,IAAI,4BAA4B,IAAI,CAAC,MAAM,IAAI,GAAG;YAC9C,OAAO,oBAAoB,GAAG,MAAM,IAAI,CAAC,MAAM;YAC/C;QACJ;QACA,IAAI,yBAAyB,IAAI,CAAC,MAAM,IAAI,GAAG;YAC3C,YAAY;YACZ,qGAAqG;YACrG,yBAAyB;YACzB,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,WAAW;YACzB;YACA,MAAM,IAAI,CAAC,OAAO,CAAC,0BAA0B,SAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBACxE,yCAAyC;gBACzC,IAAI,OAAO,KAAK;oBACZ,OAAO,qBAAqB,GAAG,GAAG,MAAM;gBAC5C,OAEK,IAAI,MAAM,EAAE,CAAC,EAAE,KAAK,KAAK;oBAC1B,OAAO,qBAAqB,GAAG,GAAG,MAAM;gBAC5C,OAEK,IAAI,MAAM,IAAI;oBACf,OAAO,qBAAqB,GAAG,GAAG,MAAM;oBACxC,OAAO,qBAAqB,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM;gBACxD,OACK;oBACD,OAAO,qBAAqB,GAAG,GAAG,MAAM;oBACxC,OAAO,qBAAqB,GAAG,GAAG,MAAM;gBAC5C;gBACA,OAAO;YACX;YACA,IAAI,MAAM,MAAM,OAAO,CAAC,EAAE;YAC1B,wGAAwG;YACxG,IAAI,QAAQ,KAAK;gBACb,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;oBAAE,qBAAqB;gBAAiB;YACpF,OACK,IAAI,KAAK;gBACV,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS,0BAA0B;YACtE;YACA;QACJ;QACA,+GAA+G;QAC/G,IAAI,4BAA4B,IAAI,CAAC,MAAM,IAAI,GAAG;YAC9C,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS,0BAA0B,MAAM,IAAI;YAC5E;QACJ;QACA,IAAI,WAAW,UAAU,MAAM,IAAI;QACnC,IAAI,UAAU;YACV,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;QAC5C;QACA,IAAI,sCAAsC,yCAAyC,MAAM,IAAI;QAC7F,IAAI,qCAAqC;YACrC,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;QAC5C;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-skeleton-parser/lib/index.js"], "sourcesContent": ["export * from './date-time';\nexport * from './number';\n"], "names": [], "mappings": ";AAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/time-data.generated.js"], "sourcesContent": ["// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,oBAAoB;;;;AACb,IAAI,WAAW;IAClB,OAAO;QACH;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,UAAU;QACN;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,UAAU;QACN;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js"], "sourcesContent": ["import { timeData } from './time-data.generated';\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nexport function getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = timeData[regionTag || ''] ||\n        timeData[languageTag || ''] ||\n        timeData[\"\".concat(languageTag, \"-001\")] ||\n        timeData['001'];\n    return hourCycles[0];\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,SAAS,eAAe,QAAQ,EAAE,MAAM;IAC3C,IAAI,eAAe;IACnB,IAAK,IAAI,aAAa,GAAG,aAAa,SAAS,MAAM,EAAE,aAAc;QACjE,IAAI,cAAc,SAAS,MAAM,CAAC;QAClC,IAAI,gBAAgB,KAAK;YACrB,IAAI,cAAc;YAClB,MAAO,aAAa,IAAI,SAAS,MAAM,IACnC,SAAS,MAAM,CAAC,aAAa,OAAO,YAAa;gBACjD;gBACA;YACJ;YACA,IAAI,UAAU,IAAI,CAAC,cAAc,CAAC;YAClC,IAAI,eAAe,cAAc,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC;YAC9D,IAAI,gBAAgB;YACpB,IAAI,WAAW,+BAA+B;YAC9C,IAAI,YAAY,OAAO,YAAY,KAAK;gBACpC,eAAe;YACnB;YACA,MAAO,iBAAiB,EAAG;gBACvB,gBAAgB;YACpB;YACA,MAAO,YAAY,EAAG;gBAClB,eAAe,WAAW;YAC9B;QACJ,OACK,IAAI,gBAAgB,KAAK;YAC1B,gBAAgB;QACpB,OACK;YACD,gBAAgB;QACpB;IACJ;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,+BAA+B,MAAM;IAC1C,IAAI,YAAY,OAAO,SAAS;IAChC,IAAI,cAAc,aACd,gDAAgD;IAChD,OAAO,UAAU,IACjB,aAAa;IACb,OAAO,UAAU,CAAC,MAAM,EAAE;QAC1B,aAAa;QACb,YAAY,OAAO,UAAU,CAAC,EAAE;IACpC;IACA,IAAI,WAAW;QACX,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,MAAM,IAAI,MAAM;QACxB;IACJ;IACA,oFAAoF;IACpF,IAAI,cAAc,OAAO,QAAQ;IACjC,IAAI;IACJ,IAAI,gBAAgB,QAAQ;QACxB,YAAY,OAAO,QAAQ,GAAG,MAAM;IACxC;IACA,IAAI,aAAa,gMAAA,CAAA,WAAQ,CAAC,aAAa,GAAG,IACtC,gMAAA,CAAA,WAAQ,CAAC,eAAe,GAAG,IAC3B,gMAAA,CAAA,WAAQ,CAAC,GAAG,MAAM,CAAC,aAAa,QAAQ,IACxC,gMAAA,CAAA,WAAQ,CAAC,MAAM;IACnB,OAAO,UAAU,CAAC,EAAE;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/parser.js"], "sourcesContent": ["var _a;\nimport { __assign } from \"tslib\";\nimport { <PERSON>rrorKind } from './error';\nimport { SKELETON_TYPE, TYPE, } from './types';\nimport { SPACE_SEPARATOR_REGEX } from './regex.generated';\nimport { parseNumberSkeleton, parseNumberSkeletonFromString, parseDateTimeSkeleton, } from '@formatjs/icu-skeleton-parser';\nimport { getBestPattern } from './date-time-pattern-generator';\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = getBestPattern(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? parseDateTimeSkeleton(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? TYPE.date : TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? TYPE.number\n                            : argType === 'date'\n                                ? TYPE.date\n                                : TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = parseNumberSkeletonFromString(skeleton);\n        }\n        catch (e) {\n            return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? parseNumberSkeleton(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA,IAAI;;;;;;;AAOJ,IAAI,8BAA8B,IAAI,OAAO,IAAI,MAAM,CAAC,yLAAA,CAAA,wBAAqB,CAAC,MAAM,EAAE;AACtF,IAAI,4BAA4B,IAAI,OAAO,GAAG,MAAM,CAAC,yLAAA,CAAA,wBAAqB,CAAC,MAAM,EAAE;AACnF,SAAS,eAAe,KAAK,EAAE,GAAG;IAC9B,OAAO;QAAE,OAAO;QAAO,KAAK;IAAI;AACpC;AACA,oBAAoB;AACpB,0EAA0E;AAC1E,IAAI,sBAAsB,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,IAAI,KAAK,UAAU,CAAC,KAAK;AAChF,IAAI,yBAAyB,CAAC,CAAC,OAAO,aAAa;AACnD,IAAI,uBAAuB,CAAC,CAAC,OAAO,WAAW;AAC/C,IAAI,uBAAuB,CAAC,CAAC,OAAO,SAAS,CAAC,WAAW;AACzD,IAAI,eAAe,CAAC,CAAC,OAAO,SAAS,CAAC,SAAS;AAC/C,IAAI,aAAa,CAAC,CAAC,OAAO,SAAS,CAAC,OAAO;AAC3C,IAAI,yBAAyB,CAAC,CAAC,OAAO,aAAa;AACnD,IAAI,gBAAgB,yBACd,OAAO,aAAa,GACpB,SAAU,CAAC;IACT,OAAQ,OAAO,MAAM,YACjB,SAAS,MACT,KAAK,KAAK,CAAC,OAAO,KAClB,KAAK,GAAG,CAAC,MAAM;AACvB;AACJ,iCAAiC;AACjC,IAAI,yBAAyB;AAC7B,IAAI;IACA,IAAI,KAAK,GAAG,6CAA6C;IACzD;;;;;KAKC,GACD,yBAAyB,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,MAAM;AAClG,EACA,OAAO,GAAG;IACN,yBAAyB;AAC7B;AACA,IAAI,aAAa,sBAET,SAAS,WAAW,CAAC,EAAE,MAAM,EAAE,QAAQ;IACnC,OAAO,EAAE,UAAU,CAAC,QAAQ;AAChC,IAEA,SAAS,WAAW,CAAC,EAAE,MAAM,EAAE,QAAQ;IACnC,OAAO,EAAE,KAAK,CAAC,UAAU,WAAW,OAAO,MAAM,MAAM;AAC3D;AACR,IAAI,gBAAgB,yBACd,OAAO,aAAa,GAElB,SAAS;IACL,IAAI,aAAa,EAAE;IACnB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,UAAU,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IAClC;IACA,IAAI,WAAW;IACf,IAAI,SAAS,WAAW,MAAM;IAC9B,IAAI,IAAI;IACR,IAAI;IACJ,MAAO,SAAS,EAAG;QACf,OAAO,UAAU,CAAC,IAAI;QACtB,IAAI,OAAO,UACP,MAAM,WAAW,OAAO;QAC5B,YACI,OAAO,UACD,OAAO,YAAY,CAAC,QACpB,OAAO,YAAY,CAAC,CAAC,CAAC,QAAQ,OAAO,KAAK,EAAE,IAAI,QAAQ,AAAC,OAAO,QAAS;IACvF;IACA,OAAO;AACX;AACR,IAAI,cACJ,SAAS;AACT,uBACM,OAAO,WAAW,GAEhB,SAAS,YAAY,OAAO;IACxB,IAAI,MAAM,CAAC;IACX,IAAK,IAAI,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,MAAM,EAAE,KAAM;QAC/D,IAAI,KAAK,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5C,GAAG,CAAC,EAAE,GAAG;IACb;IACA,OAAO;AACX;AACR,IAAI,cAAc,uBAEV,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,OAAO,EAAE,WAAW,CAAC;AACzB,IAEA,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,IAAI,OAAO,EAAE,MAAM;IACnB,IAAI,QAAQ,KAAK,SAAS,MAAM;QAC5B,OAAO;IACX;IACA,IAAI,QAAQ,EAAE,UAAU,CAAC;IACzB,IAAI;IACJ,OAAO,QAAQ,UACX,QAAQ,UACR,QAAQ,MAAM,QACd,CAAC,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,IAAI,UACrC,SAAS,SACP,QACA,CAAC,AAAC,QAAQ,UAAW,EAAE,IAAI,CAAC,SAAS,MAAM,IAAI;AACzD;AACR,IAAI,YAAY,eAER,SAAS,UAAU,CAAC;IAChB,OAAO,EAAE,SAAS;AACtB,IAEA,SAAS,UAAU,CAAC;IAChB,OAAO,EAAE,OAAO,CAAC,6BAA6B;AAClD;AACR,IAAI,UAAU,aAEN,SAAS,QAAQ,CAAC;IACd,OAAO,EAAE,OAAO;AACpB,IAEA,SAAS,QAAQ,CAAC;IACd,OAAO,EAAE,OAAO,CAAC,2BAA2B;AAChD;AACR,kGAAkG;AAClG,SAAS,GAAG,CAAC,EAAE,IAAI;IACf,OAAO,IAAI,OAAO,GAAG;AACzB;AACA,aAAa;AACb,IAAI;AACJ,IAAI,wBAAwB;IACxB,SAAS;IACT,IAAI,yBAAyB,GAAG,6CAA6C;IAC7E,yBAAyB,SAAS,uBAAuB,CAAC,EAAE,KAAK;QAC7D,IAAI;QACJ,uBAAuB,SAAS,GAAG;QACnC,IAAI,QAAQ,uBAAuB,IAAI,CAAC;QACxC,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC5D;AACJ,OACK;IACD,OAAO;IACP,yBAAyB,SAAS,uBAAuB,CAAC,EAAE,KAAK;QAC7D,IAAI,QAAQ,EAAE;QACd,MAAO,KAAM;YACT,IAAI,IAAI,YAAY,GAAG;YACvB,IAAI,MAAM,aAAa,cAAc,MAAM,iBAAiB,IAAI;gBAC5D;YACJ;YACA,MAAM,IAAI,CAAC;YACX,SAAS,KAAK,UAAU,IAAI;QAChC;QACA,OAAO,cAAc,KAAK,CAAC,KAAK,GAAG;IACvC;AACJ;AACA,IAAI,SAAwB;IACxB,SAAS,OAAO,OAAO,EAAE,OAAO;QAC5B,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU,CAAC;QAAG;QACxC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;YAAE,QAAQ;YAAG,MAAM;YAAG,QAAQ;QAAE;QAChD,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,SAAS;QACpC,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;QAC5B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,QAAQ,mBAAmB;QACxD,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,QAAQ,oBAAoB;IAC9D;IACA,OAAO,SAAS,CAAC,KAAK,GAAG;QACrB,IAAI,IAAI,CAAC,MAAM,OAAO,GAAG;YACrB,MAAM,MAAM;QAChB;QACA,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI;IACpC;IACA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY,EAAE,aAAa,EAAE,iBAAiB;QACpF,IAAI,WAAW,EAAE;QACjB,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,OAAO,IAAI,CAAC,IAAI;YACpB,IAAI,SAAS,IAAI,OAAO,KAAI;gBACxB,IAAI,SAAS,IAAI,CAAC,aAAa,CAAC,cAAc;gBAC9C,IAAI,OAAO,GAAG,EAAE;oBACZ,OAAO;gBACX;gBACA,SAAS,IAAI,CAAC,OAAO,GAAG;YAC5B,OACK,IAAI,SAAS,IAAI,OAAO,OAAM,eAAe,GAAG;gBACjD;YACJ,OACK,IAAI,SAAS,GAAG,OAAO,OACxB,CAAC,kBAAkB,YAAY,kBAAkB,eAAe,GAAG;gBACnE,IAAI,WAAW,IAAI,CAAC,aAAa;gBACjC,IAAI,CAAC,IAAI;gBACT,SAAS,IAAI,CAAC;oBACV,MAAM,4KAAA,CAAA,OAAI,CAAC,KAAK;oBAChB,UAAU,eAAe,UAAU,IAAI,CAAC,aAAa;gBACzD;YACJ,OACK,IAAI,SAAS,GAAG,OAAO,OACxB,CAAC,IAAI,CAAC,SAAS,IACf,IAAI,CAAC,IAAI,OAAO,GAAG,oBAAoB;cACzC;gBACE,IAAI,mBAAmB;oBACnB;gBACJ,OACK;oBACD,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;gBAC9G;YACJ,OACK,IAAI,SAAS,GAAG,OAAO,OACxB,CAAC,IAAI,CAAC,SAAS,IACf,SAAS,IAAI,CAAC,IAAI,MAAM,IAAI;gBAC5B,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,cAAc;gBACzC,IAAI,OAAO,GAAG,EAAE;oBACZ,OAAO;gBACX;gBACA,SAAS,IAAI,CAAC,OAAO,GAAG;YAC5B,OACK;gBACD,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,cAAc;gBAC7C,IAAI,OAAO,GAAG,EAAE;oBACZ,OAAO;gBACX;gBACA,SAAS,IAAI,CAAC,OAAO,GAAG;YAC5B;QACJ;QACA,OAAO;YAAE,KAAK;YAAU,KAAK;QAAK;IACtC;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAU,YAAY,EAAE,aAAa;QAC7D,IAAI,gBAAgB,IAAI,CAAC,aAAa;QACtC,IAAI,CAAC,IAAI,IAAI,MAAM;QACnB,IAAI,UAAU,IAAI,CAAC,YAAY;QAC/B,IAAI,CAAC,SAAS;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO;YACnB,mBAAmB;YACnB,OAAO;gBACH,KAAK;oBACD,MAAM,4KAAA,CAAA,OAAI,CAAC,OAAO;oBAClB,OAAO,IAAI,MAAM,CAAC,SAAS;oBAC3B,UAAU,eAAe,eAAe,IAAI,CAAC,aAAa;gBAC9D;gBACA,KAAK;YACT;QACJ,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;YACvB,IAAI,iBAAiB,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,eAAe;YACxE,IAAI,eAAe,GAAG,EAAE;gBACpB,OAAO;YACX;YACA,IAAI,WAAW,eAAe,GAAG;YACjC,wBAAwB;YACxB,IAAI,sBAAsB,IAAI,CAAC,aAAa;YAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO;gBACnB,IAAI,IAAI,CAAC,KAAK,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,KAAK;oBACxC,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,WAAW,EAAE,eAAe,qBAAqB,IAAI,CAAC,aAAa;gBACnG;gBACA,IAAI,8BAA8B,IAAI,CAAC,aAAa;gBACpD,IAAI,iBAAiB,IAAI,CAAC,YAAY;gBACtC,IAAI,YAAY,gBAAgB;oBAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,6BAA6B,IAAI,CAAC,aAAa;gBACrH;gBACA,IAAI,CAAC,SAAS;gBACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;oBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,WAAW,EAAE,eAAe,qBAAqB,IAAI,CAAC,aAAa;gBACnG;gBACA,OAAO;oBACH,KAAK;wBACD,MAAM,4KAAA,CAAA,OAAI,CAAC,GAAG;wBACd,OAAO;wBACP,UAAU;wBACV,UAAU,eAAe,eAAe,IAAI,CAAC,aAAa;oBAC9D;oBACA,KAAK;gBACT;YACJ,OACK;gBACD,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,YAAY,EAAE,eAAe,eAAe,IAAI,CAAC,aAAa;YAC9F;QACJ,OACK;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,WAAW,EAAE,eAAe,eAAe,IAAI,CAAC,aAAa;QAC7F;IACJ;IACA;;KAEC,GACD,OAAO,SAAS,CAAC,YAAY,GAAG;QAC5B,IAAI,cAAc,IAAI,CAAC,MAAM;QAC7B,IAAI,CAAC,IAAI,IAAI,+BAA+B;QAC5C,MAAO,CAAC,IAAI,CAAC,KAAK,MAAM,4BAA4B,IAAI,CAAC,IAAI,IAAK;YAC9D,IAAI,CAAC,IAAI;QACb;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,MAAM;IACtD;IACA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY,EAAE,aAAa;QACjE,IAAI,QAAQ,IAAI,CAAC,aAAa;QAC9B,IAAI,QAAQ;QACZ,MAAO,KAAM;YACT,IAAI,mBAAmB,IAAI,CAAC,aAAa,CAAC;YAC1C,IAAI,kBAAkB;gBAClB,SAAS;gBACT;YACJ;YACA,IAAI,sBAAsB,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAC9D,IAAI,qBAAqB;gBACrB,SAAS;gBACT;YACJ;YACA,IAAI,uBAAuB,IAAI,CAAC,wBAAwB;YACxD,IAAI,sBAAsB;gBACtB,SAAS;gBACT;YACJ;YACA;QACJ;QACA,IAAI,WAAW,eAAe,OAAO,IAAI,CAAC,aAAa;QACvD,OAAO;YACH,KAAK;gBAAE,MAAM,4KAAA,CAAA,OAAI,CAAC,OAAO;gBAAE,OAAO;gBAAO,UAAU;YAAS;YAC5D,KAAK;QACT;IACJ;IACA,OAAO,SAAS,CAAC,wBAAwB,GAAG;QACxC,IAAI,CAAC,IAAI,CAAC,KAAK,MACX,IAAI,CAAC,IAAI,OAAO,GAAG,OAAO,OAC1B,CAAC,IAAI,CAAC,SAAS,IACX,uDAAuD;QACvD,CAAC,gBAAgB,IAAI,CAAC,IAAI,MAAM,EAAE,GAAG;YACzC,IAAI,CAAC,IAAI,IAAI,MAAM;YACnB,OAAO;QACX;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,aAAa,GAAG,SAAU,aAAa;QACpD,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,IAAI,OAAO,GAAG,OAAO,KAAI;YAC9C,OAAO;QACX;QACA,4FAA4F;QAC5F,sCAAsC;QACtC,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK,GAAG,OAAO;gBACX,iDAAiD;gBACjD,IAAI,CAAC,IAAI;gBACT,IAAI,CAAC,IAAI;gBACT,OAAO;YACX,qBAAqB;YACrB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;gBACD,IAAI,kBAAkB,YAAY,kBAAkB,iBAAiB;oBACjE;gBACJ;gBACA,OAAO;YACX;gBACI,OAAO;QACf;QACA,IAAI,CAAC,IAAI,IAAI,aAAa;QAC1B,IAAI,aAAa;YAAC,IAAI,CAAC,IAAI;SAAG,EAAE,eAAe;QAC/C,IAAI,CAAC,IAAI;QACT,4DAA4D;QAC5D,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,KAAK,IAAI,CAAC,IAAI;YAClB,IAAI,OAAO,GAAG,OAAO,KAAI;gBACrB,IAAI,IAAI,CAAC,IAAI,OAAO,GAAG,OAAO,KAAI;oBAC9B,WAAW,IAAI,CAAC;oBAChB,2DAA2D;oBAC3D,IAAI,CAAC,IAAI;gBACb,OACK;oBACD,+BAA+B;oBAC/B,IAAI,CAAC,IAAI;oBACT;gBACJ;YACJ,OACK;gBACD,WAAW,IAAI,CAAC;YACpB;YACA,IAAI,CAAC,IAAI;QACb;QACA,OAAO,cAAc,KAAK,CAAC,KAAK,GAAG;IACvC;IACA,OAAO,SAAS,CAAC,gBAAgB,GAAG,SAAU,YAAY,EAAE,aAAa;QACrE,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO;QACX;QACA,IAAI,KAAK,IAAI,CAAC,IAAI;QAClB,IAAI,OAAO,GAAG,OAAO,OACjB,OAAO,IAAI,OAAO,OACjB,OAAO,GAAG,OAAO,OACd,CAAC,kBAAkB,YAAY,kBAAkB,eAAe,KACnE,OAAO,IAAI,OAAO,OAAM,eAAe,GAAI;YAC5C,OAAO;QACX,OACK;YACD,IAAI,CAAC,IAAI;YACT,OAAO,cAAc;QACzB;IACJ;IACA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAU,YAAY,EAAE,iBAAiB;QACtE,IAAI,uBAAuB,IAAI,CAAC,aAAa;QAC7C,IAAI,CAAC,IAAI,IAAI,MAAM;QACnB,IAAI,CAAC,SAAS;QACd,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACtH;QACA,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,OAAO,KAAI;YAC/B,IAAI,CAAC,IAAI;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,cAAc,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACvG;QACA,gBAAgB;QAChB,IAAI,QAAQ,IAAI,CAAC,yBAAyB,GAAG,KAAK;QAClD,IAAI,CAAC,OAAO;YACR,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,kBAAkB,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QAC3G;QACA,IAAI,CAAC,SAAS;QACd,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACtH;QACA,OAAQ,IAAI,CAAC,IAAI;YACb,4BAA4B;YAC5B,KAAK,IAAI,OAAO;gBAAI;oBAChB,IAAI,CAAC,IAAI,IAAI,MAAM;oBACnB,OAAO;wBACH,KAAK;4BACD,MAAM,4KAAA,CAAA,OAAI,CAAC,QAAQ;4BACnB,yDAAyD;4BACzD,OAAO;4BACP,UAAU,eAAe,sBAAsB,IAAI,CAAC,aAAa;wBACrE;wBACA,KAAK;oBACT;gBACJ;YACA,+CAA+C;YAC/C,KAAK,GAAG,OAAO;gBAAI;oBACf,IAAI,CAAC,IAAI,IAAI,MAAM;oBACnB,IAAI,CAAC,SAAS;oBACd,IAAI,IAAI,CAAC,KAAK,IAAI;wBACd,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;oBACtH;oBACA,OAAO,IAAI,CAAC,oBAAoB,CAAC,cAAc,mBAAmB,OAAO;gBAC7E;YACA;gBACI,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,kBAAkB,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QAC/G;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,yBAAyB,GAAG;QACzC,IAAI,mBAAmB,IAAI,CAAC,aAAa;QACzC,IAAI,cAAc,IAAI,CAAC,MAAM;QAC7B,IAAI,QAAQ,uBAAuB,IAAI,CAAC,OAAO,EAAE;QACjD,IAAI,YAAY,cAAc,MAAM,MAAM;QAC1C,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,cAAc,IAAI,CAAC,aAAa;QACpC,IAAI,WAAW,eAAe,kBAAkB;QAChD,OAAO;YAAE,OAAO;YAAO,UAAU;QAAS;IAC9C;IACA,OAAO,SAAS,CAAC,oBAAoB,GAAG,SAAU,YAAY,EAAE,iBAAiB,EAAE,KAAK,EAAE,oBAAoB;QAC1G,IAAI;QACJ,oBAAoB;QACpB,sBAAsB;QACtB,eAAe;QACf,IAAI,oBAAoB,IAAI,CAAC,aAAa;QAC1C,IAAI,UAAU,IAAI,CAAC,yBAAyB,GAAG,KAAK;QACpD,IAAI,kBAAkB,IAAI,CAAC,aAAa;QACxC,OAAQ;YACJ,KAAK;gBACD,iFAAiF;gBACjF,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE,eAAe,mBAAmB;YACxF,KAAK;YACL,KAAK;YACL,KAAK;gBAAQ;oBACT,oBAAoB;oBACpB,wBAAwB;oBACxB,yBAAyB;oBACzB,IAAI,CAAC,SAAS;oBACd,IAAI,mBAAmB;oBACvB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;wBAClB,IAAI,CAAC,SAAS;wBACd,IAAI,qBAAqB,IAAI,CAAC,aAAa;wBAC3C,IAAI,SAAS,IAAI,CAAC,6BAA6B;wBAC/C,IAAI,OAAO,GAAG,EAAE;4BACZ,OAAO;wBACX;wBACA,IAAI,QAAQ,QAAQ,OAAO,GAAG;wBAC9B,IAAI,MAAM,MAAM,KAAK,GAAG;4BACpB,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;wBAC9G;wBACA,IAAI,gBAAgB,eAAe,oBAAoB,IAAI,CAAC,aAAa;wBACzE,mBAAmB;4BAAE,OAAO;4BAAO,eAAe;wBAAc;oBACpE;oBACA,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;oBAChD,IAAI,eAAe,GAAG,EAAE;wBACpB,OAAO;oBACX;oBACA,IAAI,aAAa,eAAe,sBAAsB,IAAI,CAAC,aAAa;oBACxE,4BAA4B;oBAC5B,IAAI,oBAAoB,WAAW,qBAAqB,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,KAAK,EAAE,MAAM,IAAI;wBACrI,6BAA6B;wBAC7B,IAAI,WAAW,UAAU,iBAAiB,KAAK,CAAC,KAAK,CAAC;wBACtD,IAAI,YAAY,UAAU;4BACtB,IAAI,SAAS,IAAI,CAAC,6BAA6B,CAAC,UAAU,iBAAiB,aAAa;4BACxF,IAAI,OAAO,GAAG,EAAE;gCACZ,OAAO;4BACX;4BACA,OAAO;gCACH,KAAK;oCAAE,MAAM,4KAAA,CAAA,OAAI,CAAC,MAAM;oCAAE,OAAO;oCAAO,UAAU;oCAAY,OAAO,OAAO,GAAG;gCAAC;gCAChF,KAAK;4BACT;wBACJ,OACK;4BACD,IAAI,SAAS,MAAM,KAAK,GAAG;gCACvB,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,yBAAyB,EAAE;4BAC3D;4BACA,IAAI,kBAAkB;4BACtB,oEAAoE;4BACpE,iEAAiE;4BACjE,4BAA4B;4BAC5B,IAAI,IAAI,CAAC,MAAM,EAAE;gCACb,kBAAkB,CAAA,GAAA,2MAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,IAAI,CAAC,MAAM;4BAC1D;4BACA,IAAI,QAAQ;gCACR,MAAM,4KAAA,CAAA,gBAAa,CAAC,QAAQ;gCAC5B,SAAS;gCACT,UAAU,iBAAiB,aAAa;gCACxC,eAAe,IAAI,CAAC,oBAAoB,GAClC,CAAA,GAAA,8KAAA,CAAA,wBAAqB,AAAD,EAAE,mBACtB,CAAC;4BACX;4BACA,IAAI,OAAO,YAAY,SAAS,4KAAA,CAAA,OAAI,CAAC,IAAI,GAAG,4KAAA,CAAA,OAAI,CAAC,IAAI;4BACrD,OAAO;gCACH,KAAK;oCAAE,MAAM;oCAAM,OAAO;oCAAO,UAAU;oCAAY,OAAO;gCAAM;gCACpE,KAAK;4BACT;wBACJ;oBACJ;oBACA,6BAA6B;oBAC7B,OAAO;wBACH,KAAK;4BACD,MAAM,YAAY,WACZ,4KAAA,CAAA,OAAI,CAAC,MAAM,GACX,YAAY,SACR,4KAAA,CAAA,OAAI,CAAC,IAAI,GACT,4KAAA,CAAA,OAAI,CAAC,IAAI;4BACnB,OAAO;4BACP,UAAU;4BACV,OAAO,CAAC,KAAK,qBAAqB,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBAC9I;wBACA,KAAK;oBACT;gBACJ;YACA,KAAK;YACL,KAAK;YACL,KAAK;gBAAU;oBACX,oBAAoB;oBACpB,0BAA0B;oBAC1B,2BAA2B;oBAC3B,IAAI,oBAAoB,IAAI,CAAC,aAAa;oBAC1C,IAAI,CAAC,SAAS;oBACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;wBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,8BAA8B,EAAE,eAAe,mBAAmB,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG;oBAC/G;oBACA,IAAI,CAAC,SAAS;oBACd,gBAAgB;oBAChB,oCAAoC;oBACpC,yBAAyB;oBACzB,EAAE;oBACF,uBAAuB;oBACvB,EAAE;oBACF,wCAAwC;oBACxC,sBAAsB;oBACtB,IAAI,wBAAwB,IAAI,CAAC,yBAAyB;oBAC1D,IAAI,eAAe;oBACnB,IAAI,YAAY,YAAY,sBAAsB,KAAK,KAAK,UAAU;wBAClE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;4BACnB,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,mCAAmC,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;wBAC5H;wBACA,IAAI,CAAC,SAAS;wBACd,IAAI,SAAS,IAAI,CAAC,sBAAsB,CAAC,4KAAA,CAAA,YAAS,CAAC,mCAAmC,EAAE,4KAAA,CAAA,YAAS,CAAC,oCAAoC;wBACtI,IAAI,OAAO,GAAG,EAAE;4BACZ,OAAO;wBACX;wBACA,8CAA8C;wBAC9C,IAAI,CAAC,SAAS;wBACd,wBAAwB,IAAI,CAAC,yBAAyB;wBACtD,eAAe,OAAO,GAAG;oBAC7B;oBACA,IAAI,gBAAgB,IAAI,CAAC,6BAA6B,CAAC,cAAc,SAAS,mBAAmB;oBACjG,IAAI,cAAc,GAAG,EAAE;wBACnB,OAAO;oBACX;oBACA,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;oBAChD,IAAI,eAAe,GAAG,EAAE;wBACpB,OAAO;oBACX;oBACA,IAAI,aAAa,eAAe,sBAAsB,IAAI,CAAC,aAAa;oBACxE,IAAI,YAAY,UAAU;wBACtB,OAAO;4BACH,KAAK;gCACD,MAAM,4KAAA,CAAA,OAAI,CAAC,MAAM;gCACjB,OAAO;gCACP,SAAS,YAAY,cAAc,GAAG;gCACtC,UAAU;4BACd;4BACA,KAAK;wBACT;oBACJ,OACK;wBACD,OAAO;4BACH,KAAK;gCACD,MAAM,4KAAA,CAAA,OAAI,CAAC,MAAM;gCACjB,OAAO;gCACP,SAAS,YAAY,cAAc,GAAG;gCACtC,QAAQ;gCACR,YAAY,YAAY,WAAW,aAAa;gCAChD,UAAU;4BACd;4BACA,KAAK;wBACT;oBACJ;gBACJ;YACA;gBACI,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,mBAAmB;QAC7F;IACJ;IACA,OAAO,SAAS,CAAC,qBAAqB,GAAG,SAAU,oBAAoB;QACnE,0CAA0C;QAC1C,EAAE;QACF,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI,OAAO,KAAI;YAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACtH;QACA,IAAI,CAAC,IAAI,IAAI,MAAM;QACnB,OAAO;YAAE,KAAK;YAAM,KAAK;QAAK;IAClC;IACA;;KAEC,GACD,OAAO,SAAS,CAAC,6BAA6B,GAAG;QAC7C,IAAI,eAAe;QACnB,IAAI,gBAAgB,IAAI,CAAC,aAAa;QACtC,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,KAAK,IAAI,CAAC,IAAI;YAClB,OAAQ;gBACJ,KAAK,GAAG,OAAO;oBAAI;wBACf,gEAAgE;wBAChE,2CAA2C;wBAC3C,IAAI,CAAC,IAAI;wBACT,IAAI,qBAAqB,IAAI,CAAC,aAAa;wBAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;4BACtB,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,gCAAgC,EAAE,eAAe,oBAAoB,IAAI,CAAC,aAAa;wBACvH;wBACA,IAAI,CAAC,IAAI;wBACT;oBACJ;gBACA,KAAK,IAAI,OAAO;oBAAI;wBAChB,gBAAgB;wBAChB,IAAI,CAAC,IAAI;wBACT;oBACJ;gBACA,KAAK,IAAI,OAAO;oBAAI;wBAChB,IAAI,eAAe,GAAG;4BAClB,gBAAgB;wBACpB,OACK;4BACD,OAAO;gCACH,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,IAAI,CAAC,MAAM;gCACzD,KAAK;4BACT;wBACJ;wBACA;oBACJ;gBACA;oBACI,IAAI,CAAC,IAAI;oBACT;YACR;QACJ;QACA,OAAO;YACH,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,IAAI,CAAC,MAAM;YACzD,KAAK;QACT;IACJ;IACA,OAAO,SAAS,CAAC,6BAA6B,GAAG,SAAU,QAAQ,EAAE,QAAQ;QACzE,IAAI,SAAS,EAAE;QACf,IAAI;YACA,SAAS,CAAA,GAAA,wKAAA,CAAA,gCAA6B,AAAD,EAAE;QAC3C,EACA,OAAO,GAAG;YACN,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,uBAAuB,EAAE;QACzD;QACA,OAAO;YACH,KAAK;gBACD,MAAM,4KAAA,CAAA,gBAAa,CAAC,MAAM;gBAC1B,QAAQ;gBACR,UAAU;gBACV,eAAe,IAAI,CAAC,oBAAoB,GAClC,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,UACpB,CAAC;YACX;YACA,KAAK;QACT;IACJ;IACA;;;;;;;;;KASC,GACD,OAAO,SAAS,CAAC,6BAA6B,GAAG,SAAU,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB;QACzH,IAAI;QACJ,IAAI,iBAAiB;QACrB,IAAI,UAAU,EAAE;QAChB,IAAI,kBAAkB,IAAI;QAC1B,IAAI,WAAW,sBAAsB,KAAK,EAAE,mBAAmB,sBAAsB,QAAQ;QAC7F,SAAS;QACT,kBAAkB;QAClB,OAAO;QACP,MAAO,KAAM;YACT,IAAI,SAAS,MAAM,KAAK,GAAG;gBACvB,IAAI,gBAAgB,IAAI,CAAC,aAAa;gBACtC,IAAI,kBAAkB,YAAY,IAAI,CAAC,MAAM,CAAC,MAAM;oBAChD,iCAAiC;oBACjC,IAAI,SAAS,IAAI,CAAC,sBAAsB,CAAC,4KAAA,CAAA,YAAS,CAAC,+BAA+B,EAAE,4KAAA,CAAA,YAAS,CAAC,gCAAgC;oBAC9H,IAAI,OAAO,GAAG,EAAE;wBACZ,OAAO;oBACX;oBACA,mBAAmB,eAAe,eAAe,IAAI,CAAC,aAAa;oBACnE,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnE,OACK;oBACD;gBACJ;YACJ;YACA,6BAA6B;YAC7B,IAAI,gBAAgB,GAAG,CAAC,WAAW;gBAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,WAC9B,4KAAA,CAAA,YAAS,CAAC,kCAAkC,GAC5C,4KAAA,CAAA,YAAS,CAAC,kCAAkC,EAAE;YACxD;YACA,IAAI,aAAa,SAAS;gBACtB,iBAAiB;YACrB;YACA,SAAS;YACT,kBAAkB;YAClB,mBAAmB;YACnB,IAAI,CAAC,SAAS;YACd,IAAI,uBAAuB,IAAI,CAAC,aAAa;YAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,WAC9B,4KAAA,CAAA,YAAS,CAAC,wCAAwC,GAClD,4KAAA,CAAA,YAAS,CAAC,wCAAwC,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;YACrH;YACA,IAAI,iBAAiB,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,eAAe;YACxE,IAAI,eAAe,GAAG,EAAE;gBACpB,OAAO;YACX;YACA,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;YAChD,IAAI,eAAe,GAAG,EAAE;gBACpB,OAAO;YACX;YACA,QAAQ,IAAI,CAAC;gBACT;gBACA;oBACI,OAAO,eAAe,GAAG;oBACzB,UAAU,eAAe,sBAAsB,IAAI,CAAC,aAAa;gBACrE;aACH;YACD,uCAAuC;YACvC,gBAAgB,GAAG,CAAC;YACpB,6BAA6B;YAC7B,IAAI,CAAC,SAAS;YACb,KAAK,IAAI,CAAC,yBAAyB,IAAI,WAAW,GAAG,KAAK,EAAE,mBAAmB,GAAG,QAAQ;QAC/F;QACA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACtB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,WAC9B,4KAAA,CAAA,YAAS,CAAC,+BAA+B,GACzC,4KAAA,CAAA,YAAS,CAAC,+BAA+B,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;QAC5G;QACA,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,gBAAgB;YAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,4KAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;QAC7G;QACA,OAAO;YAAE,KAAK;YAAS,KAAK;QAAK;IACrC;IACA,OAAO,SAAS,CAAC,sBAAsB,GAAG,SAAU,iBAAiB,EAAE,kBAAkB;QACrF,IAAI,OAAO;QACX,IAAI,mBAAmB,IAAI,CAAC,aAAa;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CACtB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;YACvB,OAAO,CAAC;QACZ;QACA,IAAI,YAAY;QAChB,IAAI,UAAU;QACd,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,KAAK,IAAI,CAAC,IAAI;YAClB,IAAI,MAAM,GAAG,OAAO,OAAM,MAAM,GAAG,OAAO,KAAI;gBAC1C,YAAY;gBACZ,UAAU,UAAU,KAAK,CAAC,KAAK,EAAE;gBACjC,IAAI,CAAC,IAAI;YACb,OACK;gBACD;YACJ;QACJ;QACA,IAAI,WAAW,eAAe,kBAAkB,IAAI,CAAC,aAAa;QAClE,IAAI,CAAC,WAAW;YACZ,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB;QACzC;QACA,WAAW;QACX,IAAI,CAAC,cAAc,UAAU;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB;QAC1C;QACA,OAAO;YAAE,KAAK;YAAS,KAAK;QAAK;IACrC;IACA,OAAO,SAAS,CAAC,MAAM,GAAG;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;IAC/B;IACA,OAAO,SAAS,CAAC,KAAK,GAAG;QACrB,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;IAChD;IACA,OAAO,SAAS,CAAC,aAAa,GAAG;QAC7B,sDAAsD;QACtD,OAAO;YACH,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;YAC5B,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI;YACxB,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;QAChC;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG;QACpB,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;QACjC,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAC/B,MAAM,MAAM;QAChB;QACA,IAAI,OAAO,YAAY,IAAI,CAAC,OAAO,EAAE;QACrC,IAAI,SAAS,WAAW;YACpB,MAAM,MAAM,UAAU,MAAM,CAAC,QAAQ;QACzC;QACA,OAAO;IACX;IACA,OAAO,SAAS,CAAC,KAAK,GAAG,SAAU,IAAI,EAAE,QAAQ;QAC7C,OAAO;YACH,KAAK;YACL,KAAK;gBACD,MAAM;gBACN,SAAS,IAAI,CAAC,OAAO;gBACrB,UAAU;YACd;QACJ;IACJ;IACA,kDAAkD,GAClD,OAAO,SAAS,CAAC,IAAI,GAAG;QACpB,IAAI,IAAI,CAAC,KAAK,IAAI;YACd;QACJ;QACA,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,SAAS,GAAG,QAAQ,KAAI;YACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI;YACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;YACvB,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI;QAC5B,OACK;YACD,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI;YACxB,iEAAiE;YACjE,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,UAAU,IAAI;QACjD;IACJ;IACA;;;;;KAKC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAU,MAAM;QACtC,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,MAAM,KAAK;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACpC,IAAI,CAAC,IAAI;YACb;YACA,OAAO;QACX;QACA,OAAO;IACX;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,SAAS,GAAG,SAAU,OAAO;QAC1C,IAAI,gBAAgB,IAAI,CAAC,MAAM;QAC/B,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS;QAC1C,IAAI,SAAS,GAAG;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,OAAO;QACX,OACK;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;YAC/B,OAAO;QACX;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAU,YAAY;QAC5C,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc;YAC9B,MAAM,MAAM,gBAAgB,MAAM,CAAC,cAAc,yDAAyD,MAAM,CAAC,IAAI,CAAC,MAAM;QAChI;QACA,eAAe,KAAK,GAAG,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM;QACzD,MAAO,KAAM;YACT,IAAI,SAAS,IAAI,CAAC,MAAM;YACxB,IAAI,WAAW,cAAc;gBACzB;YACJ;YACA,IAAI,SAAS,cAAc;gBACvB,MAAM,MAAM,gBAAgB,MAAM,CAAC,cAAc;YACrD;YACA,IAAI,CAAC,IAAI;YACT,IAAI,IAAI,CAAC,KAAK,IAAI;gBACd;YACJ;QACJ;IACJ;IACA,oFAAoF,GACpF,OAAO,SAAS,CAAC,SAAS,GAAG;QACzB,MAAO,CAAC,IAAI,CAAC,KAAK,MAAM,cAAc,IAAI,CAAC,IAAI,IAAK;YAChD,IAAI,CAAC,IAAI;QACb;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG;QACpB,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO;QACX;QACA,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,UAAU,IAAI,CAAC;QACxE,OAAO,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW;IACjE;IACA,OAAO;AACX;;AAEA;;;;CAIC,GACD,SAAS,SAAS,SAAS;IACvB,OAAQ,AAAC,aAAa,MAAM,aAAa,OACpC,aAAa,MAAM,aAAa;AACzC;AACA,SAAS,gBAAgB,SAAS;IAC9B,OAAO,SAAS,cAAc,cAAc,IAAI,OAAO;AAC3D;AACA,kCAAkC,GAClC,SAAS,4BAA4B,CAAC;IAClC,OAAQ,MAAM,GAAG,OAAO,OACpB,MAAM,GAAG,OAAO,OACf,KAAK,MAAM,KAAK,MACjB,MAAM,GAAG,OAAO,OACf,KAAK,MAAM,KAAK,OAChB,KAAK,MAAM,KAAK,MACjB,KAAK,QACJ,KAAK,QAAQ,KAAK,QAClB,KAAK,QAAQ,KAAK,QAClB,KAAK,QAAQ,KAAK,SAClB,KAAK,SAAS,KAAK,UACnB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,WAAW,KAAK;AAC9B;AACA;;;CAGC,GACD,SAAS,cAAc,CAAC;IACpB,OAAQ,AAAC,KAAK,UAAU,KAAK,UACzB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM;AACd;AACA;;;CAGC,GACD,SAAS,iBAAiB,CAAC;IACvB,OAAQ,AAAC,KAAK,UAAU,KAAK,UACzB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/manipulator.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport { isArgumentElement, isDateElement, isNumberElement, isPluralElement, isSelectElement, isTagElement, isTimeElement, TYPE, } from './types';\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return __spreadArray([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors(__spreadArray(__spreadArray(__spreadArray([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return isPluralElement(el) || isSelectElement(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if (isTagElement(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nexport function hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if (isTagElement(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if (isArgumentElement(el) ||\n            isDateElement(el) ||\n            isTimeElement(el) ||\n            isNumberElement(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if (isPluralElement(el) || isSelectElement(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if (isTagElement(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nexport function isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(TYPE[type], \" vs \").concat(TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACA,SAAS,UAAU,GAAG;IAClB,IAAI,MAAM,OAAO,CAAC,MAAM;QACpB,uBAAuB;QACvB,OAAO,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,EAAE,IAAI,GAAG,CAAC,YAAY;IACjD;IACA,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;QACzC,uBAAuB;QACvB,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,SAAU,MAAM,EAAE,CAAC;YAC9C,uBAAuB;YACvB,MAAM,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,EAAE;YAC5B,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,2BAA2B,GAAG,EAAE,EAAE,EAAE,gBAAgB;IACzD,kDAAkD;IAClD,IAAI,SAAS,UAAU;IACvB,IAAI,UAAU,OAAO,OAAO;IAC5B,OAAO,OAAO,GAAG,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QACzD,IAAI,WAAW,eAAe,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,EAAE,IAAI,KAAK,CAAC,GAAG,mBAAmB,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,mBAAmB,IAAI;QAC7K,GAAG,CAAC,EAAE,GAAG;YACL,OAAO;QACX;QACA,OAAO;IACX,GAAG,CAAC;IACJ,OAAO;AACX;AACA,SAAS,wBAAwB,EAAE;IAC/B,OAAO,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;AAClD;AACA,SAAS,0BAA0B,GAAG;IAClC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,SAAU,EAAE;QAC1B,IAAI,wBAAwB,KAAK;YAC7B,OAAO;QACX;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YAClB,OAAO,0BAA0B,GAAG,QAAQ;QAChD;QACA,OAAO;IACX;AACJ;AAYO,SAAS,eAAe,GAAG;IAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,KAAK,GAAG,CAAC,EAAE;QACf,IAAI,wBAAwB,KAAK;YAC7B,OAAO;gBAAC,2BAA2B,KAAK,IAAI;aAAG;QACnD;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,OAAO,0BAA0B;YAAC;SAAG,GAAG;YACrD,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,iBAAiB,GAAG,EAAE,IAAI;IAC/B,IAAI,SAAS,KAAK,GAAG;QAAE,OAAO,IAAI;IAAO;IACzC,IAAI,OAAO,CAAC,SAAU,EAAE;QACpB,IAAI,CAAA,GAAA,4KAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE,OACd,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE,OACd,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,GAAG,KAAK,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,GAAG,IAAI,EAAE;gBACpD,MAAM,IAAI,MAAM,YAAY,MAAM,CAAC,GAAG,KAAK,EAAE;YACjD;YACA,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI;QAC9B;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YAC5C,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI;YAC1B,OAAO,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,CAAC,SAAU,CAAC;gBACvC,iBAAiB,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;YAC1C;QACJ;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YAClB,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI;YAC1B,iBAAiB,GAAG,QAAQ,EAAE;QAClC;IACJ;AACJ;AAQO,SAAS,mBAAmB,CAAC,EAAE,CAAC;IACnC,IAAI,QAAQ,IAAI;IAChB,IAAI,QAAQ,IAAI;IAChB,iBAAiB,GAAG;IACpB,iBAAiB,GAAG;IACpB,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE;QAC3B,OAAO;YACH,SAAS;YACT,OAAO,IAAI,MAAM,mCAAmC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,UAAU,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO;QAC1J;IACJ;IACA,OAAO,MAAM,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,CAAC,SAAU,MAAM,EAAE,EAAE;QAC1D,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;QAC7B,IAAI,CAAC,OAAO,OAAO,EAAE;YACjB,OAAO;QACX;QACA,IAAI,QAAQ,MAAM,GAAG,CAAC;QACtB,IAAI,SAAS,MAAM;YACf,OAAO;gBACH,SAAS;gBACT,OAAO,IAAI,MAAM,oBAAoB,MAAM,CAAC,KAAK;YACrD;QACJ;QACA,IAAI,UAAU,MAAM;YAChB,OAAO;gBACH,SAAS;gBACT,OAAO,IAAI,MAAM,YAAY,MAAM,CAAC,KAAK,4BAA4B,MAAM,CAAC,4KAAA,CAAA,OAAI,CAAC,KAAK,EAAE,QAAQ,MAAM,CAAC,4KAAA,CAAA,OAAI,CAAC,MAAM;YACtH;QACJ;QACA,OAAO;IACX,GAAG;QAAE,SAAS;IAAK;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40formatjs/icu-messageformat-parser/lib/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { ErrorKind } from './error';\nimport { Parser } from './parser';\nimport { isDateElement, isDateTimeSkeleton, isNumberElement, isNumberSkeleton, isPluralElement, isSelectElement, isTagElement, isTimeElement, } from './types';\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if (isSelectElement(el) || isPluralElement(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if (isNumberElement(el) && isNumberSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if ((isDateElement(el) || isTimeElement(el)) &&\n            isDateTimeSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if (isTagElement(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nexport function parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexport * from './types';\n// only for testing\nexport var _Parser = Parser;\nexport { isStructurallySame } from './manipulator';\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AA0CA;;;;;AAzCA,SAAS,cAAc,GAAG;IACtB,IAAI,OAAO,CAAC,SAAU,EAAE;QACpB,OAAO,GAAG,QAAQ;QAClB,IAAI,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YAC5C,IAAK,IAAI,KAAK,GAAG,OAAO,CAAE;gBACtB,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,QAAQ;gBAC7B,cAAc,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK;YACrC;QACJ,OACK,IAAI,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,KAAK,GAAG;YACxD,OAAO,GAAG,KAAK,CAAC,QAAQ;QAC5B,OACK,IAAI,CAAC,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,KAC5C,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,KAAK,GAAG;YAC9B,OAAO,GAAG,KAAK,CAAC,QAAQ;QAC5B,OACK,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YACvB,cAAc,GAAG,QAAQ;QAC7B;IACJ;AACJ;AACO,SAAS,MAAM,OAAO,EAAE,IAAI;IAC/B,IAAI,SAAS,KAAK,GAAG;QAAE,OAAO,CAAC;IAAG;IAClC,OAAO,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,sBAAsB;QAAM,qBAAqB;IAAK,GAAG;IAC3E,IAAI,SAAS,IAAI,6KAAA,CAAA,SAAM,CAAC,SAAS,MAAM,KAAK;IAC5C,IAAI,OAAO,GAAG,EAAE;QACZ,IAAI,QAAQ,YAAY,4KAAA,CAAA,YAAS,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC;QAClD,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,OAAO,GAAG,CAAC,QAAQ;QACpC,0CAA0C;QAC1C,MAAM,eAAe,GAAG,OAAO,GAAG,CAAC,OAAO;QAC1C,MAAM;IACV;IACA,IAAI,CAAC,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,eAAe,GAAG;QACrE,cAAc,OAAO,GAAG;IAC5B;IACA,OAAO,OAAO,GAAG;AACrB;;AAGO,IAAI,UAAU,6KAAA,CAAA,SAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4055, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/intl-messageformat/lib/src/error.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nexport var ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    __extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexport { FormatError };\nvar InvalidValueError = /** @class */ (function (_super) {\n    __extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexport { InvalidValueError };\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    __extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexport { InvalidValueTypeError };\nvar MissingValueError = /** @class */ (function (_super) {\n    __extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexport { MissingValueError };\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,oDAAoD;IACpD,SAAS,CAAC,gBAAgB,GAAG;IAC7B,iCAAiC;IACjC,SAAS,CAAC,gBAAgB,GAAG;IAC7B,wDAAwD;IACxD,SAAS,CAAC,mBAAmB,GAAG;AACpC,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,IAAI,cAA6B,SAAU,MAAM;IAC7C,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACvB,SAAS,YAAY,GAAG,EAAE,IAAI,EAAE,eAAe;QAC3C,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI;QAC1C,MAAM,IAAI,GAAG;QACb,MAAM,eAAe,GAAG;QACxB,OAAO;IACX;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,oBAAoB,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1E;IACA,OAAO;AACX,EAAE;;AAEF,IAAI,oBAAmC,SAAU,MAAM;IACnD,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;IAC7B,SAAS,kBAAkB,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe;QAClE,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,wBAAwB,MAAM,CAAC,YAAY,UAAU,MAAM,CAAC,OAAO,sBAAsB,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,OAAO,UAAU,aAAa,EAAE,oBAAoB,IAAI;IACxN;IACA,OAAO;AACX,EAAE;;AAEF,IAAI,wBAAuC,SAAU,MAAM;IACvD,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD,EAAE,uBAAuB;IACjC,SAAS,sBAAsB,KAAK,EAAE,IAAI,EAAE,eAAe;QACvD,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,eAAe,MAAM,CAAC,OAAO,uBAAuB,MAAM,CAAC,OAAO,UAAU,aAAa,EAAE,oBAAoB,IAAI;IAChJ;IACA,OAAO;AACX,EAAE;;AAEF,IAAI,oBAAmC,SAAU,MAAM;IACnD,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;IAC7B,SAAS,kBAAkB,UAAU,EAAE,eAAe;QAClD,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,sCAAsC,MAAM,CAAC,YAAY,wCAAwC,MAAM,CAAC,iBAAiB,OAAO,UAAU,aAAa,EAAE,oBAAoB,IAAI;IAC9M;IACA,OAAO;AACX,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/intl-messageformat/lib/src/formatters.js"], "sourcesContent": ["import { isArgumentElement, isDateElement, isDateTimeSkeleton, isLiteralElement, isNumberElement, isNumberSkeleton, isPluralElement, isPoundElement, isSelectElement, isTagElement, isTimeElement, } from '@formatjs/icu-messageformat-parser';\nimport { ErrorCode, FormatError, InvalidValueError, InvalidValueTypeError, MissingValueError, } from './error';\nexport var PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nexport function isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nexport function formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && isLiteralElement(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if (isLiteralElement(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if (isPoundElement(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if (isArgumentElement(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if (isDateElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTimeElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isNumberElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : isNumberSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTagElement(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if (isSelectElement(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if (isPluralElement(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;AACO,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;IACtC,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GAAG;AACzC,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,SAAS,aAAa,KAAK;IACvB,IAAI,MAAM,MAAM,GAAG,GAAG;QAClB,OAAO;IACX;IACA,OAAO,MAAM,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACnC,IAAI,WAAW,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAClC,IAAI,CAAC,YACD,SAAS,IAAI,KAAK,UAAU,OAAO,IACnC,KAAK,IAAI,KAAK,UAAU,OAAO,EAAE;YACjC,IAAI,IAAI,CAAC;QACb,OACK;YACD,SAAS,KAAK,IAAI,KAAK,KAAK;QAChC;QACA,OAAO;IACX,GAAG,EAAE;AACT;AACO,SAAS,qBAAqB,EAAE;IACnC,OAAO,OAAO,OAAO;AACzB;AAEO,SAAS,cAAc,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAC3F,gBAAgB;AAChB,eAAe;IACX,gDAAgD;IAChD,IAAI,IAAI,MAAM,KAAK,KAAK,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,CAAC,EAAE,GAAG;QAC9C,OAAO;YACH;gBACI,MAAM,UAAU,OAAO;gBACvB,OAAO,GAAG,CAAC,EAAE,CAAC,KAAK;YACvB;SACH;IACL;IACA,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,KAAK,GAAG,QAAQ,KAAK,KAAK,MAAM,MAAM,EAAE,KAAM;QACnD,IAAI,KAAK,KAAK,CAAC,GAAG;QAClB,+BAA+B;QAC/B,IAAI,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;YACtB,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,GAAG,KAAK;YACnB;YACA;QACJ;QACA,0CAA0C;QAC1C,6DAA6D;QAC7D,IAAI,CAAA,GAAA,4KAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;YACpB,IAAI,OAAO,uBAAuB,UAAU;gBACxC,OAAO,IAAI,CAAC;oBACR,MAAM,UAAU,OAAO;oBACvB,OAAO,WAAW,eAAe,CAAC,SAAS,MAAM,CAAC;gBACtD;YACJ;YACA;QACJ;QACA,IAAI,UAAU,GAAG,KAAK;QACtB,+DAA+D;QAC/D,IAAI,CAAC,CAAC,UAAU,WAAW,MAAM,GAAG;YAChC,MAAM,IAAI,4JAAA,CAAA,oBAAiB,CAAC,SAAS;QACzC;QACA,IAAI,QAAQ,MAAM,CAAC,QAAQ;QAC3B,IAAI,CAAA,GAAA,4KAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK;YACvB,IAAI,CAAC,SAAS,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;gBAClE,QACI,OAAO,UAAU,YAAY,OAAO,UAAU,WACxC,OAAO,SACP;YACd;YACA,OAAO,IAAI,CAAC;gBACR,MAAM,OAAO,UAAU,WAAW,UAAU,OAAO,GAAG,UAAU,MAAM;gBACtE,OAAO;YACX;YACA;QACJ;QACA,sEAAsE;QACtE,iEAAiE;QACjE,yDAAyD;QACzD,IAAI,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YACnB,IAAI,QAAQ,OAAO,GAAG,KAAK,KAAK,WAC1B,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,GACtB,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,KAAK,IACvB,GAAG,KAAK,CAAC,aAAa,GACtB;YACV,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,WACF,iBAAiB,CAAC,SAAS,OAC3B,MAAM,CAAC;YAChB;YACA;QACJ;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YACnB,IAAI,QAAQ,OAAO,GAAG,KAAK,KAAK,WAC1B,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,GACtB,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,KAAK,IACvB,GAAG,KAAK,CAAC,aAAa,GACtB,QAAQ,IAAI,CAAC,MAAM;YAC7B,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,WACF,iBAAiB,CAAC,SAAS,OAC3B,MAAM,CAAC;YAChB;YACA;QACJ;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,QAAQ,OAAO,GAAG,KAAK,KAAK,WAC1B,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,GACxB,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,KAAK,IACrB,GAAG,KAAK,CAAC,aAAa,GACtB;YACV,IAAI,SAAS,MAAM,KAAK,EAAE;gBACtB,QACI,QACI,CAAC,MAAM,KAAK,IAAI,CAAC;YAC7B;YACA,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,WACF,eAAe,CAAC,SAAS,OACzB,MAAM,CAAC;YAChB;YACA;QACJ;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YAClB,IAAI,WAAW,GAAG,QAAQ,EAAE,UAAU,GAAG,KAAK;YAC9C,IAAI,WAAW,MAAM,CAAC,QAAQ;YAC9B,IAAI,CAAC,qBAAqB,WAAW;gBACjC,MAAM,IAAI,4JAAA,CAAA,wBAAqB,CAAC,SAAS,YAAY;YACzD;YACA,IAAI,QAAQ,cAAc,UAAU,SAAS,YAAY,SAAS,QAAQ;YAC1E,IAAI,SAAS,SAAS,MAAM,GAAG,CAAC,SAAU,CAAC;gBAAI,OAAO,EAAE,KAAK;YAAE;YAC/D,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;gBACxB,SAAS;oBAAC;iBAAO;YACrB;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,OAAO,GAAG,CAAC,SAAU,CAAC;gBAC5C,OAAO;oBACH,MAAM,OAAO,MAAM,WAAW,UAAU,OAAO,GAAG,UAAU,MAAM;oBAClE,OAAO;gBACX;YACJ;QACJ;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK;YAC/C,IAAI,CAAC,KAAK;gBACN,MAAM,IAAI,4JAAA,CAAA,oBAAiB,CAAC,GAAG,KAAK,EAAE,OAAO,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG;YAC1E;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,cAAc,IAAI,KAAK,EAAE,SAAS,YAAY,SAAS;YACjF;QACJ;QACA,IAAI,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO;YACvC,IAAI,CAAC,KAAK;gBACN,IAAI,CAAC,KAAK,WAAW,EAAE;oBACnB,MAAM,IAAI,4JAAA,CAAA,cAAW,CAAC,qHAAqH,4JAAA,CAAA,YAAS,CAAC,gBAAgB,EAAE;gBAC3K;gBACA,IAAI,OAAO,WACN,cAAc,CAAC,SAAS;oBAAE,MAAM,GAAG,UAAU;gBAAC,GAC9C,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;gBACnC,MAAM,GAAG,OAAO,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK;YAC9C;YACA,IAAI,CAAC,KAAK;gBACN,MAAM,IAAI,4JAAA,CAAA,oBAAiB,CAAC,GAAG,KAAK,EAAE,OAAO,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG;YAC1E;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,cAAc,IAAI,KAAK,EAAE,SAAS,YAAY,SAAS,QAAQ,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;YAChH;QACJ;IACJ;IACA,OAAO,aAAa;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/intl-messageformat/lib/src/core.js"], "sourcesContent": ["/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { parse, } from '@formatjs/icu-messageformat-parser';\nimport { formatToParts, PART_TYPE, } from './formatters';\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return __assign(__assign(__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = __assign(__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, __assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: strategies.variadic,\n        }),\n        getDateTimeFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return formatToParts(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = __rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, __assign(__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexport { IntlMessageFormat };\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;AACA;AACA;AACA;AAAA;AACA;;;;;AACA,4EAA4E;AAC5E,SAAS,YAAY,EAAE,EAAE,EAAE;IACvB,IAAI,CAAC,IAAI;QACL,OAAO;IACX;IACA,OAAO,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAI,MAAM,CAAC,IAAM,MAAM,CAAC,IAAK,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QACnG,GAAG,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAI,EAAE,CAAC,EAAE,IAAI,CAAC;QAClD,OAAO;IACX,GAAG,CAAC;AACR;AACA,SAAS,aAAa,aAAa,EAAE,OAAO;IACxC,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IACA,OAAO,OAAO,IAAI,CAAC,eAAe,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QACrD,GAAG,CAAC,EAAE,GAAG,YAAY,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QACjD,OAAO;IACX,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG;AACpB;AACA,SAAS,uBAAuB,KAAK;IACjC,OAAO;QACH,QAAQ;YACJ,OAAO;gBACH,KAAK,SAAU,GAAG;oBACd,OAAO,KAAK,CAAC,IAAI;gBACrB;gBACA,KAAK,SAAU,GAAG,EAAE,KAAK;oBACrB,KAAK,CAAC,IAAI,GAAG;gBACjB;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,wBAAwB,KAAK;IAClC,IAAI,UAAU,KAAK,GAAG;QAAE,QAAQ;YAC5B,QAAQ,CAAC;YACT,UAAU,CAAC;YACX,aAAa,CAAC;QAClB;IAAG;IACH,OAAO;QACH,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE;YACrB,IAAI;YACJ,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YAC5B;YACA,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE;gBAAC,KAAK;aAAE,EAAE,MAAM,OAAO;QAC7F,GAAG;YACC,OAAO,uBAAuB,MAAM,MAAM;YAC1C,UAAU,6JAAA,CAAA,aAAU,CAAC,QAAQ;QACjC;QACA,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE;YACvB,IAAI;YACJ,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YAC5B;YACA,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE;gBAAC,KAAK;aAAE,EAAE,MAAM,OAAO;QAC/F,GAAG;YACC,OAAO,uBAAuB,MAAM,QAAQ;YAC5C,UAAU,6JAAA,CAAA,aAAU,CAAC,QAAQ;QACjC;QACA,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE;YACpB,IAAI;YACJ,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YAC5B;YACA,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE;gBAAC,KAAK;aAAE,EAAE,MAAM,OAAO;QAC5F,GAAG;YACC,OAAO,uBAAuB,MAAM,WAAW;YAC/C,UAAU,6JAAA,CAAA,aAAU,CAAC,QAAQ;QACjC;IACJ;AACJ;AACA,IAAI,oBAAmC;IACnC,SAAS,kBAAkB,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI;QAC9D,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU,kBAAkB,aAAa;QAAE;QACrE,IAAI,QAAQ,IAAI;QAChB,IAAI,CAAC,cAAc,GAAG;YAClB,QAAQ,CAAC;YACT,UAAU,CAAC;YACX,aAAa,CAAC;QAClB;QACA,IAAI,CAAC,MAAM,GAAG,SAAU,MAAM;YAC1B,IAAI,QAAQ,MAAM,aAAa,CAAC;YAChC,gDAAgD;YAChD,IAAI,MAAM,MAAM,KAAK,GAAG;gBACpB,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK;YACzB;YACA,IAAI,SAAS,MAAM,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;gBACzC,IAAI,CAAC,IAAI,MAAM,IACX,KAAK,IAAI,KAAK,iKAAA,CAAA,YAAS,CAAC,OAAO,IAC/B,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,UAAU;oBACzC,IAAI,IAAI,CAAC,KAAK,KAAK;gBACvB,OACK;oBACD,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,IAAI,KAAK,KAAK;gBACrC;gBACA,OAAO;YACX,GAAG,EAAE;YACL,IAAI,OAAO,MAAM,IAAI,GAAG;gBACpB,OAAO,MAAM,CAAC,EAAE,IAAI;YACxB;YACA,OAAO;QACX;QACA,IAAI,CAAC,aAAa,GAAG,SAAU,MAAM;YACjC,OAAO,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,GAAG,EAAE,MAAM,OAAO,EAAE,MAAM,UAAU,EAAE,MAAM,OAAO,EAAE,QAAQ,WAAW,MAAM,OAAO;QACpH;QACA,IAAI,CAAC,eAAe,GAAG;YACnB,IAAI;YACJ,OAAQ;gBACJ,QAAQ,CAAC,CAAC,KAAK,MAAM,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,EAAE,KACnF,KAAK,YAAY,CAAC,kBAAkB,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE;YAC9D;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;YAAc,OAAO,MAAM,GAAG;QAAE;QAC9C,+DAA+D;QAC/D,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,cAAc,GAAG,kBAAkB,aAAa,CAAC;QACtD,IAAI,OAAO,YAAY,UAAU;YAC7B,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,kBAAkB,OAAO,EAAE;gBAC5B,MAAM,IAAI,UAAU;YACxB;YACA,IAAI,KAAK,QAAQ,CAAC,GAAG,aAAa,GAAG,UAAU,EAAE,YAAY,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,EAAE,IAAI;gBAAC;aAAa;YACtF,qCAAqC;YACrC,IAAI,CAAC,GAAG,GAAG,kBAAkB,OAAO,CAAC,SAAS,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,YAAY;gBAAE,QAAQ,IAAI,CAAC,cAAc;YAAC;QAClH,OACK;YACD,IAAI,CAAC,GAAG,GAAG;QACf;QACA,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG;YAC1B,MAAM,IAAI,UAAU;QACxB;QACA,4EAA4E;QAC5E,WAAW;QACX,IAAI,CAAC,OAAO,GAAG,aAAa,kBAAkB,OAAO,EAAE;QACvD,IAAI,CAAC,UAAU,GACX,AAAC,QAAQ,KAAK,UAAU,IAAK,wBAAwB,IAAI,CAAC,cAAc;IAChF;IACA,OAAO,cAAc,CAAC,mBAAmB,iBAAiB;QACtD,KAAK;YACD,IAAI,CAAC,kBAAkB,qBAAqB,EAAE;gBAC1C,kBAAkB,qBAAqB,GACnC,IAAI,KAAK,YAAY,GAAG,eAAe,GAAG,MAAM;YACxD;YACA,OAAO,kBAAkB,qBAAqB;QAClD;QACA,YAAY;QACZ,cAAc;IAClB;IACA,kBAAkB,qBAAqB,GAAG;IAC1C,kBAAkB,aAAa,GAAG,SAAU,OAAO;QAC/C,IAAI,OAAO,KAAK,MAAM,KAAK,aAAa;YACpC;QACJ;QACA,IAAI,mBAAmB,KAAK,YAAY,CAAC,kBAAkB,CAAC;QAC5D,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC7B,OAAO,IAAI,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE;QAC9C;QACA,OAAO,IAAI,KAAK,MAAM,CAAC,OAAO,YAAY,WAAW,UAAU,OAAO,CAAC,EAAE;IAC7E;IACA,kBAAkB,OAAO,GAAG,4LAAA,CAAA,QAAK;IACjC,gFAAgF;IAChF,+EAA+E;IAC/E,qCAAqC;IACrC,kBAAkB,OAAO,GAAG;QACxB,QAAQ;YACJ,SAAS;gBACL,uBAAuB;YAC3B;YACA,UAAU;gBACN,OAAO;YACX;YACA,SAAS;gBACL,OAAO;YACX;QACJ;QACA,MAAM;YACF,OAAO;gBACH,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;YACA,QAAQ;gBACJ,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;YACA,MAAM;gBACF,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;YACA,MAAM;gBACF,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;QACJ;QACA,MAAM;YACF,OAAO;gBACH,MAAM;gBACN,QAAQ;YACZ;YACA,QAAQ;gBACJ,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACZ;YACA,MAAM;gBACF,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,cAAc;YAClB;YACA,MAAM;gBACF,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,cAAc;YAClB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js"], "sourcesContent": ["import { IntlMessageFormat } from 'intl-messageformat';\nimport { isValidElement, cloneElement } from 'react';\nimport { memoize, strategies } from '@formatjs/fast-memoize';\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = IntlMessageFormat.formats.date;\n  const mfTimeDefaults = IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/isValidElement(result) ? /*#__PURE__*/cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexport { IntlError as I, IntlErrorCode as a, createIntlFormatters as b, createFormatter as c, createCache as d, createBaseTranslator as e, defaultGetMessageFallback as f, defaultOnError as g, initializeConfig as i, resolveNamespace as r };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,kBAAkB;IACtB,YAAY,IAAI,EAAE,eAAe,CAAE;QACjC,IAAI,UAAU;QACd,IAAI,iBAAiB;YACnB,WAAW,OAAO;QACpB;QACA,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,iBAAiB;YACnB,IAAI,CAAC,eAAe,GAAG;QACzB;IACF;AACF;AAEA,IAAI,gBAAgB,WAAW,GAAE,SAAU,aAAa;IACtD,aAAa,CAAC,kBAAkB,GAAG;IACnC,aAAa,CAAC,iBAAiB,GAAG;IAClC,aAAa,CAAC,uBAAuB,GAAG;IACxC,aAAa,CAAC,oBAAoB,GAAG;IACrC,aAAa,CAAC,kBAAkB,GAAG;IACnC,aAAa,CAAC,cAAc,GAAG;IAC/B,aAAa,CAAC,mBAAmB,GAAG;IACpC,OAAO;AACT,EAAE,iBAAiB,CAAC;AAEpB;;;;;;CAMC,GACD,SAAS,kCAAkC,aAAa,EAAE,aAAa,EAAE,QAAQ;IAC/E,MAAM,iBAAiB,2JAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,IAAI;IACrD,MAAM,iBAAiB,2JAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,IAAI;IACrD,MAAM,kBAAkB;QACtB,GAAG,eAAe,QAAQ;QAC1B,GAAG,eAAe,QAAQ;IAC5B;IACA,MAAM,aAAa;QACjB,MAAM;YACJ,GAAG,cAAc;YACjB,GAAG,eAAe;QACpB;QACA,MAAM;YACJ,GAAG,cAAc;YACjB,GAAG,eAAe;QACpB;QACA,QAAQ;YACN,GAAG,eAAe,MAAM;YACxB,GAAG,eAAe,MAAM;QAC1B;IAEF;IACA,IAAI,UAAU;QACZ,4FAA4F;QAC5F,sHAAsH;QACtH;YAAC;YAAQ;SAAO,CAAC,OAAO,CAAC,CAAA;YACvB,MAAM,UAAU,UAAU,CAAC,SAAS;YACpC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;gBAClD,OAAO,CAAC,IAAI,GAAG;oBACb;oBACA,GAAG,KAAK;gBACV;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,SAAS,GAAG,KAAK;IACxB,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC;AACpC;AAEA;;;CAGC,GAED,SAAS,0BAA0B,KAAK;IACtC,OAAO,SAAS,MAAM,SAAS,EAAE,MAAM,GAAG;AAC5C;AACA,SAAS,eAAe,KAAK;IAC3B,QAAQ,KAAK,CAAC;AAChB;AAEA,SAAS;IACP,OAAO;QACL,UAAU,CAAC;QACX,QAAQ,CAAC;QACT,SAAS,CAAC;QACV,cAAc,CAAC;QACf,aAAa,CAAC;QACd,MAAM,CAAC;QACP,cAAc,CAAC;IACjB;AACF;AACA,SAAS,gBAAgB,KAAK;IAC5B,OAAO;QACL;YACE,OAAO;gBACL,KAAI,GAAG;oBACL,OAAO,KAAK,CAAC,IAAI;gBACnB;gBACA,KAAI,GAAG,EAAE,KAAK;oBACZ,KAAK,CAAC,IAAI,GAAG;gBACf;YACF;QACF;IACF;AACF;AACA,SAAS,OAAO,EAAE,EAAE,KAAK;IACvB,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,IAAI;QACjB,OAAO,gBAAgB;QACvB,UAAU,6JAAA,CAAA,aAAU,CAAC,QAAQ;IAC/B;AACF;AACA,SAAS,gBAAgB,aAAa,EAAE,KAAK;IAC3C,OAAO,OAAO,CAAC,GAAG,OAAS,IAAI,iBAAiB,OAAO;AACzD;AACA,SAAS,qBAAqB,KAAK;IACjC,MAAM,oBAAoB,gBAAgB,KAAK,cAAc,EAAE,MAAM,QAAQ;IAC7E,MAAM,kBAAkB,gBAAgB,KAAK,YAAY,EAAE,MAAM,MAAM;IACvE,MAAM,iBAAiB,gBAAgB,KAAK,WAAW,EAAE,MAAM,WAAW;IAC1E,MAAM,wBAAwB,gBAAgB,KAAK,kBAAkB,EAAE,MAAM,YAAY;IACzF,MAAM,gBAAgB,gBAAgB,KAAK,UAAU,EAAE,MAAM,IAAI;IACjE,MAAM,kBAAkB,gBAAgB,KAAK,YAAY,EAAE,MAAM,YAAY;IAC7E,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,wEAAwE;AACxE,kEAAkE;AAClE,SAAS,uBAAuB,KAAK,EAAE,cAAc;IACnD,MAAM,mBAAmB,OAAO,CAAC,GAAG,OAAS,IAAI,2JAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;YAC5F,YAAY;YACZ,GAAG,IAAI,CAAC,EAAE;QACZ,IAAI,MAAM,OAAO;IACjB,OAAO;AACT;AACA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS;IACnD,MAAM,UAAU,SAAS,WAAW;IACpC,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,UAAU,GAAG,CAAC;IAC9D;IACA,IAAI,UAAU;IACd,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;QACrB,MAAM,OAAO,OAAO,CAAC,KAAK;QAE1B,uEAAuE;QACvE,IAAI,QAAQ,QAAQ,QAAQ,MAAM;YAChC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,QAAQ,4BAA4B,EAAE,OAAO,GAAG,CAAC;QAC1F;QACA,UAAU;IACZ;IACA,OAAO;AACT;AACA,SAAS,yBAAyB,MAAM;IACtC,kEAAkE;IAClE,MAAM,oBAAoB,CAAC;IAC3B,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;QAC1B,IAAI,QAAQ;QACZ,MAAM,QAAQ,MAAM,CAAC,IAAI;QACzB,IAAI;QACJ,IAAI,OAAO,UAAU,YAAY;YAC/B,cAAc,CAAA;gBACZ,MAAM,SAAS,MAAM;gBACrB,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;oBAC7E,KAAK,MAAM;gBACb,KAAK;YACP;QACF,OAAO;YACL,cAAc;QAChB;QACA,iBAAiB,CAAC,IAAI,GAAG;IAC3B;IACA,OAAO;AACT;AACA,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,cAAc;IAC/E,IAAI;QACF,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,4BAA4B,CAAC;QAChD;QACA,MAAM,oBAAoB,YAAY,YAAY,QAAQ,UAAU,aAAa;QAEjF,uEAAuE;QACvE,IAAI,CAAC,mBAAmB;YACtB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,UAAU,SAAS,CAAC;QACrE;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,YAAY,IAAI,UAAU,cAAc,eAAe,EAAE,MAAM,OAAO;QAC5E,QAAQ;QACR,OAAO;IACT;AACF;AACA,SAAS,gBAAgB,SAAS,EAAE,MAAM;IACxC;QACE,gCAAgC;QAChC,IAAI,QAAQ,OAAO;QAEnB,oEAAoE;QACpE,mEAAmE;QACnE,mEAAmE;QACnE,MAAM,mBAAmB,UAAU,OAAO,CAAC,aAAa;QACxD,MAAM,kBAAkB,MAAM,IAAI,CAAC;QACnC,IAAI,CAAC,iBAAiB;YACpB,OAAO;QACT;IACF;AACF;AACA,SAAS,qBAAqB,MAAM;IAClC,MAAM,kBAAkB,mBAAmB,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,OAAO,OAAO;IAC3G,OAAO,yBAAyB;QAC9B,GAAG,MAAM;QACT;IACF;AACF;AACA,SAAS,yBAAyB,EAChC,KAAK,EACL,SAAS,aAAa,EACtB,UAAU,EACV,qBAAqB,yBAAyB,EAC9C,MAAM,EACN,eAAe,EACf,SAAS,EACT,OAAO,EACP,QAAQ,EACT;IACC,MAAM,mBAAmB,2BAA2B;IACpD,SAAS,8BAA8B,GAAG,EAAE,IAAI,EAAE,OAAO;QACvD,MAAM,QAAQ,IAAI,UAAU,MAAM;QAClC,QAAQ;QACR,OAAO,mBAAmB;YACxB;YACA;YACA;QACF;IACF;IACA,SAAS,gBAAgB,6EAA6E,GACtG,GAAG,EAAE,gEAAgE,GACrE,MAAM,EAAE,yDAAyD,GACjE,OAAO;QACL,IAAI,kBAAkB;YACpB,kDAAkD;YAClD,OAAO,mBAAmB;gBACxB,OAAO;gBACP;gBACA;YACF;QACF;QACA,MAAM,WAAW;QACjB,IAAI;QACJ,IAAI;YACF,UAAU,YAAY,QAAQ,UAAU,KAAK;QAC/C,EAAE,OAAO,OAAO;YACd,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,MAAM,OAAO;QACxF;QACA,IAAI,OAAO,YAAY,UAAU;YAC/B,IAAI,MAAM;YACV,IAAI,MAAM,OAAO,CAAC,UAAU;gBAC1B,OAAO,cAAc,eAAe;gBACpC;oBACE,eAAe,CAAC,aAAa,EAAE,SAAS,WAAW,KAAK,yHAAyH,CAAC;gBACpL;YACF,OAAO;gBACL,OAAO,cAAc,iBAAiB;gBACtC;oBACE,eAAe,CAAC,aAAa,EAAE,SAAS,WAAW,KAAK,qKAAqK,CAAC;gBAChO;YACF;YACA,OAAO,8BAA8B,KAAK,MAAM;QAClD;QACA,IAAI;QAEJ,gEAAgE;QAChE,MAAM,eAAe,gBAAgB,SAAS;QAC9C,IAAI,cAAc,OAAO;QAEzB,kDAAkD;QAClD,kDAAkD;QAClD,IAAI,CAAC,WAAW,gBAAgB,EAAE;YAChC,WAAW,gBAAgB,GAAG,uBAAuB,OAAO;QAC9D;QACA,IAAI;YACF,gBAAgB,WAAW,gBAAgB,CAAC,SAAS,QAAQ,kCAAkC,eAAe,SAAS,WAAW;gBAChI,YAAY;oBACV,GAAG,UAAU;oBACb,mBAAkB,OAAO,EAAE,OAAO;wBAChC,kEAAkE;wBAClE,OAAO,WAAW,iBAAiB,CAAC,SAAS;4BAC3C;4BACA,GAAG,OAAO;wBACZ;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,cAAc;YACpB,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,YAAY,OAAO,GAAG,CAAC,qBAAqB,cAAc,CAAC,EAAE,EAAE,YAAY,eAAe,CAAC,CAAC,CAAC,GAAG,EAAE;QAC7K;QACA,IAAI;YACF,MAAM,mBAAmB,cAAc,MAAM,CAC7C,mEAAmE;YACnE,2DAA2D;YAC3D,4DAA4D;YAC5D,iCAAiC;YACjC,SAAS,yBAAyB,UAAU;YAC5C,IAAI,oBAAoB,MAAM;gBAC5B,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,IAAI,MAAM,EAAE,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,GAAG,YAAY;YAC3G;YAEA,mEAAmE;YACnE,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,qBACnC,2BAA2B;YAC3B,MAAM,OAAO,CAAC,qBAAqB,OAAO,qBAAqB,WAAW,mBAAmB,OAAO;QACtG,EAAE,OAAO,OAAO;YACd,OAAO,8BAA8B,KAAK,cAAc,gBAAgB,EAAE,MAAM,OAAO;QACzF;IACF;IACA,SAAS,YAAY,6EAA6E,GAClG,GAAG,EAAE,gEAAgE,GACrE,MAAM,EAAE,yDAAyD,GACjE,OAAO;QACL,MAAM,SAAS,gBAAgB,KAAK,QAAQ;QAC5C,IAAI,OAAO,WAAW,UAAU;YAC9B,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,CAAC,cAAc,EAAE,IAAI,MAAM,EAAE,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,GAAG,WAAW,qFAAqF,CAAC;QACpP;QACA,OAAO;IACT;IACA,YAAY,IAAI,GAAG;IAEnB,oDAAoD;IACpD,YAAY,MAAM,GAAG,CAAC,KAAK,QAAQ;QACjC,MAAM,SAAS,gBAAgB,KAC/B,0EAA0E;QAC1E,qEAAqE;QACrE,QAAQ;QACR,IAAI,OAAO,WAAW,UAAU;YAC9B,MAAM,QAAQ,IAAI,UAAU,cAAc,gBAAgB,EAAE;YAC5D,QAAQ;YACR,OAAO,mBAAmB;gBACxB;gBACA;gBACA;YACF;QACF;QACA,OAAO;IACT;IACA,YAAY,GAAG,GAAG,CAAA;QAChB,IAAI,kBAAkB;YACpB,kDAAkD;YAClD,OAAO,mBAAmB;gBACxB,OAAO;gBACP;gBACA;YACF;QACF;QACA,MAAM,WAAW;QACjB,IAAI;YACF,OAAO,YAAY,QAAQ,UAAU,KAAK;QAC5C,EAAE,OAAO,OAAO;YACd,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,MAAM,OAAO;QACxF;IACF;IACA,YAAY,GAAG,GAAG,CAAA;QAChB,IAAI,kBAAkB;YACpB,OAAO;QACT;QACA,IAAI;YACF,YAAY,QAAQ,iBAAiB,KAAK;YAC1C,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,iBAAiB,SAAS,EAAE,eAAe;IAClD,OAAO,cAAc,kBAAkB,YAAY,UAAU,KAAK,CAAC,CAAC,kBAAkB,GAAG,EAAE,MAAM;AACnG;AAEA,MAAM,SAAS;AACf,MAAM,SAAS,SAAS;AACxB,MAAM,OAAO,SAAS;AACtB,MAAM,MAAM,OAAO;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,QAAQ,MAAM,CAAC,MAAM,EAAE,GAAG,gBAAgB;AAChD,MAAM,UAAU,QAAQ;AACxB,MAAM,OAAO,MAAM;AACnB,MAAM,eAAe;IACnB,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,OAAO;AACT;AACA,SAAS,wBAAwB,OAAO;IACtC,MAAM,WAAW,KAAK,GAAG,CAAC;IAC1B,IAAI,WAAW,QAAQ;QACrB,OAAO;IACT,OAAO,IAAI,WAAW,MAAM;QAC1B,OAAO;IACT,OAAO,IAAI,WAAW,KAAK;QACzB,OAAO;IACT,OAAO,IAAI,WAAW,MAAM;QAC1B,OAAO;IACT,OAAO,IAAI,WAAW,OAAO;QAC3B,OAAO;IACT,OAAO,IAAI,WAAW,MAAM;QAC1B,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,2BAA2B,OAAO,EAAE,IAAI;IAC/C,sEAAsE;IACtE,+CAA+C;IAC/C,OAAO,KAAK,KAAK,CAAC,UAAU,YAAY,CAAC,KAAK;AAChD;AACA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,EACJ,QAAQ,QAAQ,aAAa,EAC7B,aAAa,aAAa,qBAAqB,MAAM,EACrD,OAAO,EACP,MAAM,EACN,UAAU,cAAc,EACxB,UAAU,cAAc,EACzB,GAAG;IACJ,SAAS,cAAc,OAAO;QAC5B,IAAI,CAAC,SAAS,UAAU;YACtB,IAAI,gBAAgB;gBAClB,UAAU;oBACR,GAAG,OAAO;oBACV,UAAU;gBACZ;YACF,OAAO;gBACL,QAAQ,IAAI,UAAU,cAAc,oBAAoB,EAAE,CAAC,mPAAmP,CAAC;YACjT;QACF;QACA,OAAO;IACT;IACA,SAAS,uBAAuB,WAAW,EAAE,eAAe,EAAE,SAAS;QACrE,IAAI;QACJ,IAAI,OAAO,oBAAoB,UAAU;YACvC,MAAM,aAAa;YACnB,UAAU,aAAa,CAAC,WAAW;YACnC,IAAI,CAAC,SAAS;gBACZ,MAAM,QAAQ,IAAI,UAAU,cAAc,cAAc,EAAE,CAAC,SAAS,EAAE,WAAW,oBAAoB,CAAC;gBACtG,QAAQ;gBACR,MAAM;YACR;QACF,OAAO;YACL,UAAU;QACZ;QACA,IAAI,WAAW;YACb,UAAU;gBACR,GAAG,OAAO;gBACV,GAAG,SAAS;YACd;QACF;QACA,OAAO;IACT;IACA,SAAS,kBAAkB,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW;QACxF,IAAI;QACJ,IAAI;YACF,UAAU,uBAAuB,aAAa,iBAAiB;QACjE,EAAE,OAAM;YACN,OAAO;QACT;QACA,IAAI;YACF,OAAO,UAAU;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,UAAU,cAAc,gBAAgB,EAAE,MAAM,OAAO;YACnE,OAAO;QACT;IACF;IACA,SAAS,SAAS,KAAK,EAAE,eAAe,EAAE,SAAS;QACjD,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,UAAU,CAAA;YACtE,UAAU,cAAc;YACxB,OAAO,WAAW,iBAAiB,CAAC,QAAQ,SAAS,MAAM,CAAC;QAC9D,GAAG,IAAM,OAAO;IAClB;IACA,SAAS,cAAc,KAAK,EAAE,GAAG,EAAE,eAAe,EAAE,SAAS;QAC3D,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,UAAU,CAAA;YACtE,UAAU,cAAc;YACxB,OAAO,WAAW,iBAAiB,CAAC,QAAQ,SAAS,WAAW,CAAC,OAAO;QAC1E,GAAG,IAAM;gBAAC,SAAS;gBAAQ,SAAS;aAAK,CAAC,IAAI,CAAC;IACjD;IACA,SAAS,OAAO,KAAK,EAAE,eAAe,EAAE,SAAS;QAC/C,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,QAAQ,CAAA,UAAW,WAAW,eAAe,CAAC,QAAQ,SAAS,MAAM,CAAC,QAAQ,IAAM,OAAO;IAC3J;IACA,SAAS;QACP,mEAAmE;QACnE,iEAAiE;QACjE,IAAI,MAAM,GAAG,EAAE;YACb,OAAO,MAAM,GAAG;QAClB,OAAO;YACL,QAAQ,IAAI,UAAU,cAAc,oBAAoB,EAAE,CAAC,kOAAkO,CAAC;YAC9R,OAAO,IAAI;QACb;IACF;IACA,SAAS,aAAa,IAAI,EAAE,YAAY;QACtC,IAAI;YACF,IAAI,SAAS;YACb,MAAM,OAAO,CAAC;YACd,IAAI,wBAAwB,QAAQ,OAAO,iBAAiB,UAAU;gBACpE,UAAU,IAAI,KAAK;YACrB,OAAO,IAAI,cAAc;gBACvB,IAAI,aAAa,GAAG,IAAI,MAAM;oBAC5B,UAAU,IAAI,KAAK,aAAa,GAAG;gBACrC,OAAO;oBACL,UAAU;gBACZ;gBACA,OAAO,aAAa,IAAI;gBACxB,KAAK,KAAK,GAAG,aAAa,KAAK;gBAC/B,kDAAkD;gBAClD,KAAK,eAAe,GAAG,aAAa,eAAe;YACrD;YACA,IAAI,CAAC,SAAS;gBACZ,UAAU;YACZ;YACA,MAAM,WAAW,IAAI,KAAK;YAC1B,MAAM,UAAU,CAAC,SAAS,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;YAC3D,IAAI,CAAC,MAAM;gBACT,OAAO,wBAAwB;YACjC;YAEA,uEAAuE;YACvE,yEAAyE;YACzE,wEAAwE;YACxE,mEAAmE;YACnE,sEAAsE;YACtE,uEAAuE;YACvE,2EAA2E;YAC3E,iDAAiD;YACjD,KAAK,OAAO,GAAG,SAAS,WAAW,SAAS;YAC5C,MAAM,QAAQ,2BAA2B,SAAS;YAClD,OAAO,WAAW,qBAAqB,CAAC,QAAQ,MAAM,MAAM,CAAC,OAAO;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,UAAU,cAAc,gBAAgB,EAAE,MAAM,OAAO;YACnE,OAAO,OAAO;QAChB;IACF;IACA,SAAS,KAAK,KAAK,EAAE,eAAe,EAAE,SAAS;QAC7C,MAAM,kBAAkB,EAAE;QAC1B,MAAM,aAAa,IAAI;QAEvB,yEAAyE;QACzE,4EAA4E;QAC5E,iCAAiC;QACjC,IAAI,QAAQ;QACZ,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;YACJ,IAAI,OAAO,SAAS,UAAU;gBAC5B,iBAAiB,OAAO;gBACxB,WAAW,GAAG,CAAC,gBAAgB;YACjC,OAAO;gBACL,iBAAiB,OAAO;YAC1B;YACA,gBAAgB,IAAI,CAAC;YACrB;QACF;QACA,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,MAC9D,uIAAuI;QACvI,CAAA;YACE,MAAM,SAAS,WAAW,aAAa,CAAC,QAAQ,SAAS,aAAa,CAAC,iBAAiB,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,GAAG,WAAW,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK;YACnL,IAAI,WAAW,IAAI,GAAG,GAAG;gBACvB,OAAO;YACT,OAAO;gBACL,OAAO,OAAO,IAAI,CAAC;YACrB;QACF,GAAG,IAAM,OAAO;IAClB;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,wBAAwB,QAAQ,EAAE,gBAAgB,EAAE,UAAU;IACrE,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,KAAK,kBAAkB;QACxD,IAAI,IAAI,QAAQ,CAAC,MAAM;YACrB,IAAI,WAAW;YACf,IAAI,YAAY,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACjD,iBAAiB,IAAI,CAAC;QACxB;QAEA,uEAAuE;QACvE,IAAI,qBAAqB,QAAQ,OAAO,sBAAsB,UAAU;YACtE,wBAAwB,mBAAmB,kBAAkB,SAAS,YAAY;QACpF;IACF;AACF;AACA,SAAS,iBAAiB,QAAQ,EAAE,OAAO;IACzC,MAAM,mBAAmB,EAAE;IAC3B,wBAAwB,UAAU;IAClC,IAAI,iBAAiB,MAAM,GAAG,GAAG;QAC/B,QAAQ,IAAI,UAAU,cAAc,WAAW,EAAE,CAAC;;QAE9C,EAAE,iBAAiB,MAAM,KAAK,IAAI,QAAQ,OAAO,EAAE,EAAE,iBAAiB,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BzF,CAAC;IACC;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,EACxB,OAAO,EACP,kBAAkB,EAClB,QAAQ,EACR,OAAO,EACP,GAAG,MACJ;IACC,MAAM,eAAe,WAAW;IAChC,MAAM,0BAA0B,sBAAsB;IACtD;QACE,IAAI,UAAU;YACZ,iBAAiB,UAAU;QAC7B;IACF;IACA,OAAO;QACL,GAAG,IAAI;QACP,SAAS,WAAW;QACpB,UAAU,YAAY;QACtB,SAAS;QACT,oBAAoB;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/shared/utils.js"], "sourcesContent": ["function isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  const [path, ...hashParts] = pathname.split('#');\n  const hash = hashParts.join('#');\n  let normalizedPath = path;\n  if (normalizedPath !== '/') {\n    const pathnameEndsWithSlash = normalizedPath.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      normalizedPath += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n  }\n  if (hash) {\n    normalizedPath += '#' + hash;\n  }\n  return normalizedPath;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\nexport { getLocaleAsPrefix, getLocalePrefix, getLocalizedTemplate, getSortedPathnames, hasPathnamePrefixed, isLocalizableHref, isPromise, matchesPathname, normalizeTrailingSlash, prefixPathname, templateToRegex, unprefixPathname };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,eAAe,IAAI;IAC1B,MAAM,WAAW,OAAO,SAAS,WAAW,KAAK,QAAQ,GAAG;IAC5D,OAAO,YAAY,QAAQ,CAAC,SAAS,UAAU,CAAC;AAClD;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,KAAK,IAAI,IAAI,QAAQ,KAAK,QAAQ,IAAI;IAC/C,OAAO;QACL,MAAM,cAAc,YAAY,IAAI,CAAC;QACrC,OAAO,CAAC;IACV;AACF;AACA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,YAAY,SAAS,CAAC,eAAe;AAC9C;AACA,SAAS,iBAAiB,QAAQ,EAAE,MAAM;IACxC,OAAO,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,QAAQ,GAAG,OAAO;AAC3D;AACA,SAAS,eAAe,MAAM,EAAE,QAAQ;IACtC,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,IAAI,cAAc,IAAI,CAAC,WAAW;QAChC,WAAW,SAAS,KAAK,CAAC;IAC5B;IACA,iBAAiB;IACjB,OAAO;AACT;AACA,SAAS,oBAAoB,MAAM,EAAE,QAAQ;IAC3C,OAAO,aAAa,UAAU,SAAS,UAAU,CAAC,GAAG,OAAO,CAAC,CAAC;AAChE;AACA,SAAS;IACP,IAAI;QACF,gEAAgE;QAChE,OAAO,QAAQ,GAAG,CAAC,yBAAyB,KAAK;IACnD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AACA,SAAS,qBAAqB,cAAc,EAAE,MAAM,EAAE,gBAAgB;IACpE,OAAO,OAAO,mBAAmB,WAAW,iBAAiB,cAAc,CAAC,OAAO,IAAI;AACzF;AACA,SAAS,uBAAuB,QAAQ;IACtC,MAAM,gBAAgB;IACtB,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG,SAAS,KAAK,CAAC;IAC5C,MAAM,OAAO,UAAU,IAAI,CAAC;IAC5B,IAAI,iBAAiB;IACrB,IAAI,mBAAmB,KAAK;QAC1B,MAAM,wBAAwB,eAAe,QAAQ,CAAC;QACtD,IAAI,iBAAiB,CAAC,uBAAuB;YAC3C,kBAAkB;QACpB,OAAO,IAAI,CAAC,iBAAiB,uBAAuB;YAClD,iBAAiB,eAAe,KAAK,CAAC,GAAG,CAAC;QAC5C;IACF;IACA,IAAI,MAAM;QACR,kBAAkB,MAAM;IAC1B;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,sCAAsC,GAC/D,QAAQ,EAAE,0BAA0B,GACpC,QAAQ;IACN,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,MAAM,IAAI,CAAC;AACpB;AACA,SAAS,gBAAgB,MAAM,EAAE,YAAY;IAC3C,OAAO,aAAa,IAAI,KAAK,WAAW,aAAa,QAAQ,EAAE,CAAC,OAAO,IACvE,sEAAsE;IACtE,8BAA8B;IAC9B,kBAAkB;AACpB;AACA,SAAS,kBAAkB,MAAM;IAC/B,OAAO,MAAM;AACf;AACA,SAAS,gBAAgB,QAAQ;IAC/B,MAAM,eAAe,QACrB,4CAA4C;KAC3C,OAAO,CAAC,2BAA2B,QACpC,iCAAiC;KAChC,OAAO,CAAC,uBAAuB,OAChC,uCAAuC;KACtC,OAAO,CAAC,iBAAiB;IAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACvC;AACA,SAAS,0BAA0B,QAAQ;IACzC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,kBAAkB,QAAQ;IACjC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,iBAAiB,QAAQ;IAChC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,qBAAqB,CAAC,EAAE,CAAC;IAChC,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,YAAY,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,MAAM,MAAM;IACrD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,MAAM,WAAW,KAAK,CAAC,EAAE;QACzB,MAAM,WAAW,KAAK,CAAC,EAAE;QAEzB,wDAAwD;QACxD,IAAI,CAAC,YAAY,UAAU,OAAO,CAAC;QACnC,IAAI,YAAY,CAAC,UAAU,OAAO;QAClC,IAAI,CAAC,YAAY,CAAC,UAAU;QAE5B,mDAAmD;QACnD,IAAI,CAAC,iBAAiB,aAAa,iBAAiB,WAAW,OAAO,CAAC;QACvE,IAAI,iBAAiB,aAAa,CAAC,iBAAiB,WAAW,OAAO;QAEtE,4DAA4D;QAC5D,IAAI,CAAC,kBAAkB,aAAa,kBAAkB,WAAW,OAAO,CAAC;QACzE,IAAI,kBAAkB,aAAa,CAAC,kBAAkB,WAAW,OAAO;QAExE,8EAA8E;QAC9E,IAAI,CAAC,0BAA0B,aAAa,0BAA0B,WAAW;YAC/E,OAAO,CAAC;QACV;QACA,IAAI,0BAA0B,aAAa,CAAC,0BAA0B,WAAW;YAC/E,OAAO;QACT;QACA,IAAI,aAAa,UAAU;IAC7B;IAEA,uCAAuC;IACvC,OAAO;AACT;AACA,SAAS,mBAAmB,SAAS;IACnC,OAAO,UAAU,IAAI,CAAC;AACxB;AACA,SAAS,UAAU,KAAK;IACtB,kDAAkD;IAClD,OAAO,OAAO,MAAM,IAAI,KAAK;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/web/spec-extension/cookies.ts"], "sourcesContent": ["export {\n  RequestCookies,\n  ResponseCookies,\n  stringifyCookie,\n} from 'next/dist/compiled/@edge-runtime/cookies'\n"], "names": ["RequestCookies", "ResponseCookies", "string<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;IACEA,cAAc,EAAA;eAAdA,SAAAA,cAAc;;IACdC,eAAe,EAAA;eAAfA,SAAAA,eAAe;;IACfC,eAAe,EAAA;eAAfA,SAAAA,eAAe;;;yBACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/web/spec-extension/adapters/request-cookies.ts"], "sourcesContent": ["import { RequestCookies } from '../cookies'\n\nimport { ResponseCookies } from '../cookies'\nimport { ReflectAdapter } from './reflect'\nimport { workAsyncStorage } from '../../../app-render/work-async-storage.external'\nimport {\n  getExpectedRequestStore,\n  type RequestStore,\n} from '../../../app-render/work-unit-async-storage.external'\n\n/**\n * @internal\n */\nexport class ReadonlyRequestCookiesError extends Error {\n  constructor() {\n    super(\n      'Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\n// We use this to type some APIs but we don't construct instances directly\nexport type { ResponseCookies }\n\n// The `cookies()` API is a mix of request and response cookies. For `.get()` methods,\n// we want to return the request cookie if it exists. For mutative methods like `.set()`,\n// we want to return the response cookie.\nexport type ReadonlyRequestCookies = Omit<\n  RequestCookies,\n  'set' | 'clear' | 'delete'\n> &\n  Pick<ResponseCookies, 'set' | 'delete'>\n\nexport class RequestCookiesAdapter {\n  public static seal(cookies: RequestCookies): ReadonlyRequestCookies {\n    return new Proxy(cookies as any, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'clear':\n          case 'delete':\n          case 'set':\n            return ReadonlyRequestCookiesError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n}\n\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies')\n\nexport function getModifiedCookieValues(\n  cookies: ResponseCookies\n): ResponseCookie[] {\n  const modified: ResponseCookie[] | undefined = (cookies as unknown as any)[\n    SYMBOL_MODIFY_COOKIE_VALUES\n  ]\n  if (!modified || !Array.isArray(modified) || modified.length === 0) {\n    return []\n  }\n\n  return modified\n}\n\ntype SetCookieArgs =\n  | [key: string, value: string, cookie?: Partial<ResponseCookie>]\n  | [options: ResponseCookie]\n\nexport function appendMutableCookies(\n  headers: Headers,\n  mutableCookies: ResponseCookies\n): boolean {\n  const modifiedCookieValues = getModifiedCookieValues(mutableCookies)\n  if (modifiedCookieValues.length === 0) {\n    return false\n  }\n\n  // Return a new response that extends the response with\n  // the modified cookies as fallbacks. `res` cookies\n  // will still take precedence.\n  const resCookies = new ResponseCookies(headers)\n  const returnedCookies = resCookies.getAll()\n\n  // Set the modified cookies as fallbacks.\n  for (const cookie of modifiedCookieValues) {\n    resCookies.set(cookie)\n  }\n\n  // Set the original cookies as the final values.\n  for (const cookie of returnedCookies) {\n    resCookies.set(cookie)\n  }\n\n  return true\n}\n\ntype ResponseCookie = NonNullable<\n  ReturnType<InstanceType<typeof ResponseCookies>['get']>\n>\n\nexport class MutableRequestCookiesAdapter {\n  public static wrap(\n    cookies: RequestCookies,\n    onUpdateCookies?: (cookies: string[]) => void\n  ): ResponseCookies {\n    const responseCookies = new ResponseCookies(new Headers())\n    for (const cookie of cookies.getAll()) {\n      responseCookies.set(cookie)\n    }\n\n    let modifiedValues: ResponseCookie[] = []\n    const modifiedCookies = new Set<string>()\n    const updateResponseCookies = () => {\n      // TODO-APP: change method of getting workStore\n      const workStore = workAsyncStorage.getStore()\n      if (workStore) {\n        workStore.pathWasRevalidated = true\n      }\n\n      const allCookies = responseCookies.getAll()\n      modifiedValues = allCookies.filter((c) => modifiedCookies.has(c.name))\n      if (onUpdateCookies) {\n        const serializedCookies: string[] = []\n        for (const cookie of modifiedValues) {\n          const tempCookies = new ResponseCookies(new Headers())\n          tempCookies.set(cookie)\n          serializedCookies.push(tempCookies.toString())\n        }\n\n        onUpdateCookies(serializedCookies)\n      }\n    }\n\n    const wrappedCookies = new Proxy(responseCookies, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          // A special symbol to get the modified cookie values\n          case SYMBOL_MODIFY_COOKIE_VALUES:\n            return modifiedValues\n\n          // TODO: Throw error if trying to set a cookie after the response\n          // headers have been set.\n          case 'delete':\n            return function (...args: [string] | [ResponseCookie]) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.delete(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n          case 'set':\n            return function (...args: SetCookieArgs) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.set(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n\n    return wrappedCookies\n  }\n}\n\nexport function wrapWithMutableAccessCheck(\n  responseCookies: ResponseCookies\n): ResponseCookies {\n  const wrappedCookies = new Proxy(responseCookies, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'delete':\n          return function (...args: [string] | [ResponseCookie]) {\n            ensureCookiesAreStillMutable('cookies().delete')\n            target.delete(...args)\n            return wrappedCookies\n          }\n        case 'set':\n          return function (...args: SetCookieArgs) {\n            ensureCookiesAreStillMutable('cookies().set')\n            target.set(...args)\n            return wrappedCookies\n          }\n\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  })\n  return wrappedCookies\n}\n\nexport function areCookiesMutableInCurrentPhase(requestStore: RequestStore) {\n  return requestStore.phase === 'action'\n}\n\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */\nfunction ensureCookiesAreStillMutable(callingExpression: string) {\n  const requestStore = getExpectedRequestStore(callingExpression)\n  if (!areCookiesMutableInCurrentPhase(requestStore)) {\n    // TODO: maybe we can give a more precise error message based on callingExpression?\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\nexport function responseCookiesToRequestCookies(\n  responseCookies: ResponseCookies\n): RequestCookies {\n  const requestCookies = new RequestCookies(new Headers())\n  for (const cookie of responseCookies.getAll()) {\n    requestCookies.set(cookie)\n  }\n  return requestCookies\n}\n"], "names": ["MutableRequestCookiesAdapter", "ReadonlyRequestCookiesError", "RequestCookiesAdapter", "appendMutableCookies", "areCookiesMutableInCurrentPhase", "getModifiedCookieValues", "responseCookiesToRequestCookies", "wrapWithMutableAccessCheck", "Error", "constructor", "callable", "seal", "cookies", "Proxy", "get", "target", "prop", "receiver", "ReflectAdapter", "SYMBOL_MODIFY_COOKIE_VALUES", "Symbol", "for", "modified", "Array", "isArray", "length", "headers", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resCookies", "ResponseCookies", "returnedCookies", "getAll", "cookie", "set", "wrap", "onUpdateCookies", "responseCookies", "Headers", "modifiedV<PERSON>ues", "modifiedCookies", "Set", "updateResponseCookies", "workStore", "workAsyncStorage", "getStore", "pathWasRevalidated", "allCookies", "filter", "c", "has", "name", "serializedCookies", "tempCookies", "push", "toString", "wrappedCookies", "args", "add", "delete", "ensureCookiesAreStillMutable", "requestStore", "phase", "callingExpression", "getExpectedRequestStore", "requestCookies", "RequestCookies"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAyGaA,4BAA4B,EAAA;eAA5BA;;IA5FAC,2BAA2B,EAAA;eAA3BA;;IAwBAC,qBAAqB,EAAA;eAArBA;;IAoCGC,oBAAoB,EAAA;eAApBA;;IAwIAC,+BAA+B,EAAA;eAA/BA;;IAzJAC,uBAAuB,EAAA;eAAvBA;;IA4KAC,+BAA+B,EAAA;eAA/BA;;IA9CAC,0BAA0B,EAAA;eAA1BA;;;yBAtLe;yBAGA;0CACE;8CAI1B;AAKA,MAAMN,oCAAoCO;IAC/CC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIT;IACZ;AACF;AAcO,MAAMC;IACX,OAAcS,KAAKC,OAAuB,EAA0B;QAClE,OAAO,IAAIC,MAAMD,SAAgB;YAC/BE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOf,4BAA4BS,QAAQ;oBAC7C;wBACE,OAAOQ,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;AACF;AAEA,MAAME,8BAA8BC,OAAOC,GAAG,CAAC;AAExC,SAAShB,wBACdO,OAAwB;IAExB,MAAMU,WAA0CV,OAA0B,CACxEO,4BACD;IACD,IAAI,CAACG,YAAY,CAACC,MAAMC,OAAO,CAACF,aAAaA,SAASG,MAAM,KAAK,GAAG;QAClE,OAAO,EAAE;IACX;IAEA,OAAOH;AACT;AAMO,SAASnB,qBACduB,OAAgB,EAChBC,cAA+B;IAE/B,MAAMC,uBAAuBvB,wBAAwBsB;IACrD,IAAIC,qBAAqBH,MAAM,KAAK,GAAG;QACrC,OAAO;IACT;IAEA,uDAAuD;IACvD,mDAAmD;IACnD,8BAA8B;IAC9B,MAAMI,aAAa,IAAIC,SAAAA,eAAe,CAACJ;IACvC,MAAMK,kBAAkBF,WAAWG,MAAM;IAEzC,yCAAyC;IACzC,KAAK,MAAMC,UAAUL,qBAAsB;QACzCC,WAAWK,GAAG,CAACD;IACjB;IAEA,gDAAgD;IAChD,KAAK,MAAMA,UAAUF,gBAAiB;QACpCF,WAAWK,GAAG,CAACD;IACjB;IAEA,OAAO;AACT;AAMO,MAAMjC;IACX,OAAcmC,KACZvB,OAAuB,EACvBwB,eAA6C,EAC5B;QACjB,MAAMC,kBAAkB,IAAIP,SAAAA,eAAe,CAAC,IAAIQ;QAChD,KAAK,MAAML,UAAUrB,QAAQoB,MAAM,GAAI;YACrCK,gBAAgBH,GAAG,CAACD;QACtB;QAEA,IAAIM,iBAAmC,EAAE;QACzC,MAAMC,kBAAkB,IAAIC;QAC5B,MAAMC,wBAAwB;YAC5B,+CAA+C;YAC/C,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;YAC3C,IAAIF,WAAW;gBACbA,UAAUG,kBAAkB,GAAG;YACjC;YAEA,MAAMC,aAAaV,gBAAgBL,MAAM;YACzCO,iBAAiBQ,WAAWC,MAAM,CAAC,CAACC,IAAMT,gBAAgBU,GAAG,CAACD,EAAEE,IAAI;YACpE,IAAIf,iBAAiB;gBACnB,MAAMgB,oBAA8B,EAAE;gBACtC,KAAK,MAAMnB,UAAUM,eAAgB;oBACnC,MAAMc,cAAc,IAAIvB,SAAAA,eAAe,CAAC,IAAIQ;oBAC5Ce,YAAYnB,GAAG,CAACD;oBAChBmB,kBAAkBE,IAAI,CAACD,YAAYE,QAAQ;gBAC7C;gBAEAnB,gBAAgBgB;YAClB;QACF;QAEA,MAAMI,iBAAiB,IAAI3C,MAAMwB,iBAAiB;YAChDvB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,qDAAqD;oBACrD,KAAKG;wBACH,OAAOoB;oBAET,iEAAiE;oBACjE,yBAAyB;oBACzB,KAAK;wBACH,OAAO,SAAU,GAAGkB,IAAiC;4BACnDjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACN,IAAI;4BAEtD,IAAI;gCACFpC,OAAO4C,MAAM,IAAIF;gCACjB,OAAOD;4BACT,SAAU;gCACRd;4BACF;wBACF;oBACF,KAAK;wBACH,OAAO,SAAU,GAAGe,IAAmB;4BACrCjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACN,IAAI;4BAEtD,IAAI;gCACFpC,OAAOmB,GAAG,IAAIuB;gCACd,OAAOD;4BACT,SAAU;gCACRd;4BACF;wBACF;oBAEF;wBACE,OAAOxB,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;QAEA,OAAOuC;IACT;AACF;AAEO,SAASjD,2BACd8B,eAAgC;IAEhC,MAAMmB,iBAAiB,IAAI3C,MAAMwB,iBAAiB;QAChDvB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAK;oBACH,OAAO,SAAU,GAAGyC,IAAiC;wBACnDG,6BAA6B;wBAC7B7C,OAAO4C,MAAM,IAAIF;wBACjB,OAAOD;oBACT;gBACF,KAAK;oBACH,OAAO,SAAU,GAAGC,IAAmB;wBACrCG,6BAA6B;wBAC7B7C,OAAOmB,GAAG,IAAIuB;wBACd,OAAOD;oBACT;gBAEF;oBACE,OAAOtC,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;YAC5C;QACF;IACF;IACA,OAAOuC;AACT;AAEO,SAASpD,gCAAgCyD,YAA0B;IACxE,OAAOA,aAAaC,KAAK,KAAK;AAChC;AAEA;;;;;;GAMG,GACH,SAASF,6BAA6BG,iBAAyB;IAC7D,MAAMF,eAAeG,CAAAA,GAAAA,8BAAAA,uBAAuB,EAACD;IAC7C,IAAI,CAAC3D,gCAAgCyD,eAAe;QAClD,mFAAmF;QACnF,MAAM,IAAI5D;IACZ;AACF;AAEO,SAASK,gCACd+B,eAAgC;IAEhC,MAAM4B,iBAAiB,IAAIC,SAAAA,cAAc,CAAC,IAAI5B;IAC9C,KAAK,MAAML,UAAUI,gBAAgBL,MAAM,GAAI;QAC7CiC,eAAe/B,GAAG,CAACD;IACrB;IACA,OAAOgC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5851, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAAWE;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC,OAAO;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAAWG;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoVeA,QAAQ,EAAA;eAARA;;IA3CAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IAuKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IAhXAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAgSAC,wBAAwB,EAAA;eAAxBA;;IAvcAC,gCAAgC,EAAA;eAAhCA;;IA6ZAC,yBAAyB,EAAA;eAAzBA;;IApYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IAmDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA9hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACAR,oCAAoChB,OAAOR,YAAYoB;AACzD;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASvE,4CACd6C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMe,kBAAkBf,eAAeQ,UAAU,CAACQ,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1B,MAAM5B,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBlB,qBAAqB,GAAGS;gBACxCS,gBAAgBhB,yBAAyB,GAAGuC;gBAC5C,IAAIZ,eAAekB,UAAU,KAAK,MAAM;oBACtC,2EAA2E;oBAC3E,sEAAsE;oBACtE7B,gBAAgB8B,iBAAiB,GAAG;gBACtC;YACF;QACF;QACAf,oCAAoChB,OAAOR,YAAYoB;IACzD;IACA,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASrB,SAAS,EAAE+D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C+B;IACA,IAAI/B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACqD,qBAAqBjC,OAAOR;AACtD;AAEA,SAASyC,qBAAqBjC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY+B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBhC,IAAY+B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBlB,MAAc;IAC7C,OACEA,OAAOmB,QAAQ,CACb,sEAEFnB,OAAOmB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIV,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMc,6BAA6B;AAEnC,SAASlB,gCAAgCe,OAAe;IACtD,MAAMhB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BhB,MAAcoB,MAAM,GAAGD;IACzB,OAAOnB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcoB,MAAM,KAAKD,8BAC1B,UAAUnB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASlE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgByD,MAAM,GAAG;AAClC;AAEO,SAAShF,qBACdiF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAc1D,eAAe,CAACwC,IAAI,IAAImB,cAAc3D,eAAe;IACnE,OAAO0D,cAAc1D,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJ4D,MAAM,CACL,CAACC,SACC,OAAOA,OAAOpC,KAAK,KAAK,YAAYoC,OAAOpC,KAAK,CAACgC,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEpD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLsC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAExD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASyB;IACP,IAAI,CAACtD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI6C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDe;IACA,MAAMZ,aAAa,IAAI6B;IACvB,qFAAqF;IACrF,IAAI;QACFtE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAOiC,GAAY;QACnB9B,WAAWC,KAAK,CAAC6B;IACnB;IACA,OAAO9B,WAAWQ,MAAM;AAC1B;AAOO,SAASlE,8BACdgC,aAAmC;IAEnC,MAAM0B,aAAa,IAAI6B;IAEvB,IAAIvD,cAAcyD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCzD,cAAcyD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1CjC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DiC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMlC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWQ,MAAM;AAC1B;AAEO,SAAStE,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,MAAM+D,YAAYC,0BAAAA,gBAAgB,CAAC1C,QAAQ;IAE3C,IACEyC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMjE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDhB,OAAAA,OAAK,CAACiF,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACnE,cAAcoE,YAAY,EAAEtE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9B1B,qBACEsF,UAAUvD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDxB,iCAAiCqB,YAAY+D,WAAW7D;YAC1D;QACF;IACF;AACF;AAEA,MAAMqE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASlG,0BACd4B,KAAa,EACbuE,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI4B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBtF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLsD,cAAcvD,yBAAyB,IACvCwD,cAAcxD,yBAAyB,EACvC;QACAuF,kBAAkBnF,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM6C,UAAU,CAAC,OAAO,EAAElC,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQwD,8BAA8BxC,SAASqC;QACrDC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASwD,8BACPxC,OAAe,EACfqC,cAAsB;IAEtB,MAAMrD,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BhB,MAAMX,KAAK,GAAG,YAAY2B,UAAUqC;IACpC,OAAOrD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbwE,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAIkC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIrC,cAAcvD,yBAAyB,EAAE;QAC3C0F,YAAYnC,cAAcvD,yBAAyB;QACnD2F,iBAAiBpC,cAAczD,qBAAqB;QACpD8F,aAAarC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcxD,yBAAyB,EAAE;QAClD0F,YAAYlC,cAAcxD,yBAAyB;QACnD2F,iBAAiBnC,cAAc1D,qBAAqB;QACpD8F,aAAapC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL4C,YAAY;QACZC,iBAAiB5F;QACjB6F,aAAa;IACf;IAEA,IAAIL,kBAAkBnF,oBAAoB,IAAIsF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQ5D,KAAK,CAACyD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAI5E,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;IACrD,IAAIA,cAAciD,MAAM,EAAE;QACxB,IAAK,IAAIwC,IAAI,GAAGA,IAAIzF,cAAciD,MAAM,EAAEwC,IAAK;YAC7CD,QAAQ5D,KAAK,CAAC5B,aAAa,CAACyF,EAAE;QAChC;QAEA,MAAM,IAAIhF,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACyE,kBAAkBtF,mBAAmB,EAAE;QAC1C,IAAIsF,kBAAkBrF,kBAAkB,EAAE;YACxC,IAAIwF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIwE,kBAAkBpF,kBAAkB,EAAE;YAC/C,IAAIuF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "React", "fn", "logErrorOrWarn", "process", "env", "__NEXT_DYNAMIC_IO", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": ";;;;+BAyCgBA,+CAAAA;;;eAAAA;;;+DAzCO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOC,OAAMD,KAAK,KAAK,aACnBC,OAAMD,KAAK,GACX,CAACE,KAA+BA;AAEtC,qEAAqE;AACrE,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,iBAAiBC,QAAQC,GAAG,CAACC,iBAAiB,GAChDC,QAAQC,KAAK,gCACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBV,MAC7B,AACA,CAACW,yEADyE;IAExE,IAAI;QACFR,eAAeL,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASF,4CACde,UAAoC;IAEpC,OAAO,SAASC,gBAAgB,GAAGC,IAAU;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIV,QAAQC,GAAG,CAACW,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DnB,eAAeY;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAMJ,MAAMM,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBL,uBAAuBC;YACzB;QACF,OAAO;;QAEP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  workStore.invalidUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["isRequestAPICallableInsideAfter", "throwForSearchParamsAccessInUseCache", "throwWithStaticGenerationBailoutError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "StaticGenBailoutError", "workStore", "error", "Error", "invalidUsageError", "afterTaskStore", "afterTaskAsyncStorage", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;;;;;;;;;;IAkCgBA,+BAA+B,EAAA;eAA/BA;;IAZAC,oCAAoC,EAAA;eAApCA;;IAlBAC,qCAAqC,EAAA;eAArCA;;IASAC,qDAAqD,EAAA;eAArDA;;;yCAbsB;+CACA;AAG/B,SAASD,sCACdE,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,MAAM,iDAAiD,EAAEC,WAAW,0HAA0H,CAAC,GADpM,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASF,sDACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,MAAM,4EAA4E,EAAEC,WAAW,0HAA0H,CAAC,GAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASJ,qCACdM,SAAoB;IAEpB,MAAMC,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,CAAC,MAAM,EAAEF,UAAUH,KAAK,CAAC,oVAAoV,CAAC,GADlW,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAG,UAAUG,iBAAiB,KAAKF;IAEhC,MAAMA;AACR;AAEO,SAASR;IACd,MAAMW,iBAAiBC,+BAAAA,qBAAqB,CAACC,QAAQ;IACrD,OAAOF,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBG,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6632, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/request/cookies.ts"], "sourcesContent": ["import {\n  type ReadonlyRequestCookies,\n  type ResponseCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * In this version of Next.js `cookies()` returns a Promise however you can still reference the properties of the underlying cookies object\n * synchronously to facilitate migration. The `UnsafeUnwrappedCookies` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `cookies()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedCookies` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `cookies()` value can be awaited or you should call `cookies()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedCookies` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `cookies()` will only return a Promise and you will not be able to access the underlying cookies object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedCookies` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedCookies = ReadonlyRequestCookies\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedExoticCookies(underlyingCookies)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We don't track dynamic access here because access will be tracked when you access\n        // one of the properties of the cookies object.\n        return makeDynamicallyTrackedExoticCookies(\n          workStore.route,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We are prerendering with PPR. We need track dynamic access here eagerly\n        // to keep continuity with how cookies has worked in PPR without dynamicIO.\n        postponeWithTracking(\n          workStore.route,\n          callingExpression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We track dynamic access here so we don't need to wrap the cookies in\n        // individual property access tracking.\n        throwToInterruptStaticGeneration(\n          callingExpression,\n          workStore,\n          workUnitStore\n        )\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using cookies inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  // cookies is being called in a dynamic context\n\n  const requestStore = getExpectedRequestStore(callingExpression)\n\n  let underlyingCookies: ReadonlyRequestCookies\n\n  if (areCookiesMutableInCurrentPhase(requestStore)) {\n    // We can't conditionally return different types here based on the context.\n    // To avoid confusion, we always return the readonly type here.\n    underlyingCookies =\n      requestStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n  } else {\n    underlyingCookies = requestStore.cookies\n  }\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    return makeUntrackedExoticCookiesWithDevWarnings(\n      underlyingCookies,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticCookies(underlyingCookies)\n  }\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeDynamicallyTrackedExoticCookies(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`cookies()[Symbol.iterator]()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    size: {\n      get() {\n        const expression = '`cookies().size`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    has: {\n      value: function has() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: underlyingCookies[Symbol.iterator]\n        ? underlyingCookies[Symbol.iterator].bind(underlyingCookies)\n        : // TODO this is a polyfill for when the underlying type is ResponseCookies\n          // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n          // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n          // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n          // has extra properties not available on RequestCookie instances.\n          polyfilledResponseCookiesIterator.bind(underlyingCookies),\n    },\n    size: {\n      get(): number {\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: underlyingCookies.get.bind(underlyingCookies),\n    },\n    getAll: {\n      value: underlyingCookies.getAll.bind(underlyingCookies),\n    },\n    has: {\n      value: underlyingCookies.has.bind(underlyingCookies),\n    },\n    set: {\n      value: underlyingCookies.set.bind(underlyingCookies),\n    },\n    delete: {\n      value: underlyingCookies.delete.bind(underlyingCookies),\n    },\n    clear: {\n      value:\n        // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n        typeof underlyingCookies.clear === 'function'\n          ? // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.bind(underlyingCookies)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise),\n    },\n    toString: {\n      value: underlyingCookies.toString.bind(underlyingCookies),\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = new Promise<ReadonlyRequestCookies>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingCookies))\n  )\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...cookies()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingCookies[Symbol.iterator]\n          ? underlyingCookies[Symbol.iterator].apply(\n              underlyingCookies,\n              arguments as any\n            )\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.call(underlyingCookies)\n      },\n      writable: false,\n    },\n    size: {\n      get(): number {\n        const expression = '`cookies().size`'\n        syncIODev(route, expression)\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.get.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.getAll.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    has: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.has.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.set.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.delete.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        syncIODev(route, expression)\n        // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n        return typeof underlyingCookies.clear === 'function'\n          ? // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.apply(underlyingCookies, arguments)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.call(underlyingCookies, promise)\n      },\n      writable: false,\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()` or implicit casting'\n        syncIODev(route, expression)\n        return underlyingCookies.toString.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'object' &&\n    arg !== null &&\n    typeof (arg as any).name === 'string'\n    ? `'${(arg as any).name}'`\n    : typeof arg === 'string'\n      ? `'${arg}'`\n      : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction polyfilledResponseCookiesIterator(\n  this: ResponseCookies\n): ReturnType<ReadonlyRequestCookies[typeof Symbol.iterator]> {\n  return this.getAll()\n    .map((c) => [c.name, c] as [string, any])\n    .values()\n}\n\nfunction polyfilledResponseCookiesClear(\n  this: ResponseCookies,\n  returnable: Promise<ReadonlyRequestCookies>\n): typeof returnable {\n  for (const cookie of this.getAll()) {\n    this.delete(cookie.name)\n  }\n  return returnable\n}\n\ntype CookieExtensions = {\n  [K in keyof ReadonlyRequestCookies | 'clear']: unknown\n}\n"], "names": ["cookies", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedExoticCookies", "type", "dynamicShouldError", "StaticGenBailoutError", "makeDynamicallyTrackedExoticCookies", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "requestStore", "getExpectedRequestStore", "areCookiesMutableInCurrentPhase", "userspaceMutableCookies", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeUntrackedExoticCookiesWithDevWarnings", "RequestCookiesAdapter", "seal", "RequestCookies", "Headers", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "get", "promise", "makeHangingPromise", "renderSignal", "set", "Object", "defineProperties", "Symbol", "iterator", "value", "expression", "error", "createCookiesAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "size", "arguments", "length", "describeNameArg", "getAll", "has", "arg", "delete", "clear", "toString", "cachedCookies", "Promise", "resolve", "bind", "polyfilledResponseCookiesIterator", "polyfilledResponseCookiesClear", "scheduleImmediate", "syncIODev", "apply", "call", "writable", "name", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "prefix", "map", "c", "values", "returnable", "cookie"], "mappings": ";;;;+BAiDg<PERSON>,WAAAA;;;eAAAA;;;gCA5CT;yBACwB;0CACE;8CAI1B;kCAOA;yCAE+B;uCACH;0DACyB;2BAC1B;uBACc;AAyBzC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,CAAAA,GAAAA,OAAAA,+BAA+B,KAChC;YACA,MAAM,OAAA,cAGL,CAHK,IAAIC,MAER,AADA,CACC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,+BAD+B,0MAC0M,CAAC,GAF/P,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC;YAC1B,OAAOC,2BAA2BF;QACpC;QAEA,IAAIP,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,SAAS;gBAClC,MAAM,OAAA,cAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIL,cAAcU,IAAI,KAAK,kBAAkB;gBAClD,MAAM,OAAA,cAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIR,UAAUc,kBAAkB,EAAE;YAChC,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEf,UAAUQ,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,oFAAoF;gBACpF,+CAA+C;gBAC/C,OAAOG,oCACLhB,UAAUQ,KAAK,EACfL;YAEJ,OAAO,IAAIA,cAAcU,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,0EAA0E;gBAC1E,2EAA2E;gBAC3EI,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjB,UAAUQ,KAAK,EACfT,mBACAI,cAAce,eAAe;YAEjC,OAAO,IAAIf,cAAcU,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,uEAAuE;gBACvE,uCAAuC;gBACvCM,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BpB,mBACAC,WACAG;YAEJ;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFiB,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACpB,WAAWG;IAC7C;IAEA,+CAA+C;IAE/C,MAAMkB,eAAeC,CAAAA,GAAAA,8BAAAA,uBAAuB,EAACvB;IAE7C,IAAIW;IAEJ,IAAIa,CAAAA,GAAAA,gBAAAA,+BAA+B,EAACF,eAAe;QACjD,2EAA2E;QAC3E,+DAA+D;QAC/DX,oBACEW,aAAaG,uBAAuB;IACxC,OAAO;QACLd,oBAAoBW,aAAavB,OAAO;IAC1C;IAEA,IAAI2B,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiB,CAAA,CAAC3B,aAAAA,OAAAA,KAAAA,IAAAA,UAAW4B,iBAAiB,GAAE;QAC3E,OAAOC,0CACLnB,mBACAV,aAAAA,OAAAA,KAAAA,IAAAA,UAAWQ,KAAK;IAEpB,OAAO;QACL,OAAOI,2BAA2BF;IACpC;AACF;AAEA,SAASC;IACP,OAAOmB,gBAAAA,qBAAqB,CAACC,IAAI,CAAC,IAAIC,SAAAA,cAAc,CAAC,IAAIC,QAAQ,CAAC;AACpE;AAGA,MAAMC,gBAAgB,IAAIC;AAK1B,SAASnB,oCACPR,KAAa,EACb4B,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCJ,eAAeK,YAAY,EAC3B;IAEFP,cAAcQ,GAAG,CAACN,gBAAgBG;IAElCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAgB,MAAM;YACJd;gBACE,MAAMU,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAE,KAAK;YACHS,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAoB,QAAQ;YACNT,OAAO,SAASS;gBACd,IAAIR;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAqB,KAAK;YACHV,OAAO,SAASU;gBACd,IAAIT;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAM,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMU,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPV,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLV,aAAa;oBACf;gBACF;gBACA,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAuB,QAAQ;YACNZ,OAAO;gBACL,IAAIC;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLL,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAwB,OAAO;YACLb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAyB,UAAU;YACRd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;IACF;IAEA,OAAOG;AACT;AAEA,SAAS3B,2BACPF,iBAAyC;IAEzC,MAAMoD,gBAAgB5B,cAAcI,GAAG,CAAC5B;IACxC,IAAIoD,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMvB,UAAUwB,QAAQC,OAAO,CAACtD;IAChCwB,cAAcQ,GAAG,CAAChC,mBAAmB6B;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAOrC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,GACrCpC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,CAACmB,IAAI,CAACvD,qBAExC,AACA,qGADqG,YACY;YACjH,oHAAoH;YACpH,iEAAiE;YACjEwD,kCAAkCD,IAAI,CAACvD;QAC7C;QACA0C,MAAM;YACJd;gBACE,OAAO5B,kBAAkB0C,IAAI;YAC/B;QACF;QACAd,KAAK;YACHS,OAAOrC,kBAAkB4B,GAAG,CAAC2B,IAAI,CAACvD;QACpC;QACA8C,QAAQ;YACNT,OAAOrC,kBAAkB8C,MAAM,CAACS,IAAI,CAACvD;QACvC;QACA+C,KAAK;YACHV,OAAOrC,kBAAkB+C,GAAG,CAACQ,IAAI,CAACvD;QACpC;QACAgC,KAAK;YACHK,OAAOrC,kBAAkBgC,GAAG,CAACuB,IAAI,CAACvD;QACpC;QACAiD,QAAQ;YACNZ,OAAOrC,kBAAkBiD,MAAM,CAACM,IAAI,CAACvD;QACvC;QACAkD,OAAO;YACLb,OACE,AACA,OAAOrC,kBAAkBkD,KAAK,KAAK,aAE/BlD,kBAAkBkD,KAAK,CAACK,IAAI,CAACvD,YAHwD,SAKrF,AACA,qGADqG,YACY;YACjH,oHAAoH;YACpH,iEAAiE;YACjEyD,+BAA+BF,IAAI,CAACvD,mBAAmB6B;QAC/D;QACAsB,UAAU;YACRd,OAAOrC,kBAAkBmD,QAAQ,CAACI,IAAI,CAACvD;QACzC;IACF;IAEA,OAAO6B;AACT;AAEA,SAASV,0CACPnB,iBAAyC,EACzCF,KAAc;IAEd,MAAMsD,gBAAgB5B,cAAcI,GAAG,CAAC5B;IACxC,IAAIoD,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMvB,UAAU,IAAIwB,QAAgC,CAACC,UACnDI,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMJ,QAAQtD;IAElCwB,cAAcQ,GAAG,CAAChC,mBAAmB6B;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,GACrCpC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,CAACwB,KAAK,CACtC5D,mBACA2C,aAGF,AACA,qGADqG,YACY;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEa,kCAAkCK,IAAI,CAAC7D;YAC7C;YACA8D,UAAU;QACZ;QACApB,MAAM;YACJd;gBACE,MAAMU,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB0C,IAAI;YAC/B;QACF;QACAd,KAAK;YACHS,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB4B,GAAG,CAACgC,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACAhB,QAAQ;YACNT,OAAO,SAASS;gBACd,IAAIR;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB8C,MAAM,CAACc,KAAK,CACnC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;QACAf,KAAK;YACHV,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB+C,GAAG,CAACa,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACA9B,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMU,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPV,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLV,aAAa;oBACf;gBACF;gBACAqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBgC,GAAG,CAAC4B,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACAb,QAAQ;YACNZ,OAAO;gBACL,IAAIC;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLL,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBiD,MAAM,CAACW,KAAK,CACnC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;QACAZ,OAAO;YACLb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,mFAAmF;gBACnF,OAAO,OAAOtC,kBAAkBkD,KAAK,KAAK,aAEtClD,kBAAkBkD,KAAK,CAACU,KAAK,CAAC5D,mBAAmB2C,aAGjD,AADA,qGAAqG,YACY;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEc,+BAA+BI,IAAI,CAAC7D,mBAAmB6B;YAC7D;YACAiC,UAAU;QACZ;QACAX,UAAU;YACRd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBmD,QAAQ,CAACS,KAAK,CACrC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;IACF;IAEA,OAAOjC;AACT;AAEA,SAASgB,gBAAgBG,GAAY;IACnC,OAAO,OAAOA,QAAQ,YACpBA,QAAQ,QACR,OAAQA,IAAYe,IAAI,KAAK,WAC3B,CAAC,CAAC,EAAGf,IAAYe,IAAI,CAAC,CAAC,CAAC,GACxB,OAAOf,QAAQ,WACb,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GACV;AACR;AAEA,SAASW,UAAU7D,KAAyB,EAAEwC,UAAkB;IAC9D,MAAM7C,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcU,IAAI,KAAK,aACvBV,cAAcuE,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMrD,eAAelB;QACrBwE,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACtD;IACzC;IACA,gCAAgC;IAChCuD,kBAAkBpE,OAAOwC;AAC3B;AAEA,MAAM4B,oBAAoBC,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnE3B;AAGF,SAASA,yBACP1C,KAAyB,EACzBwC,UAAkB;IAElB,MAAM8B,SAAStE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAID,MACT,GAAGuE,OAAO,KAAK,EAAE9B,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASkB;IAGP,OAAO,IAAI,CAACV,MAAM,GACfuB,GAAG,CAAC,CAACC,IAAM;YAACA,EAAEP,IAAI;YAAEO;SAAE,EACtBC,MAAM;AACX;AAEA,SAASd,+BAEPe,UAA2C;IAE3C,KAAK,MAAMC,UAAU,IAAI,CAAC3B,MAAM,GAAI;QAClC,IAAI,CAACG,MAAM,CAACwB,OAAOV,IAAI;IACzB;IACA,OAAOS;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/web/spec-extension/adapters/headers.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReadonlyHeadersError", "Error", "constructor", "callable", "Headers", "headers", "Proxy", "get", "target", "prop", "receiver", "ReflectAdapter", "lowercased", "toLowerCase", "original", "Object", "keys", "find", "o", "set", "value", "has", "deleteProperty", "seal", "merge", "Array", "isArray", "join", "from", "append", "name", "existing", "push", "delete", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "call", "key", "values", "Symbol", "iterator"], "mappings": ";;;;;;;;;;;;;;;IA2BaA,cAAc,EAAA;eAAdA;;IApBAC,oBAAoB,EAAA;eAApBA;;;yBALkB;AAKxB,MAAMA,6BAA6BC;IACxCC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIH;IACZ;AACF;AAUO,MAAMD,uBAAuBK;IAGlCF,YAAYG,OAA4B,CAAE;QACxC,2EAA2E;QAC3E,2EAA2E;QAC3E,KAAK;QAEL,IAAI,CAACA,OAAO,GAAG,IAAIC,MAAMD,SAAS;YAChCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,sEAAsE;gBACtE,sEAAsE;gBACtE,cAAc;gBACd,IAAI,OAAOD,SAAS,UAAU;oBAC5B,OAAOE,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC1C;gBAEA,MAAME,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,0DAA0D;gBAC1D,IAAI,OAAOE,aAAa,aAAa;gBAErC,mDAAmD;gBACnD,OAAOH,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQM,UAAUJ;YAC9C;YACAS,KAAIX,MAAM,EAAEC,IAAI,EAAEW,KAAK,EAAEV,QAAQ;gBAC/B,IAAI,OAAOD,SAAS,UAAU;oBAC5B,OAAOE,SAAAA,cAAc,CAACQ,GAAG,CAACX,QAAQC,MAAMW,OAAOV;gBACjD;gBAEA,MAAME,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,iEAAiE;gBACjE,OAAOD,SAAAA,cAAc,CAACQ,GAAG,CAACX,QAAQM,YAAYL,MAAMW,OAAOV;YAC7D;YACAW,KAAIb,MAAM,EAAEC,IAAI;gBACd,IAAI,OAAOA,SAAS,UAAU,OAAOE,SAAAA,cAAc,CAACU,GAAG,CAACb,QAAQC;gBAEhE,MAAMG,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,sDAAsD;gBACtD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,8CAA8C;gBAC9C,OAAOH,SAAAA,cAAc,CAACU,GAAG,CAACb,QAAQM;YACpC;YACAQ,gBAAed,MAAM,EAAEC,IAAI;gBACzB,IAAI,OAAOA,SAAS,UAClB,OAAOE,SAAAA,cAAc,CAACW,cAAc,CAACd,QAAQC;gBAE/C,MAAMG,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,qDAAqD;gBACrD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,sDAAsD;gBACtD,OAAOH,SAAAA,cAAc,CAACW,cAAc,CAACd,QAAQM;YAC/C;QACF;IACF;IAEA;;;GAGC,GACD,OAAcS,KAAKlB,OAAgB,EAAmB;QACpD,OAAO,IAAIC,MAAuBD,SAAS;YACzCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOT,qBAAqBG,QAAQ;oBACtC;wBACE,OAAOQ,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACOc,MAAMJ,KAAwB,EAAU;QAC9C,IAAIK,MAAMC,OAAO,CAACN,QAAQ,OAAOA,MAAMO,IAAI,CAAC;QAE5C,OAAOP;IACT;IAEA;;;;;GAKC,GACD,OAAcQ,KAAKvB,OAAsC,EAAW;QAClE,IAAIA,mBAAmBD,SAAS,OAAOC;QAEvC,OAAO,IAAIN,eAAeM;IAC5B;IAEOwB,OAAOC,IAAY,EAAEV,KAAa,EAAQ;QAC/C,MAAMW,WAAW,IAAI,CAAC1B,OAAO,CAACyB,KAAK;QACnC,IAAI,OAAOC,aAAa,UAAU;YAChC,IAAI,CAAC1B,OAAO,CAACyB,KAAK,GAAG;gBAACC;gBAAUX;aAAM;QACxC,OAAO,IAAIK,MAAMC,OAAO,CAACK,WAAW;YAClCA,SAASC,IAAI,CAACZ;QAChB,OAAO;YACL,IAAI,CAACf,OAAO,CAACyB,KAAK,GAAGV;QACvB;IACF;IAEOa,OAAOH,IAAY,EAAQ;QAChC,OAAO,IAAI,CAACzB,OAAO,CAACyB,KAAK;IAC3B;IAEOvB,IAAIuB,IAAY,EAAiB;QACtC,MAAMV,QAAQ,IAAI,CAACf,OAAO,CAACyB,KAAK;QAChC,IAAI,OAAOV,UAAU,aAAa,OAAO,IAAI,CAACI,KAAK,CAACJ;QAEpD,OAAO;IACT;IAEOC,IAAIS,IAAY,EAAW;QAChC,OAAO,OAAO,IAAI,CAACzB,OAAO,CAACyB,KAAK,KAAK;IACvC;IAEOX,IAAIW,IAAY,EAAEV,KAAa,EAAQ;QAC5C,IAAI,CAACf,OAAO,CAACyB,KAAK,GAAGV;IACvB;IAEOc,QACLC,UAAkE,EAClEC,OAAa,EACP;QACN,KAAK,MAAM,CAACN,MAAMV,MAAM,IAAI,IAAI,CAACiB,OAAO,GAAI;YAC1CF,WAAWG,IAAI,CAACF,SAAShB,OAAOU,MAAM,IAAI;QAC5C;IACF;IAEA,CAAQO,UAA6C;QACnD,KAAK,MAAME,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAG;YAC3C,MAAMyB,OAAOS,IAAI1B,WAAW;YAC5B,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMO,QAAQ,IAAI,CAACb,GAAG,CAACuB;YAEvB,MAAM;gBAACA;gBAAMV;aAAM;QACrB;IACF;IAEA,CAAQJ,OAAgC;QACtC,KAAK,MAAMuB,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAG;YAC3C,MAAMyB,OAAOS,IAAI1B,WAAW;YAC5B,MAAMiB;QACR;IACF;IAEA,CAAQU,SAAkC;QACxC,KAAK,MAAMD,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAG;YAC3C,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMe,QAAQ,IAAI,CAACb,GAAG,CAACgC;YAEvB,MAAMnB;QACR;IACF;IAEO,CAACqB,OAAOC,QAAQ,CAAC,GAAsC;QAC5D,OAAO,IAAI,CAACL,OAAO;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/request/headers.ts"], "sourcesContent": ["import {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * In this version of Next.js `headers()` returns a Promise however you can still reference the properties of the underlying Headers instance\n * synchronously to facilitate migration. The `UnsafeUnwrappedHeaders` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `headers()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedHeaders` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `headers()` value can be awaited or you should call `headers()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedHeaders` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `headers()` will only return a Promise and you will not be able to access the underlying Headers instance\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedHeaders` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedHeaders = ReadonlyHeaders\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedExoticHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We don't track dynamic access here because access will be tracked when you access\n        // one of the properties of the headers object.\n        return makeDynamicallyTrackedExoticHeaders(\n          workStore.route,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We are prerendering with PPR. We need track dynamic access here eagerly\n        // to keep continuity with how headers has worked in PPR without dynamicIO.\n        // TODO consider switching the semantic to throw on property access instead\n        postponeWithTracking(\n          workStore.route,\n          'headers',\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We are in a legacy static generation mode while prerendering\n        // We track dynamic access here so we don't need to wrap the headers in\n        // individual property access tracking.\n        throwToInterruptStaticGeneration('headers', workStore, workUnitStore)\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using headers inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  const requestStore = getExpectedRequestStore('headers')\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    return makeUntrackedExoticHeadersWithDevWarnings(\n      requestStore.headers,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticHeaders(requestStore.headers)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeDynamicallyTrackedExoticHeaders(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`headers()[Symbol.iterator]()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: underlyingHeaders.append.bind(underlyingHeaders),\n    },\n    delete: {\n      value: underlyingHeaders.delete.bind(underlyingHeaders),\n    },\n    get: {\n      value: underlyingHeaders.get.bind(underlyingHeaders),\n    },\n    has: {\n      value: underlyingHeaders.has.bind(underlyingHeaders),\n    },\n    set: {\n      value: underlyingHeaders.set.bind(underlyingHeaders),\n    },\n    getSetCookie: {\n      value: underlyingHeaders.getSetCookie.bind(underlyingHeaders),\n    },\n    forEach: {\n      value: underlyingHeaders.forEach.bind(underlyingHeaders),\n    },\n    keys: {\n      value: underlyingHeaders.keys.bind(underlyingHeaders),\n    },\n    values: {\n      value: underlyingHeaders.values.bind(underlyingHeaders),\n    },\n    entries: {\n      value: underlyingHeaders.entries.bind(underlyingHeaders),\n    },\n    [Symbol.iterator]: {\n      value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders),\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = new Promise<ReadonlyHeaders>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingHeaders))\n  )\n\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.append.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.delete.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.get.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.has.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.set.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.getSetCookie.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        syncIODev(route, expression)\n        return underlyingHeaders.forEach.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.keys.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.values.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.entries.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...headers()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingHeaders[Symbol.iterator].apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'string' ? `'${arg}'` : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\ntype HeadersExtensions = {\n  [K in keyof ReadonlyHeaders]: unknown\n}\n"], "names": ["headers", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "Headers", "makeUntrackedExoticHeaders", "type", "dynamicShouldError", "StaticGenBailoutError", "makeDynamicallyTrackedExoticHeaders", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "requestStore", "getExpectedRequestStore", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeUntrackedExoticHeadersWithDevWarnings", "CachedHeaders", "WeakMap", "prerenderStore", "cachedHeaders", "get", "promise", "makeHangingPromise", "renderSignal", "set", "Object", "defineProperties", "append", "value", "expression", "describeNameArg", "arguments", "error", "createHeadersAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "delete", "_delete", "has", "getSetCookie", "for<PERSON>ach", "keys", "values", "entries", "Symbol", "iterator", "Promise", "resolve", "bind", "scheduleImmediate", "syncIODev", "apply", "arg", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "prefix"], "mappings": ";;;;+BAuDgBA,WAAAA;;;eAAAA;;;yBApDT;0CAC0B;8CACO;kCAWjC;yCAC+B;uCACH;0DACyB;2BAC1B;uBACc;AAkCzC,SAASA;IACd,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,CAAAA,GAAAA,OAAAA,+BAA+B,KAChC;YACA,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,yOAAyO,CAAC,GAD/P,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC,SAAAA,cAAc,CAACC,IAAI,CAAC,IAAIC,QAAQ,CAAC;YAC3D,OAAOC,2BAA2BJ;QACpC;QAEA,IAAIP,eAAe;YACjB,IAAIA,cAAcY,IAAI,KAAK,SAAS;gBAClC,MAAM,OAAA,cAEL,CAFK,IAAIR,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIL,cAAcY,IAAI,KAAK,kBAAkB;gBAClD,MAAM,OAAA,cAEL,CAFK,IAAIR,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIR,UAAUgB,kBAAkB,EAAE;YAChC,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEjB,UAAUQ,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcY,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,oFAAoF;gBACpF,+CAA+C;gBAC/C,OAAOG,oCACLlB,UAAUQ,KAAK,EACfL;YAEJ,OAAO,IAAIA,cAAcY,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,0EAA0E;gBAC1E,2EAA2E;gBAC3E,2EAA2E;gBAC3EI,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBnB,UAAUQ,KAAK,EACf,WACAL,cAAciB,eAAe;YAEjC,OAAO,IAAIjB,cAAcY,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,+DAA+D;gBAC/D,uEAAuE;gBACvE,uCAAuC;gBACvCM,CAAAA,GAAAA,kBAAAA,gCAAgC,EAAC,WAAWrB,WAAWG;YACzD;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFmB,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACtB,WAAWG;IAC7C;IAEA,MAAMoB,eAAeC,CAAAA,GAAAA,8BAAAA,uBAAuB,EAAC;IAC7C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiB,CAAA,CAAC3B,aAAAA,OAAAA,KAAAA,IAAAA,UAAW4B,iBAAiB,GAAE;QAC3E,OAAOC,0CACLN,aAAaxB,OAAO,EACpBC,aAAAA,OAAAA,KAAAA,IAAAA,UAAWQ,KAAK;IAEpB,OAAO;QACL,OAAOM,2BAA2BS,aAAaxB,OAAO;IACxD;AACF;AAGA,MAAM+B,gBAAgB,IAAIC;AAE1B,SAASb,oCACPV,KAAa,EACbwB,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCJ,eAAeK,YAAY,EAC3B;IAEFP,cAAcQ,GAAG,CAACN,gBAAgBG;IAElCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/BM,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAME,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChF,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAiB,QAAQ;YACNP,OAAO,SAASQ;gBACd,MAAMP,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3E,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAE,KAAK;YACHQ,OAAO,SAASR;gBACd,MAAMS,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxE,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAmB,KAAK;YACHT,OAAO,SAASS;gBACd,MAAMR,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxE,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAM,KAAK;YACHI,OAAO,SAASJ;gBACd,MAAMK,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7E,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAoB,cAAc;YACZV,OAAO,SAASU;gBACd,MAAMT,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAqB,SAAS;YACPX,OAAO,SAASW;gBACd,MAAMV,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAsB,MAAM;YACJZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAuB,QAAQ;YACNb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAwB,SAAS;YACPd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACA,CAACyB,OAAOC,QAAQ,CAAC,EAAE;YACjBhB,OAAO;gBACL,MAAMC,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;IACF;IAEA,OAAOG;AACT;AAEA,SAASrB,2BACPJ,iBAAkC;IAElC,MAAMuB,gBAAgBH,cAAcI,GAAG,CAACxB;IACxC,IAAIuB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUwB,QAAQC,OAAO,CAAClD;IAChCoB,cAAcQ,GAAG,CAAC5B,mBAAmByB;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/BM,QAAQ;YACNC,OAAOhC,kBAAkB+B,MAAM,CAACoB,IAAI,CAACnD;QACvC;QACAuC,QAAQ;YACNP,OAAOhC,kBAAkBuC,MAAM,CAACY,IAAI,CAACnD;QACvC;QACAwB,KAAK;YACHQ,OAAOhC,kBAAkBwB,GAAG,CAAC2B,IAAI,CAACnD;QACpC;QACAyC,KAAK;YACHT,OAAOhC,kBAAkByC,GAAG,CAACU,IAAI,CAACnD;QACpC;QACA4B,KAAK;YACHI,OAAOhC,kBAAkB4B,GAAG,CAACuB,IAAI,CAACnD;QACpC;QACA0C,cAAc;YACZV,OAAOhC,kBAAkB0C,YAAY,CAACS,IAAI,CAACnD;QAC7C;QACA2C,SAAS;YACPX,OAAOhC,kBAAkB2C,OAAO,CAACQ,IAAI,CAACnD;QACxC;QACA4C,MAAM;YACJZ,OAAOhC,kBAAkB4C,IAAI,CAACO,IAAI,CAACnD;QACrC;QACA6C,QAAQ;YACNb,OAAOhC,kBAAkB6C,MAAM,CAACM,IAAI,CAACnD;QACvC;QACA8C,SAAS;YACPd,OAAOhC,kBAAkB8C,OAAO,CAACK,IAAI,CAACnD;QACxC;QACA,CAAC+C,OAAOC,QAAQ,CAAC,EAAE;YACjBhB,OAAOhC,iBAAiB,CAAC+C,OAAOC,QAAQ,CAAC,CAACG,IAAI,CAACnD;QACjD;IACF;IAEA,OAAOyB;AACT;AAEA,SAASN,0CACPnB,iBAAkC,EAClCF,KAAc;IAEd,MAAMyB,gBAAgBH,cAAcI,GAAG,CAACxB;IACxC,IAAIuB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAU,IAAIwB,QAAyB,CAACC,UAC5CE,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMF,QAAQlD;IAGlCoB,cAAcQ,GAAG,CAAC5B,mBAAmByB;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/BM,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAME,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChFkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB+B,MAAM,CAACuB,KAAK,CACnCtD,mBACAmC;YAEJ;QACF;QACAI,QAAQ;YACNP,OAAO,SAASQ;gBACd,MAAMP,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3EkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkBuC,MAAM,CAACe,KAAK,CACnCtD,mBACAmC;YAEJ;QACF;QACAX,KAAK;YACHQ,OAAO,SAASR;gBACd,MAAMS,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkBwB,GAAG,CAAC8B,KAAK,CAACtD,mBAAmBmC;YACxD;QACF;QACAM,KAAK;YACHT,OAAO,SAASS;gBACd,MAAMR,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkByC,GAAG,CAACa,KAAK,CAACtD,mBAAmBmC;YACxD;QACF;QACAP,KAAK;YACHI,OAAO,SAASJ;gBACd,MAAMK,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7EkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB4B,GAAG,CAAC0B,KAAK,CAACtD,mBAAmBmC;YACxD;QACF;QACAO,cAAc;YACZV,OAAO,SAASU;gBACd,MAAMT,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB0C,YAAY,CAACY,KAAK,CACzCtD,mBACAmC;YAEJ;QACF;QACAQ,SAAS;YACPX,OAAO,SAASW;gBACd,MAAMV,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB2C,OAAO,CAACW,KAAK,CACpCtD,mBACAmC;YAEJ;QACF;QACAS,MAAM;YACJZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB4C,IAAI,CAACU,KAAK,CAACtD,mBAAmBmC;YACzD;QACF;QACAU,QAAQ;YACNb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB6C,MAAM,CAACS,KAAK,CACnCtD,mBACAmC;YAEJ;QACF;QACAW,SAAS;YACPd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB8C,OAAO,CAACQ,KAAK,CACpCtD,mBACAmC;YAEJ;QACF;QACA,CAACY,OAAOC,QAAQ,CAAC,EAAE;YACjBhB,OAAO;gBACL,MAAMC,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,iBAAiB,CAAC+C,OAAOC,QAAQ,CAAC,CAACM,KAAK,CAC7CtD,mBACAmC;YAEJ;QACF;IACF;IAEA,OAAOV;AACT;AAEA,SAASS,gBAAgBqB,GAAY;IACnC,OAAO,OAAOA,QAAQ,WAAW,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GAAG;AAChD;AAEA,SAASF,UAAUvD,KAAyB,EAAEmC,UAAkB;IAC9D,MAAMxC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcY,IAAI,KAAK,aACvBZ,cAAc+D,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAM3C,eAAepB;QACrBgE,CAAAA,GAAAA,kBAAAA,sCAAsC,EAAC5C;IACzC;IACA,gCAAgC;IAChC6C,kBAAkB5D,OAAOmC;AAC3B;AAEA,MAAMyB,oBAAoBC,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEtB;AAGF,SAASA,yBACPvC,KAAyB,EACzBmC,UAAkB;IAElB,MAAM2B,SAAS9D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAID,MACT,GAAG+D,OAAO,KAAK,EAAE3B,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/request/draft-mode.ts"], "sourcesContent": ["import {\n  getDraftModeProviderForCacheScope,\n  throwForMissingRequestStore,\n} from '../app-render/work-unit-async-storage.external'\n\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\n\n/**\n * In this version of Next.js `draftMode()` returns a Promise however you can still reference the properties of the underlying draftMode object\n * synchronously to facilitate migration. The `UnsafeUnwrappedDraftMode` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `draftMode()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedDraftMode` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `draftMode()` value can be awaited or you should call `draftMode()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedDraftMode` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `draftMode()` will only return a Promise and you will not be able to access the underlying draftMode object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedDraftMode` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedDraftMode = DraftMode\n\nexport function draftMode(): Promise<DraftMode> {\n  const callingExpression = 'draftMode'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workStore || !workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'request':\n      return createOrGetCachedExoticDraftMode(\n        workUnitStore.draftMode,\n        workStore\n      )\n\n    case 'cache':\n    case 'unstable-cache':\n      // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n      // the outmost work unit store is a request store, and if draft mode is\n      // enabled.\n      const draftModeProvider = getDraftModeProviderForCacheScope(\n        workStore,\n        workUnitStore\n      )\n\n      if (draftModeProvider) {\n        return createOrGetCachedExoticDraftMode(draftModeProvider, workStore)\n      }\n\n    // Otherwise, we fall through to providing an empty draft mode.\n    // eslint-disable-next-line no-fallthrough\n    case 'prerender':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // Return empty draft mode\n      if (\n        process.env.NODE_ENV === 'development' &&\n        !workStore?.isPrefetchRequest\n      ) {\n        const route = workStore?.route\n        return createExoticDraftModeWithDevWarnings(null, route)\n      } else {\n        return createExoticDraftMode(null)\n      }\n\n    default:\n      const _exhaustiveCheck: never = workUnitStore\n      return _exhaustiveCheck\n  }\n}\n\nfunction createOrGetCachedExoticDraftMode(\n  draftModeProvider: DraftModeProvider,\n  workStore: WorkStore | undefined\n): Promise<DraftMode> {\n  const cachedDraftMode = CachedDraftModes.get(draftMode)\n\n  if (cachedDraftMode) {\n    return cachedDraftMode\n  }\n\n  let promise: Promise<DraftMode>\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    const route = workStore?.route\n    promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route)\n  } else {\n    promise = createExoticDraftMode(draftModeProvider)\n  }\n\n  CachedDraftModes.set(draftModeProvider, promise)\n\n  return promise\n}\n\ninterface CacheLifetime {}\nconst CachedDraftModes = new WeakMap<CacheLifetime, Promise<DraftMode>>()\n\nfunction createExoticDraftMode(\n  underlyingProvider: null | DraftModeProvider\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      return instance.isEnabled\n    },\n    set(newValue) {\n      Object.defineProperty(promise, 'isEnabled', {\n        value: newValue,\n        writable: true,\n        enumerable: true,\n      })\n    },\n    enumerable: true,\n    configurable: true,\n  })\n  ;(promise as any).enable = instance.enable.bind(instance)\n  ;(promise as any).disable = instance.disable.bind(instance)\n\n  return promise\n}\n\nfunction createExoticDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      const expression = '`draftMode().isEnabled`'\n      syncIODev(route, expression)\n      return instance.isEnabled\n    },\n    set(newValue) {\n      Object.defineProperty(promise, 'isEnabled', {\n        value: newValue,\n        writable: true,\n        enumerable: true,\n      })\n    },\n    enumerable: true,\n    configurable: true,\n  })\n\n  Object.defineProperty(promise, 'enable', {\n    value: function get() {\n      const expression = '`draftMode().enable()`'\n      syncIODev(route, expression)\n      return instance.enable.apply(instance, arguments as any)\n    },\n  })\n\n  Object.defineProperty(promise, 'disable', {\n    value: function get() {\n      const expression = '`draftMode().disable()`'\n      syncIODev(route, expression)\n      return instance.disable.apply(instance, arguments as any)\n    },\n  })\n\n  return promise\n}\n\nclass DraftMode {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _provider: null | DraftModeProvider\n\n  constructor(provider: null | DraftModeProvider) {\n    this._provider = provider\n  }\n  get isEnabled() {\n    if (this._provider !== null) {\n      return this._provider.isEnabled\n    }\n    return false\n  }\n  public enable() {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    trackDynamicDraftMode('draftMode().enable()')\n    if (this._provider !== null) {\n      this._provider.enable()\n    }\n  }\n  public disable() {\n    trackDynamicDraftMode('draftMode().disable()')\n    if (this._provider !== null) {\n      this._provider.disable()\n    }\n  }\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createDraftModeAccessError\n)\n\nfunction createDraftModeAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`draftMode()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction trackDynamicDraftMode(expression: string) {\n  const store = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (store) {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      } else if (workUnitStore.phase === 'after') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`\n        )\n      }\n    }\n\n    if (store.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        const error = new Error(\n          `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`\n        )\n        abortAndThrowOnSynchronousRequestDataAccess(\n          store.route,\n          expression,\n          error,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender\n        postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // legacy Prerender\n        workUnitStore.revalidate = 0\n\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      } else if (\n        process.env.NODE_ENV === 'development' &&\n        workUnitStore &&\n        workUnitStore.type === 'request'\n      ) {\n        workUnitStore.usedDynamic = true\n      }\n    }\n  }\n}\n"], "names": ["draftMode", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "throwForMissingRequestStore", "type", "createOrGetCachedExoticDraftMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDraftModeProviderForCacheScope", "process", "env", "NODE_ENV", "isPrefetchRequest", "route", "createExoticDraftModeWithDevWarnings", "createExoticDraftMode", "_exhaustiveCheck", "cachedDraftMode", "CachedDraftModes", "get", "promise", "set", "WeakMap", "underlyingProvider", "instance", "DraftMode", "Promise", "resolve", "Object", "defineProperty", "isEnabled", "newValue", "value", "writable", "enumerable", "configurable", "enable", "bind", "disable", "expression", "syncIODev", "apply", "arguments", "constructor", "provider", "_provider", "trackDynamicDraftMode", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "createDraftModeAccessError", "prefix", "Error", "store", "phase", "dynamicShouldError", "StaticGenBailoutError", "error", "abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "usedDynamic"], "mappings": ";;;;+BA4Cg<PERSON>,aAAAA;;;eAAAA;;;8CAzCT;0CAOA;kCAMA;0DACqD;yCACtB;oCACH;AAyB5B,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IAEnD,IAAI,CAACF,aAAa,CAACG,eAAe;QAChCE,CAAAA,GAAAA,8BAAAA,2BAA2B,EAACN;IAC9B;IAEA,OAAQI,cAAcG,IAAI;QACxB,KAAK;YACH,OAAOC,iCACLJ,cAAcL,SAAS,EACvBE;QAGJ,KAAK;QACL,KAAK;YACH,0EAA0E;YAC1E,uEAAuE;YACvE,WAAW;YACX,MAAMQ,oBAAoBC,CAAAA,GAAAA,8BAAAA,iCAAiC,EACzDT,WACAG;YAGF,IAAIK,mBAAmB;gBACrB,OAAOD,iCAAiCC,mBAAmBR;YAC7D;QAEF,+DAA+D;QAC/D,0CAA0C;QAC1C,KAAK;QACL,KAAK;QACL,KAAK;YACH,0BAA0B;YAC1B,IACEU,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,CAAA,CAACZ,aAAAA,OAAAA,KAAAA,IAAAA,UAAWa,iBAAiB,GAC7B;gBACA,MAAMC,QAAQd,aAAAA,OAAAA,KAAAA,IAAAA,UAAWc,KAAK;gBAC9B,OAAOC,qCAAqC,MAAMD;YACpD,OAAO;gBACL,OAAOE,sBAAsB;YAC/B;QAEF;YACE,MAAMC,mBAA0Bd;YAChC,OAAOc;IACX;AACF;AAEA,SAASV,iCACPC,iBAAoC,EACpCR,SAAgC;IAEhC,MAAMkB,kBAAkBC,iBAAiBC,GAAG,CAACtB;IAE7C,IAAIoB,iBAAiB;QACnB,OAAOA;IACT;IAEA,IAAIG;IAEJ,IAAIX,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiB,CAAA,CAACZ,aAAAA,OAAAA,KAAAA,IAAAA,UAAWa,iBAAiB,GAAE;QAC3E,MAAMC,QAAQd,aAAAA,OAAAA,KAAAA,IAAAA,UAAWc,KAAK;QAC9BO,UAAUN,qCAAqCP,mBAAmBM;IACpE,OAAO;QACLO,UAAUL,sBAAsBR;IAClC;IAEAW,iBAAiBG,GAAG,CAACd,mBAAmBa;IAExC,OAAOA;AACT;AAGA,MAAMF,mBAAmB,IAAII;AAE7B,SAASP,sBACPQ,kBAA4C;IAE5C,MAAMC,WAAW,IAAIC,UAAUF;IAC/B,MAAMH,UAAUM,QAAQC,OAAO,CAACH;IAEhCI,OAAOC,cAAc,CAACT,SAAS,aAAa;QAC1CD;YACE,OAAOK,SAASM,SAAS;QAC3B;QACAT,KAAIU,QAAQ;YACVH,OAAOC,cAAc,CAACT,SAAS,aAAa;gBAC1CY,OAAOD;gBACPE,UAAU;gBACVC,YAAY;YACd;QACF;QACAA,YAAY;QACZC,cAAc;IAChB;IACEf,QAAgBgB,MAAM,GAAGZ,SAASY,MAAM,CAACC,IAAI,CAACb;IAC9CJ,QAAgBkB,OAAO,GAAGd,SAASc,OAAO,CAACD,IAAI,CAACb;IAElD,OAAOJ;AACT;AAEA,SAASN,qCACPS,kBAA4C,EAC5CV,KAAyB;IAEzB,MAAMW,WAAW,IAAIC,UAAUF;IAC/B,MAAMH,UAAUM,QAAQC,OAAO,CAACH;IAEhCI,OAAOC,cAAc,CAACT,SAAS,aAAa;QAC1CD;YACE,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASM,SAAS;QAC3B;QACAT,KAAIU,QAAQ;YACVH,OAAOC,cAAc,CAACT,SAAS,aAAa;gBAC1CY,OAAOD;gBACPE,UAAU;gBACVC,YAAY;YACd;QACF;QACAA,YAAY;QACZC,cAAc;IAChB;IAEAP,OAAOC,cAAc,CAACT,SAAS,UAAU;QACvCY,OAAO,SAASb;YACd,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASY,MAAM,CAACK,KAAK,CAACjB,UAAUkB;QACzC;IACF;IAEAd,OAAOC,cAAc,CAACT,SAAS,WAAW;QACxCY,OAAO,SAASb;YACd,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASc,OAAO,CAACG,KAAK,CAACjB,UAAUkB;QAC1C;IACF;IAEA,OAAOtB;AACT;AAEA,MAAMK;IAMJkB,YAAYC,QAAkC,CAAE;QAC9C,IAAI,CAACC,SAAS,GAAGD;IACnB;IACA,IAAId,YAAY;QACd,IAAI,IAAI,CAACe,SAAS,KAAK,MAAM;YAC3B,OAAO,IAAI,CAACA,SAAS,CAACf,SAAS;QACjC;QACA,OAAO;IACT;IACOM,SAAS;QACd,oEAAoE;QACpE,+DAA+D;QAC/DU,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACT,MAAM;QACvB;IACF;IACOE,UAAU;QACfQ,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACP,OAAO;QACxB;IACF;AACF;AAEA,SAASE,UAAU3B,KAAyB,EAAE0B,UAAkB;IAC9D,MAAMrC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAc6C,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAe9C;QACrB+C,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;IACA,gCAAgC;IAChCE,kBAAkBrC,OAAO0B;AAC3B;AAEA,MAAMW,oBAAoBC,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,SAASA,2BACPvC,KAAyB,EACzB0B,UAAkB;IAElB,MAAMc,SAASxC,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIyC,MACT,GAAGD,OAAO,KAAK,EAAEd,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASO,sBAAsBP,UAAkB;IAC/C,MAAMgB,QAAQvD,0BAAAA,gBAAgB,CAACC,QAAQ;IACvC,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IAAIsD,OAAO;QACT,oEAAoE;QACpE,+DAA+D;QAC/D,IAAIrD,eAAe;YACjB,IAAIA,cAAcG,IAAI,KAAK,SAAS;gBAClC,MAAM,OAAA,cAEL,CAFK,IAAIiD,MACR,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,OAAO,EAAE0B,WAAW,uNAAuN,CAAC,GAD7P,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIrC,cAAcG,IAAI,KAAK,kBAAkB;gBAClD,MAAM,OAAA,cAEL,CAFK,IAAIiD,MACR,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,OAAO,EAAE0B,WAAW,gQAAgQ,CAAC,GADtS,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIrC,cAAcsD,KAAK,KAAK,SAAS;gBAC1C,MAAM,OAAA,cAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,OAAO,EAAE0B,WAAW,0MAA0M,CAAC,GADhP,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAIgB,MAAME,kBAAkB,EAAE;YAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEH,MAAM1C,KAAK,CAAC,8EAA8E,EAAE0B,WAAW,4HAA4H,CAAC,GADzO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIrC,eAAe;YACjB,IAAIA,cAAcG,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,MAAMsD,QAAQ,OAAA,cAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,MAAM,EAAE0B,WAAW,+HAA+H,CAAC,GAD5J,qBAAA;2BAAA;gCAAA;kCAAA;gBAEd;gBACAqB,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCL,MAAM1C,KAAK,EACX0B,YACAoB,OACAzD;YAEJ,OAAO,IAAIA,cAAcG,IAAI,KAAK,iBAAiB;gBACjD,gBAAgB;gBAChBwD,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBN,MAAM1C,KAAK,EACX0B,YACArC,cAAc4D,eAAe;YAEjC,OAAO,IAAI5D,cAAcG,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnBH,cAAc6D,UAAU,GAAG;gBAE3B,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEV,MAAM1C,KAAK,CAAC,mDAAmD,EAAE0B,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAgB,MAAMW,uBAAuB,GAAG3B;gBAChCgB,MAAMY,iBAAiB,GAAGH,IAAII,KAAK;gBAEnC,MAAMJ;YACR,OAAO,IACLvD,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBT,iBACAA,cAAcG,IAAI,KAAK,WACvB;gBACAH,cAAcmE,WAAW,GAAG;YAC9B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/headers.js"], "sourcesContent": ["module.exports.cookies = require('./dist/server/request/cookies').cookies\nmodule.exports.headers = require('./dist/server/request/headers').headers\nmodule.exports.draftMode = require('./dist/server/request/draft-mode').draftMode\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,CAAC,OAAO,GAAG,6GAAyC,OAAO;AACzE,OAAO,OAAO,CAAC,OAAO,GAAG,6GAAyC,OAAO;AACzE,OAAO,OAAO,CAAC,SAAS,GAAG,gHAA4C,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/shared/constants.js"], "sourcesContent": ["// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\nexport { HEADER_LOCALE_NAME };\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C,MAAM,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js"], "sourcesContent": ["import { cache } from 'react';\n\n// See https://github.com/vercel/next.js/discussions/58862\nfunction getCacheImpl() {\n  const value = {\n    locale: undefined\n  };\n  return value;\n}\nconst getCache = cache(getCacheImpl);\nfunction getCachedRequestLocale() {\n  return getCache().locale;\n}\nfunction setCachedRequestLocale(locale) {\n  getCache().locale = locale;\n}\n\nexport { getCachedRequestLocale, setCachedRequestLocale };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,0DAA0D;AAC1D,SAAS;IACP,MAAM,QAAQ;QACZ,QAAQ;IACV;IACA,OAAO;AACT;AACA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;AACvB,SAAS;IACP,OAAO,WAAW,MAAM;AAC1B;AACA,SAAS,uBAAuB,MAAM;IACpC,WAAW,MAAM,GAAG;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js"], "sourcesContent": ["import { headers } from 'next/headers';\nimport { cache } from 'react';\nimport { HEADER_LOCALE_NAME } from '../../shared/constants.js';\nimport { isPromise } from '../../shared/utils.js';\nimport { getCachedRequestLocale } from './RequestLocaleCache.js';\n\nasync function getHeadersImpl() {\n  const promiseOrValue = headers();\n\n  // Compatibility with Next.js <15\n  return isPromise(promiseOrValue) ? await promiseOrValue : promiseOrValue;\n}\nconst getHeaders = cache(getHeadersImpl);\nasync function getLocaleFromHeaderImpl() {\n  let locale;\n  try {\n    locale = (await getHeaders()).get(HEADER_LOCALE_NAME) || undefined;\n  } catch (error) {\n    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {\n      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {\n        cause: error\n      });\n      wrappedError.digest = error.digest;\n      throw wrappedError;\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\nconst getLocaleFromHeader = cache(getLocaleFromHeaderImpl);\nasync function getRequestLocale() {\n  return getCachedRequestLocale() || (await getLocaleFromHeader());\n}\n\nexport { getRequestLocale };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,eAAe;IACb,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE7B,iCAAiC;IACjC,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,MAAM,iBAAiB;AAC5D;AACA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;AACzB,eAAe;IACb,IAAI;IACJ,IAAI;QACF,SAAS,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,iLAAA,CAAA,qBAAkB,KAAK;IAC3D,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,SAAS,MAAM,MAAM,KAAK,wBAAwB;YACrE,MAAM,eAAe,IAAI,MAAM,6TAA6T;gBAC1V,OAAO;YACT;YACA,aAAa,MAAM,GAAG,MAAM,MAAM;YAClC,MAAM;QACR,OAAO;YACL,MAAM;QACR;IACF;IACA,OAAO;AACT;AACA,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;AAClC,eAAe;IACb,OAAO,CAAA,GAAA,6MAAA,CAAA,yBAAsB,AAAD,OAAQ,MAAM;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js"], "sourcesContent": ["/**\n * Should be called in `i18n/request.ts` to create the configuration for the current request.\n */\nfunction getRequestConfig(createRequestConfig) {\n  return createRequestConfig;\n}\n\nexport { getRequestConfig as default };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,SAAS,iBAAiB,mBAAmB;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "REDIRECT_ERROR_CODE", "digest", "getStore", "isAction", "RedirectType", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at"], "mappings": ";;;;;;;;;;;;;;;;;;;IAegBA,gBAAgB,EAAA;eAAhBA;;IA6EAC,8BAA8B,EAAA;eAA9BA;;IARAC,wBAAwB,EAAA;eAAxBA;;IARAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,iBAAiB,EAAA;eAAjBA;;IAvBAC,QAAQ,EAAA;eAARA;;;oCArCmB;+BAM5B;AAEP,MAAMC,qBACJ,OAAOC,WAAW,qBAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAAST,iBACdU,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCC,oBAAAA,kBAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMC,eAAAA,mBAAmB,GAA7B,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CF,MAAMG,MAAM,GAAMD,eAAAA,mBAAmB,GAAC,MAAGN,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOG;AACT;AAcO,SAASV,SACd,2BAA2B,GAC3BK,GAAW,EACXC,IAAmB;QAEVL;IAATK,QAAAA,OAAAA,OAAAA,OAASL,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,IAC7CC,eAAAA,YAAY,CAACC,IAAI,GACjBD,eAAAA,YAAY,CAACE,OAAO;IAExB,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACC,iBAAiB;AACxE;AAaO,SAASV,kBACd,2BAA2B,GAC3BM,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBU,eAAAA,YAAY,CAACE,OAAO;IAEzC,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACW,iBAAiB;AACxE;AAUO,SAASrB,wBAAwBY,KAAc;IACpD,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAAS1B,yBAAyBa,KAAoB;IAC3D,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASzB,+BAA+Bc,KAAoB;IACjE,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOa,OAAOd,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["notFound", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "error", "Error", "digest"], "mappings": ";;;;+BAsBgBA,YAAAA;;;eAAAA;;;oCAnBT;AAEP;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,4CAA4C;IAC5C,MAAMG,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["forbidden", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAqBgBA,aAAAA;;;eAAAA;;;oCAlBT;AAEP,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["unauthorized", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAsBgBA,gBAAAA;;;eAAAA;;;oCAnBT;AAEP,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "Error", "cause"], "mappings": ";;;;+BAOg<PERSON>,oBAAAA;;;eAAAA;;;uCAP+B;4BACpB;8BACS;mCACF;kCACA;oCACG;AAE9B,SAASA,iBAAiBC,KAAc;IAC7C,IACEC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAClBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,UACpBG,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACH,UACrBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ,UAClBK,CAAAA,GAAAA,YAAAA,UAAU,EAACL,UACXM,CAAAA,GAAAA,uBAAAA,8BAA8B,EAACN,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBO,SAAS,WAAWP,OAAO;QAC9CD,iBAAiBC,MAAMQ,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACY<PERSON>,oBAAAA;;;eAAAA;;;AAAN,MAAMA,mBACX,OAAOC,WAAW,qBAEZC,QAAQ,wHACRF,gBAAgB,GAClB,AACEE,QAAQ,8BACRF,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8499, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc,GAAA;;;;;;;;;;;;;;;;;;;;;IAkCLA,uBAAuB,EAAA;eAAvBA;;IALAC,YAAY,EAAA;eAAZA,eAAAA,YAAY;;IAEZC,SAAS,EAAA;eAATA,WAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAFEC,iBAAiB,EAAA;eAAjBA,UAAAA,iBAAiB;;IAA3BC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAIRC,YAAY,EAAA;eAAZA,cAAAA,YAAY;;IACZC,gBAAgB,EAAA;eAAhBA,iBAAAA,gBAAgB;;;0BALmB;+BACf;0BACJ;2BACC;8BACG;iCACI;AAhCjC,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMV,gCAAgCW;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next/src/api/navigation.react-server.ts"], "sourcesContent": ["export * from '../client/components/navigation.react-server'\n"], "names": [], "mappings": ";AAAA,cAAc,+CAA8C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js"], "sourcesContent": ["function validateLocale(locale) {\n  try {\n    const constructed = new Intl.Locale(locale);\n    if (!constructed.language) {\n      throw new Error('Language is required');\n    }\n  } catch {\n    console.error(`An invalid locale was provided: \"${locale}\"\\nPlease ensure you're using a valid Unicode locale identifier (e.g. \"en-US\").`);\n  }\n}\n\nexport { validateLocale as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,MAAM;IAC5B,IAAI;QACF,MAAM,cAAc,IAAI,KAAK,MAAM,CAAC;QACpC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAM;QACN,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,+EAA+E,CAAC;IAC3I;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js"], "sourcesContent": ["import { cache } from 'react';\nimport { initializeConfig, _createIntlFormatters, _createCache } from 'use-intl/core';\nimport { isPromise } from '../../shared/utils.js';\nimport { getRequestLocale } from './RequestLocale.js';\nimport getRuntimeConfig from 'next-intl/config';\nimport validateLocale from './validateLocale.js';\n\n// This is automatically inherited by `NextIntlClientProvider` if\n// the component is rendered from a Server Component\nfunction getDefaultTimeZoneImpl() {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n}\nconst getDefaultTimeZone = cache(getDefaultTimeZoneImpl);\nasync function receiveRuntimeConfigImpl(getConfig, localeOverride) {\n  if (typeof getConfig !== 'function') {\n    throw new Error(`Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n`);\n  }\n  const params = {\n    locale: localeOverride,\n    // In case the consumer doesn't read `params.locale` and instead provides the\n    // `locale` (either in a single-language workflow or because the locale is\n    // read from the user settings), don't attempt to read the request locale.\n    get requestLocale() {\n      return localeOverride ? Promise.resolve(localeOverride) : getRequestLocale();\n    }\n  };\n  let result = getConfig(params);\n  if (isPromise(result)) {\n    result = await result;\n  }\n  if (!result.locale) {\n    throw new Error('No locale was returned from `getRequestConfig`.\\n\\nSee https://next-intl.dev/docs/usage/configuration#i18n-request');\n  }\n  {\n    validateLocale(result.locale);\n  }\n  return result;\n}\nconst receiveRuntimeConfig = cache(receiveRuntimeConfigImpl);\nconst getFormatters = cache(_createIntlFormatters);\nconst getCache = cache(_createCache);\nasync function getConfigImpl(localeOverride) {\n  const runtimeConfig = await receiveRuntimeConfig(getRuntimeConfig, localeOverride);\n  return {\n    ...initializeConfig(runtimeConfig),\n    _formatters: getFormatters(getCache()),\n    timeZone: runtimeConfig.timeZone || getDefaultTimeZone()\n  };\n}\nconst getConfig = cache(getConfigImpl);\n\nexport { getConfig as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,iEAAiE;AACjE,oDAAoD;AACpD,SAAS;IACP,OAAO,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;AACzD;AACA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;AACjC,eAAe,yBAAyB,SAAS,EAAE,cAAc;IAC/D,IAAI,OAAO,cAAc,YAAY;QACnC,MAAM,IAAI,MAAM,CAAC;;;;;;;AAOrB,CAAC;IACC;IACA,MAAM,SAAS;QACb,QAAQ;QACR,6EAA6E;QAC7E,0EAA0E;QAC1E,0EAA0E;QAC1E,IAAI,iBAAgB;YAClB,OAAO,iBAAiB,QAAQ,OAAO,CAAC,kBAAkB,CAAA,GAAA,wMAAA,CAAA,mBAAgB,AAAD;QAC3E;IACF;IACA,IAAI,SAAS,UAAU;IACvB,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACrB,SAAS,MAAM;IACjB;IACA,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IACA;QACE,CAAA,GAAA,yMAAA,CAAA,UAAc,AAAD,EAAE,OAAO,MAAM;IAC9B;IACA,OAAO;AACT;AACA,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;AACnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,0OAAA,CAAA,wBAAqB;AACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,iOAAA,CAAA,eAAY;AACnC,eAAe,cAAc,cAAc;IACzC,MAAM,gBAAgB,MAAM,qBAAqB,sHAAA,CAAA,UAAgB,EAAE;IACnE,OAAO;QACL,GAAG,CAAA,GAAA,kOAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc;QAClC,aAAa,cAAc;QAC3B,UAAU,cAAc,QAAQ,IAAI;IACtC;AACF;AACA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\n\nasync function getConfigNowImpl(locale) {\n  const config = await getConfig(locale);\n  return config.now;\n}\nconst getConfigNow = cache(getConfigNowImpl);\n\nexport { getConfigNow as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,eAAe,iBAAiB,MAAM;IACpC,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,OAAO,GAAG;AACnB;AACA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\n\nasync function getFormatsCachedImpl() {\n  const config = await getConfig();\n  return config.formats;\n}\nconst getFormats = cache(getFormatsCachedImpl);\n\nexport { getFormats as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,eAAe;IACb,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD;IAC7B,OAAO,OAAO,OAAO;AACvB;AACA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgV,GAC7W,8GACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4T,GACzV,0FACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8761, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\n\nasync function getTimeZoneCachedImpl(locale) {\n  const config = await getConfig(locale);\n  return config.timeZone;\n}\nconst getTimeZoneCached = cache(getTimeZoneCachedImpl);\nasync function getTimeZone(opts) {\n  return getTimeZoneCached(opts?.locale);\n}\n\nexport { getTimeZone as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,eAAe,sBAAsB,MAAM;IACzC,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,OAAO,QAAQ;AACxB;AACA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;AAChC,eAAe,YAAY,IAAI;IAC7B,OAAO,kBAAkB,MAAM;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\n\nfunction getMessagesFromConfig(config) {\n  if (!config.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages');\n  }\n  return config.messages;\n}\nasync function getMessagesCachedImpl(locale) {\n  const config = await getConfig(locale);\n  return getMessagesFromConfig(config);\n}\nconst getMessagesCached = cache(getMessagesCachedImpl);\nasync function getMessages(opts) {\n  return getMessagesCached(opts?.locale);\n}\n\nexport { getMessages as default, getMessagesFromConfig };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,sBAAsB,MAAM;IACnC,IAAI,CAAC,OAAO,QAAQ,EAAE;QACpB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,OAAO,QAAQ;AACxB;AACA,eAAe,sBAAsB,MAAM;IACzC,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,sBAAsB;AAC/B;AACA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;AAChC,eAAe,YAAY,IAAI;IAC7B,OAAO,kBAAkB,MAAM;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\n\nasync function getLocaleCachedImpl() {\n  const config = await getConfig();\n  return config.locale;\n}\nconst getLocaleCached = cache(getLocaleCachedImpl);\n\nexport { getLocaleCached as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,eAAe;IACb,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD;IAC7B,OAAO,OAAO,MAAM;AACtB;AACA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8841, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js"], "sourcesContent": ["import getConfigNow from '../server/react-server/getConfigNow.js';\nimport getFormats from '../server/react-server/getFormats.js';\nimport NextIntlClientProvider from '../shared/NextIntlClientProvider.js';\nimport { jsx } from 'react/jsx-runtime';\nimport getTimeZone from '../server/react-server/getTimeZone.js';\nimport getMessages from '../server/react-server/getMessages.js';\nimport getLocaleCached from '../server/react-server/getLocale.js';\n\nasync function NextIntlClientProviderServer({\n  formats,\n  locale,\n  messages,\n  now,\n  timeZone,\n  ...rest\n}) {\n  return /*#__PURE__*/jsx(NextIntlClientProvider\n  // We need to be careful about potentially reading from headers here.\n  // See https://github.com/amannn/next-intl/issues/631\n  , {\n    formats: formats === undefined ? await getFormats() : formats,\n    locale: locale ?? (await getLocaleCached()),\n    messages: messages === undefined ? await getMessages() : messages\n    // Note that we don't assign a default for `now` here,\n    // we only read one from the request config - if any.\n    // Otherwise this would cause a `dynamicIO` error.\n    ,\n    now: now ?? (await getConfigNow()),\n    timeZone: timeZone ?? (await getTimeZone()),\n    ...rest\n  });\n}\n\nexport { NextIntlClientProviderServer as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,eAAe,6BAA6B,EAC1C,OAAO,EACP,MAAM,EACN,QAAQ,EACR,GAAG,EACH,QAAQ,EACR,GAAG,MACJ;IACC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,8LAAA,CAAA,UAAsB,EAG5C;QACA,SAAS,YAAY,YAAY,MAAM,CAAA,GAAA,qMAAA,CAAA,UAAU,AAAD,MAAM;QACtD,QAAQ,UAAW,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAe,AAAD;QACvC,UAAU,aAAa,YAAY,MAAM,CAAA,GAAA,sMAAA,CAAA,UAAW,AAAD,MAAM;QAKzD,KAAK,OAAQ,MAAM,CAAA,GAAA,uMAAA,CAAA,UAAY,AAAD;QAC9B,UAAU,YAAa,MAAM,CAAA,GAAA,sMAAA,CAAA,UAAW,AAAD;QACvC,GAAG,IAAI;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/use-intl/dist/esm/development/core.js"], "sourcesContent": ["import { r as resolveNamespace, e as createBaseTranslator, f as defaultGetMessageFallback, b as createIntlFormatters, d as createCache, g as defaultOnError } from './initializeConfig-CRD6euuK.js';\nexport { I as IntlError, a as IntlErrorCode, c as createFormatter, i as initializeConfig } from './initializeConfig-CRD6euuK.js';\n\n\n\n\nfunction createTranslatorImpl({\n  messages,\n  namespace,\n  ...rest\n}, namespacePrefix) {\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = resolveNamespace(namespace, namespacePrefix);\n  return createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n// This type is slightly more loose than `AbstractIntlMessages`\n// in order to avoid a type error.\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator({\n  _cache = createCache(),\n  _formatters = createIntlFormatters(_cache),\n  getMessageFallback = defaultGetMessageFallback,\n  messages,\n  namespace,\n  onError = defaultOnError,\n  ...rest\n}) {\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? `!.${namespace}` : '!'\n  }, '!');\n}\n\n/**\n * Checks if a locale exists in a list of locales.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale\n */\nfunction hasLocale(locales, candidate) {\n  return locales.includes(candidate);\n}\n\nexport { createCache as _createCache, createIntlFormatters as _createIntlFormatters, createTranslator, hasLocale };\n"], "names": [], "mappings": ";;;;AAAA;;;AAMA,SAAS,qBAAqB,EAC5B,QAAQ,EACR,SAAS,EACT,GAAG,MACJ,EAAE,eAAe;IAChB,oDAAoD;IACpD,8CAA8C;IAC9C,WAAW,QAAQ,CAAC,gBAAgB;IACpC,YAAY,CAAA,GAAA,yLAAA,CAAA,IAAgB,AAAD,EAAE,WAAW;IACxC,OAAO,CAAA,GAAA,yLAAA,CAAA,IAAoB,AAAD,EAAE;QAC1B,GAAG,IAAI;QACP;QACA;IACF;AACF;AAEA,+DAA+D;AAC/D,kCAAkC;AAElC;;;;;;;CAOC,GACD,SAAS,iBAAiB,EACxB,SAAS,CAAA,GAAA,yLAAA,CAAA,IAAW,AAAD,GAAG,EACtB,cAAc,CAAA,GAAA,yLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO,EAC1C,qBAAqB,yLAAA,CAAA,IAAyB,EAC9C,QAAQ,EACR,SAAS,EACT,UAAU,yLAAA,CAAA,IAAc,EACxB,GAAG,MACJ;IACC,6EAA6E;IAC7E,6EAA6E;IAC7E,iCAAiC;IACjC,uDAAuD;IACvD,OAAO,qBAAqB;QAC1B,GAAG,IAAI;QACP;QACA,OAAO;QACP,YAAY;QACZ;QACA,+FAA+F;QAC/F,UAAU;YACR,KAAK;QACP;QACA,WAAW,YAAY,CAAC,EAAE,EAAE,WAAW,GAAG;IAC5C,GAAG;AACL;AAEA;;;;CAIC,GACD,SAAS,UAAU,OAAO,EAAE,SAAS;IACnC,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js"], "sourcesContent": ["import { cache } from 'react';\nimport { createTranslator } from 'use-intl/core';\n\nfunction getServerTranslatorImpl(config, namespace) {\n  return createTranslator({\n    ...config,\n    namespace\n  });\n}\nvar getServerTranslator = cache(getServerTranslatorImpl);\n\nexport { getServerTranslator as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,wBAAwB,MAAM,EAAE,SAAS;IAChD,OAAO,CAAA,GAAA,iLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,GAAG,MAAM;QACT;IACF;AACF;AACA,IAAI,sBAAsB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}]}