(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1051,2210,3615,3712,5908,7841,8950],{1120:(e,t,s)=>{"use strict";s.d(t,{useExport:()=>p});var a=s(95155),l=s(28890),r=s(12115),i=s(3925);let n=l.vv.create({header:{color:"#333",fontSize:24,marginBottom:10,textAlign:"center"},page:{backgroundColor:"#E4E4E4",flexDirection:"column",padding:30},section:{flexGrow:1,margin:10,padding:10},subheader:{color:"#555",fontSize:16,marginBottom:5},text:{fontSize:12,marginBottom:3}}),c=e=>{var t,s,i,c,o,d,m,u,x;let{data:h,filters:p,reportDate:g,reportTitle:j}=e,{metadata:v,reportData:y,summary:f,totalCount:N}=r.useMemo(()=>{var e,t,s,a,l;if(!h)return{metadata:{},reportData:{},summary:{message:"No data available"},totalCount:0};let r=null!=(t=null!=(e=null==h?void 0:h.data)?e:h)?t:{},i=null!=(s=null==h?void 0:h.metadata)?s:{},n="object"!=typeof r||null===r||Array.isArray(r)?{}:r;return{metadata:i,reportData:n,summary:null!=(a=n.summary)?a:{message:"No summary available"},totalCount:null!=(l=n.totalCount)?l:Array.isArray(n)?n.length:0}},[h]),b=null!=p?p:{},w=null!=(d=null==b?void 0:b.dateRange)?d:{},k=null==b?void 0:b.status,S=null!=g?g:new Date().toLocaleString();return(0,a.jsx)(l.yo,{children:(0,a.jsx)(l.YW,{size:"A4",style:n.page,children:(0,a.jsxs)(l.Ss,{style:n.section,children:[(0,a.jsx)(l.EY,{style:n.header,children:null!=j?j:"Delegation Report"}),(0,a.jsxs)(l.EY,{style:n.text,children:["Report Generated: ",S]}),(0,a.jsxs)(l.EY,{style:n.text,children:["Total Delegations: ",N]}),f&&Object.keys(f).length>0&&(0,a.jsxs)(l.Ss,{style:{marginTop:15},children:[(0,a.jsx)(l.EY,{style:n.subheader,children:"Summary:"}),Object.entries(f).map(e=>{let[t,s]=e;return(0,a.jsxs)(l.EY,{style:n.text,children:[t.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),": ",String(null!=s?s:"N/A")]},t)})]}),b&&Object.keys(b).length>0&&(0,a.jsxs)(l.Ss,{style:{marginTop:20},children:[(0,a.jsx)(l.EY,{style:n.subheader,children:"Filters Applied:"}),(0,a.jsxs)(l.EY,{style:n.text,children:["Date Range:"," ",null!=(m=null==(s=w.from)||null==(t=s.toLocaleDateString)?void 0:t.call(s))?m:"N/A"," -"," ",null!=(u=null==(c=w.to)||null==(i=c.toLocaleDateString)?void 0:i.call(c))?u:"N/A",(0,a.jsxs)(l.EY,{style:n.text,children:["Status: ",null!=(x=null==k||null==(o=k.join)?void 0:o.call(k,", "))?x:"All"]})]})]}),v.id&&(0,a.jsxs)(l.Ss,{style:{marginTop:15},children:[(0,a.jsx)(l.EY,{style:n.subheader,children:"Report Details:"}),(0,a.jsxs)(l.EY,{style:n.text,children:["Report ID: ",v.id]}),(0,a.jsxs)(l.EY,{style:n.text,children:["Generated At:"," ",v.generatedAt?new Date(v.generatedAt).toLocaleString():"N/A"]})]})]})})})},o=l.vv.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#7c3aed",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#faf5ff",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#faf5ff",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),d=e=>{var t,s,i,n,c,d;let{data:m,reportTitle:u,metadata:x}=e,h=r.useMemo(()=>{if(!m)return{};let e=(null==m?void 0:m.data)||m;return e&&"object"==typeof e?{totalCount:e.totalCount||0,activeCount:e.activeCount||0,onLeaveCount:e.onLeaveCount||0,averagePerformanceScore:e.averagePerformanceScore||0,satisfactionRate:e.satisfactionRate||0,performanceMetrics:e.performanceMetrics||{},departmentDistribution:Array.isArray(e.departmentDistribution)?e.departmentDistribution:[],taskAssignments:e.taskAssignments||{},workloadDistribution:Array.isArray(e.workloadDistribution)?e.workloadDistribution:[],availabilityMetrics:e.availabilityMetrics||{},...e}:{}},[m]);return(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{size:"A4",style:o.page,children:[(0,a.jsx)(l.EY,{style:o.header,children:u||"Employee Report"}),(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Employee Summary"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Total Employees:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.totalCount||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Active Employees:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.activeCount||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"On Leave:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.onLeaveCount||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Average Performance Score:"}),(0,a.jsx)(l.EY,{style:o.value,children:(null==(t=h.averagePerformanceScore)?void 0:t.toFixed(2))||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Employee Satisfaction Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(s=h.satisfactionRate)?void 0:s.toFixed(2))||0,"%"]})]})]}),h.performanceMetrics&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Performance Metrics"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"High Performers:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.performanceMetrics.highPerformers||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Average Performers:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.performanceMetrics.averagePerformers||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Low Performers:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.performanceMetrics.lowPerformers||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Performance Improvement Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(i=h.performanceMetrics.improvementRate)?void 0:i.toFixed(2))||0,"%"]})]})]}),h.departmentDistribution&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Department Distribution"}),(0,a.jsxs)(l.Ss,{style:o.table,children:[(0,a.jsxs)(l.Ss,{style:o.tableHeader,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:"Department"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Percentage"})]}),h.departmentDistribution.map((e,t)=>{var s;return(0,a.jsxs)(l.Ss,{style:o.tableRow,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.department)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(s=e._count)?void 0:s.department)||0}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"dept-".concat(t))})]})]}),h.taskAssignments&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Task Assignment Metrics"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Total Tasks Assigned:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.taskAssignments.totalAssigned||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Completed Tasks:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.taskAssignments.completed||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Pending Tasks:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.taskAssignments.pending||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Task Completion Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(n=h.taskAssignments.completionRate)?void 0:n.toFixed(2))||0,"%"]})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Average Tasks per Employee:"}),(0,a.jsx)(l.EY,{style:o.value,children:(null==(c=h.taskAssignments.averagePerEmployee)?void 0:c.toFixed(1))||0})]})]}),h.workloadDistribution&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Workload Distribution"}),(0,a.jsxs)(l.Ss,{style:o.table,children:[(0,a.jsxs)(l.Ss,{style:o.tableHeader,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:"Workload Level"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Employee Count"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Percentage"})]}),h.workloadDistribution.map((e,t)=>{var s;return(0,a.jsxs)(l.Ss,{style:o.tableRow,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.level)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(s=e._count)?void 0:s.level)||0}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"workload-".concat(t))})]})]}),h.availabilityMetrics&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Availability Metrics"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Available Employees:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.availabilityMetrics.available||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"On Assignment:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.availabilityMetrics.onAssignment||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"On Leave:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.availabilityMetrics.onLeave||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Availability Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(d=h.availabilityMetrics.availabilityRate)?void 0:d.toFixed(2))||0,"%"]})]})]}),x&&(0,a.jsxs)(l.Ss,{style:o.metadata,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Report Information"}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Report ID: ",x.id]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Type: ",x.type]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Entity Type: ",x.entityType]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Generated: ",new Date(x.generatedAt).toLocaleString()]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Generated By: ",x.generatedBy]})]})]})})},m=l.vv.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#059669",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#ecfdf5",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#f0fdf4",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),u=e=>{var t,s,i;let{data:n,reportTitle:c,metadata:o}=e,d=r.useMemo(()=>{if(!n)return{};let e=(null==n?void 0:n.data)||n;return e&&"object"==typeof e?{totalCount:e.totalCount||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,overdueCount:e.overdueCount||0,completionRate:e.completionRate||0,averageCompletionTime:e.averageCompletionTime||0,statusDistribution:Array.isArray(e.statusDistribution)?e.statusDistribution:[],priorityDistribution:Array.isArray(e.priorityDistribution)?e.priorityDistribution:[],assignmentMetrics:e.assignmentMetrics||{},...e}:{}},[n]),u=o||{};return(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{size:"A4",style:m.page,children:[(0,a.jsx)(l.EY,{style:m.header,children:c||"Task Report"}),(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Task Summary"}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Total Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.totalCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Completed Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.completedTasks||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Pending Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.pendingTasks||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Overdue Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.overdueCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Completion Rate:"}),(0,a.jsxs)(l.EY,{style:m.value,children:[(null==(t=d.completionRate)?void 0:t.toFixed(2))||0,"%"]})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Average Completion Time:"}),(0,a.jsxs)(l.EY,{style:m.value,children:[(null==(s=d.averageCompletionTime)?void 0:s.toFixed(2))||0," hours"]})]})]}),d.statusDistribution&&(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Status Distribution"}),(0,a.jsxs)(l.Ss,{style:m.table,children:[(0,a.jsxs)(l.Ss,{style:m.tableHeader,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:"Status"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Percentage"})]}),d.statusDistribution.map((e,t)=>{var s;return(0,a.jsxs)(l.Ss,{style:m.tableRow,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.status)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(s=e._count)?void 0:s.status)||0}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"status-".concat(t))})]})]}),d.priorityDistribution&&(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Priority Distribution"}),(0,a.jsxs)(l.Ss,{style:m.table,children:[(0,a.jsxs)(l.Ss,{style:m.tableHeader,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:"Priority"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Percentage"})]}),d.priorityDistribution.map((e,t)=>{var s;return(0,a.jsxs)(l.Ss,{style:m.tableRow,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.priority)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(s=e._count)?void 0:s.priority)||0}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"priority-".concat(t))})]})]}),d.assignmentMetrics&&(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Assignment Metrics"}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Assigned Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.assignmentMetrics.assignedCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Unassigned Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.assignmentMetrics.unassignedCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Assignment Rate:"}),(0,a.jsxs)(l.EY,{style:m.value,children:[(null==(i=d.assignmentMetrics.assignmentRate)?void 0:i.toFixed(2))||0,"%"]})]})]}),u&&Object.keys(u).length>0&&(0,a.jsxs)(l.Ss,{style:m.metadata,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Report Information"}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Report ID: ",u.id||"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Type: ",u.type||"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Entity Type: ",u.entityType||"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Generated:"," ",u.generatedAt?new Date(u.generatedAt).toLocaleString():"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Generated By: ",u.generatedBy||"N/A"]})]})]})})},x=l.vv.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#dc2626",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#fef2f2",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#fef2f2",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),h=e=>{var t,s,i,n,c,o,d;let{data:m,reportTitle:u,metadata:h}=e,p=r.useMemo(()=>{if(!m)return{};let e=(null==m?void 0:m.data)||m;return e&&"object"==typeof e?{totalCount:e.totalCount||0,activeCount:e.activeCount||0,maintenanceCount:e.maintenanceCount||0,outOfServiceCount:e.outOfServiceCount||0,utilizationRate:e.utilizationRate||0,averageMileage:e.averageMileage||0,statusDistribution:Array.isArray(e.statusDistribution)?e.statusDistribution:[],typeDistribution:Array.isArray(e.typeDistribution)?e.typeDistribution:[],maintenanceMetrics:e.maintenanceMetrics||{},...e}:{}},[m]);return(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{size:"A4",style:x.page,children:[(0,a.jsx)(l.EY,{style:x.header,children:u||"Vehicle Report"}),(0,a.jsxs)(l.Ss,{style:x.section,children:[(0,a.jsx)(l.EY,{style:x.subheader,children:"Fleet Summary"}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Total Vehicles:"}),(0,a.jsx)(l.EY,{style:x.value,children:p.totalCount||0})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Active Vehicles:"}),(0,a.jsx)(l.EY,{style:x.value,children:p.activeCount||0})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"In Maintenance:"}),(0,a.jsx)(l.EY,{style:x.value,children:p.maintenanceCount||0})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Out of Service:"}),(0,a.jsx)(l.EY,{style:x.value,children:p.outOfServiceCount||0})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Fleet Utilization Rate:"}),(0,a.jsxs)(l.EY,{style:x.value,children:[(null==(t=p.utilizationRate)?void 0:t.toFixed(2))||0,"%"]})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Average Mileage:"}),(0,a.jsxs)(l.EY,{style:x.value,children:[(null==(s=p.averageMileage)?void 0:s.toFixed(0))||0," km"]})]})]}),p.statusDistribution&&(0,a.jsxs)(l.Ss,{style:x.section,children:[(0,a.jsx)(l.EY,{style:x.subheader,children:"Vehicle Status Distribution"}),(0,a.jsxs)(l.Ss,{style:x.table,children:[(0,a.jsxs)(l.Ss,{style:x.tableHeader,children:[(0,a.jsx)(l.EY,{style:x.tableCell,children:"Status"}),(0,a.jsx)(l.EY,{style:x.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:x.tableCell,children:"Percentage"})]}),p.statusDistribution.map((e,t)=>{var s;return(0,a.jsxs)(l.Ss,{style:x.tableRow,children:[(0,a.jsx)(l.EY,{style:x.tableCell,children:(null==e?void 0:e.status)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:x.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(s=e._count)?void 0:s.status)||0}),(0,a.jsx)(l.EY,{style:x.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"status-".concat(t))})]})]}),p.typeDistribution&&(0,a.jsxs)(l.Ss,{style:x.section,children:[(0,a.jsx)(l.EY,{style:x.subheader,children:"Vehicle Type Distribution"}),(0,a.jsxs)(l.Ss,{style:x.table,children:[(0,a.jsxs)(l.Ss,{style:x.tableHeader,children:[(0,a.jsx)(l.EY,{style:x.tableCell,children:"Type"}),(0,a.jsx)(l.EY,{style:x.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:x.tableCell,children:"Percentage"})]}),p.typeDistribution.map((e,t)=>{var s;return(0,a.jsxs)(l.Ss,{style:x.tableRow,children:[(0,a.jsx)(l.EY,{style:x.tableCell,children:(null==e?void 0:e.type)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:x.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(s=e._count)?void 0:s.type)||0}),(0,a.jsx)(l.EY,{style:x.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"type-".concat(t))})]})]}),p.maintenanceMetrics&&(0,a.jsxs)(l.Ss,{style:x.section,children:[(0,a.jsx)(l.EY,{style:x.subheader,children:"Maintenance Metrics"}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Total Maintenance Records:"}),(0,a.jsx)(l.EY,{style:x.value,children:p.maintenanceMetrics.totalRecords||0})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Average Cost per Service:"}),(0,a.jsxs)(l.EY,{style:x.value,children:["$",(null==(i=p.maintenanceMetrics.averageCost)?void 0:i.toFixed(2))||0]})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Total Maintenance Cost:"}),(0,a.jsxs)(l.EY,{style:x.value,children:["$",(null==(n=p.maintenanceMetrics.totalCost)?void 0:n.toFixed(2))||0]})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Preventive Maintenance Rate:"}),(0,a.jsxs)(l.EY,{style:x.value,children:[(null==(c=p.maintenanceMetrics.preventiveRate)?void 0:c.toFixed(2))||0,"%"]})]})]}),p.utilizationMetrics&&(0,a.jsxs)(l.Ss,{style:x.section,children:[(0,a.jsx)(l.EY,{style:x.subheader,children:"Utilization Metrics"}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Average Daily Usage:"}),(0,a.jsxs)(l.EY,{style:x.value,children:[(null==(o=p.utilizationMetrics.averageDailyUsage)?void 0:o.toFixed(2))||0," ","hours"]})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Peak Usage Hours:"}),(0,a.jsx)(l.EY,{style:x.value,children:p.utilizationMetrics.peakUsageHours||"N/A"})]}),(0,a.jsxs)(l.Ss,{style:x.row,children:[(0,a.jsx)(l.EY,{style:x.label,children:"Idle Time Percentage:"}),(0,a.jsxs)(l.EY,{style:x.value,children:[(null==(d=p.utilizationMetrics.idleTimePercentage)?void 0:d.toFixed(2))||0,"%"]})]})]}),h&&(0,a.jsxs)(l.Ss,{style:x.metadata,children:[(0,a.jsx)(l.EY,{style:x.subheader,children:"Report Information"}),(0,a.jsxs)(l.EY,{style:x.metadataText,children:["Report ID: ",h.id]}),(0,a.jsxs)(l.EY,{style:x.metadataText,children:["Type: ",h.type]}),(0,a.jsxs)(l.EY,{style:x.metadataText,children:["Entity Type: ",h.entityType]}),(0,a.jsxs)(l.EY,{style:x.metadataText,children:["Generated: ",new Date(h.generatedAt).toLocaleString()]}),(0,a.jsxs)(l.EY,{style:x.metadataText,children:["Generated By: ",h.generatedBy]})]})]})})},p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"report",[t,s]=(0,r.useState)(!1),[n,o]=(0,r.useState)(null),m=(0,r.useCallback)(async(t,r,i)=>{s(!0),o(null);try{let s=await (0,l.x8)((0,a.jsx)(c,{data:t,filters:r,reportDate:new Date().toLocaleString(),reportTitle:i.filename||e})).toBlob(),n=URL.createObjectURL(s),o=document.createElement("a");o.href=n,o.setAttribute("download","".concat(i.filename||e,".pdf")),document.body.append(o),o.click(),o.remove(),URL.revokeObjectURL(n)}catch(e){console.error("PDF export failed:",e),o(e.message||"Failed to export to PDF.")}finally{s(!1)}},[e]),x=(0,r.useCallback)((t,a)=>{s(!0),o(null);try{let s=i.Wp.json_to_sheet(t),l=i.Wp.book_new();i.Wp.book_append_sheet(l,s,"Report Data"),i._h(l,"".concat(a.filename||e,".xlsx"))}catch(e){console.error("Excel export failed:",e),o(e.message||"Failed to export to Excel.")}finally{s(!1)}},[e]),p=(0,r.useCallback)((t,a)=>{s(!0),o(null);try{let s=Object.keys(t[0]||{}).join(","),l=t.map(e=>Object.values(e).map(e=>'"'.concat(String(e).replaceAll('"','""'),'"')).join(",")),r=[s,...l].join("\n"),i=new Blob([r],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let t=URL.createObjectURL(i);n.setAttribute("href",t),n.setAttribute("download","".concat(a.filename||e,".csv")),n.style.visibility="hidden",document.body.append(n),n.click(),n.remove()}}catch(e){console.error("CSV export failed:",e),o(e.message||"Failed to export to CSV.")}finally{s(!1)}},[e]),g=(0,r.useCallback)(async(e,t,r)=>{s(!0),o(null);try{let s=await (0,l.x8)((0,a.jsx)(c,{data:e,filters:t,includeCharts:!0,includeServiceHistory:!!t.includeServiceHistory,reportDate:new Date().toLocaleString(),reportTitle:r||"Dashboard Report"})).toBlob(),i=URL.createObjectURL(s),n=document.createElement("a");n.href=i,n.setAttribute("download","".concat(r||"dashboard-report",".pdf")),document.body.append(n),n.click(),n.remove(),URL.revokeObjectURL(i)}catch(e){console.error("Dashboard PDF export failed:",e),o(e.message||"Failed to export dashboard to PDF.")}finally{s(!1)}},[]),j=(0,r.useCallback)(async(e,t,r)=>{s(!0),o(null);try{let s=await (0,l.x8)((0,a.jsx)(()=>(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{orientation:"landscape",size:"A4",style:{padding:30},children:[(0,a.jsxs)(l.Ss,{style:{marginBottom:20},children:[(0,a.jsx)(l.EY,{style:{fontSize:20,fontWeight:"bold"},children:t}),(0,a.jsxs)(l.EY,{style:{fontSize:10,marginTop:5},children:["Generated on: ",new Date().toLocaleDateString()]})]}),(0,a.jsx)(l.Ss,{style:{alignItems:"center",flex:1,justifyContent:"center"},children:(0,a.jsxs)(l.EY,{style:{fontSize:14},children:["Chart data: ",JSON.stringify(e,null,2)]})})]})}),{})).toBlob(),i=URL.createObjectURL(s),n=document.createElement("a");n.href=i,n.setAttribute("download",r||"".concat(t.toLowerCase().replaceAll(/\s+/g,"-"),".pdf")),document.body.append(n),n.click(),n.remove(),URL.revokeObjectURL(i)}catch(e){console.error("Chart PDF export failed:",e),o(e.message||"Failed to export chart to PDF.")}finally{s(!1)}},[]),v=(0,r.useCallback)(async(e,t,r,i)=>{s(!0),o(null);let n=e=>{if(null==e)return null;if(Array.isArray(e))return e.map(n);if("object"==typeof e&&e.constructor===Object){let t={};for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&"__proto__"!==s&&"constructor"!==s){let a=n(e[s]);void 0!==a&&(t[s]=a)}return t}return"function"==typeof e||"symbol"==typeof e?null:e};try{let s,o;if(!e)throw Error("No report data provided for PDF export");let x=(null==e?void 0:e.data)||e||{},p=n(x),g={data:{totalCount:(null==p?void 0:p.totalCount)||0,summary:(null==p?void 0:p.summary)||{message:"No data available"},records:(null==p?void 0:p.records)||[],statusDistribution:(null==p?void 0:p.statusDistribution)||[],priorityDistribution:(null==p?void 0:p.priorityDistribution)||[],...p},metadata:(null==e?void 0:e.metadata)||{entityType:t,format:"pdf",generatedAt:new Date().toISOString(),generatedBy:"system",id:"".concat(t,"_").concat(Date.now()),type:"aggregate"}};if(0===Object.keys(g.data).length)throw Error("After sanitization, no valid data is available for PDF export.");let j=r||"".concat(t," Report"),v=g.metadata||{};switch(t){case"delegations":{let e={dateRange:{from:new Date,to:new Date},employees:[],locations:[],status:[],vehicles:[]};s=(0,a.jsx)(c,{data:g,filters:e,reportDate:new Date().toLocaleString(),reportTitle:j});break}case"employees":s=(0,a.jsx)(d,{data:g,metadata:v,reportTitle:j});break;case"tasks":s=(0,a.jsx)(u,{data:g,metadata:v,reportTitle:j});break;case"vehicles":s=(0,a.jsx)(h,{data:g,metadata:v,reportTitle:j});break;default:throw Error("Unsupported entity type: ".concat(t,". Supported types are: delegations, tasks, vehicles, employees"))}console.log("Generating PDF with SANITIZED data:",g),console.log("Entity Type:",t),console.log("Raw data structure:",e),console.log("Normalized data structure:",g),console.log("PDF Component:",s),console.log("Entity type:",t),console.log("Report title:",j);try{console.log("Starting PDF blob generation..."),o=await (0,l.x8)(s).toBlob(),console.log("PDF blob generated successfully:",{size:o.size,type:o.type})}catch(e){var m;throw console.error("PDF generation failed:",e),console.error("PDF error stack:",e.stack),Error("PDF generation failed: ".concat(null!=(m=e.message)?m:"Unknown PDF error"))}if(!o||0===o.size)throw Error("Generated PDF blob is empty or invalid");console.log("Creating download link...");let y=URL.createObjectURL(o),f=document.createElement("a");f.href=y;let N="".concat(i||"".concat(t,"-report"),".pdf");f.setAttribute("download",N),console.log("Triggering download for:",N),document.body.append(f),f.click(),f.remove(),URL.revokeObjectURL(y),console.log("Download triggered successfully")}catch(e){console.error("Report PDF export failed:",e),o(e.message||"Failed to export report to PDF.")}finally{s(!1)}},[]);return{exportChartToPDF:j,exportDashboardToPDF:g,exportError:n,exportReportToExcel:(0,r.useCallback)((e,t,a)=>{s(!0),o(null);try{let s=i.Wp.book_new(),l=e.data||e;if(l.summary||l.totalCount){let e=[];if(l.totalCount&&e.push({Metric:"Total Count",Value:l.totalCount}),l.summary)for(let[t,s]of Object.entries(l.summary))e.push({Metric:t.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),Value:s});let t=i.Wp.json_to_sheet(e);i.Wp.book_append_sheet(s,t,"Summary")}if(l.statusDistribution){let e=i.Wp.json_to_sheet(l.statusDistribution);i.Wp.book_append_sheet(s,e,"Status Distribution")}if(l.priorityDistribution){let e=i.Wp.json_to_sheet(l.priorityDistribution);i.Wp.book_append_sheet(s,e,"Priority Distribution")}if(l.locationMetrics){let e=i.Wp.json_to_sheet(l.locationMetrics);i.Wp.book_append_sheet(s,e,"Location Metrics")}if(l.maintenanceMetrics){let e=i.Wp.json_to_sheet([l.maintenanceMetrics]);i.Wp.book_append_sheet(s,e,"Maintenance Metrics")}if(l.performanceMetrics){let e=i.Wp.json_to_sheet([l.performanceMetrics]);i.Wp.book_append_sheet(s,e,"Performance Metrics")}i._h(s,"".concat(a||"".concat(t,"-report"),".xlsx"))}catch(e){console.error("Report Excel export failed:",e),o(e.message||"Failed to export report to Excel.")}finally{s(!1)}},[]),exportReportToPDF:v,exportToCSV:p,exportToExcel:x,exportToPDF:m,isExporting:t}}},6560:(e,t,s)=>{"use strict";s.d(t,{r:()=>c});var a=s(95155),l=s(50172),r=s(12115),i=s(30285),n=s(54036);let c=r.forwardRef((e,t)=>{let{actionType:s="primary",asChild:r=!1,children:c,className:o,disabled:d,icon:m,isLoading:u=!1,loadingText:x,...h}=e,{className:p,variant:g}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[s];return(0,a.jsx)(i.$,{asChild:r,className:(0,n.cn)(p,o),disabled:u||d,ref:t,variant:g,...h,children:u?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}),x||c]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,a.jsx)("span",{className:"mr-2",children:m}),c]})})});c.displayName="ActionButton"},14636:(e,t,s)=>{"use strict";s.d(t,{AM:()=>n,Wv:()=>c,hl:()=>o});var a=s(95155),l=s(20547),r=s(12115),i=s(54036);let n=l.bL,c=l.l9;l.bm;let o=r.forwardRef((e,t)=>{let{align:s="center",className:r,sideOffset:n=4,...c}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{align:s,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",r),ref:t,sideOffset:n,...c})})});o.displayName=l.UC.displayName},17759:(e,t,s)=>{"use strict";s.d(t,{C5:()=>v,MJ:()=>g,Rr:()=>j,eI:()=>h,lR:()=>p,lV:()=>o,zB:()=>m});var a=s(95155),l=s(12115),r=s(99708),i=s(62177),n=s(54036),c=s(85057);let o=i.Op,d=l.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},u=()=>{let e=l.useContext(d),t=l.useContext(x),{getFieldState:s,formState:a}=(0,i.xW)(),r=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...r}},x=l.createContext({}),h=l.forwardRef((e,t)=>{let{className:s,...r}=e,i=l.useId();return(0,a.jsx)(x.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",s),...r})})});h.displayName="FormItem";let p=l.forwardRef((e,t)=>{let{className:s,...l}=e,{error:r,formItemId:i}=u();return(0,a.jsx)(c.J,{ref:t,className:(0,n.cn)(r&&"text-destructive",s),htmlFor:i,...l})});p.displayName="FormLabel";let g=l.forwardRef((e,t)=>{let{...s}=e,{error:l,formItemId:i,formDescriptionId:n,formMessageId:c}=u();return(0,a.jsx)(r.DX,{ref:t,id:i,"aria-describedby":l?"".concat(n," ").concat(c):"".concat(n),"aria-invalid":!!l,...s})});g.displayName="FormControl";let j=l.forwardRef((e,t)=>{let{className:s,...l}=e,{formDescriptionId:r}=u();return(0,a.jsx)("p",{ref:t,id:r,className:(0,n.cn)("text-sm text-muted-foreground",s),...l})});j.displayName="FormDescription";let v=l.forwardRef((e,t)=>{var s;let{className:l,children:r,...i}=e,{error:c,formMessageId:o}=u(),d=c?String(null!=(s=null==c?void 0:c.message)?s:""):r;return d?(0,a.jsx)("p",{ref:t,id:o,className:(0,n.cn)("text-sm font-medium text-destructive",l),...i,children:d}):null});v.displayName="FormMessage"},17841:(e,t,s)=>{"use strict";s.d(t,{er:()=>N,kA:()=>y,gd:()=>f,BD:()=>v,nB:()=>k,jM:()=>b,lG:()=>w});var a=s(71610),l=s(26715),r=s(5041),i=s(12115),n=s(54120),c=s(90111),o=s(75908);class d{static enrich(e,t,s){var a,l,r;let{employeeMap:i,vehicleMap:n}=this.createLookupMaps(t,s);return{...e,drivers:null!=(a=this.enrichDrivers(e.drivers,i))?a:[],escorts:null!=(l=this.enrichEscorts(e.escorts,i))?l:[],vehicles:null!=(r=this.enrichVehicles(e.vehicles,n))?r:[]}}static createLookupMaps(e,t){return{employeeMap:new Map(e.map(e=>[e.id,e])),vehicleMap:new Map(t.map(e=>[e.id,e]))}}static enrichDrivers(e,t){return null==e?void 0:e.map(e=>{let s=e.employee||t.get(Number(e.employeeId));return{...e,...s&&{employee:s}}})}static enrichEscorts(e,t){return null==e?void 0:e.map(e=>{let s=e.employee||t.get(Number(e.employeeId));return{...e,...s&&{employee:s}}})}static enrichVehicles(e,t){return null==e?void 0:e.map(e=>{let s=e.vehicle||t.get(e.vehicleId);return{...e,...s&&{vehicle:s}}})}}let m=(e,t,s)=>d.enrich(e,t,s);var u=s(31203);let x={all:["delegations"],detail:e=>["delegations",e]},h=e=>({enabled:!!e,queryFn:()=>o.delegationApiService.getById(e),queryKey:x.detail(e),staleTime:3e5}),p=()=>({queryFn:()=>o.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),g=()=>({queryFn:()=>o.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),j=e=>[h(e),p(),g()],v=e=>(0,c.GK)([...x.all],async()=>(await o.delegationApiService.getAll()).data,"delegation",{staleTime:0,...e}),y=e=>(0,c.GK)([...x.detail(e)],async()=>await o.delegationApiService.getById(e),"delegation",{enabled:!!e,staleTime:3e5}),f=e=>{let[t,s,l]=(0,a.E)({queries:j(e)}),r=(0,i.useMemo)(()=>{if((null==t?void 0:t.data)&&(null==s?void 0:s.data)&&(null==l?void 0:l.data))try{let e=t.data;return m(e,s.data,l.data)}catch(e){throw console.error("Error enriching delegation data:",e),e}},[null==t?void 0:t.data,null==s?void 0:s.data,null==l?void 0:l.data]),n=(0,i.useCallback)(()=>{null==t||t.refetch(),null==s||s.refetch(),null==l||l.refetch()},[null==t?void 0:t.refetch,null==s?void 0:s.refetch,null==l?void 0:l.refetch]);return{data:r,error:(null==t?void 0:t.error)||(null==s?void 0:s.error)||(null==l?void 0:l.error),isError:(null==t?void 0:t.isError)||(null==s?void 0:s.isError)||(null==l?void 0:l.isError),isLoading:(null==t?void 0:t.isLoading)||(null==s?void 0:s.isLoading)||(null==l?void 0:l.isLoading),isPending:(null==t?void 0:t.isPending)||(null==s?void 0:s.isPending)||(null==l?void 0:l.isPending),refetch:n}},N=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let t=u.G.toCreateRequest(e);return await o.delegationApiService.create(t)},onError:(t,s,a)=>{(null==a?void 0:a.previousDelegations)&&e.setQueryData(x.all,a.previousDelegations),console.error("Failed to create delegation:",t),e.invalidateQueries({queryKey:x.all})},onMutate:async t=>{await e.cancelQueries({queryKey:x.all});let s=e.getQueryData(x.all);return e.setQueryData(x.all,function(){var e,s,a,l,r,i,n,c;let o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],d="optimistic-".concat(Date.now()),m=new Date().toISOString(),u=t.flightArrivalDetails?{id:"optimistic-flight-arr-".concat(Date.now()),...t.flightArrivalDetails}:null,x=t.flightDepartureDetails?{id:"optimistic-flight-dep-".concat(Date.now()+1),...t.flightDepartureDetails}:null;return[...o,{arrivalFlight:null!=u?u:null,createdAt:m,delegates:(null==(e=t.delegates)?void 0:e.map((e,t)=>{var s;return{id:"optimistic-delegate-".concat(d,"-").concat(t),name:e.name,notes:null!=(s=e.notes)?s:null,title:e.title}}))||[],departureFlight:null!=x?x:null,drivers:(null==(s=t.drivers)?void 0:s.map(e=>{var t;return{createdAt:m,createdBy:null,delegationId:d,employeeId:e.employeeId,id:"optimistic-driver-".concat(d,"-").concat(e.employeeId),notes:null!=(t=e.notes)?t:null,updatedAt:m}}))||[],durationFrom:t.durationFrom,durationTo:t.durationTo,escorts:(null==(a=t.escorts)?void 0:a.map(e=>{var t;return{createdAt:m,createdBy:null,delegationId:d,employeeId:e.employeeId,id:"optimistic-escort-".concat(d,"-").concat(e.employeeId),notes:null!=(t=e.notes)?t:null,updatedAt:m}}))||[],eventName:t.eventName,id:d,imageUrl:null!=(r=t.imageUrl)?r:null,invitationFrom:null!=(i=t.invitationFrom)?i:null,invitationTo:null!=(n=t.invitationTo)?n:null,location:t.location,notes:null!=(c=t.notes)?c:null,status:t.status||"Planned",statusHistory:[],updatedAt:m,vehicles:(null==(l=t.vehicles)?void 0:l.map(e=>{var t,s;return{assignedDate:e.assignedDate,createdAt:m,createdBy:null,delegationId:d,id:"optimistic-vehicle-".concat(d,"-").concat(e.vehicleId),notes:null!=(t=e.notes)?t:null,returnDate:null!=(s=e.returnDate)?s:null,updatedAt:m,vehicleId:e.vehicleId}}))||[]}]}),{previousDelegations:s}},onSettled:()=>{e.invalidateQueries({queryKey:x.all})}})},b=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let{data:t,id:s}=e;return await o.delegationApiService.update(s,t)},onError:(t,s,a)=>{(null==a?void 0:a.previousDelegation)&&e.setQueryData(x.detail(s.id),a.previousDelegation),(null==a?void 0:a.previousDelegationsList)&&e.setQueryData(x.all,a.previousDelegationsList),console.error("Failed to update delegation:",t),e.invalidateQueries({queryKey:x.detail(s.id)}),e.invalidateQueries({queryKey:x.all})},onMutate:async t=>{let{data:s,id:a}=t;await e.cancelQueries({queryKey:x.all}),await e.cancelQueries({queryKey:x.detail(a)});let l=e.getQueryData(x.detail(a)),r=e.getQueryData(x.all);return e.setQueryData(x.detail(a),e=>{var t,a,l,r,i,c,o,d,m,u,x,h,p,g,j,v,y,f,N,b,w,k,S,C,A,E,D,T,R;if(!e)return;let z=new Date().toISOString();return{...e,arrivalFlight:(0,n.d$)(null===s.flightArrivalDetails?null:void 0===s.flightArrivalDetails?e.arrivalFlight:{airport:s.flightArrivalDetails.airport||(null==(t=e.arrivalFlight)?void 0:t.airport)||"",dateTime:s.flightArrivalDetails.dateTime||(null==(a=e.arrivalFlight)?void 0:a.dateTime)||"",flightNumber:s.flightArrivalDetails.flightNumber||(null==(l=e.arrivalFlight)?void 0:l.flightNumber)||"",id:(null==(r=e.arrivalFlight)?void 0:r.id)||"optimistic-arr-".concat(Date.now()),notes:null!=(g=null!=(p=s.flightArrivalDetails.notes)?p:null==(i=e.arrivalFlight)?void 0:i.notes)?g:null,terminal:null!=(v=null!=(j=s.flightArrivalDetails.terminal)?j:null==(c=e.arrivalFlight)?void 0:c.terminal)?v:null}),departureFlight:(0,n.d$)(null===s.flightDepartureDetails?null:void 0===s.flightDepartureDetails?e.departureFlight:{airport:s.flightDepartureDetails.airport||(null==(o=e.departureFlight)?void 0:o.airport)||"",dateTime:s.flightDepartureDetails.dateTime||(null==(d=e.departureFlight)?void 0:d.dateTime)||"",flightNumber:s.flightDepartureDetails.flightNumber||(null==(m=e.departureFlight)?void 0:m.flightNumber)||"",id:(null==(u=e.departureFlight)?void 0:u.id)||"optimistic-dep-".concat(Date.now()),notes:null!=(f=null!=(y=s.flightDepartureDetails.notes)?y:null==(x=e.departureFlight)?void 0:x.notes)?f:null,terminal:null!=(b=null!=(N=s.flightDepartureDetails.terminal)?N:null==(h=e.departureFlight)?void 0:h.terminal)?b:null}),durationFrom:null!=(w=s.durationFrom)?w:e.durationFrom,durationTo:null!=(k=s.durationTo)?k:e.durationTo,eventName:null!=(S=s.eventName)?S:e.eventName,imageUrl:(0,n.d$)(null!=(C=s.imageUrl)?C:e.imageUrl),invitationFrom:(0,n.d$)(null!=(A=s.invitationFrom)?A:e.invitationFrom),invitationTo:(0,n.d$)(null!=(E=s.invitationTo)?E:e.invitationTo),location:null!=(D=s.location)?D:e.location,notes:(0,n.d$)(null!=(T=s.notes)?T:e.notes),status:null!=(R=s.status)?R:e.status,updatedAt:z}}),e.setQueryData(x.all,function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.map(t=>t.id===a&&e.getQueryData(x.detail(a))||t)}),{previousDelegation:l,previousDelegationsList:r}},onSettled:(t,s,a)=>{e.invalidateQueries({queryKey:x.detail(a.id)}),e.invalidateQueries({queryKey:x.all})}})},w=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let{id:t,status:s,statusChangeReason:a}=e;return await o.delegationApiService.updateStatus(t,s,a)},onError:(t,s,a)=>{(null==a?void 0:a.previousDelegation)&&e.setQueryData(x.detail(s.id),a.previousDelegation),console.error("Failed to update delegation status:",t)},onMutate:async t=>{let{id:s,status:a}=t;await e.cancelQueries({queryKey:x.detail(s)});let l=e.getQueryData(x.detail(s));return e.setQueryData(x.detail(s),e=>e?{...e,status:a}:void 0),{previousDelegation:l}},onSettled:(t,s,a)=>{e.invalidateQueries({queryKey:x.detail(a.id)}),e.invalidateQueries({queryKey:x.all})}})},k=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>(await o.delegationApiService.delete(e),e),onError:(t,s,a)=>{(null==a?void 0:a.previousDelegationsList)&&e.setQueryData(x.all,a.previousDelegationsList),console.error("Failed to delete delegation:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:x.all}),await e.cancelQueries({queryKey:x.detail(t)});let s=e.getQueryData(x.all);return e.setQueryData(x.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==t)}),e.removeQueries({queryKey:x.detail(t)}),{previousDelegationsList:s}},onSettled:()=>{e.invalidateQueries({queryKey:x.all})}})}},21354:(e,t,s)=>{"use strict";s.d(t,{R:()=>l});class a{static enrich(e,t,s){let{employeeMap:a,vehicleMap:l}=this.createLookupMaps(t,s),r=this.enrichStaffEmployee(e,a);return r=this.enrichDriverEmployee(r,a),r=this.enrichVehicle(r,l)}static createLookupMaps(e,t){let s=Array.isArray(e)?e:[],a=Array.isArray(t)?t:[];return{employeeMap:new Map(s.map(e=>[e.id,e])),vehicleMap:new Map(a.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,t){var s,a;if(!e.driverEmployeeId)return e;let l=null!=(a=null!=(s=e.driverEmployee)?s:t.get(e.driverEmployeeId))?a:null;return{...e,driverEmployee:l}}static enrichStaffEmployee(e,t){var s,a;if(!e.staffEmployeeId)return e;let l=null!=(a=null!=(s=e.staffEmployee)?s:t.get(e.staffEmployeeId))?a:null;return{...e,staffEmployee:l}}static enrichVehicle(e,t){var s,a;if(!e.vehicleId)return e;let l=null!=(a=null!=(s=e.vehicle)?s:t.get(e.vehicleId))?a:null;return{...e,vehicle:l}}}let l=(e,t,s)=>a.enrich(e,t,s)},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(95155),l=s(74466);s(12115);var r=s(54036);let i=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function n(e){let{className:t,variant:s,...l}=e;return(0,a.jsx)("div",{className:(0,r.cn)(i({variant:s}),t),...l})}},32383:()=>{},32606:(e,t,s)=>{"use strict";s.d(t,{ReportingDashboard:()=>am});var a=s(95155),l=s(5263),r=s(80659),i=s(93223),n=s(28328),c=s(50286),o=s(16736),d=s(15300),m=s(18271),u=s(71978),x=s(9572),h=s(67554),p=s(68718),g=s(12115),j=s(52639),v=s(26126),y=s(30285),f=s(17313),N=s(66695),b=s(91721),w=s(3638),k=s(50594),S=s(47262),C=s(59409),A=s(85057),E=s(88539),D=s(62523),T=s(22346),R=s(35079),z=s(86950),M=s(51920),F=s(6548),P=s(52747);class I{async generateAggregateReport(e){try{let t=await this.apiClient.request({url:"/api/reporting/reports/aggregate/".concat(e.entityType),method:"POST",data:{filters:e.filters,template:e.template||"default",format:"json",options:e.options||{}}}),s=t.data.data||t.data;return{data:(null==s?void 0:s.aggregate)||s,metadata:t.data.metadata||(null==s?void 0:s.metadata)||{id:"aggregate_".concat(e.entityType,"_").concat(Date.now()),type:"aggregate",entityType:e.entityType,format:e.format||"json",template:e.template||"default",generatedAt:new Date().toISOString(),generatedBy:"system",filters:e.filters,options:e.options}}}catch(t){throw console.error("Failed to generate aggregate report:",t),Error("Failed to generate ".concat(e.entityType," aggregate report"))}}async generateIndividualReport(e){try{let t=await this.apiClient.request({url:"/api/reporting/reports/individual/".concat(e.entityType,"/").concat(e.entityId),method:"POST",data:{template:e.template||"default",format:"json",options:e.options||{}}});return{data:t.data.data||t.data,metadata:t.data.metadata||{id:"individual_".concat(e.entityType,"_").concat(e.entityId,"_").concat(Date.now()),type:"individual",entityType:e.entityType,entityId:e.entityId,format:e.format||"json",template:e.template||"default",generatedAt:new Date().toISOString(),generatedBy:"system",options:e.options}}}catch(t){throw console.error("Failed to generate individual report:",t),Error("Failed to generate ".concat(e.entityType," individual report"))}}async generateComprehensiveReport(e){try{let t=await this.apiClient.request({url:"/api/reporting/reports/generate",method:"POST",data:{entityTypes:e.entityTypes,filters:e.filters,template:e.template||"comprehensive",format:"json",options:e.options||{}}});return{data:t.data.data||t.data,metadata:t.data.metadata||{id:"comprehensive_".concat(Date.now()),type:"comprehensive",entityTypes:e.entityTypes,format:e.format||"json",template:e.template||"comprehensive",generatedAt:new Date().toISOString(),generatedBy:"system",filters:e.filters,options:e.options}}}catch(e){throw console.error("Failed to generate comprehensive report:",e),Error("Failed to generate comprehensive report")}}constructor(e){this.apiClient=e}}class L{async request(e){throw Error("API client not initialized. Use createReportGenerationService with proper API client.")}}let O=e=>new I(e);new I(new L);let W=()=>{let[e,t]=(0,g.useState)(!1),[a,l]=(0,g.useState)(null),{client:r}=(0,P.a8)(),i=O({request:async e=>({data:await r.request(e)})}),n=(0,g.useCallback)(async e=>{t(!0),l(null);try{return await i.generateComprehensiveReport(e)}catch(e){throw l(e instanceof Error?e.message:"Failed to generate report"),e}finally{t(!1)}},[i]),c=(0,g.useCallback)(async e=>{t(!0),l(null);try{return await i.generateIndividualReport(e)}catch(e){throw l(e instanceof Error?e.message:"Failed to generate individual report"),e}finally{t(!1)}},[i]);return{generateComprehensiveReport:n,generateIndividualReport:c,generateAggregateReport:(0,g.useCallback)(async e=>{t(!0),l(null);try{return await i.generateAggregateReport(e)}catch(e){throw l(e instanceof Error?e.message:"Failed to generate aggregate report"),e}finally{t(!1)}},[i]),exportReport:(0,g.useCallback)(async(e,t,a,l,r)=>{try{let{useExport:i}=await Promise.resolve().then(s.bind(s,1120)),{exportReportToPDF:n,exportReportToExcel:c,exportToCSV:o}=i(r||"report");switch(t){case"pdf":await n(e,a,l||"".concat(a.charAt(0).toUpperCase()+a.slice(1)," Report"),r);break;case"excel":c(e,a,r);break;case"csv":let d=Array.isArray(e.data)?e.data:[e.data||e];o(d,{filename:r||"report"});break;default:throw Error("Unsupported export format: ".concat(t))}}catch(e){throw console.error("Export failed:",e),e}},[]),isGenerating:e,error:a}},V=e=>{var t,s;let{client:a}=(0,P.a8)(),l=new URLSearchParams;(null==e?void 0:e.type)&&l.append("type",e.type),(null==e?void 0:e.entityType)&&l.append("entityType",e.entityType);let r=(0,F.Sk)(["report-history",e],async()=>await a.get("/api/reporting/reports/history?".concat(l.toString())),{cacheDuration:12e4,enableRetry:!0});return{reports:(null==(t=r.data)?void 0:t.reports)||[],pagination:null==(s=r.data)?void 0:s.pagination,isLoading:r.isLoading,error:r.error,refetch:r.refetch}},B=()=>{let[e,t]=(0,g.useState)(!1),[s,a]=(0,g.useState)(null),{client:l}=(0,P.a8)();return{downloadReport:(0,g.useCallback)(async e=>{t(!0),a(null);try{let t=await l.get("/api/reporting/reports/".concat(e,"/download"));console.log("Download result:",t),alert("Download functionality will be implemented with file storage")}catch(e){throw a(e instanceof Error?e.message:"Failed to download report"),e}finally{t(!1)}},[l]),isDownloading:e,downloadError:s}},U=()=>{let{client:e}=(0,P.a8)(),t=(0,F.Sk)(["report-templates"],async()=>{let t=await e.get("/api/reporting/reports/templates");return Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.warn("Report templates API returned unexpected format:",t),[])},{cacheDuration:6e5,enableRetry:!0});return{templates:Array.isArray(t.data)?t.data:[],isLoading:t.isLoading,error:t.error,refetch:t.refetch}};var Y=s(85511),Z=s(14636),$=s(55365),_=s(25318),G=s(31949),q=s(8376),J=s(78816),H=s(76198),Q=s(19164),K=s(74641),X=s(37140),ee=s(1407),et=s(94376),es=s(44861),ea=s(53941),el=s(63482),er=s(41784),ei=s(24386),en=s(54036);let ec=[{label:"Today",getValue:()=>({from:new Date,to:new Date})},{label:"Yesterday",getValue:()=>{let e=(0,J.e)(new Date,1);return{from:e,to:e}}},{label:"Last 3 days",getValue:()=>({from:(0,J.e)(new Date,2),to:new Date})},{label:"Last 7 days",getValue:()=>({from:(0,J.e)(new Date,6),to:new Date})},{label:"Last 2 weeks",getValue:()=>({from:(0,H.k)(new Date,2),to:new Date})},{label:"Last 30 days",getValue:()=>({from:(0,J.e)(new Date,29),to:new Date})},{label:"This week",getValue:()=>({from:(0,J.e)(new Date,new Date().getDay()),to:new Date})},{label:"This month",getValue:()=>({from:(0,Q.w)(new Date),to:(0,K.p)(new Date)})},{label:"Last month",getValue:()=>{let e=(0,X.a)(new Date,1);return{from:(0,Q.w)(e),to:(0,K.p)(e)}}},{label:"Last 3 months",getValue:()=>({from:(0,X.a)(new Date,3),to:new Date})},{label:"This year",getValue:()=>({from:(0,ee.D)(new Date),to:(0,et.Q)(new Date)})}],eo=e=>{let{value:t,onChange:s,placeholder:l="Select date range",className:r,disabled:i=!1,maxDays:n=365,minDays:c=1,maxDate:o=new Date,minDate:d=new Date(2020,0,1),showValidation:m=!0}=e,[u,x]=(0,g.useState)(!1),[h,p]=(0,g.useState)(t||null),j=(0,g.useMemo)(()=>{if(!h)return{isValid:!0};let{from:e,to:t}=h;if(!(0,es.f)(e)||!(0,es.f)(t))return{isValid:!1,message:"Invalid date selected",type:"error"};if((0,ea.d)(e,t))return{isValid:!1,message:"Start date must be before end date",type:"error"};if(d&&(0,el.Y)(e,d))return{isValid:!1,message:"Start date cannot be before ".concat((0,er.GP)(d,"MMM dd, yyyy")),type:"error"};if(o&&(0,ea.d)(t,o))return{isValid:!1,message:"End date cannot be after ".concat((0,er.GP)(o,"MMM dd, yyyy")),type:"error"};let s=(0,ei.c)(t,e)+1;return s<c?{isValid:!1,message:"Date range must be at least ".concat(c," day").concat(c>1?"s":""),type:"error"}:s>n?{isValid:!1,message:"Date range cannot exceed ".concat(n," days"),type:"error"}:s>90?{isValid:!0,message:"Large date range (".concat(s," days) may affect performance"),type:"warning"}:{isValid:!0,message:"".concat(s," day").concat(s>1?"s":""," selected"),type:"info"}},[h,n,c,o,d]);(0,g.useEffect)(()=>{p(t||null)},[t]);let f=e=>{let t=e.getValue();p(t),N(t).isValid&&(null==s||s(t),x(!1))},N=e=>{if(!e)return{isValid:!0};let{from:t,to:s}=e;return(0,ei.c)(s,t)+1>n?{isValid:!1,message:"Date range cannot exceed ".concat(n," days"),type:"error"}:{isValid:!0}};return(0,a.jsx)("div",{className:(0,en.cn)("relative",r),children:(0,a.jsxs)(Z.AM,{open:u,onOpenChange:x,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-start text-left font-normal",!t&&"text-muted-foreground"),disabled:i,children:[(0,a.jsx)(M.A,{className:"mr-2 h-4 w-4"}),t?t.from.toDateString()===t.to.toDateString()?(0,er.GP)(t.from,"MMM dd, yyyy"):"".concat((0,er.GP)(t.from,"MMM dd, yyyy")," - ").concat((0,er.GP)(t.to,"MMM dd, yyyy")):l,t&&(0,a.jsxs)(v.E,{variant:"secondary",className:"ml-auto",children:[Math.ceil((t.to.getTime()-t.from.getTime())/864e5)+1," ","days"]})]})}),(0,a.jsx)(Z.hl,{className:"w-auto max-w-4xl p-0",align:"start",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"border-r p-4 space-y-2 min-w-[160px]",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-gray-900 mb-3",children:"Quick Select"}),ec.map(e=>(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-xs h-8 px-2",onClick:()=>f(e),children:e.label},e.label)),t&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"border-t pt-2 mt-3",children:(0,a.jsxs)(y.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-sm text-red-600 hover:text-red-700",onClick:()=>{p(null),null==s||s(null),x(!1)},children:[(0,a.jsx)(_.A,{className:"mr-2 h-3 w-3"}),"Clear"]})})})]}),(0,a.jsxs)("div",{className:"p-4 min-w-[600px]",children:[(0,a.jsx)(Y.V,{mode:"range",selected:h||void 0,onSelect:e=>{if(e&&e.from){let t={from:e.from,to:e.to||e.from};p(t),N(t).isValid&&t.from&&t.to&&(null==s||s(t),x(!1))}else p(null)},numberOfMonths:2,className:"rounded-md border-0",disabled:[...d?[{before:d}]:[],...o?[{after:o}]:[]],showOutsideDays:!0,fixedWeeks:!0}),h&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h5",{className:"font-medium text-sm text-gray-900 mb-2",children:"Selected Range"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:["From: ",(0,er.GP)(h.from,"MMM dd, yyyy")]}),(0,a.jsxs)("div",{children:["To: ",(0,er.GP)(h.to,"MMM dd, yyyy")]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.ceil((h.to.getTime()-h.from.getTime())/864e5)+1," ","days"]})]}),m&&j.message&&(0,a.jsx)($.Fc,{className:(0,en.cn)("mt-3 py-2 px-3","error"===j.type&&"border-red-200 bg-red-50","warning"===j.type&&"border-yellow-200 bg-yellow-50","info"===j.type&&"border-blue-200 bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["error"===j.type&&(0,a.jsx)(G.A,{className:"h-3 w-3 text-red-600"}),"warning"===j.type&&(0,a.jsx)(G.A,{className:"h-3 w-3 text-yellow-600"}),"info"===j.type&&(0,a.jsx)(q.A,{className:"h-3 w-3 text-blue-600"}),(0,a.jsx)($.TN,{className:(0,en.cn)("text-xs","error"===j.type&&"text-red-700","warning"===j.type&&"text-yellow-700","info"===j.type&&"text-blue-700"),children:j.message})]})})]})]})]})})]})})},ed=e=>{let{className:t,size:s="md"}=e;return(0,a.jsx)("div",{className:(0,en.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[s],t)})},em=[{id:"delegations",name:"Delegations",icon:R.A,description:"Delegation assignments and status tracking",color:"bg-blue-100 text-blue-800"},{id:"tasks",name:"Tasks",icon:d.A,description:"Task completion and performance metrics",color:"bg-green-100 text-green-800"},{id:"vehicles",name:"Vehicles",icon:n.A,description:"Vehicle utilization and maintenance data",color:"bg-orange-100 text-orange-800"},{id:"employees",name:"Employees",icon:c.A,description:"Employee performance and workload analysis",color:"bg-purple-100 text-purple-800"}],eu=[{id:"pdf",name:"PDF",description:"Formatted document for printing"},{id:"excel",name:"Excel",description:"Spreadsheet with multiple sheets"},{id:"csv",name:"CSV",description:"Comma-separated values for data analysis"}];var ex=s(75074);let eh=[{id:"delegations",name:"Delegation",icon:R.A,placeholder:"Enter delegation ID",description:"Generate detailed report for a specific delegation"},{id:"tasks",name:"Task",icon:d.A,placeholder:"Enter task ID",description:"Generate detailed report for a specific task"},{id:"vehicles",name:"Vehicle",icon:n.A,placeholder:"Enter vehicle ID or license plate",description:"Generate detailed report for a specific vehicle"},{id:"employees",name:"Employee",icon:c.A,placeholder:"Enter employee ID or email",description:"Generate detailed report for a specific employee"}],ep=[{id:"pdf",name:"PDF",description:"Formatted document for printing"},{id:"excel",name:"Excel",description:"Spreadsheet format"},{id:"csv",name:"CSV",description:"Data export format"}];var eg=s(83940),ej=s(1120);let ev=[{color:"bg-blue-100 text-blue-800",description:"Aggregate delegation analytics and trends",icon:R.A,id:"delegations",metrics:["Total Count","Completion Rate","Average Duration","Status Distribution"],name:"Delegations"},{color:"bg-green-100 text-green-800",description:"Task performance and completion analytics",icon:d.A,id:"tasks",metrics:["Total Tasks","Completion Rate","Average Time","Priority Distribution"],name:"Tasks"},{color:"bg-orange-100 text-orange-800",description:"Vehicle utilization and maintenance analytics",icon:n.A,id:"vehicles",metrics:["Fleet Size","Utilization Rate","Maintenance Costs","Performance Metrics"],name:"Vehicles"},{color:"bg-purple-100 text-purple-800",description:"Employee performance and workload analytics",icon:c.A,id:"employees",metrics:["Total Employees","Performance Scores","Workload Distribution","Availability"],name:"Employees"}],ey=[{description:"Formatted analytics report",id:"pdf",name:"PDF"},{description:"Spreadsheet with charts",id:"excel",name:"Excel"},{description:"Raw data export",id:"csv",name:"CSV"}],ef={delegations:[{id:"status",name:"Status",options:["Active","Completed","Pending","Cancelled"],type:"select"},{id:"priority",name:"Priority",options:["High","Medium","Low"],type:"select"},{id:"location",name:"Location",type:"text"}],employees:[{id:"department",name:"Department",type:"text"},{id:"position",name:"Position",type:"text"},{id:"status",name:"Status",options:["Active","Inactive","On Leave"],type:"select"}],tasks:[{id:"status",name:"Status",options:["Pending","In Progress","Completed","Cancelled"],type:"select"},{id:"priority",name:"Priority",options:["High","Medium","Low"],type:"select"},{id:"assignee",name:"Assignee",type:"text"}],vehicles:[{id:"status",name:"Status",options:["Active","Maintenance","Inactive"],type:"select"},{id:"type",name:"Vehicle Type",type:"text"},{id:"location",name:"Location",type:"text"}]};var eN=s(35714),eb=s(17607);let ew=[{label:"All Types",value:""},{label:"Comprehensive",value:"comprehensive"},{label:"Individual",value:"individual"},{label:"Aggregate",value:"aggregate"}],ek=[{label:"All Entities",value:""},{label:"Delegations",value:"delegations"},{label:"Tasks",value:"tasks"},{label:"Vehicles",value:"vehicles"},{label:"Employees",value:"employees"}],eS={completed:{color:"bg-green-100 text-green-800",label:"Completed"},failed:{color:"bg-red-100 text-red-800",label:"Failed"},processing:{color:"bg-yellow-100 text-yellow-800",label:"Processing"}},eC={csv:{color:"bg-blue-100 text-blue-800",label:"CSV"},excel:{color:"bg-green-100 text-green-800",label:"Excel"},pdf:{color:"bg-red-100 text-red-800",label:"PDF"}},eA=[{id:"comprehensive",label:"Comprehensive Reports",icon:d.A,description:"Generate reports across multiple entity types",component:()=>{var e,t;let[s,l]=(0,g.useState)(["delegations"]),[r,i]=(0,g.useState)("comprehensive"),[n,c]=(0,g.useState)("pdf"),[o,u]=(0,g.useState)(null),[h,j]=(0,g.useState)({}),[f,b]=(0,g.useState)(""),[w,k]=(0,g.useState)(""),{generateComprehensiveReport:R,isGenerating:F,error:P}=W(),{templates:I,isLoading:L}=U(),O=e=>{l(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},V=async()=>{if(0===s.length)return;let e={entityTypes:s,template:r,format:n,filters:{...h,...o&&{dateRange:{from:o.from.toISOString(),to:o.to.toISOString()}}},options:{name:f||"Report ".concat(new Date().toLocaleDateString()),description:w,includeCharts:!0,includeSummary:!0}};try{await R(e)}catch(e){console.error("Failed to generate report:",e)}},B=Array.isArray(I)?I.find(e=>e.id===r):null;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Data Report Generator"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate comprehensive reports for delegations, tasks, vehicles, and employees"})]}),(0,a.jsxs)(v.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"Report Builder"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"Select Data Sources"]})}),(0,a.jsx)(N.Wu,{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:em.map(e=>{let t=e.icon,l=s.includes(e.id);return(0,a.jsx)("div",{className:"\n                        border rounded-lg p-4 cursor-pointer transition-all\n                        ".concat(l?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300","\n                      "),onClick:()=>O(e.id),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(S.S,{checked:l,onCheckedChange:()=>O(e.id)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(t,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(v.E,{className:e.color,variant:"secondary",children:e.id})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]})},e.id)})})})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Report Template"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[L?(0,a.jsx)(ed,{}):(0,a.jsxs)(C.l6,{value:r,onValueChange:i,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select a template"})}),(0,a.jsx)(C.gC,{children:Array.isArray(I)&&I.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]}),B&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:B.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:B.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:null==(e=B.sections)?void 0:e.map(e=>(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:e},e))})]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"Filters & Options"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(A.J,{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(M.A,{className:"h-4 w-4"}),"Date Range"]}),(0,a.jsx)(eo,{value:o,onChange:u,placeholder:"Select date range for data"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(C.l6,{value:n,onValueChange:c,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:eu.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{htmlFor:"reportName",className:"mb-2 block",children:"Report Name"}),(0,a.jsx)(D.p,{id:"reportName",value:f,onChange:e=>b(e.target.value),placeholder:"Enter custom report name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{htmlFor:"reportDescription",className:"mb-2 block",children:"Description"}),(0,a.jsx)(E.T,{id:"reportDescription",value:w,onChange:e=>k(e.target.value),placeholder:"Optional description for the report",rows:3})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Summary"})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Selected Entities"}),(0,a.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:s.map(e=>{let t=em.find(t=>t.id===e);return t?(0,a.jsx)(v.E,{className:t.color,variant:"secondary",children:t.name},e):null})})]}),(0,a.jsx)(T.w,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Template"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:(null==B?void 0:B.name)||"None selected"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Format"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:null==(t=eu.find(e=>e.id===n))?void 0:t.name})]}),o&&(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Date Range"}),(0,a.jsxs)("p",{className:"mt-1 text-sm",children:[o.from.toLocaleDateString()," -"," ",o.to.toLocaleDateString()]})]})]})]}),(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.Wu,{className:"pt-6",children:[P&&(0,a.jsx)($.Fc,{className:"mb-4",variant:"destructive",children:(0,a.jsx)($.TN,{children:P})}),(0,a.jsx)(y.$,{onClick:V,disabled:0===s.length||F,className:"w-full",size:"lg",children:F?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed,{className:"mr-2 h-4 w-4"}),"Generating Report..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Generate Report"]})}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 text-center",children:"Report will be generated and available for download"})]})})]})]})]})}},{id:"individual",label:"Individual Reports",icon:b.A,description:"Generate detailed reports for specific entities",component:e=>{var t,s;let{defaultEntityType:l="delegations",defaultEntityId:r="",onReportGenerated:i}=e,[n,c]=(0,g.useState)(l),[o,d]=(0,g.useState)(r),[m,u]=(0,g.useState)("default"),[x,h]=(0,g.useState)("pdf"),{generateIndividualReport:j,isGenerating:f,error:w}=W(),{templates:k,isLoading:S}=U(),E=async()=>{if(o.trim())try{let e=await j({entityType:n,entityId:o.trim(),template:m,format:x});null==i||i(e)}catch(e){console.error("Failed to generate individual report:",e)}},T=eh.find(e=>e.id===n),R=(null==k?void 0:k.filter(e=>e.entityTypes.includes(n)))||[];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Individual Report Generator"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate detailed reports for specific entities"})]}),(0,a.jsxs)(v.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Individual Report"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ex.A,{className:"h-5 w-5"}),"Entity Selection"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"mb-2 block",children:"Entity Type"}),(0,a.jsxs)(C.l6,{value:n,onValueChange:c,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:eh.map(e=>{let t=e.icon;return(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(t,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})]})},e.id)})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(A.J,{htmlFor:"entityId",className:"mb-2 block",children:[null==T?void 0:T.name," ID"]}),(0,a.jsx)(D.p,{id:"entityId",value:o,onChange:e=>d(e.target.value),placeholder:null==T?void 0:T.placeholder,className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:null==T?void 0:T.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"mb-2 block",children:"Report Template"}),S?(0,a.jsx)(ed,{}):(0,a.jsxs)(C.l6,{value:m,onValueChange:u,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select a template"})}),(0,a.jsxs)(C.gC,{children:[(0,a.jsx)(C.eb,{value:"default",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Default Template"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Standard individual report format"})]})}),R.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(C.l6,{value:x,onValueChange:h,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:ep.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Preview"})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Entity Type"}),(0,a.jsx)("div",{className:"flex items-center gap-2 mt-1",children:T&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.icon,{className:"h-4 w-4 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm",children:T.name})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Entity ID"}),(0,a.jsx)("p",{className:"mt-1 text-sm font-mono bg-white px-2 py-1 rounded border",children:o||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Template"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:(null==(t=R.find(e=>e.id===m))?void 0:t.name)||"Default Template"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"text-sm font-medium text-gray-600",children:"Format"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:null==(s=ep.find(e=>e.id===x))?void 0:s.name})]})]}),w&&(0,a.jsx)($.Fc,{variant:"destructive",children:(0,a.jsx)($.TN,{children:w})}),(0,a.jsx)(y.$,{onClick:E,disabled:!o.trim()||f,className:"w-full",size:"lg",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed,{className:"mr-2 h-4 w-4"}),"Generating Report..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Generate Individual Report"]})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 text-center",children:["Report will include detailed information about the selected"," ",null==T?void 0:T.name.toLowerCase()]})]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Quick Actions"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:eh.map(e=>{let t=e.icon;return(0,a.jsxs)(y.$,{variant:"outline",className:"h-auto p-4 flex flex-col items-center gap-2",onClick:()=>{c(e.id),d("")},children:[(0,a.jsx)(t,{className:"h-6 w-6"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.name," Report"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["Generate ",e.name.toLowerCase()," report"]})]})]},e.id)})})})]})]})}},{id:"aggregate",label:"Aggregate Analytics",icon:l.A,description:"Generate analytics reports with aggregated data",component:e=>{let{defaultEntityType:t="delegations",onReportGenerated:s}=e,[i,n]=(0,g.useState)(t),[c,o]=(0,g.useState)("default"),[d,m]=(0,g.useState)("pdf"),[u,h]=(0,g.useState)({from:(0,J.e)(new Date,29),to:new Date}),[j,f]=(0,g.useState)({}),[b,w]=(0,g.useState)(!0),[k,E]=(0,g.useState)(!0),{error:D,generateAggregateReport:T,isGenerating:R}=W(),{isLoading:z,templates:F}=U(),{exportReportToExcel:P,exportReportToPDF:I,exportToCSV:L}=(0,ej.useExport)(),O=(e,t)=>{f(s=>({...s,[e]:t}))},V=async()=>{try{let t,a={...j,...u&&{dateRange:{from:u.from.toISOString(),to:u.to.toISOString()}}};try{t=await T({entityType:i,filters:a,format:"json",options:{includeCharts:b,includeTrends:k},template:c})}catch(e){console.warn("API call failed, providing fallback data:",e),t={data:{priorityDistribution:[],records:[],statusDistribution:[],summary:{generatedAt:new Date().toISOString(),message:"Unable to fetch ".concat(i," data from server. This is a sample report with fallback data."),note:"Please check your connection and try again."},totalCount:0},metadata:{entityType:i,generatedAt:new Date().toISOString(),generatedBy:"System (Fallback)",id:"fallback_".concat(Date.now()),note:"Generated with fallback data due to API unavailability",status:"fallback"}}}if(!t)throw Error("No report data received from server");t.data||(t.data={priorityDistribution:[],records:[],statusDistribution:[],summary:{generatedAt:new Date().toISOString(),message:"No data available for the selected criteria"},totalCount:0});let l="".concat(i.charAt(0).toUpperCase()+i.slice(1)," Analytics Report"),r="".concat(i,"-analytics-").concat(new Date().toISOString().split("T")[0]);try{var e;switch(eg.JP.show({description:"Creating ".concat(d.toUpperCase()," report for ").concat(i,"..."),duration:2e3,title:"Generating Report"}),d){case"csv":{let e=Array.isArray(t.data)?t.data:[t.data];L(e,{filename:r}),console.log("CSV export completed successfully");break}case"excel":P(t,i,r),console.log("Excel export completed successfully");break;case"pdf":console.log("Starting PDF export with data:",t),await I(t,i,l,r),console.log("PDF export completed successfully");break;default:throw Error("Unsupported export format: ".concat(d))}eg.JP.success("Report Generated Successfully","".concat(d.toUpperCase()," report has been generated and downloaded. ID: ").concat((null==(e=t.metadata)?void 0:e.id)||"N/A"))}catch(e){throw console.error("Failed to export ".concat(d," report:"),e),eg.JP.error("Export Failed","Report was generated but ".concat(d.toUpperCase()," export failed: ").concat(e.message||"Unknown export error")),Error("Report generated successfully but export failed: ".concat(e.message||"Unknown export error"))}null==s||s(t)}catch(e){throw console.error("Failed to generate aggregate report:",e),e}},B=ev.find(e=>e.id===i),Y=(null==F?void 0:F.filter(e=>e.entityTypes.includes(i)))||[],Z=ef[i]||[];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Aggregate Report Generator"}),(0,a.jsx)("p",{className:"mt-1 text-gray-600",children:"Generate analytics reports with aggregated data and insights"})]}),(0,a.jsxs)(v.E,{className:"flex items-center gap-2",variant:"outline",children:[(0,a.jsx)(l.A,{className:"size-4"}),"Aggregate Report"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"space-y-6 lg:col-span-2",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"size-5"}),"Entity Type & Analytics"]})}),(0,a.jsx)(N.Wu,{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:ev.map(e=>{let t=e.icon,s=i===e.id;return(0,a.jsx)("div",{className:"\n                        cursor-pointer rounded-lg border p-4 transition-all\n                        ".concat(s?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300","\n                      "),onClick:()=>n(e.id),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(t,{className:"mt-1 size-6 text-gray-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(v.E,{className:e.color,variant:"secondary",children:"Analytics"})]}),(0,a.jsx)("p",{className:"mb-3 text-sm text-gray-600",children:e.description}),(0,a.jsx)("div",{className:"space-y-1",children:e.metrics.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,a.jsx)("div",{className:"size-1 rounded-full bg-gray-400"}),e]},e))})]})]})},e.id)})})})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"size-5"}),"Filters & Date Range"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(A.J,{className:"mb-2 flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"size-4"}),"Date Range"]}),(0,a.jsx)(eo,{onChange:h,placeholder:"Select date range for analytics",value:u})]}),Z.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Entity Filters"}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:Z.map(e=>{var t;return(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"mb-1 block text-sm",children:e.name}),"select"===e.type?(0,a.jsxs)(C.l6,{onValueChange:t=>{O(e.id,"all"===t?"":t)},value:j[e.id]||"all",children:[(0,a.jsx)(C.bq,{className:"h-8",children:(0,a.jsx)(C.yv,{placeholder:"Select ".concat(e.name.toLowerCase())})}),(0,a.jsxs)(C.gC,{children:[(0,a.jsxs)(C.eb,{value:"all",children:["All ",e.name,"s"]}),null==(t=e.options)?void 0:t.map(e=>(0,a.jsx)(C.eb,{value:e,children:e},e))]})]}):(0,a.jsx)("input",{className:"h-8 w-full rounded-md border border-gray-300 px-3 text-sm",onChange:t=>O(e.id,t.target.value),placeholder:"Filter by ".concat(e.name.toLowerCase()),type:"text",value:j[e.id]||""})]},e.id)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Report Options"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{checked:b,id:"includeCharts",onCheckedChange:e=>w(!0===e)}),(0,a.jsx)(A.J,{className:"text-sm",htmlFor:"includeCharts",children:"Include Charts & Visualizations"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{checked:k,id:"includeTrends",onCheckedChange:e=>E(!0===e)}),(0,a.jsx)(A.J,{className:"text-sm",htmlFor:"includeTrends",children:"Include Trend Analysis"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Configuration"})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"mb-2 block",children:"Template"}),z?(0,a.jsx)(ed,{}):(0,a.jsxs)(C.l6,{onValueChange:o,value:c,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select template"})}),(0,a.jsxs)(C.gC,{children:[(0,a.jsx)(C.eb,{value:"default",children:"Default Analytics"}),Y.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(A.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(C.l6,{onValueChange:m,value:d,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:ey.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2 rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:B&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(B.icon,{className:"size-4"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[B.name," Analytics"]})]})}),u&&(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:[u.from.toLocaleDateString()," -"," ",u.to.toLocaleDateString()]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(j).filter(e=>{let[t,s]=e;return s}).map(e=>{let[t,s]=e;return(0,a.jsxs)(v.E,{className:"text-xs",variant:"outline",children:[t,": ",s]},t)})})]})]})]}),(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.Wu,{className:"pt-6",children:[D&&(0,a.jsx)($.Fc,{className:"mb-4",variant:"destructive",children:(0,a.jsx)($.TN,{children:D})}),(0,a.jsx)(y.$,{className:"w-full",disabled:R,onClick:V,size:"lg",children:R?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed,{className:"mr-2 size-4"}),"Generating Analytics..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 size-4"}),"Generate Aggregate Report"]})}),(0,a.jsx)("p",{className:"mt-2 text-center text-xs text-gray-500",children:"Report will include aggregated analytics and insights"})]})})]})]})]})}},{id:"history",label:"Report History",icon:w.A,description:"View and manage generated reports",component:e=>{let{onReportSelect:t}=e,[s,l]=(0,g.useState)(""),[r,i]=(0,g.useState)(""),[n,c]=(0,g.useState)(""),{error:o,isLoading:m,pagination:u,refetch:j,reports:f}=V({...s&&{type:s},...r&&{entityType:r}}),{downloadError:b,downloadReport:k,isDownloading:S}=B(),A=async e=>{try{await k(e)}catch(e){console.error("Failed to download report:",e)}},E=f.filter(e=>{var t,s;if(!n)return!0;let a=n.toLowerCase();return e.id.toLowerCase().includes(a)||e.type.toLowerCase().includes(a)||(null==(t=e.entityType)?void 0:t.toLowerCase().includes(a))||(null==(s=e.entityTypes)?void 0:s.some(e=>e.toLowerCase().includes(a)))}),T=e=>"individual"===e.type||"aggregate"===e.type?e.entityType:e.entityTypes?e.entityTypes.join(", "):"N/A";return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Report History"}),(0,a.jsx)("p",{className:"mt-1 text-gray-600",children:"View and manage your generated reports"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(y.$,{disabled:m,onClick:()=>j(),size:"sm",variant:"outline",children:[(0,a.jsx)(h.A,{className:"mr-2 size-4 ".concat(m?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(v.E,{className:"flex items-center gap-2",variant:"outline",children:[(0,a.jsx)(w.A,{className:"size-4"}),f.length," Reports"]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"size-5"}),"Filters"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-4",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(ex.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400"}),(0,a.jsx)(D.p,{className:"pl-10",onChange:e=>c(e.target.value),placeholder:"Search reports...",value:n})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(C.l6,{onValueChange:l,value:s,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Report Type"})}),(0,a.jsx)(C.gC,{children:ew.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(C.l6,{onValueChange:i,value:r,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Entity Type"})}),(0,a.jsx)(C.gC,{children:ek.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)("div",{children:(0,a.jsx)(y.$,{className:"w-full",onClick:()=>{l(""),i(""),c("")},variant:"outline",children:"Clear Filters"})})]})})]}),(o||b)&&(0,a.jsx)($.Fc,{variant:"destructive",children:(0,a.jsx)($.TN,{children:String(o||b)})}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Generated Reports"})}),(0,a.jsx)(N.Wu,{children:m?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(ed,{className:"size-8"})}):0===E.length?(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(d.A,{className:"mx-auto mb-4 size-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Reports Found"}),(0,a.jsx)("p",{className:"text-gray-600",children:0===f.length?"No reports have been generated yet.":"No reports match your current filters."})]}):(0,a.jsx)("div",{className:"space-y-4",children:E.map(e=>{let s=eS[e.status],l=eC[e.format];return(0,a.jsx)("div",{className:"rounded-lg border p-4 transition-colors hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.id}),(0,a.jsx)(v.E,{className:null==s?void 0:s.color,variant:"secondary",children:(null==s?void 0:s.label)||e.status}),(0,a.jsx)(v.E,{className:null==l?void 0:l.color,variant:"secondary",children:(null==l?void 0:l.label)||e.format}),(0,a.jsx)(v.E,{variant:"outline",children:e.type})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm text-gray-600 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Entity Types:"}),(0,a.jsx)("p",{className:"mt-1",children:T(e)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Generated:"}),(0,a.jsxs)("p",{className:"mt-1 flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"size-3"}),(0,eN.m)(new Date(e.generatedAt),{addSuffix:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"File Size:"}),(0,a.jsx)("p",{className:"mt-1",children:e.fileSize||"N/A"})]})]}),"individual"===e.type&&e.entityId&&(0,a.jsxs)("div",{className:"mt-2 text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"font-medium",children:"Entity ID:"})," ",e.entityId]})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center gap-2",children:[t&&(0,a.jsxs)(y.$,{onClick:()=>t(e),size:"sm",variant:"outline",children:[(0,a.jsx)(eb.A,{className:"mr-1 size-4"}),"View"]}),"completed"===e.status&&(0,a.jsxs)(y.$,{disabled:S,onClick:()=>A(e.id),size:"sm",variant:"outline",children:[S?(0,a.jsx)(ed,{className:"mr-1 size-4"}):(0,a.jsx)(p.A,{className:"mr-1 size-4"}),"Download"]})]})]})},e.id)})})})]}),u&&u.totalPages>1&&(0,a.jsx)(N.Zp,{children:(0,a.jsx)(N.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(u.page-1)*u.limit+1," to"," ",Math.min(u.page*u.limit,u.total)," ","of ",u.total," reports"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.$,{disabled:u.page<=1,size:"sm",variant:"outline",children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",u.page," of ",u.totalPages]}),(0,a.jsx)(y.$,{disabled:u.page>=u.totalPages,size:"sm",variant:"outline",children:"Next"})]})]})})})]})}}],eE=()=>{let[e,t]=(0,g.useState)("comprehensive"),[s,r]=(0,g.useState)([]),i=e=>{var t;console.log("Report generated:",e);let s=(null==e||null==(t=e.data)?void 0:t.metadata)||(null==e?void 0:e.metadata);if(!s){console.error("No metadata found in report result:",e),alert("Report generated, but metadata is missing");return}let a={...e,metadata:s};r(e=>[a,...e.slice(0,4)]),alert("Report generated successfully! ID: ".concat(s.id))},n=e=>{console.log("Report selected:",e)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Data Report Generation"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Generate comprehensive reports for delegations, tasks, vehicles, and employees"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(v.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Report Builder"]}),(0,a.jsxs)(y.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Settings"]})]})]}),s.length>0&&(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5"}),"Recent Activity"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:s.map((e,t)=>{var s;let l=null==e?void 0:e.metadata;return l?(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:l.type||"Unknown"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:l.generatedAt?new Date(l.generatedAt).toLocaleTimeString():"Unknown time"})]}),(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:l.id||"No ID"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:(null==(s=l.entityTypes)?void 0:s.join(", "))||l.entityType||"Unknown entity"})]},t):null})})})]}),(0,a.jsxs)($.Fc,{children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsxs)($.TN,{children:[(0,a.jsx)("strong",{children:"Report Generation System:"})," Generate individual entity reports, aggregate analytics, or comprehensive reports across multiple data sources. All reports support PDF, Excel, and CSV export formats."]})]}),(0,a.jsxs)(f.tU,{value:e,onValueChange:t,className:"space-y-6",children:[(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.Wu,{className:"pt-6",children:[(0,a.jsx)(f.j7,{className:"grid w-full grid-cols-4",children:eA.map(e=>{let t=e.icon;return(0,a.jsxs)(f.Xi,{value:e.id,className:"flex items-center gap-2 data-[state=active]:bg-blue-50",children:[(0,a.jsx)(t,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id)})}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:eA.map(t=>{if(t.id!==e)return null;let s=t.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(s,{className:"h-5 w-5 text-gray-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:t.label}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:t.description})]})]},t.id)})})]})}),eA.map(e=>{let t=e.component;return(0,a.jsx)(f.av,{value:e.id,className:"space-y-6",children:(0,a.jsx)(t,{onReportGenerated:i,onReportSelect:n})},e.id)})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Generation Guide"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Comprehensive"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Generate reports that include data from multiple entity types with cross-entity analytics."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Individual"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Create detailed reports for specific delegations, tasks, vehicles, or employees."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Aggregate"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Generate analytics reports with aggregated metrics and trend analysis."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 text-purple-600"}),(0,a.jsx)("span",{className:"font-medium",children:"History"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"View, download, and manage all your previously generated reports."})]})]})})]})]})};var eD=s(75143),eT=s(58543),eR=s(59119),ez=s(50402),eM=s(40295),eF=s(77223),eP=s(18046);let eI=e=>{let{widget:t,onConfigure:s,onDelete:l}=e,{attributes:r,listeners:i,setNodeRef:n,transform:c,transition:o,isDragging:d}=(0,ez.gl)({id:t.id,data:{type:"widget",widget:t}}),u={transform:c?"translate3d(".concat(c.x,"px, ").concat(c.y,"px, 0)"):void 0,transition:o};return(0,a.jsx)("div",{ref:n,style:u,className:(0,en.cn)("group relative",d&&"opacity-50"),children:(0,a.jsx)(N.Zp,{className:"border-2 border-dashed border-gray-200 hover:border-blue-300 transition-colors",children:(0,a.jsxs)(N.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{...r,...i,className:"cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded",children:(0,a.jsx)(eM.A,{className:"h-4 w-4 text-gray-400"})}),(0,a.jsx)("h4",{className:"text-sm font-medium",children:t.title})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:l,className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:(0,a.jsx)(eF.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 min-h-[120px] flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)(eT.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsxs)("div",{className:"text-sm",children:[t.type," Widget"]}),(0,a.jsx)("div",{className:"text-xs mt-1",children:t.config&&Object.keys(t.config).length>0?"Configured":"Click settings to configure"})]})}),(0,a.jsxs)("div",{className:"mt-3 flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",t.type]}),(0,a.jsxs)("span",{children:["Span: ",t.span]})]})]})})})},eL=e=>{let{widgets:t,columns:s,onWidgetConfigure:l,onWidgetDelete:r,className:i=""}=e,{isOver:n,setNodeRef:c}=(0,eD.zM)({id:"drop-zone"});return(0,a.jsx)(N.Zp,{className:(0,en.cn)("min-h-[400px]",i),children:(0,a.jsxs)(N.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Report Canvas"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[t.length," widget",1!==t.length?"s":""]})]}),(0,a.jsx)("div",{ref:c,className:(0,en.cn)("min-h-[300px] border-2 border-dashed rounded-lg p-4 transition-colors",n?"border-blue-400 bg-blue-50":"border-gray-200",0===t.length&&"flex items-center justify-center"),children:0===t.length?(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)(eT.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h4",{className:"text-lg font-medium mb-2",children:"Start Building Your Report"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:"Drag widgets from the palette on the left to create your custom report"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-xs",children:[(0,a.jsx)(eP.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Drop widgets here"})]})]}):(0,a.jsx)(ez.gB,{items:t.map(e=>e.id),strategy:ez._G,children:(0,a.jsx)("div",{className:(0,en.cn)("grid gap-4",{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-4"}[s]||"grid-cols-2"),children:t.sort((e,t)=>(e.position||0)-(t.position||0)).map(e=>(0,a.jsx)(eI,{widget:e,onConfigure:()=>l(e),onDelete:()=>r(e.id)},e.id))})})}),n&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,a.jsx)(eP.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Drop widget here to add to report"})]})}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Layout: ",s," column",1!==s?"s":""]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Drag widgets to reorder • Click settings to configure"})]})})]})})};var eO=s(62177),eW=s(90221),eV=s(71153),eB=s(54165),eU=s(17759);let eY=eV.z.object({title:eV.z.string().min(1,"Title is required"),span:eV.z.string(),showTitle:eV.z.boolean().default(!0),showBorder:eV.z.boolean().default(!0),refreshInterval:eV.z.number().min(0).max(1440).optional(),height:eV.z.number().min(200).max(800).optional(),chartType:eV.z.string().optional(),xAxisField:eV.z.string().optional(),yAxisField:eV.z.string().optional(),colorScheme:eV.z.string().optional(),showLegend:eV.z.boolean().optional(),showGrid:eV.z.boolean().optional(),pageSize:eV.z.number().min(5).max(100).optional(),sortable:eV.z.boolean().optional(),filterable:eV.z.boolean().optional(),exportable:eV.z.boolean().optional(),metricType:eV.z.string().optional(),aggregationType:eV.z.string().optional(),comparisonPeriod:eV.z.string().optional()}),eZ=[{value:"col-span-1",label:"1 Column"},{value:"col-span-2",label:"2 Columns"},{value:"col-span-3",label:"3 Columns"},{value:"col-span-4",label:"4 Columns"},{value:"col-span-full",label:"Full Width"}],e$=[{value:"bar",label:"Bar Chart"},{value:"line",label:"Line Chart"},{value:"pie",label:"Pie Chart"},{value:"area",label:"Area Chart"},{value:"scatter",label:"Scatter Plot"}],e_=[{value:"default",label:"Default"},{value:"blue",label:"Blue"},{value:"green",label:"Green"},{value:"red",label:"Red"},{value:"purple",label:"Purple"},{value:"orange",label:"Orange"}],eG=e=>{var t,s,l,r,i,n,c,o,d,u,x,h,p,g,j,v,f,N,b,w,k,A,E,T;let{widget:R,onSave:z,onCancel:M}=e,F=(0,eO.mN)({resolver:(0,eW.u)(eY),defaultValues:{title:R.title,span:R.span,showTitle:null==(N=null==(t=R.config)?void 0:t.showTitle)||N,showBorder:null==(b=null==(s=R.config)?void 0:s.showBorder)||b,refreshInterval:(null==(l=R.config)?void 0:l.refreshInterval)||60,height:(null==(r=R.config)?void 0:r.height)||300,chartType:(null==(i=R.config)?void 0:i.chartType)||"bar",xAxisField:(null==(n=R.config)?void 0:n.xAxisField)||"",yAxisField:(null==(c=R.config)?void 0:c.yAxisField)||"",colorScheme:(null==(o=R.config)?void 0:o.colorScheme)||"default",showLegend:null==(w=null==(d=R.config)?void 0:d.showLegend)||w,showGrid:null==(k=null==(u=R.config)?void 0:u.showGrid)||k,pageSize:(null==(x=R.config)?void 0:x.pageSize)||10,sortable:null==(A=null==(h=R.config)?void 0:h.sortable)||A,filterable:null==(E=null==(p=R.config)?void 0:p.filterable)||E,exportable:null==(T=null==(g=R.config)?void 0:g.exportable)||T,metricType:(null==(j=R.config)?void 0:j.metricType)||"count",aggregationType:(null==(v=R.config)?void 0:v.aggregationType)||"sum",comparisonPeriod:(null==(f=R.config)?void 0:f.comparisonPeriod)||"previous-month"}}),P=["bar-chart","pie-chart","line-chart"].includes(R.type),I="data-table"===R.type;return["analytics","metrics"].includes(R.type),(0,a.jsx)(eB.lG,{open:!0,onOpenChange:M,children:(0,a.jsxs)(eB.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(eB.c7,{children:[(0,a.jsxs)(eB.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Configure Widget: ",R.title]}),(0,a.jsx)(eB.rr,{children:"Customize the appearance and behavior of this widget."})]}),(0,a.jsx)(eU.lV,{...F,children:(0,a.jsxs)("form",{onSubmit:F.handleSubmit(e=>{z({...R,title:e.title,span:e.span,config:{...R.config,...e}})}),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Basic Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eU.zB,{control:F.control,name:"title",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Widget Title"}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(D.p,{placeholder:"Enter widget title",...t})}),(0,a.jsx)(eU.C5,{})]})}}),(0,a.jsx)(eU.zB,{control:F.control,name:"span",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Width"}),(0,a.jsxs)(C.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select width"})})}),(0,a.jsx)(C.gC,{children:eZ.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eU.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eU.zB,{control:F.control,name:"height",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Height (px)"}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"200",max:"800",...t,onChange:e=>t.onChange(parseInt(e.target.value)||300)})}),(0,a.jsx)(eU.C5,{})]})}}),(0,a.jsx)(eU.zB,{control:F.control,name:"refreshInterval",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Refresh Interval (minutes)"}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"0",max:"1440",...t,onChange:e=>t.onChange(parseInt(e.target.value)||60)})}),(0,a.jsx)(eU.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eU.zB,{control:F.control,name:"showTitle",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Show Title"}),(0,a.jsx)(eU.Rr,{children:"Display widget title"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value,onCheckedChange:t.onChange})})]})}}),(0,a.jsx)(eU.zB,{control:F.control,name:"showBorder",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Show Border"}),(0,a.jsx)(eU.Rr,{children:"Display widget border"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value,onCheckedChange:t.onChange})})]})}})]})]}),P&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Chart Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eU.zB,{control:F.control,name:"chartType",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Chart Type"}),(0,a.jsxs)(C.l6,{onValueChange:t.onChange,value:t.value||"",children:[(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select chart type"})})}),(0,a.jsx)(C.gC,{children:e$.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eU.C5,{})]})}}),(0,a.jsx)(eU.zB,{control:F.control,name:"colorScheme",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Color Scheme"}),(0,a.jsxs)(C.l6,{onValueChange:t.onChange,value:t.value||"",children:[(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select color scheme"})})}),(0,a.jsx)(C.gC,{children:e_.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eU.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eU.zB,{control:F.control,name:"showLegend",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Show Legend"}),(0,a.jsx)(eU.Rr,{children:"Display chart legend"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value||!1,onCheckedChange:t.onChange})})]})}}),(0,a.jsx)(eU.zB,{control:F.control,name:"showGrid",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Show Grid"}),(0,a.jsx)(eU.Rr,{children:"Display chart grid lines"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value||!1,onCheckedChange:t.onChange})})]})}})]})]}),I&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Table Settings"}),(0,a.jsx)(eU.zB,{control:F.control,name:"pageSize",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Page Size"}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"5",max:"100",...t,onChange:e=>t.onChange(parseInt(e.target.value)||10)})}),(0,a.jsx)(eU.Rr,{children:"Number of rows per page"}),(0,a.jsx)(eU.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)(eU.zB,{control:F.control,name:"sortable",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Sortable"}),(0,a.jsx)(eU.Rr,{children:"Enable column sorting"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value||!1,onCheckedChange:t.onChange})})]})}}),(0,a.jsx)(eU.zB,{control:F.control,name:"filterable",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Filterable"}),(0,a.jsx)(eU.Rr,{children:"Enable column filters"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value||!1,onCheckedChange:t.onChange})})]})}}),(0,a.jsx)(eU.zB,{control:F.control,name:"exportable",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Exportable"}),(0,a.jsx)(eU.Rr,{children:"Enable data export"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value||!1,onCheckedChange:t.onChange})})]})}})]})]}),(0,a.jsxs)(eB.Es,{children:[(0,a.jsx)(y.$,{type:"button",variant:"outline",onClick:M,children:"Cancel"}),(0,a.jsx)(y.$,{type:"submit",children:"Save Configuration"})]})]})})]})})};var eq=s(9041),eJ=s(58527),eH=s(88390);let eQ=e=>{let{widget:t,isDisabled:s=!1}=e,{attributes:l,listeners:r,setNodeRef:i,transform:n,isDragging:c}=(0,eD.PM)({id:t.id,data:{type:"widget-type",widgetType:t.id},disabled:s}),o=n?{transform:"translate3d(".concat(n.x,"px, ").concat(n.y,"px, 0)")}:void 0;return(0,a.jsxs)("div",{ref:i,style:o,...r,...l,className:(0,en.cn)("p-3 border rounded-lg cursor-grab active:cursor-grabbing transition-all",c&&"opacity-50",s?"opacity-50 cursor-not-allowed":"hover:shadow-md hover:border-blue-300",!s&&"bg-white"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[t.icon,(0,a.jsx)("span",{className:"text-sm font-medium",children:t.name})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:t.description}),(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:t.category})]})},eK=[{id:"analytics",name:"Analytics Widget",description:"Key metrics and performance indicators",icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),category:"Analytics",supportedDataSources:["delegations","tasks","vehicles","employees","cross-entity"]},{id:"metrics",name:"Metrics Widget",description:"Display key performance metrics",icon:(0,a.jsx)(l.A,{className:"h-4 w-4"}),category:"Analytics",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"bar-chart",name:"Bar Chart",description:"Compare values across categories",icon:(0,a.jsx)(l.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"pie-chart",name:"Pie Chart",description:"Show proportional data distribution",icon:(0,a.jsx)(eq.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"line-chart",name:"Line Chart",description:"Display trends over time",icon:(0,a.jsx)(eJ.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"data-table",name:"Data Table",description:"Tabular data with sorting and filtering",icon:(0,a.jsx)(u.A,{className:"h-4 w-4"}),category:"Data",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"employee-performance",name:"Employee Performance",description:"Employee performance metrics and charts",icon:(0,a.jsx)(c.A,{className:"h-4 w-4"}),category:"Employee",supportedDataSources:["employees","cross-entity"]},{id:"vehicle-utilization",name:"Vehicle Utilization",description:"Vehicle usage and utilization metrics",icon:(0,a.jsx)(n.A,{className:"h-4 w-4"}),category:"Vehicle",supportedDataSources:["vehicles","cross-entity"]},{id:"task-status",name:"Task Status",description:"Task completion and status tracking",icon:(0,a.jsx)(i.A,{className:"h-4 w-4"}),category:"Task",supportedDataSources:["tasks","cross-entity"]},{id:"delegation-overview",name:"Delegation Overview",description:"Delegation status and distribution",icon:(0,a.jsx)(d.A,{className:"h-4 w-4"}),category:"Delegation",supportedDataSources:["delegations","cross-entity"]},{id:"correlation",name:"Correlation Analysis",description:"Cross-entity relationships and correlations",icon:(0,a.jsx)(o.A,{className:"h-4 w-4"}),category:"Analysis",supportedDataSources:["cross-entity"]},{id:"timeline",name:"Timeline Widget",description:"Events and activities over time",icon:(0,a.jsx)(M.A,{className:"h-4 w-4"}),category:"Timeline",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"cost-analysis",name:"Cost Analysis",description:"Financial metrics and cost tracking",icon:(0,a.jsx)(eH.A,{className:"h-4 w-4"}),category:"Financial",supportedDataSources:["vehicles","employees","cross-entity"]}],eX=e=>{let{dataSource:t,className:s=""}=e,l=eK.filter(e=>e.supportedDataSources.includes(t)||e.supportedDataSources.includes("cross-entity")),r=l.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{});return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsxs)(N.aR,{className:"pb-3",children:[(0,a.jsx)(N.ZB,{className:"text-lg",children:"Widget Palette"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Drag widgets to the report canvas to build your custom report"}),(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs w-fit",children:[l.length," widgets available"]})]}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[Object.entries(r).map(e=>{let[s,l]=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2 text-gray-700",children:s}),(0,a.jsx)("div",{className:"space-y-2",children:l.map(e=>(0,a.jsx)(eQ,{widget:e,isDisabled:!e.supportedDataSources.includes(t)&&!e.supportedDataSources.includes("cross-entity")},e.id))})]},s)}),0===l.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,a.jsx)("div",{className:"text-sm",children:"No widgets available for this data source"})})]})]})},e0=e=>{let{className:t="",onPreview:s,onSave:l,reportType:r}=e,[i,n]=(0,g.useState)((null==r?void 0:r.widgetConfigs)||[]),[c,o]=(0,g.useState)(null),[d,m]=(0,g.useState)(!1),[u,x]=(0,g.useState)(null),[h,p]=(0,g.useState)({dataSource:(null==r?void 0:r.dataSource)||"delegations",description:(null==r?void 0:r.description)||"",filters:(null==r?void 0:r.filters)||[],layout:{columns:2,spacing:4},name:(null==r?void 0:r.name)||"New Report",widgets:i});return(0,a.jsxs)("div",{className:(0,en.cn)("space-y-6",t),children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eT.A,{className:"size-5"}),"Report Builder"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{className:"text-xs",variant:"secondary",children:[i.length," widgets"]}),(0,a.jsxs)(y.$,{onClick:()=>{s&&s(h)},size:"sm",variant:"outline",children:[(0,a.jsx)(eb.A,{className:"mr-2 size-4"}),"Preview"]}),(0,a.jsxs)(y.$,{onClick:()=>{l&&l(h)},size:"sm",children:[(0,a.jsx)(eR.A,{className:"mr-2 size-4"}),"Save Report"]})]})]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Report Name"}),(0,a.jsx)("input",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>p(t=>({...t,name:e.target.value})),type:"text",value:h.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Data Source"}),(0,a.jsxs)("select",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>p(t=>({...t,dataSource:e.target.value})),value:h.dataSource,children:[(0,a.jsx)("option",{value:"delegations",children:"Delegations"}),(0,a.jsx)("option",{value:"tasks",children:"Tasks"}),(0,a.jsx)("option",{value:"vehicles",children:"Vehicles"}),(0,a.jsx)("option",{value:"employees",children:"Employees"}),(0,a.jsx)("option",{value:"cross-entity",children:"Cross-Entity"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Layout Columns"}),(0,a.jsxs)("select",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>p(t=>({...t,layout:{...t.layout,columns:Number.parseInt(e.target.value)}})),value:h.layout.columns,children:[(0,a.jsx)("option",{value:1,children:"1 Column"}),(0,a.jsx)("option",{value:2,children:"2 Columns"}),(0,a.jsx)("option",{value:3,children:"3 Columns"}),(0,a.jsx)("option",{value:4,children:"4 Columns"})]})]})]})})]}),(0,a.jsxs)(eD.Mp,{onDragEnd:e=>{var t,s;let{active:a,over:l}=e;if(!l)return void x(null);if("drop-zone"===l.id&&(null==(t=a.data.current)?void 0:t.type)==="widget-type"){let e=a.data.current.widgetType,t={config:{},id:"widget-".concat(Date.now()),position:i.length,span:"col-span-1",title:"".concat(e," Widget"),type:e};n(e=>[...e,t]),p(e=>({...e,widgets:[...e.widgets,t]}))}if(l.id!==a.id&&(null==(s=a.data.current)?void 0:s.type)==="widget"){let e=i.findIndex(e=>e.id===a.id),t=i.findIndex(e=>e.id===l.id);if(-1!==e&&-1!==t){let s=[...i],[a]=s.splice(e,1);a&&s.splice(t,0,a);let l=s.map((e,t)=>({...e,position:t}));n(l),p(e=>({...e,widgets:l}))}}x(null)},onDragStart:e=>{x(e.active.id)},children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(eX,{dataSource:h.dataSource})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(eL,{columns:h.layout.columns,onWidgetConfigure:e=>{o(e),m(!0)},onWidgetDelete:e=>{let t=i.filter(t=>t.id!==e);n(t),p(e=>({...e,widgets:t}))},widgets:i})})]}),(0,a.jsx)(eD.Hd,{children:u?(0,a.jsx)("div",{className:"rounded-lg border bg-white p-4 shadow-lg",children:(0,a.jsx)("div",{className:"text-sm font-medium",children:u.replace("-"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())})}):null})]}),d&&c&&(0,a.jsx)(eG,{onCancel:()=>{m(!1),o(null)},onSave:e=>{let t=i.map(t=>t.id===e.id?e:t);n(t),p(e=>({...e,widgets:t})),m(!1),o(null)},widget:c})]})};var e1=s(26715),e2=s(5041),e4=s(43772),e5=s(72248);let e3=()=>{let e=(0,e1.jE)(),t=(0,e4.Sk)(["report-types"],async()=>{var e;return(null==(e=(await e5.uE.get("/reporting/report-types")).data)?void 0:e.data)||[]},{cacheDuration:3e5,enableRetry:!0}),s=(0,e2.n)({mutationFn:async e=>{var t;let s=await e5.uE.post("/reporting/report-types",e);return(null==(t=s.data)?void 0:t.data)||s.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),a=(0,e2.n)({mutationFn:async e=>{var t;let{id:s,...a}=e,l=await e5.uE.put("/reporting/report-types/".concat(s),a);return(null==(t=l.data)?void 0:t.data)||l.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),l=(0,e2.n)({mutationFn:async e=>{await e5.uE.delete("/reporting/report-types/".concat(e))},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),r=(0,e2.n)({mutationFn:async e=>{var t;let s=await e5.uE.post("/reporting/report-types/".concat(e,"/duplicate"));return(null==(t=s.data)?void 0:t.data)||s.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),i=(0,e2.n)({mutationFn:async e=>{var t;let{id:s,isActive:a}=e,l=await e5.uE.patch("/reporting/report-types/".concat(s,"/toggle-active"),{isActive:a});return(null==(t=l.data)?void 0:t.data)||l.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}});return{createReportType:s,data:t.data,deleteReportType:l,duplicateReportType:r,error:t.error,isLoading:t.isLoading,refetch:t.refetch,toggleReportTypeActive:i,updateReportType:a}};var e6=s(77023);function e8(e){let{error:t,title:s="Error",onRetry:l,className:r,showRetry:i=!0,retryText:n="Try Again"}=e;if(!t)return null;let c=t instanceof Error?t.message:String(t);return(0,a.jsxs)($.Fc,{variant:"destructive",className:r,children:[(0,a.jsx)(G.A,{className:"h-4 w-4"}),(0,a.jsx)($.XL,{children:s}),(0,a.jsxs)($.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-3",children:c}),i&&l&&(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:l,className:"h-8",children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-2"}),n]})]})]})}let e7=eV.z.object({name:eV.z.string().min(1,"Name is required").max(100,"Name must be less than 100 characters"),description:eV.z.string().optional(),category:eV.z.string().min(1,"Category is required"),dataSource:eV.z.string().min(1,"Data source is required"),widgets:eV.z.array(eV.z.string()).min(1,"At least one widget is required"),filters:eV.z.array(eV.z.string()).optional(),isActive:eV.z.boolean().default(!0),isPublic:eV.z.boolean().default(!1),refreshInterval:eV.z.number().min(1).max(1440).optional(),tags:eV.z.array(eV.z.string()).optional()}),e9=[{value:"analytics",label:"Analytics Widget"},{value:"chart",label:"Chart Widget"},{value:"table",label:"Data Table"},{value:"metrics",label:"Metrics Widget"},{value:"correlation",label:"Correlation Widget"}],te=[{value:"delegations",label:"Delegations"},{value:"tasks",label:"Tasks"},{value:"vehicles",label:"Vehicles"},{value:"employees",label:"Employees"},{value:"cross-entity",label:"Cross-Entity"}],tt=[{value:"operational",label:"Operational"},{value:"performance",label:"Performance"},{value:"financial",label:"Financial"},{value:"compliance",label:"Compliance"},{value:"analytics",label:"Analytics"}],ts=e=>{var t,s;let{reportType:l,onSubmit:r,onCancel:i,isLoading:n=!1}=e,c=(0,eO.mN)({resolver:(0,eW.u)(e7),defaultValues:{name:(null==l?void 0:l.name)||"",description:(null==l?void 0:l.description)||"",category:(null==l?void 0:l.category)||"",dataSource:(null==l?void 0:l.dataSource)||"",widgets:(null==l?void 0:l.widgets)||[],filters:(null==l?void 0:l.filters)||[],isActive:null==(t=null==l?void 0:l.isActive)||t,isPublic:null!=(s=null==l?void 0:l.isPublic)&&s,refreshInterval:(null==l?void 0:l.refreshInterval)||60,tags:(null==l?void 0:l.tags)||[]}}),o=async e=>{try{await r(e)}catch(e){console.error("Form submission error:",e)}};return(0,a.jsx)(eB.lG,{open:!0,onOpenChange:i,children:(0,a.jsxs)(eB.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(eB.c7,{children:[(0,a.jsxs)(eB.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),l?"Edit Report Type":"Create Report Type"]}),(0,a.jsx)(eB.rr,{children:l?"Update the report type configuration and settings.":"Create a new report type with custom widgets and data sources."})]}),(0,a.jsx)(eU.lV,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(o),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Basic Information"}),(0,a.jsx)(eU.zB,{control:c.control,name:"name",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Name *"}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(D.p,{placeholder:"Enter report type name",...t})}),(0,a.jsx)(eU.C5,{})]})}}),(0,a.jsx)(eU.zB,{control:c.control,name:"description",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Description"}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(E.T,{placeholder:"Describe what this report type shows",rows:3,...t})}),(0,a.jsx)(eU.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eU.zB,{control:c.control,name:"category",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Category *"}),(0,a.jsxs)(C.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select category"})})}),(0,a.jsx)(C.gC,{children:tt.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eU.C5,{})]})}}),(0,a.jsx)(eU.zB,{control:c.control,name:"dataSource",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Data Source *"}),(0,a.jsxs)(C.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select data source"})})}),(0,a.jsx)(C.gC,{children:te.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eU.C5,{})]})}})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Widget Configuration"}),(0,a.jsx)(eU.zB,{control:c.control,name:"widgets",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Widgets *"}),(0,a.jsx)(eU.Rr,{children:"Select the widgets to include in this report type"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:e9.map(e=>{var s;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{id:e.value,checked:null==(s=t.value)?void 0:s.includes(e.value),onCheckedChange:s=>{let a=t.value||[];s?t.onChange([...a,e.value]):t.onChange(a.filter(t=>t!==e.value))}}),(0,a.jsx)("label",{htmlFor:e.value,className:"text-sm",children:e.label})]},e.value)})}),(0,a.jsx)(eU.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eU.zB,{control:c.control,name:"isActive",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Active"}),(0,a.jsx)(eU.Rr,{children:"Enable this report type for use"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value,onCheckedChange:t.onChange})})]})}}),(0,a.jsx)(eU.zB,{control:c.control,name:"isPublic",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eU.lR,{children:"Public"}),(0,a.jsx)(eU.Rr,{children:"Make available to all users"})]}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(S.S,{checked:t.value,onCheckedChange:t.onChange})})]})}})]}),(0,a.jsx)(eU.zB,{control:c.control,name:"refreshInterval",render:e=>{let{field:t}=e;return(0,a.jsxs)(eU.eI,{children:[(0,a.jsx)(eU.lR,{children:"Refresh Interval (minutes)"}),(0,a.jsx)(eU.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"1",max:"1440",placeholder:"60",...t,onChange:e=>t.onChange(parseInt(e.target.value)||60)})}),(0,a.jsx)(eU.Rr,{children:"How often the report data should refresh (1-1440 minutes)"}),(0,a.jsx)(eU.C5,{})]})}})]}),(0,a.jsxs)(eB.Es,{children:[(0,a.jsx)(y.$,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),(0,a.jsx)(y.$,{type:"submit",disabled:n,children:n?"Saving...":l?"Update":"Create"})]})]})})]})})};var ta=s(44838),tl=s(3561),tr=s(13300),ti=s(18763),tn=s(15599),tc=s(53764),to=s(37648);let td=e=>{switch(e.toLowerCase()){case"operational":return"bg-blue-100 text-blue-800";case"performance":return"bg-green-100 text-green-800";case"financial":return"bg-yellow-100 text-yellow-800";case"compliance":return"bg-red-100 text-red-800";case"analytics":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},tm=e=>{switch(e.toLowerCase()){case"delegations":case"tasks":case"vehicles":default:return(0,a.jsx)(d.A,{className:"h-4 w-4"});case"employees":return(0,a.jsx)(c.A,{className:"h-4 w-4"})}},tu=e=>{var t,s,l,r;let{reportType:i,onSelect:n,onEdit:c,onDelete:o,onDuplicate:d,onToggleActive:m,className:u="",showActions:x=!0}=e,h=(e,t)=>{e.stopPropagation(),t()};return(0,a.jsxs)(N.Zp,{className:(0,en.cn)("cursor-pointer transition-all duration-200 hover:shadow-md",!i.isActive&&"opacity-60",u),onClick:()=>{n&&n(i)},children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)(N.ZB,{className:"text-lg flex items-center gap-2",children:[tm(i.dataSource),i.name]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,a.jsx)(v.E,{className:(0,en.cn)("text-xs",td(i.category)),children:i.category}),!i.isActive&&(0,a.jsx)(v.E,{variant:"secondary",className:"text-xs",children:"Inactive"}),i.isPublic&&(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[(0,a.jsx)(eb.A,{className:"h-3 w-3 mr-1"}),"Public"]})]})]}),x&&(0,a.jsxs)(ta.rI,{children:[(0,a.jsx)(ta.ty,{asChild:!0,children:(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:e=>e.stopPropagation(),children:(0,a.jsx)(tl.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(ta.SQ,{align:"end",children:[n&&(0,a.jsxs)(ta._2,{onClick:e=>h(e,()=>n(i)),children:[(0,a.jsx)(tr.A,{className:"h-4 w-4 mr-2"}),"Use Report Type"]}),c&&(0,a.jsxs)(ta._2,{onClick:e=>h(e,()=>c(i)),children:[(0,a.jsx)(ti.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),d&&(0,a.jsxs)(ta._2,{onClick:e=>h(e,()=>d(i)),children:[(0,a.jsx)(tn.A,{className:"h-4 w-4 mr-2"}),"Duplicate"]}),m&&(0,a.jsx)(ta._2,{onClick:e=>h(e,()=>m(i)),children:i.isActive?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tc.A,{className:"h-4 w-4 mr-2"}),"Deactivate"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tr.A,{className:"h-4 w-4 mr-2"}),"Activate"]})}),o&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ta.mB,{}),(0,a.jsxs)(ta._2,{onClick:e=>h(e,()=>o(i)),className:"text-red-600",children:[(0,a.jsx)(eF.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[i.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:i.description}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-2",children:["Widgets (",(null==(t=i.widgets)?void 0:t.length)||0,")"]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[null==(s=i.widgets)?void 0:s.slice(0,3).map((e,t)=>(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:e},t)),((null==(l=i.widgets)?void 0:l.length)||0)>3&&(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:["+",((null==(r=i.widgets)?void 0:r.length)||0)-3," more"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[tm(i.dataSource),(0,a.jsx)("span",{children:i.dataSource})]}),i.refreshInterval&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(to.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[i.refreshInterval,"m"]})]})]}),i.tags&&i.tags.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[i.tags.slice(0,3).map((e,t)=>(0,a.jsx)(v.E,{variant:"secondary",className:"text-xs",children:e},t)),i.tags.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",i.tags.length-3]})]}),(0,a.jsx)("div",{className:"pt-2 border-t text-xs text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:["Created: ",i.createdAt?(0,er.GP)(new Date(i.createdAt),"MMM dd, yyyy"):"Unknown"]}),i.updatedAt&&(0,a.jsxs)("span",{children:["Updated: ",(0,er.GP)(new Date(i.updatedAt),"MMM dd")]})]})})]})]})},tx=e=>{let{className:t="",onReportTypeSelect:s,allowEdit:l=!0,allowDelete:r=!0}=e,[i,n]=(0,g.useState)(!1),[c,o]=(0,g.useState)(null),[u,x]=(0,g.useState)(""),{data:h,isLoading:p,error:j,createReportType:f,updateReportType:b,deleteReportType:w,duplicateReportType:k}=e3(),S=async e=>{try{let t={name:e.name,category:e.category,dataSource:e.dataSource,widgets:e.widgets,isActive:e.isActive,isPublic:e.isPublic,...e.description&&{description:e.description},...e.filters&&{filters:e.filters},...e.refreshInterval&&{refreshInterval:e.refreshInterval},...e.tags&&{tags:e.tags}};c?await b.mutateAsync({id:c.id,...t}):await f.mutateAsync(t),n(!1),o(null)}catch(e){console.error("Failed to save report type:",e)}},C=e=>{o(e),n(!0)},A=async e=>{if(window.confirm('Are you sure you want to delete "'.concat(e.name,'"?')))try{await w.mutateAsync(e.id)}catch(e){console.error("Failed to delete report type:",e)}},E=async e=>{try{await k.mutateAsync(e.id)}catch(e){console.error("Failed to duplicate report type:",e)}},D=(0,g.useMemo)(()=>(null==h?void 0:h.filter(e=>{var t;return e.name.toLowerCase().includes(u.toLowerCase())||(null==(t=e.description)?void 0:t.toLowerCase().includes(u.toLowerCase()))}))||[],[h,u]);return p?(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Report Type Manager"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]}):j?(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Report Type Manager"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:j})})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-6",t),children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Report Type Manager"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[D.length," types"]}),(0,a.jsxs)(y.$,{onClick:()=>n(!0),size:"sm",className:"flex items-center gap-2",children:[(0,a.jsx)(eP.A,{className:"h-4 w-4"}),"New Report Type"]})]})]})}),(0,a.jsxs)(N.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"text",placeholder:"Search report types...",value:u,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]}),0===D.length?(0,a.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Report Types Found"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:u?"No report types match your search.":"Get started by creating your first report type."}),(0,a.jsxs)(y.$,{onClick:()=>n(!0),children:[(0,a.jsx)(eP.A,{className:"h-4 w-4 mr-2"}),"Create Report Type"]})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:D.map(e=>(0,a.jsx)(tu,{reportType:e,...s&&{onSelect:s},...l&&{onEdit:C},...r&&{onDelete:A},onDuplicate:E},e.id))})]})]}),i&&(0,a.jsx)(ts,{reportType:c,onSubmit:S,onCancel:()=>{n(!1),o(null)},isLoading:f.isPending||b.isPending})]})};var th=s(85127),tp=s(36268),tg=s(11032),tj=s(61051),tv=s(27150);let ty=e=>{var t,s,l;let{className:r="",showExportOptions:n=!0,maxRows:c=100}=e,[o,d]=g.useState([]),[m,u]=g.useState([]),[h,j]=g.useState({}),[f,b]=g.useState({}),{data:w,isLoading:k,error:S}=(0,tj.si)({staleTime:12e4}),C=(0,g.useMemo)(()=>w?w.slice(0,c).map(e=>{var t,s;return{id:e.id,title:e.description,status:e.status,priority:e.priority,assignedTo:(null==(t=e.staffEmployee)?void 0:t.name)||(null==(s=e.driverEmployee)?void 0:s.name)||"Unassigned",dueDate:e.deadline?(0,er.GP)(new Date(e.deadline),"yyyy-MM-dd"):"No deadline",createdAt:(0,er.GP)(new Date(e.createdAt),"yyyy-MM-dd"),completedAt:"Completed"===e.status?(0,er.GP)(new Date(e.updatedAt),"yyyy-MM-dd"):void 0,estimatedHours:e.estimatedDuration||0,actualHours:e.estimatedDuration||0}}):[],[w,c]),A=(0,g.useMemo)(()=>[{accessorKey:"title",header:e=>{let{column:t}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-8 px-2",children:["Task Title",(0,a.jsx)(tv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:t}=e;return(0,a.jsx)("div",{className:"font-medium",children:t.getValue("title")})}},{accessorKey:"status",header:e=>{let{column:t}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-8 px-2",children:["Status",(0,a.jsx)(tv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:t}=e,s=t.getValue("status");return(0,a.jsx)(v.E,{variant:tf(s),children:tb(s)})}},{accessorKey:"priority",header:e=>{let{column:t}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-8 px-2",children:["Priority",(0,a.jsx)(tv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:t}=e,s=t.getValue("priority");return(0,a.jsx)(v.E,{variant:tN(s),children:s})}},{accessorKey:"assignedTo",header:e=>{let{column:t}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-8 px-2",children:["Assigned To",(0,a.jsx)(tv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:t}=e;return(0,a.jsx)("div",{className:"text-sm",children:t.getValue("assignedTo")})}},{accessorKey:"dueDate",header:e=>{let{column:t}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-8 px-2",children:["Due Date",(0,a.jsx)(tv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:t}=e;return(0,a.jsx)("div",{className:"text-sm",children:t.getValue("dueDate")})}},{accessorKey:"estimatedHours",header:"Est. Hours",cell:e=>{let{row:t}=e;return(0,a.jsxs)("div",{className:"text-sm text-center",children:[t.getValue("estimatedHours"),"h"]})}},{accessorKey:"actualHours",header:"Actual Hours",cell:e=>{let{row:t}=e;return(0,a.jsxs)("div",{className:"text-sm text-center",children:[t.getValue("actualHours"),"h"]})}},{id:"actions",header:"Actions",cell:e=>{let{row:t}=e,s=t.original;return(0,a.jsxs)(ta.rI,{children:[(0,a.jsx)(ta.ty,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,a.jsx)(tl.A,{className:"h-4 w-4"})]})}),(0,a.jsxs)(ta.SQ,{align:"end",children:[(0,a.jsxs)(ta.hO,{onClick:()=>console.log("View task:",s.id),children:[(0,a.jsx)(eb.A,{className:"mr-2 h-4 w-4"}),"View Details"]}),(0,a.jsxs)(ta.hO,{onClick:()=>console.log("Edit task:",s.id),children:[(0,a.jsx)(ti.A,{className:"mr-2 h-4 w-4"}),"Edit Task"]}),(0,a.jsx)(ta.hO,{onClick:()=>navigator.clipboard.writeText(s.id),children:"Copy Task ID"})]})]})}}],[]),E=(0,tp.N4)({data:C,columns:A,onSortingChange:d,onColumnFiltersChange:u,getCoreRowModel:(0,tg.HT)(),getPaginationRowModel:(0,tg.kW)(),getSortedRowModel:(0,tg.h5)(),getFilteredRowModel:(0,tg.hM)(),onColumnVisibilityChange:j,onRowSelectionChange:b,state:{sorting:o,columnFilters:m,columnVisibility:h,rowSelection:f}}),T=async()=>{try{if(!C||0===C.length)return void console.warn("No data to export");let e=C.map(e=>({"Task ID":e.id,Title:e.title,Status:e.status,Priority:e.priority,"Assigned To":e.assignedTo,"Due Date":e.dueDate,"Created Date":e.createdAt,"Completed Date":e.completedAt||"","Estimated Hours":e.estimatedHours||"","Actual Hours":e.actualHours||""}));if(0===e.length)return void console.warn("No data to export");let t=[Object.keys(e[0]).join(","),...e.map(e=>Object.values(e).join(","))].join("\n"),s=new Blob([t],{type:"text/csv"}),a=window.URL.createObjectURL(s),l=document.createElement("a");l.href=a,l.download="task-report-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l)}catch(e){console.error("Export failed:",e)}};return k?(0,a.jsxs)(N.Zp,{className:r,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5"}),"Task Report"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse"}),(0,a.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsx)("div",{className:"h-12 bg-muted rounded animate-pulse"},t))})]})})]}):S?(0,a.jsxs)(N.Zp,{className:r,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5"}),"Task Report"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Failed to load task data"}),(0,a.jsx)("p",{className:"text-sm",children:S.message})]})})]}):(0,a.jsxs)(N.Zp,{className:r,children:[(0,a.jsxs)(N.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5"}),"Task Report"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[C.length," tasks found"]})]}),n&&(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:T,className:"h-8",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Export CSV"]})]}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(D.p,{placeholder:"Filter tasks...",value:null!=(l=null==(t=E.getColumn("title"))?void 0:t.getFilterValue())?l:"",onChange:e=>{var t;return null==(t=E.getColumn("title"))?void 0:t.setFilterValue(e.target.value)},className:"max-w-sm"})}),(0,a.jsxs)(ta.rI,{children:[(0,a.jsx)(ta.ty,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:"ml-auto",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Columns"]})}),(0,a.jsx)(ta.SQ,{align:"end",children:E.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,a.jsx)(ta.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:t=>e.toggleVisibility(!!t),children:e.id},e.id))})]})]}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(th.XI,{children:[(0,a.jsx)(th.A0,{children:E.getHeaderGroups().map(e=>(0,a.jsx)(th.Hj,{children:e.headers.map(e=>(0,a.jsx)(th.nd,{children:e.isPlaceholder?null:(0,tp.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(th.BF,{children:(null==(s=E.getRowModel().rows)?void 0:s.length)?E.getRowModel().rows.map(e=>(0,a.jsx)(th.Hj,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,a.jsx)(th.nA,{children:(0,tp.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(th.Hj,{children:(0,a.jsx)(th.nA,{colSpan:A.length,className:"h-24 text-center",children:"No tasks found."})})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[E.getFilteredSelectedRowModel().rows.length," of"," ",E.getFilteredRowModel().rows.length," row(s) selected."]}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)(y.$,{variant:"outline",size:"sm",onClick:()=>E.previousPage(),disabled:!E.getCanPreviousPage(),children:"Previous"}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",onClick:()=>E.nextPage(),disabled:!E.getCanNextPage(),children:"Next"})]})]})]})]})},tf=e=>{switch(e){case"Completed":return"default";case"In_Progress":return"secondary";case"Cancelled":return"destructive";default:return"outline"}},tN=e=>{switch(e){case"High":return"destructive";case"Medium":return"secondary";case"Low":return"outline";default:return"default"}},tb=e=>"In_Progress"===e?"In Progress":e;var tw=s(83540),tk=s(8782),tS=s(34e3),tC=s(54811),tA=s(94517);let tE=e=>({Planned:"#3b82f6",Confirmed:"#10b981",In_Progress:"#f59e0b",Completed:"#22c55e",Cancelled:"#ef4444",No_details:"#6b7280"})[e]||"#6b7280",tD=e=>{var t,s,a,l,r,i,n,c,o;let d={totalCount:e.totalCount||0,statusDistribution:(null==(t=e.statusDistribution)?void 0:t.map(e=>({status:e.status,count:e.count||0,percentage:e.percentage||0,color:tE(e.status)})))||[],trendData:(null==(s=e.trendData)?void 0:s.map(e=>({date:e.date,created:e.created||0,completed:e.completed||0,inProgress:e.inProgress||0})))||[],locationMetrics:e.locationMetrics||[],summary:{totalDelegations:(null==(a=e.summary)?void 0:a.totalDelegations)||0,activeDelegations:(null==(l=e.summary)?void 0:l.activeDelegations)||0,completedDelegations:(null==(r=e.summary)?void 0:r.completedDelegations)||0,totalDelegates:(null==(i=e.summary)?void 0:i.totalDelegates)||0,averageDuration:(null==(n=e.summary)?void 0:n.averageDuration)||0,completionRate:(null==(c=e.summary)?void 0:c.completionRate)||0},delegations:[]};return e.serviceHistory&&(d.serviceHistory=tM(e.serviceHistory)),e.serviceCosts&&"object"==typeof e.serviceCosts&&!Array.isArray(e.serviceCosts)&&(d.serviceCosts=tF(e.serviceCosts)),e.taskData&&"object"==typeof e.taskData&&!Array.isArray(e.taskData)&&(d.taskData=tP(e.taskData)),d.delegations=(null==(o=e.delegations)?void 0:o.map(e=>{var t,s,a,l,r,i;return{id:(e.id||0).toString(),delegationId:e.delegationId||(null==(t=e.id)?void 0:t.toString())||"",customerName:e.customerName||(null==(s=e.customer)?void 0:s.name)||"Unknown Customer",vehicleModel:e.vehicleModel||(null==(a=e.vehicle)?void 0:a.model)||"Unknown Vehicle",licensePlate:e.licensePlate||(null==(l=e.vehicle)?void 0:l.licensePlate)||"Unknown",status:e.status,assignedEmployee:(null==(r=e.driverEmployee)?void 0:r.name)||(null==(i=e.staffEmployee)?void 0:i.name)||"Unassigned",location:e.location||"",createdAt:e.createdAt||"",completedAt:e.completedAt}}))||[],d},tT=e=>{var t,s;return{totalTasks:e.totalTasks||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,inProgressTasks:e.inProgressTasks||0,overdueTasks:e.overdueTasks||0,averageCompletionTime:e.averageCompletionTime||0,tasksByPriority:(null==(t=e.tasksByPriority)?void 0:t.map(e=>({priority:e.priority,count:e.count||0})))||[],tasksByStatus:(null==(s=e.tasksByStatus)?void 0:s.map(e=>({status:e.status,count:e.count||0})))||[]}},tR=e=>(null==e?void 0:e.map(e=>({date:e.date,created:e.created||0,completed:e.completed||0,inProgress:e.inProgress||0})))||[],tz=e=>(null==e?void 0:e.map(e=>({location:e.location||"",delegationCount:e.delegationCount||e.delegationsCount||e.count||0,averageDuration:e.averageDuration||0,completionRate:e.completionRate||0})))||[],tM=e=>({id:e.id||"",vehicleId:e.vehicleId||0,vehicleName:e.vehicleName||"",serviceType:e.serviceType,status:e.status,scheduledDate:e.scheduledDate||"",completedDate:e.completedDate,cost:e.cost||0,description:e.description||"",relatedDelegationId:e.relatedDelegationId,relatedTaskId:e.relatedTaskId}),tF=e=>{var t,s;return{totalCost:e.totalCost||0,averageCostPerService:e.averageCostPerService||0,costByType:(null==(t=e.costByType)?void 0:t.map(e=>({type:e.type,cost:e.cost||0,count:e.count||0})))||[],monthlyTrend:(null==(s=e.monthlyTrend)?void 0:s.map(e=>({month:e.month||"",cost:e.cost||0})))||[]}},tP=e=>{var t;return{totalTasks:e.totalTasks||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,overdueTasks:e.overdueTasks||0,averageCompletionTime:e.averageCompletionTime||0,tasksByPriority:(null==(t=e.tasksByPriority)?void 0:t.map(e=>({priority:e.priority,count:e.count||0})))||[]}};class tI{async getCrossEntityAnalytics(e){try{let t=this.buildQueryParams(e),s=new URLSearchParams(t);e.includeCrossEntityCorrelations&&s.append("includeCrossEntityCorrelations","true");let a=await e5.uE.get("/reporting/cross-entity/analytics?".concat(s.toString()));return a.data||a}catch(e){throw console.error("Error fetching cross-entity analytics:",e),Error("Failed to load cross-entity analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getDelegationAnalytics(e){try{let t=this.buildQueryParams(e),s=new URLSearchParams(t);e.includeServiceHistory&&s.append("includeServiceHistory","true"),e.includeTaskData&&s.append("includeTaskData","true");let a=await e5.uE.get("/reporting/delegations/analytics?".concat(s.toString()));return tD(a.data||a)}catch(e){throw console.error("Error fetching delegation analytics:",e),Error("Failed to load delegation analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getDelegations(e,t){try{let s=this.buildQueryParams(e),a=new URLSearchParams(s);a.append("page",t.page.toString()),a.append("pageSize",t.pageSize.toString());let l=await e5.uE.get("/reporting/delegations?".concat(a.toString()));return l.data||l}catch(e){throw console.error("Error fetching delegations:",e),Error("Failed to load delegations: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getEmployeeAnalytics(e){try{let t=this.buildQueryParams(e),s=await e5.uE.get("/reporting/employee/analytics?".concat(t));return s.data||s}catch(e){throw console.error("Error fetching employee analytics:",e),Error("Failed to load employee analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getLocationMetrics(e){try{let t=this.buildQueryParams(e),s=await e5.uE.get("/reporting/locations/metrics?".concat(t));return tz(s.data||s)}catch(e){throw console.error("Error fetching location metrics:",e),Error("Failed to load location metrics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getServiceCostSummary(e){try{let t=this.buildQueryParams(e);return(await e5.uE.get("/reporting/services/costs?".concat(t))).data||{averageCostPerService:0,costByType:[],monthlyTrend:[],totalCost:0}}catch(e){throw console.error("Error fetching service costs:",e),Error("Failed to load service costs: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getServiceHistory(e){try{let t=this.buildQueryParams(e);return(await e5.uE.get("/reporting/services/history?".concat(t))).data||[]}catch(e){throw console.error("Error fetching service history:",e),Error("Failed to load service history: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getTaskAnalytics(e){try{let t=this.buildQueryParams(e),s=new URLSearchParams(t);e.taskStatus&&e.taskStatus.length>0&&s.append("taskStatus",e.taskStatus.join(",")),e.taskPriority&&e.taskPriority.length>0&&s.append("taskPriority",e.taskPriority.join(","));let a=await e5.uE.get("/reporting/tasks/analytics?".concat(s.toString()));return a.data||a}catch(e){throw console.error("Error fetching task analytics:",e),Error("Failed to load task analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getTaskMetrics(e){try{let t=(null==e?void 0:e.length)?"delegationIds=".concat(e.join(",")):"",s=await e5.uE.get("/reporting/tasks/metrics?".concat(t));return tT(s.data||s)}catch(e){throw console.error("Error fetching task metrics:",e),Error("Failed to load task metrics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getTrendData(e){try{let t=this.buildQueryParams(e),s=await e5.uE.get("/reporting/trends?".concat(t));return tR(s.data||s)}catch(e){throw console.error("Error fetching trend data:",e),Error("Failed to load trend data: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getVehicleAnalytics(e){try{let t=this.buildQueryParams(e),s=new URLSearchParams(t);e.vehicles&&e.vehicles.length>0&&s.append("vehicles",e.vehicles.join(",")),e.serviceTypes&&e.serviceTypes.length>0&&s.append("serviceTypes",e.serviceTypes.join(",")),e.serviceStatus&&e.serviceStatus.length>0&&s.append("serviceStatus",e.serviceStatus.join(","));let a="/reporting/vehicles/analytics".concat(s.toString()?"?".concat(s.toString()):""),l=await e5.uE.get(a);return l.data||l}catch(e){throw console.error("Error fetching vehicle analytics:",e),e}}appendArrayParams(e,t,s){s&&s.length>0&&e.append(t,s.join(","))}buildQueryParams(e){let t=new URLSearchParams;try{let s=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);if(isNaN(s.getTime())||isNaN(a.getTime()))throw TypeError("Invalid date range provided");t.append("dateRange.from",s.toISOString().split("T")[0]||s.toISOString()),t.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}catch(a){console.error("Error processing date range:",a);let e=new Date,s=new Date(Date.now()-2592e6);t.append("dateRange.from",s.toISOString().split("T")[0]||s.toISOString()),t.append("dateRange.to",e.toISOString().split("T")[0]||e.toISOString())}return this.appendArrayParams(t,"status",e.status),this.appendArrayParams(t,"locations",e.locations),this.appendArrayParams(t,"employees",e.employees),this.appendArrayParams(t,"vehicles",e.vehicles),e.taskStatus&&this.appendArrayParams(t,"taskStatus",e.taskStatus),e.taskPriority&&this.appendArrayParams(t,"taskPriority",e.taskPriority),e.serviceTypes&&this.appendArrayParams(t,"serviceTypes",e.serviceTypes),e.serviceStatus&&this.appendArrayParams(t,"serviceStatus",e.serviceStatus),e.costRange&&(t.append("minCost",e.costRange.min.toString()),t.append("maxCost",e.costRange.max.toString())),t.toString()}constructor(e="/api/reporting"){this.baseUrl=e}}let tL=new tI;var tO=s(14056);let tW=(e,t,s)=>{var a;let l=(0,tO.Sk)(["delegations",e,t],()=>tL.getDelegationAnalytics(e),{placeholderData:e=>e,showErrorToast:!0,...s}),r=(0,g.useMemo)(()=>{var e;if(!(null==(e=l.data)?void 0:e.delegations))return;let s=l.data.delegations,a=(t.page-1)*t.pageSize,r=a+t.pageSize;return{data:s.slice(a,r).map(e=>({id:e.id,delegationId:e.id.toString(),customerName:e.title,vehicleModel:"N/A",licensePlate:"N/A",status:e.status,assignedEmployee:e.assignedTo,location:e.location,createdAt:e.createdAt,completedAt:e.completedAt||null})),meta:{total:s.length,page:t.page,pageSize:t.pageSize,totalPages:Math.ceil(s.length/t.pageSize)}}},[null==(a=l.data)?void 0:a.delegations,t.page,t.pageSize]);return{...l,data:r}};s(38549);var tV=s(28755);let tB={all:["reporting"],analytics:()=>[...tB.all,"analytics"],delegationAnalytics:e=>[...tB.analytics(),"delegations",e],taskMetrics:e=>[...tB.all,"tasks","metrics",e],trends:e=>[...tB.all,"trends",e],locationMetrics:e=>[...tB.all,"locations","metrics",e],serviceHistory:e=>[...tB.all,"services","history",e],serviceCosts:e=>[...tB.all,"services","costs",e],taskAnalytics:e=>[...tB.analytics(),"tasks",e],vehicleAnalytics:e=>[...tB.analytics(),"vehicles",e],employeeAnalytics:e=>[...tB.analytics(),"employees",e],crossEntityAnalytics:e=>[...tB.analytics(),"cross-entity",e]},tU=(e,t)=>{let s=(0,g.useMemo)(()=>tB.delegationAnalytics(e),[e]),a=(0,g.useCallback)(()=>tL.getDelegationAnalytics(e),[e]);return(0,tV.I)({queryKey:s,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnMount:!0,...t})},tY=(e,t)=>{let s=(0,g.useMemo)(()=>tB.trends(e),[e]),a=(0,g.useCallback)(()=>tL.getTrendData(e),[e]);return(0,tV.I)({queryKey:s,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,...t})},tZ=(e,t)=>{let s=(0,g.useMemo)(()=>tB.locationMetrics(e),[e]),a=(0,g.useCallback)(()=>tL.getLocationMetrics(e),[e]);return(0,tV.I)({queryKey:s,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,...t})},t$=(e,t)=>{let s=(0,g.useMemo)(()=>tB.taskAnalytics(e),[e]),a=(0,g.useCallback)(()=>tL.getTaskAnalytics(e),[e]);return(0,tV.I)({queryKey:s,queryFn:a,staleTime:18e4,gcTime:48e4,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,enabled:!!e.includeTaskData,...t})};var t_=s(65453),tG=s(46786),tq=s(21603);let tJ=()=>{let e=new Date;return{costRange:{max:1e4,min:0},dateRange:{from:new Date(e.getTime()-2592e6),to:e},employees:[],includeServiceHistory:!1,includeTaskData:!1,locations:[],serviceStatus:[],serviceTypes:[],status:[],vehicles:[]}};new Date().getTime();let tH=e=>{let t={};return e.dateRange&&(e.dateRange.from>e.dateRange.to&&(t.dateRange="Start date must be before end date"),Math.abs(e.dateRange.to.getTime()-e.dateRange.from.getTime())/864e5>365&&(t.dateRange="Date range cannot exceed 365 days")),e.status.length>10&&(t.status="Too many statuses selected (maximum 10)"),e.locations.length>50&&(t.locations="Too many locations selected (maximum 50)"),e.employees.length>100&&(t.employees="Too many employees selected (maximum 100)"),e.vehicles.length>100&&(t.vehicles="Too many vehicles selected (maximum 100)"),e.serviceTypes&&e.serviceTypes.length>20&&(t.serviceTypes="Too many service types selected (maximum 20)"),e.serviceStatus&&e.serviceStatus.length>10&&(t.serviceStatus="Too many service statuses selected (maximum 10)"),e.costRange&&(e.costRange.min<0&&(t.costRange="Minimum cost cannot be negative"),e.costRange.min>=e.costRange.max&&(t.costRange="Minimum cost must be less than maximum cost"),e.costRange.max>1e6&&(t.costRange="Maximum cost cannot exceed $1,000,000")),t},tQ=(0,t_.v)()((0,tG.lt)((0,tG.eh)((0,tG.Zr)((e,t)=>({applyFilters:()=>{let{filters:s,isValid:a}=t();a&&e({hasUnsavedChanges:!1,lastAppliedFilters:{...s}})},applyPreset:t=>{try{let s=localStorage.getItem("reporting-filter-presets"),a=(s?JSON.parse(s):{})[t];a&&e(e=>({filters:{...a},hasUnsavedChanges:!0,lastAppliedFilters:e.lastAppliedFilters}))}catch(e){console.error("Failed to apply preset:",e)}},clearValidationErrors:()=>{e({isValid:!0,validationErrors:{}})},deletePreset:e=>{try{let t=localStorage.getItem("reporting-filter-presets"),s=t?JSON.parse(t):{};delete s[e],localStorage.setItem("reporting-filter-presets",JSON.stringify(s))}catch(e){console.error("Failed to delete preset:",e)}},filters:tJ(),getPresets:()=>{try{let e=localStorage.getItem("reporting-filter-presets");return e?JSON.parse(e):{}}catch(e){return{}}},hasUnsavedChanges:!1,isFilterPanelOpen:!1,isValid:!0,lastAppliedFilters:tJ(),resetFilters:()=>{e({filters:tJ(),hasUnsavedChanges:!0,isValid:!0,validationErrors:{}})},revertChanges:()=>{let{lastAppliedFilters:s}=t();e({filters:{...s},hasUnsavedChanges:!1,isValid:!0,validationErrors:{}})},saveAsPreset:e=>{try{let{filters:s}=t(),a=localStorage.getItem("reporting-filter-presets"),l=a?JSON.parse(a):{};l[e]={...s},localStorage.setItem("reporting-filter-presets",JSON.stringify(l))}catch(e){console.error("Failed to save preset:",e)}},setCostRange:(t,s)=>{e(e=>{let a={...e.filters,costRange:{max:s,min:t}},l=tH(a);return{filters:a,hasUnsavedChanges:!0,isValid:0===Object.keys(l).length,validationErrors:l}})},setDateRange:(t,s)=>{e(e=>{let a={...e.filters,dateRange:{from:t,to:s}},l=tH(a);return{filters:a,hasUnsavedChanges:!0,isValid:0===Object.keys(l).length,validationErrors:l}})},setEmployees:t=>{e(e=>{let s={...e.filters,employees:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setFilterPanelOpen:t=>{e({isFilterPanelOpen:t})},setFilters:t=>{e(e=>{let s={...e.filters,...t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setIncludeServiceHistory:t=>{e(e=>{let s={...e.filters,includeServiceHistory:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setIncludeTaskData:t=>{e(e=>{let s={...e.filters,includeTaskData:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setLocations:t=>{e(e=>{let s={...e.filters,locations:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setServiceStatus:t=>{e(e=>{let s={...e.filters,serviceStatus:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setServiceTypes:t=>{e(e=>{let s={...e.filters,serviceTypes:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setStatus:t=>{e(e=>{let s={...e.filters,status:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setTaskPriorities:t=>{e(e=>{let s={...e.filters,taskPriorities:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setTaskStatus:t=>{e(e=>{let s={...e.filters,taskStatus:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setVehicles:t=>{e(e=>{let s={...e.filters,vehicles:t},a=tH(s);return{filters:s,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},toggleFilterPanel:()=>{e(e=>({isFilterPanelOpen:!e.isFilterPanelOpen}))},validateFilters:()=>{let{filters:s}=t(),a=tH(s),l=0===Object.keys(a).length;return e({isValid:l,validationErrors:a}),l},validationErrors:{}}),{name:"reporting-filters-storage",partialize:e=>({filters:e.filters,lastAppliedFilters:e.lastAppliedFilters}),storage:(0,tG.KU)(()=>localStorage)})),{name:"reporting-filters-store"})),tK=e=>e.filters,tX=e=>({applyFilters:e.applyFilters,resetFilters:e.resetFilters,revertChanges:e.revertChanges,setCostRange:e.setCostRange,setDateRange:e.setDateRange,setEmployees:e.setEmployees,setFilters:e.setFilters,setIncludeServiceHistory:e.setIncludeServiceHistory,setIncludeTaskData:e.setIncludeTaskData,setLocations:e.setLocations,setServiceStatus:e.setServiceStatus,setServiceTypes:e.setServiceTypes,setStatus:e.setStatus,setTaskPriorities:e.setTaskPriorities,setTaskStatus:e.setTaskStatus,setVehicles:e.setVehicles}),t0=e=>({hasUnsavedChanges:e.hasUnsavedChanges,isFilterPanelOpen:e.isFilterPanelOpen,setFilterPanelOpen:e.setFilterPanelOpen,toggleFilterPanel:e.toggleFilterPanel}),t1=e=>({clearValidationErrors:e.clearValidationErrors,isValid:e.isValid,validateFilters:e.validateFilters,validationErrors:e.validationErrors}),t2=e=>({applyPreset:e.applyPreset,deletePreset:e.deletePreset,getPresets:e.getPresets,saveAsPreset:e.saveAsPreset}),t4=()=>tQ((0,tq.k)(tK)),t5=()=>tQ((0,tq.k)(tX)),t3=()=>tQ((0,tq.k)(t0)),t6=()=>tQ((0,tq.k)(t1)),t8=()=>tQ((0,tq.k)(t2));var t7=s(68856);let t9=e=>{let{payload:t}=e;return t&&0!==t.length?(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-6 pt-4 border-t border-border",children:t.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full border border-gray-300",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:e.value}),e.payload&&(0,a.jsxs)("span",{className:"text-muted-foreground",children:["(",e.payload.count,")"]})]},"legend-".concat(t)))}):null},se=e=>{let{active:t,payload:s}=e;if(t&&s&&s.length){let e=s[0],t=s[0].payload.total||100,l=t>0?Math.round(e.value/t*100):0;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-2",children:e.name}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Count:"}),(0,a.jsx)("span",{className:"font-bold text-lg",style:{color:e.color},children:e.value})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Percentage:"}),(0,a.jsxs)("span",{className:"font-medium text-primary",children:[l,"%"]})]})]})]})}return null},st=()=>{var e;let{data:t,isLoading:s,error:r}=tU(t4());if(s)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(t7.E,{className:"h-7 w-48"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(t7.E,{className:"h-[400px] w-full rounded-lg"})})]});if(r)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(l.A,{className:"h-6 w-6"}),"Delegation Status"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:r.message})]})})]});let i=null!=(e=null==t?void 0:t.statusDistribution)?e:[],n=i.reduce((e,t)=>e+t.count,0),c=i.map(e=>({...e,total:n})),o=["#22c55e","#f59e0b","#ef4444","#3b82f6","#8b5cf6"],d=c.map((e,t)=>({...e,color:e.color||o[t%o.length]}));return(0,a.jsxs)(N.Zp,{children:[(0,a.jsxs)(N.aR,{className:"pb-6",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(l.A,{className:"h-6 w-6 text-primary"}),"Delegation Status"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Distribution of delegation statuses"})]})]}),(0,a.jsxs)(N.Wu,{className:"pt-6",children:[(0,a.jsx)(tw.u,{width:"100%",height:300,children:(0,a.jsxs)(tk.r,{children:[(0,a.jsx)(tS.F,{data:d,cx:"50%",cy:"50%",labelLine:!1,outerRadius:100,innerRadius:40,fill:"#8884d8",dataKey:"count",nameKey:"status",stroke:"#fff",strokeWidth:2,children:d.map((e,t)=>(0,a.jsx)(tC.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(tA.m,{content:(0,a.jsx)(se,{})})]})}),(0,a.jsx)(t9,{payload:d.map(e=>({value:e.status,color:e.color,payload:e}))}),n>0&&(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Delegations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:n})]}),d.length>0&&d[0]&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Most Common Status:"}),(0,a.jsx)("span",{className:"font-semibold",style:{color:d[0].color},children:d[0].status})]})]})]})]})};var ss=s(93504),sa=s(94754),sl=s(96025),sr=s(16238),si=s(24026),sn=s(21374);let sc=()=>{let{data:e,isLoading:t,error:s}=tY(t4());return t?(0,a.jsx)(t7.E,{className:"h-[350px] w-full"}):s?(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:s.message})]}):(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5"}),"Delegation Trends"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(tw.u,{width:"100%",height:300,children:(0,a.jsxs)(ss.b,{data:e||[],children:[(0,a.jsx)(sa.d,{strokeDasharray:"3 3"}),(0,a.jsx)(sl.W,{dataKey:"date"}),(0,a.jsx)(sr.h,{}),(0,a.jsx)(tA.m,{}),(0,a.jsx)(si.s,{}),(0,a.jsx)(sn.N,{type:"monotone",dataKey:"created",stroke:"#8884d8",name:"Created"}),(0,a.jsx)(sn.N,{type:"monotone",dataKey:"completed",stroke:"#82ca9d",name:"Completed"}),(0,a.jsx)(sn.N,{type:"monotone",dataKey:"inProgress",stroke:"#ffc658",name:"In Progress"})]})})})]})};var so=s(3401),sd=s(83394),sm=s(83662);let su=e=>{let{active:t,payload:s,label:l}=e;if(t&&s&&s.length){let e=s[0].payload,t=s[0].payload.total||100,r=t>0?Math.round(e.delegationCount/t*100):0;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:l}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Delegations:"}),(0,a.jsx)("span",{className:"font-bold text-blue-600 dark:text-blue-400 text-lg",children:e.delegationCount})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Percentage:"}),(0,a.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:[r,"%"]})]}),void 0!==e.completionRate&&(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Completion Rate:"}),(0,a.jsxs)("span",{className:"font-medium text-green-600 dark:text-green-400",children:[e.completionRate,"%"]})]}),e.averageResponseTime&&(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Avg Response:"}),(0,a.jsxs)("span",{className:"font-medium text-yellow-600 dark:text-yellow-400",children:[e.averageResponseTime,"h"]})]})]})]})}return null},sx=()=>{let{data:e,isLoading:t,error:s}=tZ(t4());if(t)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(t7.E,{className:"h-7 w-48"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(t7.E,{className:"h-[350px] w-full rounded-lg"})})]});if(s)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(sm.A,{className:"h-6 w-6"}),"Location Distribution"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:s.message})]})})]});let l=(null==e?void 0:e.reduce((e,t)=>e+t.delegationCount,0))||0,i=(null==e?void 0:e.map(e=>({...e,total:l})))||[];return(0,a.jsxs)(N.Zp,{children:[(0,a.jsxs)(N.aR,{className:"pb-6",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(sm.A,{className:"h-6 w-6 text-primary"}),"Location Distribution"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Delegation distribution across locations"})]})]}),(0,a.jsxs)(N.Wu,{className:"pt-6",children:[(0,a.jsx)(tw.u,{width:"100%",height:350,children:(0,a.jsxs)(so.E,{data:i,layout:"vertical",margin:{top:8,right:40,left:20,bottom:8},children:[(0,a.jsx)(sa.d,{strokeDasharray:"3 3",stroke:"#f1f5f9",horizontal:!0,vertical:!1}),(0,a.jsx)(sl.W,{type:"number",fontSize:12,tick:{fill:"#64748b"},axisLine:{stroke:"#e2e8f0"},tickLine:{stroke:"#e2e8f0"}}),(0,a.jsx)(sr.h,{type:"category",dataKey:"location",width:140,fontSize:12,tick:{fill:"#374151",fontSize:12},axisLine:{stroke:"#e2e8f0"},tickLine:{stroke:"#e2e8f0"},orientation:"left"}),(0,a.jsx)(tA.m,{content:(0,a.jsx)(su,{})}),(0,a.jsx)(sd.y,{dataKey:"delegationCount",fill:"#3b82f6",name:"Delegations",radius:[0,6,6,0],stroke:"#2563eb",strokeWidth:1})]})}),l>0&&(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Delegations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:l})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Active Locations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:i.length})]})]})]})]})},sh=e=>{let{className:t="",data:s=[],height:r=300,interactive:i=!0,showLegend:n=!0}=e,c=(0,g.useMemo)(()=>s.map(e=>({color:e.color||sg(e.status),name:sp(e.status),percentage:Math.round(e.percentage),status:e.status,value:e.count})),[s]),o=(0,g.useMemo)(()=>c.reduce((e,t)=>e+t.value,0),[c]);return s&&0!==s.length?(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"size-5"}),"Task Status Distribution"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total Tasks: ",o]})]}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(tw.u,{height:r,width:"100%",children:(0,a.jsxs)(tk.r,{children:[(0,a.jsx)(tS.F,{cx:"50%",cy:"50%",data:c,dataKey:"value",fill:"#8884d8",label:e=>{let{name:t,percentage:s}=e;return"".concat(t,": ").concat(s,"%")},labelLine:!1,outerRadius:Math.min(.3*r,100),children:c.map((e,t)=>(0,a.jsx)(tC.f,{fill:e.color},"cell-".concat(t)))}),i&&(0,a.jsx)(tA.m,{content:(0,a.jsx)(e=>{let{active:t,payload:s}=e;if(t&&(null==s?void 0:s.length)){let e=s[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg dark:bg-gray-800",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Count: ",(0,a.jsx)("span",{className:"font-medium",children:e.value})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},{})}),n&&(0,a.jsx)(si.s,{content:(0,a.jsx)(e=>{let{payload:t}=e;return n&&t?(0,a.jsx)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:t.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"size-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.value," (",e.payload.percentage,"%)"]})]},t))}):null},{})})]})})})]}):(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"size-5"}),"Task Status Distribution"]})}),(0,a.jsx)(N.Wu,{className:"flex items-center justify-center",style:{height:r},children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(l.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No task status data available"})]})})]})},sp=e=>({Assigned:"Assigned",Cancelled:"Cancelled",Completed:"Completed",In_Progress:"In Progress",Pending:"Pending"})[e]||e,sg=e=>({Assigned:"#3b82f6",Cancelled:"#ef4444",Completed:"#10b981",In_Progress:"#8b5cf6",Pending:"#f59e0b"})[e]||"#6b7280";var sj=s(27300);let sv=e=>{let{data:t=[],className:s="",showLegend:l=!0,interactive:r=!0,height:i=300}=e,n=(0,g.useMemo)(()=>t.map(e=>({name:sy(e.priority),value:e.count,color:e.color||sf(e.priority),percentage:Math.round(e.percentage),priority:e.priority})),[t]),c=(0,g.useMemo)(()=>n.reduce((e,t)=>e+t.value,0),[n]);return t&&0!==t.length?(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(sj.A,{className:"h-5 w-5"}),"Task Priority Distribution"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total Tasks: ",c]})]}),(0,a.jsxs)(N.Wu,{children:[(0,a.jsx)(tw.u,{width:"100%",height:i,children:(0,a.jsxs)(so.E,{data:n,margin:{top:20,right:30,left:20,bottom:5},children:[(0,a.jsx)(sa.d,{strokeDasharray:"3 3",className:"opacity-30"}),(0,a.jsx)(sl.W,{dataKey:"name",tick:{fontSize:12},className:"text-muted-foreground"}),(0,a.jsx)(sr.h,{tick:{fontSize:12},className:"text-muted-foreground"}),r&&(0,a.jsx)(tA.m,{content:(0,a.jsx)(e=>{let{active:t,payload:s,label:l}=e;if(t&&s&&s.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:l}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Count: ",(0,a.jsx)("span",{className:"font-medium",children:e.value})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},{})}),(0,a.jsx)(sd.y,{dataKey:"value",radius:[4,4,0,0],fill:"#8884d8",children:n.map((e,t)=>(0,a.jsx)(tC.f,{fill:e.color},"cell-".concat(t)))})]})}),l&&(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-4",children:n.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.name,": ",e.value," (",e.percentage,"%)"]})]},t))})]})]}):(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(sj.A,{className:"h-5 w-5"}),"Task Priority Distribution"]})}),(0,a.jsx)(N.Wu,{className:"flex items-center justify-center",style:{height:i},children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(sj.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No task priority data available"})]})})]})},sy=e=>({Low:"Low",Medium:"Medium",High:"High"})[e]||e,sf=e=>({Low:"#10b981",Medium:"#f59e0b",High:"#ef4444"})[e]||"#6b7280";var sN=s(24944);let sb=e=>{let{data:t=[],className:s="",showExportOptions:l=!0,maxDisplayItems:i=10}=e,n=(0,g.useMemo)(()=>t.sort((e,t)=>t.completionRate-e.completionRate).slice(0,i),[t,i]),o=(0,g.useMemo)(()=>{if(!t||0===t.length)return{totalEmployees:0,totalAssignedTasks:0,totalCompletedTasks:0,averageCompletionRate:0,averageCompletionTime:0};let e=t.reduce((e,t)=>e+t.assignedTasks,0),s=t.reduce((e,t)=>e+t.completedTasks,0),a=t.reduce((e,t)=>e+t.completionRate,0)/t.length,l=t.reduce((e,t)=>e+t.averageCompletionTime,0)/t.length;return{totalEmployees:t.length,totalAssignedTasks:e,totalCompletedTasks:s,averageCompletionRate:Math.round(a),averageCompletionTime:Math.round(10*l)/10}},[t]),d=async()=>{try{if(!t||0===t.length)return void console.warn("No data to export");let e=t.map(e=>({"Employee Name":e.employeeName,"Assigned Tasks":e.assignedTasks,"Completed Tasks":e.completedTasks,"Completion Rate (%)":Math.round(e.completionRate),"Average Completion Time (days)":e.averageCompletionTime}));if(0===e.length)return void console.warn("No data to export");let s=[Object.keys(e[0]).join(","),...e.map(e=>Object.values(e).join(","))].join("\n"),a=new Blob([s],{type:"text/csv"}),l=window.URL.createObjectURL(a),r=document.createElement("a");r.href=l,r.download="task-assignment-metrics-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(l),document.body.removeChild(r)}catch(e){console.error("Export failed:",e)}},m=e=>e>=90?{variant:"default",label:"Excellent"}:e>=75?{variant:"secondary",label:"Good"}:e>=60?{variant:"outline",label:"Average"}:{variant:"destructive",label:"Needs Improvement"};return t&&0!==t.length?(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsxs)(N.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Task Assignment Metrics"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Employee task performance and completion rates"})]}),l&&(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:d,className:"h-8",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Export CSV"]})]}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-blue-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-blue-600",children:o.totalEmployees})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-blue-700",children:"Employees"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-purple-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:o.totalAssignedTasks})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-purple-700",children:"Assigned"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-green-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(q.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-green-600",children:o.totalCompletedTasks})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-green-700",children:"Completed"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-emerald-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5 text-emerald-600"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-emerald-600",children:[o.averageCompletionRate,"%"]})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-emerald-700",children:"Avg. Rate"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-orange-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(to.A,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-orange-600",children:[o.averageCompletionTime,"d"]})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-orange-700",children:"Avg. Time"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-muted-foreground",children:["Employee Performance (",n.length," of ",t.length,")"]}),(0,a.jsx)("div",{className:"space-y-3",children:n.map((e,t)=>{let s=m(e.completionRate);return(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between p-4 border rounded-lg bg-muted/20 gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 mb-3",children:[(0,a.jsx)("h5",{className:"font-medium truncate",children:e.employeeName}),(0,a.jsx)(v.E,{variant:s.variant,className:"text-xs w-fit",children:s.label})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Assigned"}),(0,a.jsx)("span",{className:"font-medium text-base",children:e.assignedTasks})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Completed"}),(0,a.jsx)("span",{className:"font-medium text-base",children:e.completedTasks})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Rate"}),(0,a.jsxs)("span",{className:"font-medium text-base",children:[Math.round(e.completionRate),"%"]})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Avg. Time"}),(0,a.jsxs)("span",{className:"font-medium text-base",children:[e.averageCompletionTime,"d"]})]})]})]}),(0,a.jsxs)("div",{className:"w-full md:w-32 flex flex-col items-center",children:[(0,a.jsx)(sN.k,{value:e.completionRate,className:"h-3 w-full"}),(0,a.jsxs)("p",{className:"text-sm font-medium mt-2 text-center",children:[Math.round(e.completionRate),"%"]})]})]},e.employeeId)})}),t.length>i&&(0,a.jsx)("div",{className:"text-center pt-2",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing top ",i," employees by completion rate"]})})]})]})]}):(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Task Assignment Metrics"]})}),(0,a.jsx)(N.Wu,{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No task assignment data available"})]})})]})},sw=e=>{let{className:t="",compact:s=!1,showCharts:n=!0}=e,{data:o,isLoading:d,error:m}=t$(t4(),{enabled:!0,staleTime:12e4}),u=(0,g.useMemo)(()=>{var e,t,s;return o?{totalTasks:o.totalCount||0,completedTasks:(null==(t=o.statusDistribution)||null==(e=t.find(e=>"Completed"===e.status))?void 0:e.count)||0,completionRate:Math.round(100*(o.completionRate||0)),overdueTasks:o.overdueCount||0,averageCompletionTime:o.averageCompletionTime||0,assignedEmployees:(null==(s=o.assignmentMetrics)?void 0:s.length)||0}:null},[o]);return d?(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{className:"pb-6",children:(0,a.jsx)(t7.E,{className:"h-7 w-48"})}),(0,a.jsxs)(N.Wu,{className:"space-y-8",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,a.jsx)(t7.E,{className:"h-32 rounded-lg"},t))}),n&&!s&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsx)(t7.E,{className:"h-80 rounded-lg"}),(0,a.jsx)(t7.E,{className:"h-80 rounded-lg"})]})]})]}):m?(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(i.A,{className:"h-6 w-6"}),"Task Analytics"]})}),(0,a.jsx)(N.Wu,{className:"pt-6",children:(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)(G.A,{className:"h-4 w-4"}),(0,a.jsx)($.XL,{children:"Error Loading Task Analytics"}),(0,a.jsx)($.TN,{children:m.message||"Failed to load task analytics data"})]})})]}):(0,a.jsxs)(N.Zp,{className:"".concat(t," overflow-hidden"),children:[(0,a.jsx)(N.aR,{className:"pb-8",children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(i.A,{className:"h-6 w-6 text-primary"}),"Task Analytics"]})}),(0,a.jsxs)(N.Wu,{className:"px-8 pb-8 space-y-10",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6",children:(0,a.jsx)(i.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-3",children:(null==u?void 0:u.totalTasks)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Total Tasks"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-green-100 dark:bg-green-900/30 mb-6",children:(0,a.jsx)(q.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-3",children:(null==u?void 0:u.completedTasks)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Completed"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-emerald-100 dark:bg-emerald-900/30 mb-6",children:(0,a.jsx)(r.A,{className:"h-8 w-8 text-emerald-600"})}),(0,a.jsxs)("div",{className:"text-4xl font-bold text-emerald-600 mb-3",children:[(null==u?void 0:u.completionRate)||0,"%"]}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Completion Rate"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-red-100 dark:bg-red-900/30 mb-6",children:(0,a.jsx)(G.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-red-600 mb-3",children:(null==u?void 0:u.overdueTasks)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Overdue"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 mb-6",children:(0,a.jsx)(to.A,{className:"h-8 w-8 text-yellow-600"})}),(0,a.jsxs)("div",{className:"text-4xl font-bold text-yellow-600 mb-3",children:[(null==u?void 0:u.averageCompletionTime)||0,"d"]}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Avg. Completion"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-6",children:(0,a.jsx)(c.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-purple-600 mb-3",children:(null==u?void 0:u.assignedEmployees)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Assigned Staff"})]})]}),n&&!s&&o&&(0,a.jsxs)("div",{className:"space-y-10",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)(N.Zp,{className:"border-2",children:[(0,a.jsx)(N.aR,{className:"pb-6",children:(0,a.jsx)(N.ZB,{className:"text-lg",children:"Status Distribution"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(sh,{data:o.statusDistribution,className:"h-80 w-full",showLegend:!0,interactive:!0})})]}),(0,a.jsxs)(N.Zp,{className:"border-2",children:[(0,a.jsx)(N.aR,{className:"pb-6",children:(0,a.jsx)(N.ZB,{className:"text-lg",children:"Priority Distribution"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(sv,{data:o.priorityDistribution,className:"h-80 w-full",showLegend:!0,interactive:!0})})]})]}),(0,a.jsx)(sb,{data:o.assignmentMetrics,className:"w-full"})]}),s&&o&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-primary",children:[null==u?void 0:u.completionRate,"%"]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"completion rate"})]}),(0,a.jsxs)(y.$,{variant:"ghost",size:"sm",className:"gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"View Details"]})]})]})]})};var sk=s(35695),sS=s(40879);s(17841);var sC=s(3235);let sA=e=>(0,e4.Sk)(["vehicle-analytics",e],async()=>{let t=new URLSearchParams;if(null==e?void 0:e.dateRange){let s=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);t.append("dateRange.from",s.toISOString().split("T")[0]||s.toISOString()),t.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}(null==e?void 0:e.vehicles)&&e.vehicles.forEach(e=>t.append("vehicles",e.toString())),(null==e?void 0:e.serviceTypes)&&e.serviceTypes.forEach(e=>t.append("serviceTypes",e)),(null==e?void 0:e.serviceStatus)&&e.serviceStatus.forEach(e=>t.append("serviceStatus",e));let s="/reporting/vehicles/analytics".concat(t.toString()?"?".concat(t.toString()):""),a=await e5.uE.get(s);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),sE=e=>{let{label:t,value:s,icon:l,trend:r,variant:i="default"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[i]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[l,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:t})]}),r&&(0,a.jsxs)(v.E,{variant:r.isPositive?"default":"destructive",className:"text-xs",children:[r.isPositive?"+":"",r.value,"%"]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:s})})]})},sD=e=>{var t,s,l,i;let{filters:c,className:o="",showExportOptions:d=!0,compact:m=!1}=e,{data:u,isLoading:x,error:h}=sA(c);if(x)return(0,a.jsxs)(N.Zp,{className:o,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(h)return(0,a.jsxs)(N.Zp,{className:o,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:h})})]});let g=(null==u?void 0:u.totalCount)||0,j=(null==u||null==(t=u.utilizationMetrics)?void 0:t.filter(e=>e.utilizationRate>0).length)||0,f=g>0?j/g*100:0,b=(null==u||null==(s=u.costAnalysis)?void 0:s.totalCost)||0,w=(null==u||null==(l=u.costAnalysis)?void 0:l.averageCostPerService)||0;return(0,a.jsxs)(N.Zp,{className:o,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[g," vehicles"]}),d&&(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(tl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(sE,{label:"Total Vehicles",value:g,icon:(0,a.jsx)(n.A,{className:"h-4 w-4"})}),(0,a.jsx)(sE,{label:"Utilization Rate",value:"".concat(Math.round(f),"%"),icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),variant:f<70?"warning":"success"}),(0,a.jsx)(sE,{label:"Total Service Cost",value:"$".concat(b.toLocaleString()),icon:(0,a.jsx)(eH.A,{className:"h-4 w-4"})}),(0,a.jsx)(sE,{label:"Avg. Cost/Service",value:"$".concat(Math.round(w)),icon:(0,a.jsx)(sC.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Active Vehicles"}),(0,a.jsx)(n.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-blue-900",children:j})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-green-700",children:"Maintenance Due"}),(0,a.jsx)(G.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-green-900",children:(null==u||null==(i=u.maintenanceSchedule)?void 0:i.filter(e=>new Date(e.nextMaintenanceDate)<=new Date).length)||0})]}),(0,a.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-orange-700",children:"Avg. Service Time"}),(0,a.jsx)(to.A,{className:"h-4 w-4 text-orange-600"})]}),(0,a.jsxs)("span",{className:"text-xl font-bold text-orange-900",children:[(null==u?void 0:u.serviceHistory)&&u.serviceHistory.length>0?Math.round(u.serviceHistory.reduce((e,t)=>e+(t.cost||0),0)/u.serviceHistory.length):0,"h"]})]})]}),!m&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Last updated: ",new Date().toLocaleTimeString()]}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",children:"View Details"})]})})]})]})},sT=e=>{let{active:t,payload:s,label:l}=e;if(t&&s&&s.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"bg-white p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.vehicleName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Utilization:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.utilizationRate,"%"]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Active Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.activeDelegations})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Total Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.totalDelegations})]})]})}return null},sR=e=>e>=80?"#ef4444":e>=60?"#f59e0b":e>=40?"#10b981":e>=20?"#3b82f6":"#6b7280",sz=e=>{let{data:t,filters:s,className:l="",showLegend:r=!0,interactive:i=!0,height:c=300}=e,{data:o,isLoading:d,error:m}=sA(s),u=t||(null==o?void 0:o.utilizationMetrics),x=(0,g.useMemo)(()=>u?u.map(e=>({vehicleName:e.vehicleName,utilizationRate:Math.round(e.utilizationRate),activeDelegations:e.activeDelegations,totalDelegations:e.totalDelegations,color:sR(e.utilizationRate),displayName:e.vehicleName.length>15?"".concat(e.vehicleName.substring(0,12),"..."):e.vehicleName})).sort((e,t)=>t.utilizationRate-e.utilizationRate).slice(0,10):[],[u]);if(d)return(0,a.jsxs)(N.Zp,{className:l,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Utilization"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(m)return(0,a.jsxs)(N.Zp,{className:l,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Utilization"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:m})})]});let h=x.length>0?Math.round(x.reduce((e,t)=>e+t.utilizationRate,0)/x.length):0,p=x.filter(e=>e.utilizationRate>=80).length;return(0,a.jsxs)(N.Zp,{className:l,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Utilization"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Avg: ",h,"%"]}),p>0&&(0,a.jsxs)(v.E,{variant:"destructive",className:"text-xs",children:[p," overutilized"]})]})]})}),(0,a.jsx)(N.Wu,{children:0===x.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No vehicle utilization data available"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tw.u,{width:"100%",height:c,children:(0,a.jsxs)(so.E,{data:x,margin:{top:20,right:30,left:20,bottom:60},children:[(0,a.jsx)(sa.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(sl.W,{dataKey:"displayName",angle:-45,textAnchor:"end",height:60,fontSize:12}),(0,a.jsx)(sr.h,{domain:[0,100],tickFormatter:e=>"".concat(e,"%"),fontSize:12}),i&&(0,a.jsx)(tA.m,{content:(0,a.jsx)(sT,{})}),(0,a.jsx)(sd.y,{dataKey:"utilizationRate",radius:[4,4,0,0],name:"Utilization Rate",children:x.map((e,t)=>(0,a.jsx)(tC.f,{fill:e.color},"cell-".concat(t)))})]})}),r&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded"}),(0,a.jsx)("span",{children:"Overutilized (80%+)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-orange-500 rounded"}),(0,a.jsx)("span",{children:"High (60-79%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,a.jsx)("span",{children:"Good (40-59%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,a.jsx)("span",{children:"Moderate (20-39%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-gray-500 rounded"}),(0,a.jsx)("span",{children:"Low (0-19%)"})]})]})]})})]})};var sM=s(70831);let sF=e=>{let{compact:t=!1,item:s}=e,l=new Date(s.nextMaintenanceDate),r=new Date,i=(0,el.Y)(l,r),c=(0,ea.d)(l,r)&&(0,el.Y)(l,(0,sM.f)(r,7)),o=()=>i?(0,a.jsx)(v.E,{variant:"destructive",children:"Overdue"}):c?(0,a.jsx)(v.E,{className:"bg-orange-100 text-orange-800",variant:"secondary",children:"Due Soon"}):(0,a.jsx)(v.E,{variant:"outline",children:"Scheduled"});return(0,a.jsxs)("div",{className:(0,en.cn)("flex items-center justify-between p-3 border rounded-lg",i&&"border-red-200 bg-red-50",c&&"border-orange-200 bg-orange-50"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[i?(0,a.jsx)(G.A,{className:"size-4 text-red-600"}):c?(0,a.jsx)(to.A,{className:"size-4 text-orange-600"}):(0,a.jsx)(M.A,{className:"size-4 text-blue-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:s.maintenanceType}),!t&&o()]}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-4 text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(n.A,{className:"size-3"}),s.vehicleName]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"size-3"}),(0,er.GP)(l,"MMM dd, yyyy")]}),s.estimatedCost&&(0,a.jsxs)("span",{children:["$",s.estimatedCost.toLocaleString()]})]})]})]}),t&&(0,a.jsx)("div",{className:"text-right",children:o()})]})},sP=e=>{let{className:t="",compact:s=!1,filters:l,maxItems:r=5}=e,{data:i,error:n,isLoading:c}=sA(l),o=(null==i?void 0:i.maintenanceSchedule)||[];if(c)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(sC.A,{className:"size-5"}),"Vehicle Maintenance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(n)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(sC.A,{className:"size-5"}),"Vehicle Maintenance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:n})})]});let d=new Date,m=o.filter(e=>(0,el.Y)(new Date(e.nextMaintenanceDate),d)),u=o.filter(e=>(0,ea.d)(new Date(e.nextMaintenanceDate),d)&&(0,el.Y)(new Date(e.nextMaintenanceDate),(0,sM.f)(d,7))),x=o.sort((e,t)=>{let s=new Date(e.nextMaintenanceDate),a=new Date(t.nextMaintenanceDate),l=(0,el.Y)(s,d),r=(0,el.Y)(a,d);return l&&!r?-1:!l&&r?1:s.getTime()-a.getTime()}).slice(0,r);return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(sC.A,{className:"size-5"}),"Vehicle Maintenance"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[m.length>0&&(0,a.jsxs)(v.E,{className:"text-xs",variant:"destructive",children:[m.length," overdue"]}),u.length>0&&(0,a.jsxs)(v.E,{className:"bg-orange-100 text-xs text-orange-800",variant:"secondary",children:[u.length," due soon"]}),(0,a.jsx)(y.$,{size:"sm",variant:"ghost",children:(0,a.jsx)(tl.A,{className:"size-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-red-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-700",children:m.length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overdue"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-orange-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-700",children:u.length}),(0,a.jsx)("div",{className:"text-xs text-orange-600",children:"Due Soon"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-green-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:0}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Completed"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===x.length?(0,a.jsxs)("div",{className:"py-8 text-center text-gray-500",children:[(0,a.jsx)(sC.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No maintenance items scheduled"})]}):x.map((e,t)=>(0,a.jsx)(sF,{compact:s,item:e},"".concat(e.vehicleId,"-").concat(e.maintenanceType,"-").concat(t)))}),!s&&o.length>r&&(0,a.jsx)("div",{className:"border-t pt-4",children:(0,a.jsxs)(y.$,{className:"w-full",size:"sm",variant:"outline",children:["View All Maintenance (",o.length," total)"]})})]})]})};var sI=s(77070);let sL=e=>{let{label:t,value:s,icon:l,trend:i,variant:n="default"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[n]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[l,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:t})]}),i&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[i.isPositive?(0,a.jsx)(r.A,{className:"h-3 w-3 text-green-600"}):(0,a.jsx)(sI.A,{className:"h-3 w-3 text-red-600"}),(0,a.jsxs)(v.E,{variant:i.isPositive?"default":"destructive",className:"text-xs",children:[i.isPositive?"+":"",i.value,"%"]})]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:s})})]})},sO=e=>{let{active:t,payload:s}=e;if(t&&s&&s.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"bg-white p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Cost:"," ",(0,a.jsxs)("span",{className:"font-medium",children:["$",e.value.toLocaleString()]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},sW=e=>{let{filters:t,className:s="",showTrend:i=!0,compact:n=!1}=e,{data:c,isLoading:o,error:d}=sA(t),m=null==c?void 0:c.costAnalysis,u=(0,g.useMemo)(()=>{if(!(null==m?void 0:m.costByType))return[];let e=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6"];return m.costByType.map((t,s)=>({name:t.type,value:t.cost,percentage:Math.round(t.cost/m.totalCost*100),color:e[s%e.length]}))},[m]),x=(0,g.useMemo)(()=>(null==m?void 0:m.monthlyTrend)?m.monthlyTrend.map((e,t)=>({month:(0,er.GP)(new Date(e.month),"MMM"),cost:e.cost,services:t+1})):[],[m]);if(o)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eH.A,{className:"h-5 w-5"}),"Cost Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(d)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eH.A,{className:"h-5 w-5"}),"Cost Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:d})})]});let h=(null==m?void 0:m.totalCost)||0,p=(null==m?void 0:m.averageCostPerService)||0,j=h>0?Math.min(h/1e4*100,100):0,f=12*h;return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eH.A,{className:"h-5 w-5"}),"Cost Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["$",h.toLocaleString()," total"]}),j>90&&(0,a.jsx)(v.E,{variant:"destructive",className:"text-xs",children:"Budget Alert"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(tl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(sL,{label:"Total Cost",value:"$".concat(h.toLocaleString()),icon:(0,a.jsx)(eH.A,{className:"h-4 w-4"})}),(0,a.jsx)(sL,{label:"Avg/Service",value:"$".concat(Math.round(p)),icon:(0,a.jsx)(l.A,{className:"h-4 w-4"})}),(0,a.jsx)(sL,{label:"Budget Used",value:"".concat(Math.round(j),"%"),icon:(0,a.jsx)(eq.A,{className:"h-4 w-4"}),variant:j>90?"destructive":j>75?"warning":"success"}),(0,a.jsx)(sL,{label:"Projected Annual",value:"$".concat(Math.round(f).toLocaleString()),icon:(0,a.jsx)(r.A,{className:"h-4 w-4"})})]}),!n&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Cost by Service Type"}),u.length>0?(0,a.jsx)(tw.u,{width:"100%",height:200,children:(0,a.jsxs)(tk.r,{children:[(0,a.jsx)(tS.F,{data:u,cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",label:e=>{let{name:t,percentage:s}=e;return"".concat(t,": ").concat(s,"%")},labelLine:!1,children:u.map((e,t)=>(0,a.jsx)(tC.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(tA.m,{content:(0,a.jsx)(sO,{})})]})}):(0,a.jsx)("div",{className:"h-48 flex items-center justify-center text-gray-500",children:"No cost breakdown data available"})]}),i&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Monthly Cost Trend"}),x.length>0?(0,a.jsx)(tw.u,{width:"100%",height:200,children:(0,a.jsxs)(ss.b,{data:x,children:[(0,a.jsx)(sa.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(sl.W,{dataKey:"month",fontSize:12}),(0,a.jsx)(sr.h,{tickFormatter:e=>"$".concat(e),fontSize:12}),(0,a.jsx)(tA.m,{formatter:e=>["$".concat(e.toLocaleString()),"Cost"],labelFormatter:e=>"Month: ".concat(e)}),(0,a.jsx)(sn.N,{type:"monotone",dataKey:"cost",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4}})]})}):(0,a.jsx)("div",{className:"h-48 flex items-center justify-center text-gray-500",children:"No trend data available"})]})]}),j>90&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(G.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Budget Alert"})]}),(0,a.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:["You've used ",Math.round(j),"% of your maintenance budget. Consider reviewing upcoming expenses."]})]})]})]})};var sV=s(57804);let sB=e=>(0,e4.Sk)(["employee-analytics",e],async()=>{let t=new URLSearchParams;if(null==e?void 0:e.dateRange){let s=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);t.append("dateRange.from",s.toISOString().split("T")[0]||s.toISOString()),t.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}(null==e?void 0:e.employees)&&e.employees.forEach(e=>t.append("employees",e.toString())),(null==e?void 0:e.locations)&&e.locations.forEach(e=>t.append("locations",e)),(null==e?void 0:e.includeEmployeeMetrics)&&t.append("includeEmployeeMetrics","true");let s="/reporting/employees/analytics".concat(t.toString()?"?".concat(t.toString()):""),a=await e5.uE.get(s);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),sU=e=>{let{label:t,value:s,icon:l,trend:r,variant:i="default"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[i]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[l,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:t})]}),r&&(0,a.jsxs)(v.E,{variant:r.isPositive?"default":"destructive",className:"text-xs",children:[r.isPositive?"+":"",r.value,"%"]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:s})})]})},sY=e=>{var t,s,l,i,n,o;let{filters:d,className:m="",showExportOptions:u=!0,compact:x=!1}=e,{data:h,isLoading:g,error:j}=sB(d);if(g)return(0,a.jsxs)(N.Zp,{className:m,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(j)return(0,a.jsxs)(N.Zp,{className:m,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:j})})]});let f=(null==h?void 0:h.totalCount)||0,b=(null==h||null==(t=h.performanceMetrics)?void 0:t.filter(e=>e.completedDelegations>0||e.completedTasks>0).length)||0,w=(null==h||null==(s=h.performanceMetrics)?void 0:s.length)&&h.performanceMetrics.length>0?Math.round(h.performanceMetrics.reduce((e,t)=>e+t.averageRating,0)/h.performanceMetrics.length):0,k=(null==h||null==(l=h.taskAssignments)?void 0:l.reduce((e,t)=>e+t.completedTasks,0))||0,S=(null==h||null==(i=h.taskAssignments)?void 0:i.reduce((e,t)=>e+t.overdueTasksCount,0))||0,C=(null==h||null==(n=h.availabilityMetrics)?void 0:n.length)&&h.availabilityMetrics.length>0?Math.round(h.availabilityMetrics.reduce((e,t)=>e+t.utilizationRate,0)/h.availabilityMetrics.length):0;return(0,a.jsxs)(N.Zp,{className:m,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[f," employees"]}),S>0&&(0,a.jsxs)(v.E,{variant:"destructive",className:"text-xs",children:[S," overdue"]}),u&&(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(tl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(sU,{label:"Total Employees",value:f,icon:(0,a.jsx)(c.A,{className:"h-4 w-4"})}),(0,a.jsx)(sU,{label:"Active Employees",value:b,icon:(0,a.jsx)(sV.A,{className:"h-4 w-4"}),variant:b<.8*f?"warning":"success"}),(0,a.jsx)(sU,{label:"Avg Performance",value:"".concat(w,"/10"),icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),variant:w<7?"warning":"success"}),(0,a.jsx)(sU,{label:"Utilization Rate",value:"".concat(C,"%"),icon:(0,a.jsx)(to.A,{className:"h-4 w-4"}),variant:C<70?"warning":C>90?"destructive":"success"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Completed Tasks"}),(0,a.jsx)(q.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-blue-900",children:k})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-red-700",children:"Overdue Tasks"}),(0,a.jsx)(G.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-red-900",children:S})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-green-700",children:"On-Time Rate"}),(0,a.jsx)(to.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)("span",{className:"text-xl font-bold text-green-900",children:[(null==h||null==(o=h.performanceMetrics)?void 0:o.length)&&h.performanceMetrics.length>0?Math.round(h.performanceMetrics.reduce((e,t)=>e+t.onTimePerformance,0)/h.performanceMetrics.length):0,"%"]})]})]}),!x&&(null==h?void 0:h.workloadDistribution)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Workload Distribution"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-700",children:h.workloadDistribution.filter(e=>"Underutilized"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Underutilized"})]}),(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-blue-700",children:h.workloadDistribution.filter(e=>"Optimal"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Optimal"})]}),(0,a.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-red-700",children:h.workloadDistribution.filter(e=>"Overloaded"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overloaded"})]})]})]}),!x&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Last updated: ",new Date().toLocaleTimeString()]}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",children:"View Details"})]})})]})]})};var sZ=s(91467),s$=s(91394);let s_=e=>{let{active:t,label:s,payload:l}=e;if(t&&(null==l?void 0:l.length)){let e=l[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.employeeName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Performance Score:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.averageRating,"/10"]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Completed Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.completedDelegations})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Completed Tasks:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.completedTasks})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["On-Time Rate:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.onTimePerformance,"%"]})]})]})}return null},sG=e=>e>=9?"#10b981":e>=8?"#3b82f6":e>=7?"#f59e0b":e>=6?"#ef4444":"#6b7280",sq=e=>{let{className:t="",data:s,filters:l,height:r=300,interactive:i=!0,maxEmployees:n=10,showLegend:o=!0}=e,{data:d,error:m,isLoading:u}=sB(l),x=s||(null==d?void 0:d.performanceMetrics),h=(0,g.useMemo)(()=>x?x.map(e=>({averageRating:e.averageRating,color:sG(e.averageRating),completedDelegations:e.completedDelegations,completedTasks:e.completedTasks,displayName:e.employeeName.length>12?"".concat(e.employeeName.slice(0,9),"..."):e.employeeName,employeeName:e.employeeName,onTimePerformance:e.onTimePerformance,overallScore:Math.round(.4*e.averageRating+.01*e.onTimePerformance*3+.3*e.workloadScore),workloadScore:e.workloadScore})).sort((e,t)=>t.overallScore-e.overallScore).slice(0,n):[],[x,n]);if(u)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-5"}),"Employee Performance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(m)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-5"}),"Employee Performance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:m})})]});let p=h.length>0?Math.round(h.reduce((e,t)=>e+t.averageRating,0)/h.length*10)/10:0,j=h.filter(e=>e.averageRating>=8).length;return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-5"}),"Employee Performance"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{className:"text-xs",variant:"secondary",children:["Avg: ",p,"/10"]}),j>0&&(0,a.jsxs)(v.E,{className:"text-xs",variant:"default",children:[(0,a.jsx)(sZ.A,{className:"mr-1 size-3"}),j," top performers"]})]})]})}),(0,a.jsx)(N.Wu,{children:0===h.length?(0,a.jsx)("div",{className:"flex h-64 items-center justify-center text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No employee performance data available"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tw.u,{height:r,width:"100%",children:(0,a.jsxs)(so.E,{data:h,margin:{bottom:60,left:20,right:30,top:20},children:[(0,a.jsx)(sa.d,{stroke:"#f0f0f0",strokeDasharray:"3 3"}),(0,a.jsx)(sl.W,{angle:-45,dataKey:"displayName",fontSize:12,height:60,textAnchor:"end"}),(0,a.jsx)(sr.h,{domain:[0,10],fontSize:12,tickFormatter:e=>"".concat(e,"/10")}),i&&(0,a.jsx)(tA.m,{content:(0,a.jsx)(s_,{})}),(0,a.jsx)(sd.y,{dataKey:"averageRating",name:"Performance Score",radius:[4,4,0,0],children:h.map((e,t)=>(0,a.jsx)(tC.f,{fill:e.color},"cell-".concat(t)))})]})}),o&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-green-500"}),(0,a.jsx)("span",{children:"Excellent (9-10)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-blue-500"}),(0,a.jsx)("span",{children:"Good (8-8.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-orange-500"}),(0,a.jsx)("span",{children:"Average (7-7.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-red-500"}),(0,a.jsx)("span",{children:"Below Average (6-6.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-gray-500"}),(0,a.jsx)("span",{children:"Poor (<6)"})]})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Top Performers"}),(0,a.jsx)("div",{className:"space-y-2",children:h.slice(0,3).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded bg-gray-50 p-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.E,{className:"flex size-6 items-center justify-center p-0 text-xs",variant:"outline",children:t+1}),(0,a.jsx)(s$.eu,{className:"size-6",children:(0,a.jsx)(s$.q5,{className:"text-xs",children:e.employeeName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.employeeName})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-bold",children:[e.averageRating,"/10"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.onTimePerformance,"% on-time"]})]})]},e.employeeName))})]})]})})]})},sJ=e=>{let{item:t,compact:s=!1}=e,l=e=>{switch(e){case"Underutilized":return"text-blue-600 bg-blue-100";case"Optimal":return"text-green-600 bg-green-100";case"Overloaded":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}};return"Overloaded"===t.status||t.status,(0,a.jsxs)("div",{className:(0,en.cn)("flex items-center justify-between p-3 border rounded-lg","Overloaded"===t.status&&"border-red-200 bg-red-50","Optimal"===t.status&&"border-green-200 bg-green-50","Underutilized"===t.status&&"border-blue-200 bg-blue-50"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 flex-1",children:[(0,a.jsx)(s$.eu,{className:"h-8 w-8",children:(0,a.jsx)(s$.q5,{className:"text-xs",children:t.employeeName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:t.employeeName}),!s&&(0,a.jsx)(v.E,{className:(0,en.cn)("text-xs",l(t.status)),children:t.status})]}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Workload: ",t.currentWorkload,"/",t.capacity]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[t.workloadPercentage,"%"]})]}),(0,a.jsx)(sN.k,{value:t.workloadPercentage,className:"h-2 mt-1",style:{"--progress-background":"Overloaded"===t.status?"#ef4444":"Optimal"===t.status?"#10b981":"#3b82f6"}})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(e=>{switch(e){case"Underutilized":return(0,a.jsx)(to.A,{className:"h-4 w-4 text-blue-600"});case"Optimal":return(0,a.jsx)(q.A,{className:"h-4 w-4 text-green-600"});case"Overloaded":return(0,a.jsx)(G.A,{className:"h-4 w-4 text-red-600"});default:return(0,a.jsx)(c.A,{className:"h-4 w-4 text-gray-600"})}})(t.status),s&&(0,a.jsxs)(v.E,{className:(0,en.cn)("text-xs",l(t.status)),children:[t.workloadPercentage,"%"]})]})]})},sH=e=>{let{filters:t,className:s="",compact:l=!1,maxItems:r=8}=e,{data:i,isLoading:n,error:o}=sB(t),d=(null==i?void 0:i.workloadDistribution)||[];if(n)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Workload"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(o)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Workload"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:o})})]});let m=d.filter(e=>"Overloaded"===e.status),u=d.filter(e=>"Underutilized"===e.status),x=d.filter(e=>"Optimal"===e.status),h=[...m,...x,...u].slice(0,r),p=d.length>0?Math.round(d.reduce((e,t)=>e+t.workloadPercentage,0)/d.length):0;return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Workload"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Avg: ",p,"%"]}),m.length>0&&(0,a.jsxs)(v.E,{variant:"destructive",className:"text-xs",children:[m.length," overloaded"]}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(tl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:u.length}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Underutilized"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:x.length}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Optimal"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-700",children:m.length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overloaded"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===h.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No workload data available"})]}):h.map((e,t)=>(0,a.jsx)(sJ,{item:e,compact:l},"".concat(e.employeeId,"-").concat(t)))}),m.length>0&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(G.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Workload Alert"})]}),(0,a.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:[m.length," employee",m.length>1?"s are":" is"," overloaded. Consider redistributing tasks or adjusting schedules."]})]}),!l&&d.length>r&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)(y.$,{variant:"outline",size:"sm",className:"w-full",children:["View All Employees (",d.length," total)"]})})]})]})};var sQ=s(83548),sK=s(63807);let sX=e=>(0,e4.Sk)(["cross-entity-analytics",e],async()=>{let t=new URLSearchParams;if(null==e?void 0:e.dateRange){let s=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);t.append("dateRange.from",s.toISOString().split("T")[0]||s.toISOString()),t.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}(null==e?void 0:e.employees)&&e.employees.forEach(e=>t.append("employees",e.toString())),(null==e?void 0:e.vehicles)&&e.vehicles.forEach(e=>t.append("vehicles",e.toString())),(null==e?void 0:e.locations)&&e.locations.forEach(e=>t.append("locations",e));let s="/reporting/cross-entity/analytics".concat(t.toString()?"?".concat(t.toString()):""),a=await e5.uE.get(s);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),s0=e=>{let{active:t,payload:s}=e;if(t&&(null==s?void 0:s.length)){let e=s[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["X-Axis: ",(0,a.jsx)("span",{className:"font-medium",children:e.x})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Y-Axis: ",(0,a.jsx)("span",{className:"font-medium",children:e.y})]}),e.correlation&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Correlation: ",(0,a.jsx)("span",{className:"font-medium",children:e.correlation})]})]})}return null},s1=e=>{let{description:t,icon:s,title:l,value:r}=e;return(0,a.jsxs)("div",{className:"rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[s,(0,a.jsx)("span",{className:"text-sm font-medium",children:l})]}),(0,a.jsx)(v.E,{className:(0,en.cn)("text-xs",(e=>{let t=Math.abs(e);return t>=.8?"text-green-600 bg-green-100":t>=.6?"text-blue-600 bg-blue-100":t>=.4?"text-orange-600 bg-orange-100":"text-gray-600 bg-gray-100"})(r)),children:(e=>{let t=Math.abs(e);return t>=.8?"Strong":t>=.6?"Moderate":t>=.4?"Weak":"Very Weak"})(r)})]}),(0,a.jsx)("div",{className:"mb-1 text-2xl font-bold",children:r.toFixed(3)}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:t})]})},s2=e=>{var t;let{className:s="",correlationType:l="all",filters:n,interactive:d=!0}=e,{data:m,error:u,isLoading:x}=sX(n),h=(0,g.useMemo)(()=>{var e,t,s,a;if(!(null==m?void 0:m.correlations))return[];switch(l){case"employee-vehicle":return(null==(e=m.correlations.employeeVehicle)?void 0:e.map((e,t)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:"".concat(e.employeeName," - ").concat(e.vehicleName),x:e.employeePerformance,y:e.vehicleUtilization})))||[];case"performance-workload":return(null==(t=m.correlations.performanceWorkload)?void 0:t.map((e,t)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.employeeName,x:e.workloadPercentage,y:e.performanceScore})))||[];case"task-delegation":return(null==(s=m.correlations.taskDelegation)?void 0:s.map((e,t)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.taskType,x:e.taskComplexity,y:e.delegationSuccess})))||[];default:return(null==(a=m.correlations.overall)?void 0:a.map((e,t)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.entityName,x:e.xValue,y:e.yValue})))||[]}},[m,l]);if(x)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"size-5"}),"Cross-Entity Correlations"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(u)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"size-5"}),"Cross-Entity Correlations"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:u})})]});let p=null!=(t=null==m?void 0:m.metrics)?t:{employeeVehicle:0,overallEfficiency:0,performanceWorkload:0,taskDelegation:0};return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"size-5"}),"Cross-Entity Correlations"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{className:"text-xs",variant:"secondary",children:[h.length," relationships"]}),(0,a.jsx)(y.$,{size:"sm",variant:"ghost",children:(0,a.jsx)(tl.A,{className:"size-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,a.jsx)(s1,{description:"Performance vs Vehicle Usage",icon:(0,a.jsx)(c.A,{className:"size-4"}),title:"Employee-Vehicle",value:p.employeeVehicle||0}),(0,a.jsx)(s1,{description:"Task Complexity vs Success",icon:(0,a.jsx)(i.A,{className:"size-4"}),title:"Task-Delegation",value:p.taskDelegation||0}),(0,a.jsx)(s1,{description:"Workload vs Performance",icon:(0,a.jsx)(r.A,{className:"size-4"}),title:"Performance-Workload",value:p.performanceWorkload||0}),(0,a.jsx)(s1,{description:"System-wide Correlation",icon:(0,a.jsx)(o.A,{className:"size-4"}),title:"Overall Efficiency",value:p.overallEfficiency||0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-3 text-sm font-medium",children:"Relationship Visualization"}),h.length>0?(0,a.jsx)(tw.u,{height:300,width:"100%",children:(0,a.jsxs)(sQ.t,{data:h,margin:{bottom:20,left:20,right:30,top:20},children:[(0,a.jsx)(sa.d,{stroke:"#f0f0f0",strokeDasharray:"3 3"}),(0,a.jsx)(sl.W,{dataKey:"x",fontSize:12,name:"X-Axis",type:"number"}),(0,a.jsx)(sr.h,{dataKey:"y",fontSize:12,name:"Y-Axis",type:"number"}),d&&(0,a.jsx)(tA.m,{content:(0,a.jsx)(s0,{})}),(0,a.jsx)(sK.X,{dataKey:"y",fill:"#3b82f6",children:h.map((e,t)=>(0,a.jsx)(tC.f,{fill:e.color},"cell-".concat(t)))})]})}):(0,a.jsx)("div",{className:"flex h-64 items-center justify-center text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No correlation data available"})]})})]}),(null==m?void 0:m.insights)&&m.insights.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Key Insights"}),(0,a.jsx)("div",{className:"space-y-2",children:m.insights.slice(0,3).map((e,t)=>(0,a.jsx)("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(r.A,{className:"mt-0.5 size-4 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-800",children:e.title}),(0,a.jsx)("p",{className:"mt-1 text-xs text-blue-700",children:e.description})]})]})},t))})]})]})]})};var s4=s(19968);let s5=e=>{let{entity:t,size:s="md"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("border rounded-lg flex items-center gap-2",(e=>{switch(e){case"employee":return"bg-blue-100 text-blue-700 border-blue-200";case"vehicle":return"bg-green-100 text-green-700 border-green-200";case"task":return"bg-orange-100 text-orange-700 border-orange-200";case"delegation":return"bg-purple-100 text-purple-700 border-purple-200";default:return"bg-gray-100 text-gray-700 border-gray-200"}})(t.type),{sm:"p-2 text-xs",md:"p-3 text-sm",lg:"p-4 text-base"}[s]),children:[(e=>{switch(e){case"employee":return(0,a.jsx)(c.A,{className:"h-4 w-4"});case"vehicle":return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"task":return(0,a.jsx)(i.A,{className:"h-4 w-4"});case"delegation":return(0,a.jsx)(d.A,{className:"h-4 w-4"});default:return(0,a.jsx)(o.A,{className:"h-4 w-4"})}})(t.type),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"font-medium truncate",children:t.name}),(0,a.jsxs)("div",{className:"text-xs opacity-75",children:[t.connections," connections"]})]}),(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[Math.round(100*t.strength),"%"]})]})},s3=e=>{let{from:t,to:s,strength:l,type:r}=e;return(0,a.jsx)("div",{className:(0,en.cn)("p-3 border rounded-lg",(e=>e>=.8?"border-green-500 bg-green-50":e>=.6?"border-blue-500 bg-blue-50":e>=.4?"border-orange-500 bg-orange-50":"border-gray-500 bg-gray-50")(l)),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[(0,a.jsx)(s5,{entity:t,size:"sm"}),(0,a.jsx)(s4.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)(s5,{entity:s,size:"sm"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[Math.round(100*l),"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:r})]})]})})},s6=e=>{let{filters:t,className:s="",maxConnections:l=10,showDetails:r=!0}=e,{data:i,isLoading:n,error:c}=sX(t),{entities:d,connections:m}=(0,g.useMemo)(()=>{var e,t;if(!(null==i?void 0:i.network))return{entities:[],connections:[]};let s=(null==(e=i.network.nodes)?void 0:e.map(e=>({id:e.id,name:e.name,type:e.type,connections:e.connectionCount||0,strength:e.strength||0})))||[],a=(null==(t=i.network.edges)?void 0:t.slice(0,l).map(e=>({from:s.find(t=>t.id===e.from)||s[0],to:s.find(t=>t.id===e.to)||s[0],strength:e.weight||0,type:e.type||"related"})))||[];return{entities:s,connections:a}},[i,l]);if(n)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"Entity Relationship Network"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(c)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"Entity Relationship Network"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:c})})]});let u=d.length,x=m.length,h=m.length>0?m.reduce((e,t)=>e+t.strength,0)/m.length:0,p=d.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{});return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"Entity Relationship Network"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[u," entities"]}),(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[x," connections"]}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(tl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:p.employee||0}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Employees"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:p.vehicle||0}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Vehicles"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-orange-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-700",children:p.task||0}),(0,a.jsx)("div",{className:"text-xs text-orange-600",children:"Tasks"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-700",children:p.delegation||0}),(0,a.jsx)("div",{className:"text-xs text-purple-600",children:"Delegations"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Most Connected Entities"}),(0,a.jsx)("div",{className:"space-y-2",children:d.sort((e,t)=>t.connections-e.connections).slice(0,5).map((e,t)=>(0,a.jsx)(s5,{entity:e},e.id))})]}),r&&m.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Strongest Connections"}),(0,a.jsx)("div",{className:"space-y-3",children:m.sort((e,t)=>t.strength-e.strength).slice(0,5).filter(e=>e.from&&e.to).map((e,t)=>(0,a.jsx)(s3,{from:e.from,to:e.to,strength:e.strength,type:e.type},"".concat(e.from.id,"-").concat(e.to.id,"-").concat(t)))})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Network Health"}),(0,a.jsx)(v.E,{variant:h>.7?"default":h>.4?"secondary":"destructive",className:"text-xs",children:h>.7?"Strong":h>.4?"Moderate":"Weak"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:["Average connection strength:"," ",Math.round(100*h),"%"]})]})]})]})},s8={entityType:"reporting",title:"Reporting Dashboard",description:"Comprehensive reporting and analytics dashboard",viewModes:["grid","list"],defaultViewMode:"grid",tabs:[{id:"overview",label:"Overview",icon:(0,a.jsx)(l.A,{className:"h-4 w-4"}),description:"High-level analytics and status distribution.",widgets:[{id:"delegation-status",component:st,span:"lg:col-span-1"},{id:"delegation-trend",component:sc,span:"lg:col-span-2"},{id:"location-distribution",component:sx,span:"lg:col-span-2"},{id:"task-metrics",component:sw,span:"lg:col-span-1"}]},{id:"details",label:"Detailed Report",icon:(0,a.jsx)(u.A,{className:"h-4 w-4"}),description:"In-depth data table with filtering and sorting.",widgets:[{id:"reporting-table",component:()=>{var e,t,s;let l=t4(),[r,i]=g.useState({page:1,pageSize:10}),n=(0,sk.useRouter)(),{toast:c}=(0,sS.dj)(),{data:o,error:d,isLoading:m}=tW(l,r),u=e=>{n.push("/delegations/".concat(e.id))},x=[{accessorKey:"delegationId",header:"Delegation ID"},{accessorKey:"customerName",header:"Customer"},{accessorKey:"vehicleModel",header:"Vehicle"},{accessorKey:"status",cell:e=>{let{row:t}=e;return(0,a.jsx)(v.E,{children:t.original.status})},header:"Status"},{accessorKey:"location",header:"Location"},{accessorKey:"createdAt",cell:e=>{let{row:t}=e;return(0,er.GP)(new Date(t.original.createdAt),"PPpp")},header:"Date"},{cell:e=>{let{row:t}=e;return(0,a.jsxs)(y.$,{size:"sm",variant:"outline",onClick:()=>u(t.original),className:"flex items-center gap-1",children:[(0,a.jsx)(eb.A,{className:"h-3 w-3"}),"View"]})},id:"actions",header:"Actions"}],h=(0,tp.N4)({columns:x,data:null!=(e=null==o?void 0:o.data)?e:[],getCoreRowModel:(0,tg.HT)(),manualPagination:!0,rowCount:null!=(t=null==o?void 0:o.meta.total)?t:0});return d?(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:d.message})]}):(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(N.ZB,{children:"Detailed Report"}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:()=>{if(!(null==o?void 0:o.data.length))return void c({title:"No Data",description:"No data available to export.",variant:"destructive"});let e=new Blob([["ID,Customer,Vehicle,Status,Location,Created At,Completed At",...o.data.map(e=>[e.delegationId,e.customerName,e.vehicleModel,e.status,e.location,e.createdAt,e.completedAt||"N/A"].join(","))].join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="delegation-report-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(t),c({title:"Export Successful",description:"Delegation data has been exported to CSV."})},disabled:!(null==o?void 0:o.data.length),className:"flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"h-3 w-3"}),"Export"]})})]})}),(0,a.jsxs)(N.Wu,{children:[(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(th.XI,{children:[(0,a.jsx)(th.A0,{children:h.getHeaderGroups().map(e=>(0,a.jsx)(th.Hj,{children:e.headers.map(e=>(0,a.jsx)(th.nd,{children:(0,tp.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(th.BF,{children:m?Array.from({length:r.pageSize}).map((e,t)=>(0,a.jsx)(th.Hj,{children:x.map((e,s)=>{var l;return(0,a.jsx)(th.nA,{children:(0,a.jsx)(t7.E,{className:"h-6 w-full"})},"loading-cell-".concat(t,"-").concat(null!=(l=e.id)?l:s))})},"loading-row-".concat(t))):h.getRowModel().rows.map(e=>(0,a.jsx)(th.Hj,{children:e.getVisibleCells().map(e=>(0,a.jsx)(th.nA,{children:(0,tp.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Page ",null==o?void 0:o.meta.page," of ",null==o?void 0:o.meta.totalPages," (",null==o?void 0:o.meta.total," total records)"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.$,{disabled:1===r.page,onClick:()=>i(e=>({...e,page:e.page-1})),size:"sm",variant:"outline",children:"Previous"}),(0,a.jsx)(y.$,{disabled:r.page>=(null!=(s=null==o?void 0:o.meta.totalPages)?s:1),onClick:()=>i(e=>({...e,page:e.page+1})),size:"sm",variant:"outline",children:"Next"})]})]})]})]})},span:"lg:col-span-3 xl:col-span-4"}]}]};s8.tabs.flatMap(e=>e.widgets),Date.now(),Date.now(),new Date().getFullYear();let s7=e=>{let{compact:t=!1,className:s=""}=e,l=t4(),{setDateRange:r}=t5(),{validationErrors:i}=t6(),[n,c]=g.useState(!1),o=e=>{(null==e?void 0:e.from)&&(null==e?void 0:e.to)&&(r(e.from,e.to),c(!1))},d=[{label:"Last 7 days",getValue:()=>({from:new Date(Date.now()-6048e5),to:new Date})},{label:"Last 30 days",getValue:()=>({from:new Date(Date.now()-2592e6),to:new Date})},{label:"Last 90 days",getValue:()=>({from:new Date(Date.now()-7776e6),to:new Date})},{label:"This year",getValue:()=>({from:new Date(new Date().getFullYear(),0,1),to:new Date})}],m=e=>{r(e.from,e.to),c(!1)},u=()=>{let{from:e,to:s}=l.dateRange;return e&&s?t?"".concat((0,er.GP)(e,"MMM d")," - ").concat((0,er.GP)(s,"MMM d")):"".concat((0,er.GP)(e,"MMM d, yyyy")," - ").concat((0,er.GP)(s,"MMM d, yyyy")):"Select date range"},x=i.dateRange;return t?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",s),children:[(0,a.jsx)(A.J,{className:"text-xs font-medium",children:"Date Range"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-start text-left font-normal",!l.dateRange.from&&"text-muted-foreground",x&&"border-red-500"),children:[(0,a.jsx)(M.A,{className:"mr-2 h-4 w-4"}),u()]})}),(0,a.jsx)(Z.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2 mb-3",children:d.map(e=>(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>m(e.getValue()),className:"text-xs",children:e.label},e.label))}),(0,a.jsx)(Y.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:o,numberOfMonths:1,className:"rounded-md border"})]})})]}),x&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:x})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",s),children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Date Range"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-start text-left font-normal",!l.dateRange.from&&"text-muted-foreground",x&&"border-red-500"),children:[(0,a.jsx)(M.A,{className:"mr-2 h-4 w-4"}),u()]})}),(0,a.jsx)(Z.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Quick Ranges"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:d.map(e=>(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>m(e.getValue()),className:"text-sm",children:e.label},e.label))})]}),(0,a.jsx)(Y.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:o,numberOfMonths:2,className:"rounded-md border"})]})})]}),x&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:x})]})};var s9=s(79556);let ae=e=>{let{compact:t=!1,className:s=""}=e,l=t4(),{setStatus:r}=t5(),{validationErrors:i}=t6(),[n,c]=g.useState(!1),o=[{value:"DRAFT",label:"Draft",color:"bg-gray-100 text-gray-800"},{value:"PENDING",label:"Pending",color:"bg-yellow-100 text-yellow-800"},{value:"APPROVED",label:"Approved",color:"bg-blue-100 text-blue-800"},{value:"IN_PROGRESS",label:"In Progress",color:"bg-purple-100 text-purple-800"},{value:"COMPLETED",label:"Completed",color:"bg-green-100 text-green-800"},{value:"CANCELLED",label:"Cancelled",color:"bg-red-100 text-red-800"}],d=e=>{let t=l.status;t.includes(e)?r(t.filter(t=>t!==e)):r([...t,e])},m=()=>{r(o.map(e=>e.value))},u=()=>{r([])},x=()=>{let e=l.status.length;if(0===e)return"All statuses";if(1===e){let e=o.find(e=>e.value===l.status[0]);return(null==e?void 0:e.label)||"Unknown"}return"".concat(e," statuses")},h=i.status;return t?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",s),children:[(0,a.jsx)(A.J,{className:"text-xs font-medium",children:"Status"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",h&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:x()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-56 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:m,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:u,children:"None"})]}),(0,a.jsx)("div",{className:"space-y-2",children:o.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{id:"status-".concat(e.value),checked:l.status.includes(e.value),onCheckedChange:()=>d(e.value)}),(0,a.jsx)(A.J,{htmlFor:"status-".concat(e.value),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsx)(v.E,{variant:"secondary",className:(0,en.cn)("text-xs",e.color),children:e.label})})]},e.value))})]})})]}),h&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:h})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",s),children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Status"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",h&&"border-red-500"),children:[(0,a.jsx)("span",{children:x()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Statuses"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:m,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:u,children:"None"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:o.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(S.S,{id:"status-".concat(e.value),checked:l.status.includes(e.value),onCheckedChange:()=>d(e.value)}),(0,a.jsx)(A.J,{htmlFor:"status-".concat(e.value),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsx)(v.E,{variant:"secondary",className:(0,en.cn)("text-sm",e.color),children:e.label})})]},e.value))})]})})]}),l.status.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:l.status.map(e=>{let t=o.find(t=>t.value===e);return t?(0,a.jsxs)(v.E,{variant:"secondary",className:(0,en.cn)("text-xs pr-1",t.color),children:[t.label,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>d(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e):null})}),h&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:h})]})},at=e=>{let{compact:t=!1,className:s=""}=e,l=t4(),{setLocations:r}=t5(),{validationErrors:i}=t6(),[n,c]=g.useState(!1),[o,d]=g.useState(""),m=["New York, NY","Los Angeles, CA","Chicago, IL","Houston, TX","Phoenix, AZ","Philadelphia, PA","San Antonio, TX","San Diego, CA","Dallas, TX","San Jose, CA","Austin, TX","Jacksonville, FL","Fort Worth, TX","Columbus, OH","Charlotte, NC","San Francisco, CA","Indianapolis, IN","Seattle, WA","Denver, CO","Washington, DC"].filter(e=>e.toLowerCase().includes(o.toLowerCase())),u=e=>{let t=l.locations;t.includes(e)?r(t.filter(t=>t!==e)):r([...t,e])},x=()=>{r(m)},h=()=>{r([])},p=()=>{let e=l.locations.length;return 0===e?"All locations":1===e?l.locations[0]:"".concat(e," locations")},j=i.locations;return t?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",s),children:[(0,a.jsx)(A.J,{className:"text-xs font-medium",children:"Location"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",j&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:p()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(ex.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search locations...",value:o,onChange:e=>d(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:x,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:m.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{id:"location-".concat(e),checked:l.locations.includes(e),onCheckedChange:()=>u(e)}),(0,a.jsxs)(A.J,{htmlFor:"location-".concat(e),className:"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2",children:[(0,a.jsx)(sm.A,{className:"h-3 w-3 text-muted-foreground"}),e]})]},e))})]})})]}),j&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:j})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",s),children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Locations"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",j&&"border-red-500"),children:[(0,a.jsx)("span",{children:p()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Locations"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:x,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(ex.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search locations...",value:o,onChange:e=>d(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:m.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(S.S,{id:"location-".concat(e),checked:l.locations.includes(e),onCheckedChange:()=>u(e)}),(0,a.jsxs)(A.J,{htmlFor:"location-".concat(e),className:"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2",children:[(0,a.jsx)(sm.A,{className:"h-4 w-4 text-muted-foreground"}),e]})]},e))})]})})]}),l.locations.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.locations.slice(0,3).map(e=>(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs pr-1",children:[e.length>15?"".concat(e.slice(0,15),"..."):e,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>u(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e)),l.locations.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",l.locations.length-3," more"]})]}),j&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:j})]})},as=e=>{let{compact:t=!1,className:s=""}=e,l=t4(),{setEmployees:r}=t5(),{validationErrors:i}=t6(),[n,c]=g.useState(!1),[o,d]=g.useState(""),m=[{id:1,name:"John Smith",department:"Operations"},{id:2,name:"Sarah Johnson",department:"Sales"},{id:3,name:"Mike Davis",department:"Engineering"},{id:4,name:"Emily Brown",department:"Marketing"},{id:5,name:"David Wilson",department:"Operations"},{id:6,name:"Lisa Anderson",department:"HR"},{id:7,name:"Tom Miller",department:"Finance"},{id:8,name:"Anna Garcia",department:"Sales"},{id:9,name:"Chris Taylor",department:"Engineering"},{id:10,name:"Jessica Lee",department:"Marketing"}],u=m.filter(e=>e.name.toLowerCase().includes(o.toLowerCase())||e.department.toLowerCase().includes(o.toLowerCase())),x=e=>{let t=l.employees;t.includes(e)?r(t.filter(t=>t!==e)):r([...t,e])},h=()=>{r(u.map(e=>e.id))},p=()=>{r([])},j=()=>{let e=l.employees.length;if(0===e)return"All employees";if(1===e){let e=m.find(e=>e.id===l.employees[0]);return(null==e?void 0:e.name)||"Unknown"}return"".concat(e," employees")},f=i.employees;return t?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",s),children:[(0,a.jsx)(A.J,{className:"text-xs font-medium",children:"Employee"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",f&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:j()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(ex.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search employees...",value:o,onChange:e=>d(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:u.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{id:"employee-".concat(e.id),checked:l.employees.includes(e.id),onCheckedChange:()=>x(e.id)}),(0,a.jsx)(A.J,{htmlFor:"employee-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-3 w-3 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.department})]})]})})]},e.id))})]})})]}),f&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:f})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",s),children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Employees"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",f&&"border-red-500"),children:[(0,a.jsx)("span",{children:j()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Employees"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(ex.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search employees...",value:o,onChange:e=>d(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:u.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(S.S,{id:"employee-".concat(e.id),checked:l.employees.includes(e.id),onCheckedChange:()=>x(e.id)}),(0,a.jsx)(A.J,{htmlFor:"employee-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.department})]})]})})]},e.id))})]})})]}),l.employees.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.employees.slice(0,3).map(e=>{let t=m.find(t=>t.id===e);return t?(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs pr-1",children:[t.name,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>x(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e):null}),l.employees.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",l.employees.length-3," more"]})]}),f&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:f})]})},aa=e=>{let{compact:t=!1,className:s=""}=e,l=t4(),{setVehicles:r}=t5(),{validationErrors:i}=t6(),[c,o]=g.useState(!1),[d,m]=g.useState(""),u=[{id:1,make:"Toyota",model:"Camry",year:2022,licensePlate:"ABC-123"},{id:2,make:"Honda",model:"Accord",year:2021,licensePlate:"DEF-456"},{id:3,make:"Ford",model:"F-150",year:2023,licensePlate:"GHI-789"},{id:4,make:"Chevrolet",model:"Silverado",year:2022,licensePlate:"JKL-012"},{id:5,make:"BMW",model:"X5",year:2021,licensePlate:"MNO-345"},{id:6,make:"Mercedes",model:"E-Class",year:2023,licensePlate:"PQR-678"},{id:7,make:"Audi",model:"A4",year:2022,licensePlate:"STU-901"},{id:8,make:"Nissan",model:"Altima",year:2021,licensePlate:"VWX-234"}],x=u.filter(e=>e.make.toLowerCase().includes(d.toLowerCase())||e.model.toLowerCase().includes(d.toLowerCase())||e.licensePlate.toLowerCase().includes(d.toLowerCase())),h=e=>{let t=l.vehicles;t.includes(e)?r(t.filter(t=>t!==e)):r([...t,e])},p=()=>{r(x.map(e=>e.id))},j=()=>{r([])},f=()=>{let e=l.vehicles.length;if(0===e)return"All vehicles";if(1===e){let e=u.find(e=>e.id===l.vehicles[0]);return e?"".concat(e.make," ").concat(e.model):"Unknown"}return"".concat(e," vehicles")},N=i.vehicles;return t?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",s),children:[(0,a.jsx)(A.J,{className:"text-xs font-medium",children:"Vehicle"}),(0,a.jsxs)(Z.AM,{open:c,onOpenChange:o,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",N&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:f()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(ex.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search vehicles...",value:d,onChange:e=>m(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:j,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:x.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{id:"vehicle-".concat(e.id),checked:l.vehicles.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsx)(A.J,{htmlFor:"vehicle-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-3 w-3 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.make," ",e.model]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.licensePlate})]})]})})]},e.id))})]})})]}),N&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:N})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",s),children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Vehicles"}),(0,a.jsxs)(Z.AM,{open:c,onOpenChange:o,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",N&&"border-red-500"),children:[(0,a.jsx)("span",{children:f()}),(0,a.jsx)(s9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Vehicles"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:j,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(ex.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search vehicles...",value:d,onChange:e=>m(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:x.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(S.S,{id:"vehicle-".concat(e.id),checked:l.vehicles.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsx)(A.J,{htmlFor:"vehicle-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.make," ",e.model," (",e.year,")"]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.licensePlate})]})]})})]},e.id))})]})})]}),l.vehicles.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.vehicles.slice(0,3).map(e=>{let t=u.find(t=>t.id===e);return t?(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs pr-1",children:[t.make," ",t.model,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>h(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e):null}),l.vehicles.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",l.vehicles.length-3," more"]})]}),N&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:N})]})};var al=s(30462);let ar=()=>{let[e,t]=g.useState({}),[s,a]=g.useState(0),l=g.useCallback(()=>{try{let e=localStorage.getItem("reporting-filter-presets"),s=e?JSON.parse(e):{};t(s)}catch(e){t({})}},[]);return g.useEffect(()=>{l()},[l,s]),{presets:e,refreshPresets:g.useCallback(()=>{a(e=>e+1)},[])}},ai=e=>{let{className:t=""}=e,{applyPreset:s,saveAsPreset:l,deletePreset:r}=t8(),i=t4(),{presets:n,refreshPresets:c}=ar(),[o,d]=g.useState(!1),[m,u]=g.useState(""),x=g.useCallback(()=>{m.trim()&&(l(m.trim()),u(""),d(!1),c())},[m,l,c]),h=g.useCallback(e=>{s(e)},[s]),p=g.useCallback(e=>{r(e),c()},[r,c]),j=g.useCallback(e=>{var t,s,a,l;let r=[];return(null==(t=e.status)?void 0:t.length)>0&&r.push("".concat(e.status.length," status")),(null==(s=e.locations)?void 0:s.length)>0&&r.push("".concat(e.locations.length," locations")),(null==(a=e.employees)?void 0:a.length)>0&&r.push("".concat(e.employees.length," employees")),(null==(l=e.vehicles)?void 0:l.length)>0&&r.push("".concat(e.vehicles.length," vehicles")),r.length>0?r.join(", "):"No filters"},[]),v=[{name:"Last 30 Days",description:"All delegations from the last 30 days",action:()=>{console.log("Applying 30-day preset",{dateRange:{from:new Date(Date.now()-2592e6),to:new Date},status:[],locations:[],employees:[],vehicles:[]})}},{name:"Active Delegations",description:"In progress and approved delegations",action:()=>{console.log("Applying active delegations preset")}},{name:"Completed This Month",description:"Completed delegations from current month",action:()=>{console.log("Applying completed this month preset")}}],f=Object.keys(n);return(0,a.jsxs)("div",{className:(0,en.cn)("space-y-3",t),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Filter Presets"}),(0,a.jsxs)(eB.lG,{open:o,onOpenChange:d,children:[(0,a.jsx)(eB.zM,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",size:"sm",className:"h-8",children:[(0,a.jsx)(eR.A,{className:"h-4 w-4 mr-2"}),"Save"]})}),(0,a.jsxs)(eB.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(eB.c7,{children:[(0,a.jsx)(eB.L3,{children:"Save Filter Preset"}),(0,a.jsx)(eB.rr,{children:"Save your current filter settings as a preset for quick access later."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A.J,{htmlFor:"preset-name",children:"Preset Name"}),(0,a.jsx)(D.p,{id:"preset-name",placeholder:"Enter preset name...",value:m,onChange:e=>u(e.target.value),onKeyDown:e=>{"Enter"===e.key&&x()}})]}),(0,a.jsxs)("div",{className:"p-3 bg-muted rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Current filters:"}),(0,a.jsx)("p",{className:"text-sm",children:j(i)})]})]}),(0,a.jsxs)(eB.Es,{children:[(0,a.jsx)(y.$,{variant:"outline",onClick:()=>d(!1),children:"Cancel"}),(0,a.jsx)(y.$,{onClick:x,disabled:!m.trim(),children:"Save Preset"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Quick Presets"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:v.map(e=>(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:e.action,className:"h-8 text-xs",children:[(0,a.jsx)(al.A,{className:"h-3 w-3 mr-1"}),e.name]},e.name))})]}),f.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Saved Presets"}),(0,a.jsx)("div",{className:"space-y-1",children:f.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg border bg-card",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:j(n[e])})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>h(e),className:"h-8 px-2 text-xs",children:"Apply"}),(0,a.jsxs)(ta.rI,{children:[(0,a.jsx)(ta.ty,{asChild:!0,children:(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,a.jsx)(s9.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(ta.SQ,{align:"end",children:[(0,a.jsx)(ta._2,{onClick:()=>h(e),children:"Apply Preset"}),(0,a.jsx)(ta.mB,{}),(0,a.jsxs)(ta._2,{onClick:()=>p(e),className:"text-red-600",children:[(0,a.jsx)(eF.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]},e))})]})]})};var an=s(8531),ac=s(10518);let ao=e=>{let{className:t="",showPresets:s=!0,compact:l=!1,includeServiceFilters:r=!1,includeTaskFilters:i=!1}=e,n=t4(),{resetFilters:c,applyFilters:o,revertChanges:d}=t5(),{isFilterPanelOpen:m,hasUnsavedChanges:u,setFilterPanelOpen:h}=t3(),{validationErrors:p,isValid:j}=t6(),f=()=>{c()},b=()=>{j&&o()},w=g.useMemo(()=>{let e=0;if(n.status.length>0&&e++,n.locations.length>0&&e++,n.employees.length>0&&e++,n.vehicles.length>0&&e++,r){var t,s;(null==(t=n.serviceTypes)?void 0:t.length)&&e++,(null==(s=n.serviceStatus)?void 0:s.length)&&e++,n.costRange&&e++}return i&&n.includeTaskData&&e++,e},[n,r,i]),k=g.useMemo(()=>{let e=[];if(n.status.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Status: ",n.status.length]},"status")),n.locations.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Locations: ",n.locations.length]},"locations")),n.employees.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Employees: ",n.employees.length]},"employees")),n.vehicles.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Vehicles: ",n.vehicles.length]},"vehicles")),r){var t,s;(null==(t=n.serviceTypes)?void 0:t.length)&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Service Types: ",n.serviceTypes.length]},"serviceTypes")),(null==(s=n.serviceStatus)?void 0:s.length)&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Service Status: ",n.serviceStatus.length]},"serviceStatus")),n.costRange&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Cost: $",n.costRange.min," - $",n.costRange.max]},"costRange"))}return i&&n.includeTaskData&&e.push((0,a.jsx)(v.E,{variant:"secondary",className:"text-xs",children:"Task Data Included"},"taskData")),e},[n,r,i]),S=()=>{let e=Object.values(p);return 0===e.length?null:(0,a.jsx)("div",{className:"space-y-1",children:e.map((e,t)=>(0,a.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:e},t))})};return l?(0,a.jsxs)("div",{className:"space-y-4 ".concat(t),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filters"}),w>0&&(0,a.jsx)(v.E,{variant:"default",className:"text-xs",children:w})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:f,children:(0,a.jsx)(an.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"default",size:"sm",onClick:b,disabled:!j||!u,children:(0,a.jsx)(ac.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsx)(s7,{compact:!0}),(0,a.jsx)(ae,{compact:!0}),(0,a.jsx)(at,{compact:!0}),(0,a.jsx)(as,{compact:!0}),(0,a.jsx)(aa,{compact:!0})]}),S()]}):(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsxs)(N.aR,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"Reporting Filters",w>0&&(0,a.jsxs)(v.E,{variant:"default",className:"text-xs",children:[w," active"]})]}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>{h(!1)},className:"h-8 w-8 p-0",children:(0,a.jsx)(_.A,{className:"h-4 w-4"})})]}),k.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:k})]}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ai,{}),(0,a.jsx)(T.w,{})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(s7,{}),(0,a.jsx)(ae,{}),(0,a.jsx)(at,{}),(0,a.jsx)(as,{}),(0,a.jsx)(aa,{})]}),S(),(0,a.jsx)(T.w,{}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(y.$,{variant:"outline",onClick:f,className:"flex items-center gap-2",children:[(0,a.jsx)(an.A,{className:"h-4 w-4"}),"Reset All"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[u&&(0,a.jsx)(y.$,{variant:"ghost",onClick:()=>{d()},children:"Revert"}),(0,a.jsxs)(y.$,{onClick:b,disabled:!j||!u,className:"flex items-center gap-2",children:[(0,a.jsx)(ac.A,{className:"h-4 w-4"}),"Apply Filters"]})]})]})]})]})},ad=e=>{let{title:t,description:s,children:l,actions:r,filters:i}=e;return(0,a.jsxs)("div",{className:"flex flex-col h-full p-4 md:p-6 lg:p-8 gap-6",children:[(0,a.jsxs)("header",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100",children:t}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:s})]}),r&&(0,a.jsx)("div",{className:"flex-shrink-0",children:r})]}),i&&(0,a.jsx)("aside",{children:i}),(0,a.jsx)("main",{className:"flex-1",children:l})]})},am=e=>{let{className:t=""}=e,[s,N]=g.useState("overview"),[b,w]=g.useState(!1),k={costRange:{max:1e4,min:0},dateRange:{from:new Date(Date.now()-2592e6),to:new Date},employees:[],includeServiceHistory:!1,includeTaskData:!1,locations:[],serviceStatus:[],serviceTypes:[],status:[],vehicles:[]},S=[{description:"High-level metrics and key performance indicators",icon:(0,a.jsx)(l.A,{className:"size-4"}),id:"overview",label:"Overview"},{description:"Detailed analytics and trend analysis",icon:(0,a.jsx)(r.A,{className:"size-4"}),id:"analytics",label:"Analytics"},{description:"Task metrics and performance analysis",icon:(0,a.jsx)(i.A,{className:"size-4"}),id:"tasks",label:"Tasks"},{description:"Vehicle utilization and maintenance analytics",icon:(0,a.jsx)(n.A,{className:"size-4"}),id:"vehicles",label:"Vehicles"},{description:"Employee performance and workload analysis",icon:(0,a.jsx)(c.A,{className:"size-4"}),id:"employees",label:"Employees"},{description:"Cross-entity relationships and correlations",icon:(0,a.jsx)(o.A,{className:"size-4"}),id:"correlations",label:"Correlations"},{description:"Generate comprehensive data reports for all entities",icon:(0,a.jsx)(d.A,{className:"size-4"}),id:"generation",label:"Generate Reports"},{description:"Manage report types and build custom reports",icon:(0,a.jsx)(m.A,{className:"size-4"}),id:"management",label:"Management"},{description:"Raw delegation data in tabular format",icon:(0,a.jsx)(u.A,{className:"size-4"}),id:"data",label:"Data"}],C=e=>{switch(e){case"overview":return[{component:st,id:"status",span:"col-span-12 lg:col-span-4"},{component:sw,id:"tasks",span:"col-span-12 lg:col-span-8"},{component:sc,id:"trend",span:"col-span-12 lg:col-span-8"},{component:sx,id:"location",span:"col-span-12 lg:col-span-4"}];case"analytics":return[{component:sc,id:"trend",span:"col-span-12 lg:col-span-8"},{component:sx,id:"location",span:"col-span-12 lg:col-span-4"},{component:sw,id:"tasks",span:"col-span-12 lg:col-span-6"},{component:st,id:"status",span:"col-span-12 lg:col-span-6"}];case"correlations":return[{component:s2,id:"cross-entity-correlations",span:"col-span-12"},{component:s6,id:"entity-relationships",span:"col-span-12 lg:col-span-6"},{component:sw,id:"task-correlations",span:"col-span-12 lg:col-span-6"}];case"tasks":return[{component:sw,id:"task-metrics",span:"col-span-12"},{component:sb,id:"task-assignments",span:"col-span-12 lg:col-span-6"},{component:sh,id:"task-status-chart",span:"col-span-12 lg:col-span-6"}];case"vehicles":return[{component:sD,id:"vehicle-analytics",span:"col-span-12 lg:col-span-6"},{component:sz,id:"vehicle-utilization",span:"col-span-12 lg:col-span-6"},{component:sP,id:"vehicle-maintenance",span:"col-span-12 lg:col-span-6"},{component:sW,id:"vehicle-costs",span:"col-span-12 lg:col-span-6"}];case"employees":return[{component:sY,id:"employee-analytics",span:"col-span-12 lg:col-span-8"},{component:sH,id:"employee-workload",span:"col-span-12 lg:col-span-4"},{component:sq,id:"employee-performance",span:"col-span-12"}];case"generation":return[{component:()=>(0,a.jsx)(eE,{}),id:"report-generation",span:"col-span-12"}];case"management":return[{component:()=>(0,a.jsx)(tx,{}),id:"report-type-manager",span:"col-span-12 lg:col-span-6"},{component:()=>(0,a.jsx)(e0,{}),id:"report-builder",span:"col-span-12 lg:col-span-6"}];case"data":return[{component:()=>(0,a.jsx)(ty,{}),id:"data-table",span:"col-span-12"}];default:return[]}},A=async()=>{try{let e={activeTab:s,filters:k,metadata:{exportedBy:"Reporting Dashboard",version:"2.0.0"},timestamp:new Date().toISOString()},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=globalThis.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download="dashboard-export-".concat(new Date().toISOString().split("T")[0],".json"),document.body.append(l),l.click(),l.remove(),globalThis.URL.revokeObjectURL(a)}catch(e){console.error("Export failed:",e)}},E=()=>{let e=C(s);return(0,a.jsx)("div",{className:"grid grid-cols-12 gap-6",children:e.map(e=>{let{component:t,id:l,span:r}=e;return(0,a.jsx)("div",{className:r,children:(0,a.jsx)(t,{})},"".concat(s,"-").concat(l))})})};return(0,a.jsx)(j.GW,{className:t,config:s8,children:(0,a.jsx)(ad,{actions:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(y.$,{className:b?"bg-primary text-primary-foreground":"",onClick:()=>w(!b),size:"sm",variant:"outline",children:[(0,a.jsx)(x.A,{className:"mr-2 size-4"}),"Filters"]}),(0,a.jsxs)(y.$,{onClick:()=>{globalThis.location.reload()},size:"sm",variant:"outline",children:[(0,a.jsx)(h.A,{className:"mr-2 size-4"}),"Refresh"]}),(0,a.jsxs)(y.$,{onClick:A,size:"sm",variant:"outline",children:[(0,a.jsx)(p.A,{className:"mr-2 size-4"}),"Export"]})]}),description:"Interactive dashboard with real-time metrics and insights",filters:b?(0,a.jsx)(ao,{}):void 0,title:"Reporting Dashboard",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(()=>{let e=[];return(k.status.length>0&&e.push("".concat(k.status.length," status")),k.locations.length>0&&e.push("".concat(k.locations.length," locations")),k.employees.length>0&&e.push("".concat(k.employees.length," employees")),k.vehicles.length>0&&e.push("".concat(k.vehicles.length," vehicles")),0===e.length)?null:(0,a.jsxs)("div",{className:"flex items-center gap-3 text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:"Active filters:"}),e.map((e,t)=>(0,a.jsx)(v.E,{className:"text-xs",variant:"secondary",children:e},"filter-".concat(t,"-").concat(e)))]})})(),(0,a.jsxs)(f.tU,{className:"w-full",onValueChange:N,value:s,children:[(0,a.jsx)(f.j7,{className:"grid w-full grid-cols-9 h-12",children:S.map(e=>(0,a.jsxs)(f.Xi,{className:"flex items-center gap-2 text-sm font-medium",value:e.id,children:[e.icon,(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id))}),S.map(e=>(0,a.jsxs)(f.av,{className:"space-y-8",value:e.id,children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("h2",{className:"text-2xl font-semibold tracking-tight flex items-center gap-3",children:[e.icon,e.label]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description})]}),E()]},e.id))]})]})})})}},40879:(e,t,s)=>{"use strict";s.d(t,{dj:()=>u,oR:()=>m});var a=s(12115);let l=0,r=new Map,i=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),d({toastId:e,type:"REMOVE_TOAST"})},1e6);r.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:s}=t;if(s)i(s);else for(let t of e.toasts)i(t.id);return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},c=[],o={toasts:[]};function d(e){for(let t of(o=n(o,e),c))t(o)}function m(e){let{...t}=e,s=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>d({toastId:s,type:"DISMISS_TOAST"});return d({toast:{...t,id:s,onOpenChange:e=>{e||a()},open:!0},type:"ADD_TOAST"}),{dismiss:a,id:s,update:e=>d({toast:{...e,id:s},type:"UPDATE_TOAST"})}}function u(){let[e,t]=a.useState(o);return a.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);-1!==e&&c.splice(e,1)}),[e]),{...e,dismiss:e=>d({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:m}}},44838:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>u,_2:()=>x,hO:()=>h,lp:()=>p,mB:()=>g,rI:()=>d,ty:()=>m});var a=s(95155),l=s(12115),r=s(48698),i=s(73158),n=s(10518),c=s(70154),o=s(54036);let d=r.bL,m=r.l9;r.YJ,r.ZL,r.Pb,r.z6,l.forwardRef((e,t)=>{let{className:s,inset:l,children:n,...c}=e;return(0,a.jsxs)(r.ZP,{ref:t,className:(0,o.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",l&&"pl-8",s),...c,children:[n,(0,a.jsx)(i.A,{className:"ml-auto"})]})}).displayName=r.ZP.displayName,l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(r.G5,{ref:t,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...l})}).displayName=r.G5.displayName;let u=l.forwardRef((e,t)=>{let{className:s,sideOffset:l=4,...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{ref:t,sideOffset:l,className:(0,o.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...i})})});u.displayName=r.UC.displayName;let x=l.forwardRef((e,t)=>{let{className:s,inset:l,...i}=e;return(0,a.jsx)(r.q7,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",l&&"pl-8",s),...i})});x.displayName=r.q7.displayName;let h=l.forwardRef((e,t)=>{let{className:s,children:l,checked:i,...c}=e;return(0,a.jsxs)(r.H_,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...void 0!==i&&{checked:i},...c,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),l]})});h.displayName=r.H_.displayName,l.forwardRef((e,t)=>{let{className:s,children:l,...i}=e;return(0,a.jsxs)(r.hN,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(c.A,{className:"h-2 w-2 fill-current"})})}),l]})}).displayName=r.hN.displayName;let p=l.forwardRef((e,t)=>{let{className:s,inset:l,...i}=e;return(0,a.jsx)(r.JU,{ref:t,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",s),...i})});p.displayName=r.JU.displayName;let g=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(r.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),...l})});g.displayName=r.wv.displayName},50477:()=>{},52639:(e,t,s)=>{"use strict";s.d(t,{GW:()=>n});var a=s(95155);s(12115);var l=s(88240),r=s(54036);let i=e=>{let{children:t,className:s="",config:i}=e;return(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:(0,r.cn)("min-h-screen bg-background",s),children:(0,a.jsx)("main",{className:"flex-1",children:(0,a.jsx)("div",{className:"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8",children:t})})})})},n=e=>{let{children:t,className:s="",config:l}=e;return(0,a.jsx)(i,{config:l,className:s,children:(0,a.jsx)("div",{className:"space-y-8",children:t})})}},53414:(e,t,s)=>{Promise.resolve().then(s.bind(s,32606))},53712:(e,t,s)=>{"use strict";s.d(t,{O_:()=>i,t6:()=>r});var a=s(12115),l=s(83940);function r(){let e=(0,a.useCallback)((e,t)=>l.JP.success(e,t),[]),t=(0,a.useCallback)((e,t)=>l.JP.error(e,t),[]),s=(0,a.useCallback)((e,t)=>l.JP.info(e,t),[]),r=(0,a.useCallback)(t=>e((null==t?void 0:t.successTitle)||"Success",(null==t?void 0:t.successDescription)||"Operation completed successfully"),[e]),i=(0,a.useCallback)((e,s)=>{let a=e instanceof Error?e.message:e;return t((null==s?void 0:s.errorTitle)||"Error",(null==s?void 0:s.errorDescription)||a||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:s,showFormSuccess:r,showFormError:i}}function i(e){let t;switch(e){case"employee":t=s(83940).Ok;break;case"vehicle":t=s(83940).G7;break;case"task":t=s(83940).z0;break;case"delegation":t=s(83940).Qu;break;default:throw Error("Unknown entity type: ".concat(e))}return function(e,t){let{showFormSuccess:s,showFormError:i}=r(),n=t||(e?(0,l.iw)(e):null),c=(0,a.useCallback)(e=>n?n.entityCreated(e):s({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,s]),o=(0,a.useCallback)(e=>n?n.entityUpdated(e):s({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,s]),d=(0,a.useCallback)(e=>n?n.entityDeleted(e):s({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,s]),m=(0,a.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityCreationError(t)}return i(e,{errorTitle:"Creation Failed"})},[n,i]);return{showEntityCreated:c,showEntityUpdated:o,showEntityDeleted:d,showEntityCreationError:m,showEntityUpdateError:(0,a.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityUpdateError(t)}return i(e,{errorTitle:"Update Failed"})},[n,i]),showEntityDeletionError:(0,a.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityDeletionError(t)}return i(e,{errorTitle:"Deletion Failed"})},[n,i]),showFormSuccess:s,showFormError:i}}(void 0,t)}},55012:(e,t,s)=>{"use strict";s.d(t,{gv:()=>d,j9:()=>o});var a=s(14298),l=s(38549),r=s(14163),i=s(28113),n=s(87358);class c{static getInstance(e){var t;return null!=c.instance||(c.instance=new c(e)),c.instance}async connect(){var e;if(null==(e=this.socket)?void 0:e.connected)return void console.debug("WebSocket already connected");this.setConnectionState("connecting");try{let{data:{session:e},error:t}=await r.N.auth.getSession();t&&console.warn("Failed to get session for WebSocket connection:",t);let s={forceNew:!0,timeout:this.config.timeout,transports:["websocket","polling"],withCredentials:!0};if(null==e?void 0:e.access_token){s.auth={token:e.access_token},console.debug("\uD83D\uDD10 WebSocket connecting with authentication token");let t=e.expires_at?1e3*e.expires_at:0,a=Date.now();t-a<=6e4&&console.warn("⚠️ WebSocket token expires soon, may need refresh")}else console.warn("⚠️ WebSocket connecting without authentication token - connection may fail");this.socket=(0,a.io)(this.config.url,s),this.setupEventHandlers()}catch(e){console.error("Failed to connect WebSocket:",e),this.setConnectionState("error"),this.scheduleReconnect()}}destroy(){this.disconnect(),this.subscriptions.clear(),this.stateListeners.clear(),c.instance=null}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionState("disconnected"),this.reconnectAttempts=0}emit(e,t,s){var a;if(!(null==(a=this.socket)?void 0:a.connected))return void console.warn("Cannot emit ".concat(e,":").concat(t," - WebSocket not connected"));this.socket.emit(t,s)}getConnectionState(){return this.connectionState}isConnected(){var e;return"connected"===this.connectionState&&(null==(e=this.socket)?void 0:e.connected)===!0}joinRoom(e){var t;if(!(null==(t=this.socket)?void 0:t.connected))return void console.warn("Cannot join room ".concat(e," - WebSocket not connected"));this.socket.emit("join-room",e)}leaveRoom(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.socket.emit("leave-room",e)}onStateChange(e){return this.stateListeners.add(e),()=>{this.stateListeners.delete(e)}}subscribe(e,t,s){var a;let l="".concat(e,":").concat(t);return this.subscriptions.has(l)||this.subscriptions.set(l,new Set),this.subscriptions.get(l).add(s),(null==(a=this.socket)?void 0:a.connected)&&t&&this.socket.on(t,s),()=>{let e=this.subscriptions.get(l);e&&(e.delete(s),0===e.size&&this.subscriptions.delete(l)),this.socket&&t&&this.socket.off(t,s)}}handleAuthenticationError(){let e=(0,i.Q)();console.log("\uD83D\uDD10 Handling WebSocket authentication error..."),this.socket&&(this.socket.disconnect(),this.socket=null),e.refreshNow().then(e=>{e?console.log("\uD83D\uDD04 Token refresh successful, retrying WebSocket connection"):(console.error("\uD83D\uDD04 Token refresh failed, scheduling normal reconnect"),this.scheduleReconnect())}).catch(e=>{console.error("\uD83D\uDD04 Token refresh error:",e),this.scheduleReconnect()})}resubscribeToEvents(){if(this.socket)for(let[e,t]of this.subscriptions){let[,s]=e.split(":");for(let e of t)s&&this.socket.on(s,e)}}scheduleReconnect(){if(this.reconnectAttempts>=this.config.reconnectAttempts){console.error("Max reconnection attempts reached"),this.setConnectionState("error");return}this.setConnectionState("reconnecting"),this.reconnectAttempts++,setTimeout(()=>{console.info("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.config.reconnectAttempts,")")),this.connect()},this.config.reconnectDelay*Math.pow(2,this.reconnectAttempts-1))}setConnectionState(e){if(this.connectionState!==e)for(let t of(this.connectionState=e,this.stateListeners))t(e)}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.info("WebSocket connected"),this.setConnectionState("connected"),this.reconnectAttempts=0,this.resubscribeToEvents()}),this.socket.on("disconnect",e=>{console.warn("WebSocket disconnected:",e),this.setConnectionState("disconnected"),"io server disconnect"!==e&&this.scheduleReconnect()}),this.socket.on("connect_error",e=>{var t,s,a,l;console.error("WebSocket connection error:",e),this.setConnectionState("error"),(null==(t=e.message)?void 0:t.includes("Authentication"))||(null==(s=e.message)?void 0:s.includes("token"))||(null==(a=e.message)?void 0:a.includes("No token provided"))||(null==(l=e.message)?void 0:l.includes("Unauthorized"))?(console.warn("\uD83D\uDD10 Authentication error detected, attempting token refresh"),this.handleAuthenticationError()):this.scheduleReconnect()}),this.socket.on("auth_error",e=>{console.error("\uD83D\uDD10 Server authentication error:",e),this.handleAuthenticationError()}),this.socket.on("token_refresh_required",()=>{console.warn("\uD83D\uDD04 Server requested token refresh"),this.handleAuthenticationError()}))}setupTokenRefreshHandling(){(0,i.Q)().subscribe((e,t)=>{switch(e){case"critical_refresh_failed":console.error("\uD83D\uDD04 Critical token refresh failure, disconnecting WebSocket"),this.disconnect(),this.setConnectionState("error");break;case"refresh_failed":console.error("\uD83D\uDD04 Token refresh failed, WebSocket may lose connection");break;case"refresh_success":console.log("\uD83D\uDD04 Token refreshed, reconnecting WebSocket with new token"),this.socket&&(this.socket.disconnect(),this.socket=null),setTimeout(()=>this.connect(),500)}})}constructor(e={}){var t,s,a,r,i,c;this.connectionState="disconnected",this.reconnectAttempts=0,this.socket=null,this.stateListeners=new Set,this.subscriptions=new Map,this.config={autoConnect:null==(t=e.autoConnect)||t,reconnectAttempts:null!=(s=e.reconnectAttempts)?s:5,reconnectDelay:null!=(a=e.reconnectDelay)?a:1e3,timeout:null!=(r=e.timeout)?r:1e4,url:null!=(c=null!=(i=e.url)?i:n.env.NEXT_PUBLIC_WEBSOCKET_URL)?c:(0,l.Qq)().wsUrl.replace("ws://","http://").replace("wss://","https://")},this.config.autoConnect&&this.connect(),this.setupTokenRefreshHandling()}}c.instance=null;let o=e=>c.getInstance(e),d=()=>{let e=o();return{connectionState:e.getConnectionState(),isConnected:e.isConnected()}}},55365:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>c,TN:()=>d,XL:()=>o});var a=s(95155),l=s(74466),r=s(12115),i=s(54036);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),c=r.forwardRef((e,t)=>{let{className:s,variant:l,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:l}),s),ref:t,role:"alert",...r})});c.displayName="Alert";let o=r.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("h5",{className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",s),ref:t,...l})});o.displayName="AlertTitle";let d=r.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{className:(0,i.cn)("text-sm [&_p]:leading-relaxed",s),ref:t,...l})});d.displayName="AlertDescription"},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>g,gC:()=>p,l6:()=>d,yv:()=>m});var a=s(95155),l=s(31992),r=s(79556),i=s(77381),n=s(10518),c=s(12115),o=s(54036);let d=l.bL;l.YJ;let m=l.WT,u=c.forwardRef((e,t)=>{let{children:s,className:i,...n}=e;return(0,a.jsxs)(l.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",i),ref:t,...n,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=c.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...r,children:(0,a.jsx)(i.A,{className:"size-4"})})});x.displayName=l.PP.displayName;let h=c.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(l.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...i,children:(0,a.jsx)(r.A,{className:"size-4"})})});h.displayName=l.wn.displayName;let p=c.forwardRef((e,t)=>{let{children:s,className:r,position:i="popper",...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:i,ref:t,...n,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,c.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),ref:t,...r})}).displayName=l.JU.displayName;let g=c.memo(c.forwardRef((e,t)=>{let{children:s,className:r,...i}=e,d=c.useCallback(e=>{"function"==typeof t?t(e):t&&(t.current=e)},[t]);return(0,a.jsxs)(l.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),ref:d,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(l.p4,{children:s})]})}));g.displayName=l.q7.displayName,c.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),ref:t,...r})}).displayName=l.wv.displayName},61051:(e,t,s)=>{"use strict";s.d(t,{ZY:()=>f,AK:()=>b,b7:()=>v,xo:()=>y,si:()=>j,K:()=>N});var a=s(71610),l=s(26715),r=s(5041),i=s(12115),n=s(90111),c=s(75908),o=s(21354),d=s(62494);let m={all:["tasks"],detail:e=>["tasks",e]},u=e=>({enabled:!!e,queryFn:()=>c.taskApiService.getById(e),queryKey:m.detail(e),staleTime:3e5}),x=()=>({queryFn:()=>c.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),h=()=>({queryFn:()=>c.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),p=e=>[u(e),x(),h()];var g=s(54120);let j=e=>(0,n.GK)([...m.all],async()=>(await c.taskApiService.getAll()).data,"task",{staleTime:0,...e}),v=e=>(0,n.GK)([...m.detail(e)],async()=>await c.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),y=e=>{let[t,s,l]=(0,a.E)({queries:p(e)}),r=(0,i.useMemo)(()=>{if((null==t?void 0:t.data)&&(null==s?void 0:s.data)&&(null==l?void 0:l.data))try{let e=d.J.fromApi(t.data),a=Array.isArray(s.data)?s.data:[],r=Array.isArray(l.data)?l.data:[];return(0,o.R)(e,a,r)}catch(e){throw console.error("Error enriching task data:",e),e}},[null==t?void 0:t.data,null==s?void 0:s.data,null==l?void 0:l.data]),n=(0,i.useCallback)(()=>{null==t||t.refetch(),null==s||s.refetch(),null==l||l.refetch()},[null==t?void 0:t.refetch,null==s?void 0:s.refetch,null==l?void 0:l.refetch]);return{data:r,error:(null==t?void 0:t.error)||(null==s?void 0:s.error)||(null==l?void 0:l.error),isError:(null==t?void 0:t.isError)||(null==s?void 0:s.isError)||(null==l?void 0:l.isError),isLoading:(null==t?void 0:t.isLoading)||(null==s?void 0:s.isLoading)||(null==l?void 0:l.isLoading),isPending:(null==t?void 0:t.isPending)||(null==s?void 0:s.isPending)||(null==l?void 0:l.isPending),refetch:n}},f=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let t=d.J.toCreateRequest(e);return await c.taskApiService.create(t)},onError:(t,s,a)=>{(null==a?void 0:a.previousTasks)&&e.setQueryData(m.all,a.previousTasks),console.error("Failed to create task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:m.all});let s=e.getQueryData(m.all);return e.setQueryData(m.all,function(){var e,s,a,l,r,i,n,c,o,d;let m=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],u="optimistic-"+Date.now().toString(),x=new Date().toISOString();return[...m,{createdAt:x,dateTime:null!=(s=t.dateTime)?s:null,deadline:null!=(a=t.deadline)?a:null,description:t.description,driverEmployee:null,driverEmployeeId:null!=(l=t.driverEmployeeId)?l:null,estimatedDuration:null!=(r=t.estimatedDuration)?r:null,id:u,location:null!=(i=t.location)?i:null,notes:null!=(n=t.notes)?n:null,priority:t.priority,requiredSkills:null!=(c=t.requiredSkills)?c:null,staffEmployee:null,staffEmployeeId:null!=(o=t.staffEmployeeId)?o:null,status:t.status||"Pending",subtasks:(null==(e=t.subtasks)?void 0:e.map(e=>({completed:e.completed||!1,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:u,title:e.title})))||[],updatedAt:x,vehicle:null,vehicleId:null!=(d=t.vehicleId)?d:null}]}),{previousTasks:s}},onSettled:()=>{e.invalidateQueries({queryKey:m.all})}})},N=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let{data:t,id:s}=e,a=d.J.toUpdateRequest(t);return await c.taskApiService.update(s,a)},onError:(t,s,a)=>{(null==a?void 0:a.previousTask)&&e.setQueryData(m.detail(s.id),a.previousTask),(null==a?void 0:a.previousTasksList)&&e.setQueryData(m.all,a.previousTasksList),console.error("Failed to update task:",t)},onMutate:async t=>{let{data:s,id:a}=t;await e.cancelQueries({queryKey:m.all}),await e.cancelQueries({queryKey:m.detail(a)});let l=e.getQueryData(m.detail(a)),r=e.getQueryData(m.all);return e.setQueryData(m.detail(a),e=>{var t,l,r,i;if(!e)return e;let n=new Date().toISOString();return{...e,dateTime:void 0!==s.dateTime?s.dateTime:e.dateTime,deadline:(0,g.d$)(void 0!==s.deadline?s.deadline:e.deadline),description:null!=(l=s.description)?l:e.description,driverEmployeeId:(0,g.d$)(void 0!==s.driverEmployeeId?s.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==s.estimatedDuration?s.estimatedDuration:e.estimatedDuration,location:void 0!==s.location?s.location:e.location,notes:(0,g.d$)(void 0!==s.notes?s.notes:e.notes),priority:null!=(r=s.priority)?r:e.priority,requiredSkills:void 0!==s.requiredSkills?s.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==s.staffEmployeeId?s.staffEmployeeId:e.staffEmployeeId,status:null!=(i=s.status)?i:e.status,subtasks:(null==(t=s.subtasks)?void 0:t.map(e=>{var t;return{completed:null!=(t=e.completed)&&t,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:a,title:e.title}}))||e.subtasks||[],updatedAt:n,vehicleId:(0,g.d$)(void 0!==s.vehicleId?s.vehicleId:e.vehicleId)}}),e.setQueryData(m.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(e=>{if(e.id===a){var t,l,r,i;let n=new Date().toISOString(),c=(null==(t=s.subtasks)?void 0:t.map(e=>{var t;return{completed:null!=(t=e.completed)&&t,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:a,title:e.title}}))||e.subtasks||[];return{...e,dateTime:void 0!==s.dateTime?s.dateTime:e.dateTime,deadline:(0,g.d$)(void 0!==s.deadline?s.deadline:e.deadline),description:null!=(l=s.description)?l:e.description,driverEmployeeId:(0,g.d$)(void 0!==s.driverEmployeeId?s.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==s.estimatedDuration?s.estimatedDuration:e.estimatedDuration,location:void 0!==s.location?s.location:e.location,notes:(0,g.d$)(void 0!==s.notes?s.notes:e.notes),priority:null!=(r=s.priority)?r:e.priority,requiredSkills:void 0!==s.requiredSkills?s.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==s.staffEmployeeId?s.staffEmployeeId:e.staffEmployeeId,status:null!=(i=s.status)?i:e.status,subtasks:c,updatedAt:n,vehicleId:(0,g.d$)(void 0!==s.vehicleId?s.vehicleId:e.vehicleId)}}return e})}),{previousTask:l,previousTasksList:r}},onSettled:(t,s,a)=>{e.invalidateQueries({queryKey:m.detail(a.id)}),e.invalidateQueries({queryKey:m.all})}})},b=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>(await c.taskApiService.delete(e),e),onError:(t,s,a)=>{(null==a?void 0:a.previousTasksList)&&e.setQueryData(m.all,a.previousTasksList),console.error("Failed to delete task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:m.all}),await e.cancelQueries({queryKey:m.detail(t)});let s=e.getQueryData(m.all);return e.setQueryData(m.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==t)}),e.removeQueries({queryKey:m.detail(t)}),{previousTasksList:s}},onSettled:()=>{e.invalidateQueries({queryKey:m.all})}})}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(95155),l=s(12115),r=s(54036);let i=l.forwardRef((e,t)=>{let{className:s,type:l,...i}=e;return(0,a.jsx)("input",{className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,type:l,...i})});i.displayName="Input"},68856:(e,t,s)=>{"use strict";s.d(t,{E:()=>r});var a=s(95155),l=s(54036);function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",t),...s})}},75908:(e,t,s)=>{"use strict";s.d(t,{cl:()=>p,delegationApiService:()=>j,employeeApiService:()=>y,reliabilityApiService:()=>f,taskApiService:()=>v,vehicleApiService:()=>g});var a=s(55411),l=s(976),r=s(12430),i=s(25982);let n={fromApi:e=>e,toApi:e=>e};class c extends i.v{async acknowledgeAlert(e,t,s){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.post("/alerts/".concat(e,"/acknowledge"),{acknowledgedBy:s,note:t});return this.cache.invalidatePattern(RegExp("^alerts:")),a})}async getActiveAlerts(){return this.executeWithInfrastructure("alerts:active",async()=>{try{let e=await this.apiClient.get("/alerts");return(null==e?void 0:e.alerts)||[]}catch(e){return console.error("Failed to get active alerts:",e),[]}})}async getAlertHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return this.executeWithInfrastructure("alerts:history:".concat(e,":").concat(t),async()=>{let s=new URLSearchParams({limit:t.toString(),page:e.toString()});return await this.apiClient.get("/alerts/history?".concat(s.toString()))})}async getAlertStatistics(){return this.executeWithInfrastructure("alerts:statistics",async()=>{try{return await this.apiClient.get("/alerts/statistics")}catch(e){return console.error("Failed to get alert statistics:",e),{acknowledged:0,active:0,averageResolutionTime:0,bySeverity:{critical:0,high:0,low:0,medium:0},recentTrends:{last7Days:0,last24Hours:0,last30Days:0},resolved:0,total:0}}})}async getCircuitBreakerHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h",t=arguments.length>1?arguments[1]:void 0;return this.executeWithInfrastructure("circuit-breakers:history:".concat(e,":").concat(t||"all"),async()=>{let s=new URLSearchParams({timeframe:e});return t&&s.append("breakerName",t),await this.apiClient.get("/monitoring/circuit-breakers/history?".concat(s.toString()))})}async getCircuitBreakerStatus(){return this.executeWithInfrastructure("monitoring:circuit-breakers",async()=>{try{let e=await this.apiClient.get("/circuit-breakers"),t=(null==e?void 0:e.circuitBreakers)||[];return{circuitBreakers:t||[],summary:{closed:(null==t?void 0:t.filter(e=>"CLOSED"===e.state).length)||0,halfOpen:(null==t?void 0:t.filter(e=>"HALF_OPEN"===e.state).length)||0,open:(null==t?void 0:t.filter(e=>"OPEN"===e.state).length)||0,total:(null==t?void 0:t.length)||0}}}catch(e){return console.error("Failed to get circuit breaker status:",e),{circuitBreakers:[],summary:{closed:0,halfOpen:0,open:0,total:0}}}})}async getCriticalAlertCount(){try{return(await this.getAlertStatistics()).bySeverity.critical}catch(e){return 0}}async getDeduplicationMetrics(){return this.executeWithInfrastructure("monitoring:deduplication",async()=>await this.apiClient.get("/monitoring/deduplication"))}async getDependencyHealth(){return this.executeWithInfrastructure("health:dependencies",async()=>await this.apiClient.get("/health/dependencies"))}async getDetailedHealth(){return this.executeWithInfrastructure("health:detailed",async()=>await this.apiClient.get("/health/detailed"))}async getHealthTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h";return this.executeWithInfrastructure("health:trends:".concat(e),async()=>await this.apiClient.get("/health/trends?timeframe=".concat(e)))}async getHttpRequestMetrics(){return this.executeWithInfrastructure("http:metrics",async()=>await this.apiClient.get("/http-request-metrics"))}async getMetrics(){return this.executeWithInfrastructure("metrics:system",async()=>await this.apiClient.get("/metrics",{headers:{Accept:"application/json"}}))}async getReliabilityDashboardData(){let[e,t,s,a,l,r]=await Promise.all([this.getSystemHealth(),this.getDetailedHealth(),this.getCircuitBreakerStatus(),this.getMetrics(),this.getActiveAlerts(),this.getAlertStatistics()]);return{activeAlerts:l,alertStatistics:r,circuitBreakers:s,detailedHealth:t,metrics:a,systemHealth:e}}async getSystemHealth(){return this.executeWithInfrastructure("health:system",async()=>await this.apiClient.get("/health"))}async isSystemHealthy(){try{let e=await this.getSystemHealth();return"healthy"===e.status}catch(e){return!1}}async resolveAlert(e,t,s){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.post("/alerts/".concat(e,"/resolve"),{reason:t,resolvedBy:s});return this.cache.invalidatePattern(RegExp("^alerts:")),a})}async testAlerts(){return this.executeWithInfrastructure(null,async()=>{var e;let t=await this.apiClient.post("/alerts/test");return{message:(null==t?void 0:t.message)||"Test alert triggered",success:(null==t?void 0:t.status)==="success",testAlertId:null==t||null==(e=t.data)?void 0:e.id}})}constructor(e,t){super(e,{cacheDuration:6e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/reliability",this.transformer=n}}var o=s(90137),d=s(97966),m=s(38549),u=s(72248);function x(){let e=(0,u.Sk)();if(!e)return null;try{return e()}catch(e){return console.error("❌ Factory: Error getting auth token from secure provider:",e),null}}class h{getApiClient(){return this.apiClient}getDelegationService(){return this.delegationService||(this.delegationService=new l.y(this.apiClient)),this.delegationService}getEmployeeService(){return this.employeeService||(this.employeeService=new r.Q(this.apiClient)),this.employeeService}getReliabilityService(){return this.reliabilityService||(this.reliabilityService=new c(this.apiClient)),this.reliabilityService}getTaskService(){return this.taskService||(this.taskService=new o.D(this.apiClient)),this.taskService}getVehicleService(){return this.vehicleService||(this.vehicleService=new d.C(this.apiClient)),this.vehicleService}constructor(e){this.apiClient=new a.O({...e,getAuthToken:x})}}let p=new h({baseURL:(0,m.Qq)().apiBaseUrl,headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4}),g=p.getVehicleService(),j=p.getDelegationService(),v=p.getTaskService(),y=p.getEmployeeService(),f=p.getReliabilityService()},77023:(e,t,s)=>{"use strict";s.d(t,{gO:()=>u,jt:()=>g,pp:()=>x});var a=s(95155),l=s(11133),r=s(50172);s(12115);var i=s(6560),n=s(55365),c=s(68856),o=s(54036);let d={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function u(e){let{children:t,className:s,data:l,emptyComponent:r,error:i,errorComponent:n,isLoading:c,loadingComponent:d,onRetry:m}=e;return c?d||(0,a.jsx)(p,{...s&&{className:s},text:"Loading..."}):i?n||(0,a.jsx)(h,{...s&&{className:s},message:i,...m&&{onRetry:m}}):!l||Array.isArray(l)&&0===l.length?r||(0,a.jsx)("div",{className:(0,o.cn)("text-center py-8",s),children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,a.jsx)("div",{className:s,children:t(l)})}function x(e){let{className:t,description:s,icon:l,primaryAction:r,secondaryAction:n,title:c}=e;return(0,a.jsxs)("div",{className:(0,o.cn)("space-y-6 text-center py-12",t),children:[l&&(0,a.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,a.jsx)(l,{className:"h-10 w-10 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:c}),s&&(0,a.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[r&&(0,a.jsx)(i.r,{actionType:"primary",asChild:!!r.href,icon:r.icon,onClick:r.onClick,children:r.href?(0,a.jsx)("a",{href:r.href,children:r.label}):r.label}),n&&(0,a.jsx)(i.r,{actionType:"tertiary",asChild:!!n.href,icon:n.icon,onClick:n.onClick,children:n.href?(0,a.jsx)("a",{href:n.href,children:n.label}):n.label})]})]})}function h(e){let{className:t,message:s,onRetry:c}=e;return(0,a.jsxs)(n.Fc,{className:(0,o.cn)("my-4",t),variant:"destructive",children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)(n.XL,{children:"Error"}),(0,a.jsx)(n.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),c&&(0,a.jsx)(i.r,{actionType:"tertiary",icon:(0,a.jsx)(r.A,{className:"size-4"}),onClick:c,size:"sm",children:"Try Again"})]})})]})}function p(e){let{className:t,fullPage:s=!1,size:l="md",text:i}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",t),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(r.A,{className:(0,o.cn)("animate-spin text-primary",d[l])}),i&&(0,a.jsx)("span",{className:(0,o.cn)("mt-2 text-muted-foreground",m[l]),children:i})]})})}function g(e){let{className:t,count:s=1,testId:l="loading-skeleton",variant:r="default"}=e;return"card"===r?(0,a.jsx)("div",{className:(0,o.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",t),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,a.jsx)(c.E,{className:"aspect-[16/10] w-full"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(c.E,{className:"mb-1 h-7 w-3/4"}),(0,a.jsx)(c.E,{className:"mb-3 h-4 w-1/2"}),(0,a.jsx)(c.E,{className:"my-3 h-px w-full"}),(0,a.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.E,{className:"mr-2.5 size-5 rounded-full"}),(0,a.jsx)(c.E,{className:"h-5 w-2/3"})]},t))})]})]},t))}):"table"===r?(0,a.jsxs)("div",{className:(0,o.cn)("space-y-3",t),"data-testid":l,children:[(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,t)=>(0,a.jsx)(c.E,{className:"h-8 flex-1"},t))}),Array(s).fill(0).map((e,t)=>(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,t)=>(0,a.jsx)(c.E,{className:"h-6 flex-1"},t))},t))]}):"list"===r?(0,a.jsx)("div",{className:(0,o.cn)("space-y-3",t),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(c.E,{className:"size-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(c.E,{className:"h-4 w-1/3"}),(0,a.jsx)(c.E,{className:"h-4 w-full"})]})]},t))}):"stats"===r?(0,a.jsx)("div",{className:(0,o.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",t),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(c.E,{className:"h-5 w-1/3"}),(0,a.jsx)(c.E,{className:"size-5 rounded-full"})]}),(0,a.jsx)(c.E,{className:"mt-3 h-8 w-1/2"}),(0,a.jsx)(c.E,{className:"mt-2 h-4 w-2/3"})]},t))}):(0,a.jsx)("div",{className:(0,o.cn)("space-y-2",t),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,a.jsx)(c.E,{className:"h-5 w-full"},t))})}},83686:()=>{},83940:(e,t,s)=>{"use strict";s.d(t,{G7:()=>u,Gb:()=>c,JP:()=>o,Ok:()=>d,Qu:()=>m,iw:()=>n,oz:()=>h,z0:()=>x});var a=s(40879);class l{show(e){return(0,a.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class r extends l{entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}constructor(e){super(),this.config=e}}class i extends l{serviceRecordCreated(e,t){return this.success("Service Record Added","".concat(t,' service for "').concat(e,'" has been successfully logged.'))}serviceRecordUpdated(e,t){return this.success("Service Record Updated","".concat(t,' service for "').concat(e,'" has been updated.'))}serviceRecordDeleted(e,t){return this.success("Service Record Deleted","".concat(t,' service record for "').concat(e,'" has been permanently removed.'))}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new r(e)}function c(e,t){return new r({entityName:e,getDisplayName:t,messages:{created:{title:"".concat(e," Created"),description:t=>"The ".concat(e.toLowerCase(),' "').concat(t,'" has been successfully created.')},updated:{title:"".concat(e," Updated Successfully"),description:e=>"".concat(e," has been updated.")},deleted:{title:"".concat(e," Deleted Successfully"),description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create ".concat(e),description:t=>t||"An unexpected error occurred while creating the ".concat(e.toLowerCase(),".")},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the ".concat(e.toLowerCase(),".")},deletionError:{title:"Failed to Delete ".concat(e),description:t=>t||"An unexpected error occurred while deleting the ".concat(e.toLowerCase(),".")}}})}let o=new l,d=new r({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>'The employee "'.concat(e,'" has been successfully created.')},updated:{title:"Employee Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Employee Deleted Successfully",description:e=>"".concat(e," has been permanently removed from the system.")},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),m=new r({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>'The delegation "'.concat(e,'" has been successfully created.')},updated:{title:"Delegation Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Delegation Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),u=new r({entityName:"Vehicle",getDisplayName:e=>"".concat(e.make," ").concat(e.model),messages:{created:{title:"Vehicle Added",description:e=>'The vehicle "'.concat(e,'" has been successfully created.')},updated:{title:"Vehicle Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Vehicle Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),x=new r({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>'The task "'.concat(e,'" has been successfully created.')},updated:{title:"Task Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Task Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new i},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var a=s(95155),l=s(12115),r=s(40968),i=s(74466),n=s(54036);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(r.b,{ref:t,className:(0,n.cn)(c(),s),...l})});o.displayName=r.b.displayName},85127:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>c,Hj:()=>o,XI:()=>i,nA:()=>m,nd:()=>d});var a=s(95155),l=s(12115),r=s(54036);let i=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{className:(0,r.cn)("w-full caption-bottom text-sm",s),ref:t,...l})})});i.displayName="Table";let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("thead",{className:(0,r.cn)("[&_tr]:border-b",s),ref:t,...l})});n.displayName="TableHeader";let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("tbody",{className:(0,r.cn)("[&_tr:last-child]:border-0",s),ref:t,...l})});c.displayName="TableBody",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("tfoot",{className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),ref:t,...l})}).displayName="TableFooter";let o=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("tr",{className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),ref:t,...l})});o.displayName="TableRow";let d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("th",{className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),ref:t,...l})});d.displayName="TableHead";let m=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("td",{className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),ref:t,...l})});m.displayName="TableCell",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("caption",{className:(0,r.cn)("mt-4 text-sm text-muted-foreground",s),ref:t,...l})}).displayName="TableCaption"},85511:(e,t,s)=>{"use strict";s.d(t,{V:()=>o});var a=s(95155),l=s(965),r=s(73158);s(12115);var i=s(33683),n=s(30285),c=s(54036);function o(e){let{className:t,classNames:s,showOutsideDays:o=!0,...d}=e;return(0,a.jsx)(i.hv,{className:(0,c.cn)("p-3",t),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,c.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...s},components:{IconLeft:e=>{let{className:t,...s}=e;return(0,a.jsx)(l.A,{className:(0,c.cn)("h-4 w-4",t),...s})},IconRight:e=>{let{className:t,...s}=e;return(0,a.jsx)(r.A,{className:(0,c.cn)("h-4 w-4",t),...s})}},showOutsideDays:o,...d})}o.displayName="Calendar"},86719:(e,t,s)=>{"use strict";s.d(t,{M:()=>r});var a=s(12115),l=s(17652);function r(){let[e,t]=(0,a.useState)({}),[s,r]=(0,a.useState)(!1),i=(0,l.c3)("auth.validation"),n=(0,a.useCallback)(e=>e?/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)?void 0:i("emailInvalid"):i("emailRequired"),[i]),c=(0,a.useCallback)(e=>e?e.length<6?i("passwordTooShort"):void 0:i("passwordRequired"),[i]),o=(0,a.useCallback)(e=>{let s={},a=n(e.email),l=c(e.password);return a&&(s.email=a),l&&(s.password=l),t(s),{isValid:0===Object.keys(s).length,errors:s}},[n,c]),d=(0,a.useCallback)((e,s)=>{let a;switch(e){case"email":a=n(s);break;case"password":a=c(s);break;default:return}t(t=>({...t,[e]:a}))},[n,c]),m=(0,a.useCallback)(e=>{t(t=>{let s={...t};return delete s[e],s})},[]),u=(0,a.useCallback)(()=>{t({})},[]),x=(0,a.useCallback)(()=>{r(!0)},[]),h=(0,a.useCallback)(()=>{t({}),r(!1)},[]),p=(0,a.useCallback)((t,a)=>s&&a&&!e[t],[e,s]);return{errors:e,isFormTouched:s,validateForm:o,validateField:d,clearFieldError:m,clearAllErrors:u,markFormTouched:x,resetValidation:h,isFieldValid:p}}},88539:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(95155),l=s(12115),r=s(54036);let i=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...l})});i.displayName="Textarea"},90111:(e,t,s)=>{"use strict";s.d(t,{GK:()=>c,ol:()=>o});var a=s(26715),l=s(28755),r=s(12115),i=s(55012);let n={crud:"entity-updates",notifications:"notifications-monitoring",reliability:"reliability-monitoring",system:"system-monitoring"};function c(e,t,s,a){return d(e,t,{channel:"crud",events:["".concat(s,":created"),"".concat(s,":updated"),"".concat(s,":deleted"),"refresh:".concat(s)],fallbackInterval:3e4},a)}function o(e,t,s,a){let l=(0,i.j9)();return(0,r.useEffect)(()=>{l.isConnected()&&(console.debug("[ReliabilityQuery] Joining reliability-monitoring room for ".concat(s)),l.joinRoom("reliability-monitoring"));let e=l.onStateChange(e=>{"connected"===e&&(console.debug("[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ".concat(s)),l.joinRoom("reliability-monitoring"))});return()=>{e(),l.isConnected()&&l.leaveRoom("reliability-monitoring")}},[l,s]),d(e,t,{channel:"reliability",events:["".concat(s,"-update"),"".concat(s,"-created"),"".concat(s,"-resolved")],fallbackInterval:{alerts:3e4,"circuit-breakers":6e4,health:45e3,metrics:6e4}[s]},a)}function d(e,t,s,c){let{channel:o,enableFallback:d=!0,enableWebSocket:m=!0,events:u,fallbackInterval:x=3e4}=s,[h,p]=(0,r.useState)(!1),g=(0,i.j9)();(0,r.useEffect)(()=>{let e=()=>{p(g.isConnected())};return e(),g.onStateChange(e)},[g]);let j=d&&(!m||!h),v={gcTime:6e5,queryFn:t,queryKey:e,refetchInterval:!!j&&x,refetchOnReconnect:!0,refetchOnWindowFocus:j,staleTime:3e4*!h,...c},y=(0,a.jE)(),f=(0,l.I)(v);return(0,r.useEffect)(()=>{if(!m||!h)return;let e=n[o];if(!e)return void console.warn("[SmartQuery] No room mapping found for channel: ".concat(o));try{g.joinRoom(e),console.log("[SmartQuery] Joined room: ".concat(e," for channel: ").concat(o))}catch(t){console.error("[SmartQuery] Failed to join room ".concat(e,":"),t)}return()=>{try{g.leaveRoom(e),console.log("[SmartQuery] Left room: ".concat(e," for channel: ").concat(o))}catch(t){console.error("[SmartQuery] Failed to leave room ".concat(e,":"),t)}}},[m,h,o,g]),(0,r.useEffect)(()=>{if(!m||!h||0===u.length)return;let t=[];for(let s of u){let a=g.subscribe(o,s,t=>{console.log("[SmartQuery] WebSocket event received: ".concat(o,":").concat(s),t),y.invalidateQueries({queryKey:e})});t.push(a)}return()=>{for(let e of t)e()}},[m,h,u,o,g,y,e]),{...f,isUsingFallback:j,isWebSocketConnected:h}}},91394:(e,t,s)=>{"use strict";s.d(t,{BK:()=>c,eu:()=>n,q5:()=>o});var a=s(95155),l=s(54011),r=s(12115),i=s(54036);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.bL,{className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),ref:t,...r})});n.displayName=l.bL.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l._V,{className:(0,i.cn)("aspect-square h-full w-full",s),ref:t,...r})});c.displayName=l._V.displayName;let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.H4,{className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),ref:t,...r})});o.displayName=l.H4.displayName}}]);