(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8004],{62606:(e,t,s)=>{Promise.resolve().then(s.bind(s,80115))},80115:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var i=s(95155);let a=(0,s(40157).A)("ListPlus",[["path",{d:"M11 12H3",key:"51ecnj"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M16 18H3",key:"12xzn7"}],["path",{d:"M18 9v6",key:"1twb98"}],["path",{d:"M21 12h-6",key:"bt1uis"}]]);var d=s(35695),r=s(76541),o=s(95647),n=s(53712),l=s(61051);function c(){let e=(0,d.useRouter)(),{showEntityCreated:t,showEntityCreationError:s}=(0,n.O_)("task"),{error:c,isPending:p,mutateAsync:u}=(0,l.ZY)(),h=async i=>{try{let s={dateTime:i.dateTime,deadline:i.deadline||void 0,description:i.description,driverEmployeeId:i.driverEmployeeId||void 0,estimatedDuration:i.estimatedDuration,location:i.location,notes:i.notes||void 0,priority:i.priority,requiredSkills:i.requiredSkills,staffEmployeeId:i.staffEmployeeId,status:i.status.replace(" ","_"),subtasks:i.subtasks,vehicleId:i.vehicleId||void 0};await u(s);let a={title:i.description.slice(0,30)+(i.description.length>30?"...":""),name:i.description.slice(0,30)+(i.description.length>30?"...":"")};t(a),e.push("/tasks")}catch(e){console.error("Error adding task:",e),s(e.message||(null==c?void 0:c.message)||"Failed to add task. Please try again.")}};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(o.z,{description:"Enter the details for the new task or job.",icon:a,title:"Add New Task"}),c&&(0,i.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error: ",c.message]}),(0,i.jsx)(r.A,{isEditing:!1,onSubmit:h,isLoading:p})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,5669,4629,4036,4767,8950,3712,7515,1051,9124,8441,1684,7358],()=>t(62606)),_N_E=e.O()}]);