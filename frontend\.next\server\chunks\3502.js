"use strict";exports.id=3502,exports.ids=[3502],exports.modules={63502:(e,t,i)=>{i.d(t,{er:()=>Q,kA:()=>F,gd:()=>f,BD:()=>D,nB:()=>w,jM:()=>A,lG:()=>q});var a=i(93425),r=i(8693),l=i(54050),n=i(43210),o=i(38118),s=i(46349),d=i(49603);class u{static enrich(e,t,i){let{employeeMap:a,vehicleMap:r}=this.createLookupMaps(t,i);return{...e,drivers:this.enrichDrivers(e.drivers,a)??[],escorts:this.enrichEscorts(e.escorts,a)??[],vehicles:this.enrichVehicles(e.vehicles,r)??[]}}static createLookupMaps(e,t){return{employeeMap:new Map(e.map(e=>[e.id,e])),vehicleMap:new Map(t.map(e=>[e.id,e]))}}static enrichDrivers(e,t){return e?.map(e=>{let i=e.employee||t.get(Number(e.employeeId));return{...e,...i&&{employee:i}}})}static enrichEscorts(e,t){return e?.map(e=>{let i=e.employee||t.get(Number(e.employeeId));return{...e,...i&&{employee:i}}})}static enrichVehicles(e,t){return e?.map(e=>{let i=e.vehicle||t.get(e.vehicleId);return{...e,...i&&{vehicle:i}}})}}let g=(e,t,i)=>u.enrich(e,t,i);var c=i(68983);let p={all:["delegations"],detail:e=>["delegations",e]},v=e=>({enabled:!!e,queryFn:()=>d.delegationApiService.getById(e),queryKey:p.detail(e),staleTime:3e5}),y=()=>({queryFn:()=>d.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),m=()=>({queryFn:()=>d.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),h=e=>[v(e),y(),m()],D=e=>(0,s.GK)([...p.all],async()=>(await d.delegationApiService.getAll()).data,"delegation",{staleTime:0,...e}),F=e=>(0,s.GK)([...p.detail(e)],async()=>await d.delegationApiService.getById(e),"delegation",{enabled:!!e,staleTime:3e5}),f=e=>{let[t,i,r]=(0,a.E)({queries:h(e)}),l=(0,n.useMemo)(()=>{if(t?.data&&i?.data&&r?.data)try{let e=t.data;return g(e,i.data,r.data)}catch(e){throw console.error("Error enriching delegation data:",e),e}},[t?.data,i?.data,r?.data]),o=(0,n.useCallback)(()=>{t?.refetch(),i?.refetch(),r?.refetch()},[t?.refetch,i?.refetch,r?.refetch]);return{data:l,error:t?.error||i?.error||r?.error,isError:t?.isError||i?.isError||r?.isError,isLoading:t?.isLoading||i?.isLoading||r?.isLoading,isPending:t?.isPending||i?.isPending||r?.isPending,refetch:o}},Q=()=>{let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let t=c.G.toCreateRequest(e);return await d.delegationApiService.create(t)},onError:(t,i,a)=>{a?.previousDelegations&&e.setQueryData(p.all,a.previousDelegations),console.error("Failed to create delegation:",t),e.invalidateQueries({queryKey:p.all})},onMutate:async t=>{await e.cancelQueries({queryKey:p.all});let i=e.getQueryData(p.all);return e.setQueryData(p.all,(e=[])=>{let i=`optimistic-${Date.now()}`,a=new Date().toISOString(),r=t.flightArrivalDetails?{id:`optimistic-flight-arr-${Date.now()}`,...t.flightArrivalDetails}:null,l=t.flightDepartureDetails?{id:`optimistic-flight-dep-${Date.now()+1}`,...t.flightDepartureDetails}:null;return[...e,{arrivalFlight:r??null,createdAt:a,delegates:t.delegates?.map((e,t)=>({id:`optimistic-delegate-${i}-${t}`,name:e.name,notes:e.notes??null,title:e.title}))||[],departureFlight:l??null,drivers:t.drivers?.map(e=>({createdAt:a,createdBy:null,delegationId:i,employeeId:e.employeeId,id:`optimistic-driver-${i}-${e.employeeId}`,notes:e.notes??null,updatedAt:a}))||[],durationFrom:t.durationFrom,durationTo:t.durationTo,escorts:t.escorts?.map(e=>({createdAt:a,createdBy:null,delegationId:i,employeeId:e.employeeId,id:`optimistic-escort-${i}-${e.employeeId}`,notes:e.notes??null,updatedAt:a}))||[],eventName:t.eventName,id:i,imageUrl:t.imageUrl??null,invitationFrom:t.invitationFrom??null,invitationTo:t.invitationTo??null,location:t.location,notes:t.notes??null,status:t.status||"Planned",statusHistory:[],updatedAt:a,vehicles:t.vehicles?.map(e=>({assignedDate:e.assignedDate,createdAt:a,createdBy:null,delegationId:i,id:`optimistic-vehicle-${i}-${e.vehicleId}`,notes:e.notes??null,returnDate:e.returnDate??null,updatedAt:a,vehicleId:e.vehicleId}))||[]}]}),{previousDelegations:i}},onSettled:()=>{e.invalidateQueries({queryKey:p.all})}})},A=()=>{let e=(0,r.jE)();return(0,l.n)({mutationFn:async({data:e,id:t})=>await d.delegationApiService.update(t,e),onError:(t,i,a)=>{a?.previousDelegation&&e.setQueryData(p.detail(i.id),a.previousDelegation),a?.previousDelegationsList&&e.setQueryData(p.all,a.previousDelegationsList),console.error("Failed to update delegation:",t),e.invalidateQueries({queryKey:p.detail(i.id)}),e.invalidateQueries({queryKey:p.all})},onMutate:async({data:t,id:i})=>{await e.cancelQueries({queryKey:p.all}),await e.cancelQueries({queryKey:p.detail(i)});let a=e.getQueryData(p.detail(i)),r=e.getQueryData(p.all);return e.setQueryData(p.detail(i),e=>{if(!e)return;let i=new Date().toISOString();return{...e,arrivalFlight:(0,o.d$)(null===t.flightArrivalDetails?null:void 0===t.flightArrivalDetails?e.arrivalFlight:{airport:t.flightArrivalDetails.airport||e.arrivalFlight?.airport||"",dateTime:t.flightArrivalDetails.dateTime||e.arrivalFlight?.dateTime||"",flightNumber:t.flightArrivalDetails.flightNumber||e.arrivalFlight?.flightNumber||"",id:e.arrivalFlight?.id||`optimistic-arr-${Date.now()}`,notes:t.flightArrivalDetails.notes??e.arrivalFlight?.notes??null,terminal:t.flightArrivalDetails.terminal??e.arrivalFlight?.terminal??null}),departureFlight:(0,o.d$)(null===t.flightDepartureDetails?null:void 0===t.flightDepartureDetails?e.departureFlight:{airport:t.flightDepartureDetails.airport||e.departureFlight?.airport||"",dateTime:t.flightDepartureDetails.dateTime||e.departureFlight?.dateTime||"",flightNumber:t.flightDepartureDetails.flightNumber||e.departureFlight?.flightNumber||"",id:e.departureFlight?.id||`optimistic-dep-${Date.now()}`,notes:t.flightDepartureDetails.notes??e.departureFlight?.notes??null,terminal:t.flightDepartureDetails.terminal??e.departureFlight?.terminal??null}),durationFrom:t.durationFrom??e.durationFrom,durationTo:t.durationTo??e.durationTo,eventName:t.eventName??e.eventName,imageUrl:(0,o.d$)(t.imageUrl??e.imageUrl),invitationFrom:(0,o.d$)(t.invitationFrom??e.invitationFrom),invitationTo:(0,o.d$)(t.invitationTo??e.invitationTo),location:t.location??e.location,notes:(0,o.d$)(t.notes??e.notes),status:t.status??e.status,updatedAt:i}}),e.setQueryData(p.all,(t=[])=>t.map(t=>t.id===i&&e.getQueryData(p.detail(i))||t)),{previousDelegation:a,previousDelegationsList:r}},onSettled:(t,i,a)=>{e.invalidateQueries({queryKey:p.detail(a.id)}),e.invalidateQueries({queryKey:p.all})}})},q=()=>{let e=(0,r.jE)();return(0,l.n)({mutationFn:async({id:e,status:t,statusChangeReason:i})=>await d.delegationApiService.updateStatus(e,t,i),onError:(t,i,a)=>{a?.previousDelegation&&e.setQueryData(p.detail(i.id),a.previousDelegation),console.error("Failed to update delegation status:",t)},onMutate:async({id:t,status:i})=>{await e.cancelQueries({queryKey:p.detail(t)});let a=e.getQueryData(p.detail(t));return e.setQueryData(p.detail(t),e=>e?{...e,status:i}:void 0),{previousDelegation:a}},onSettled:(t,i,a)=>{e.invalidateQueries({queryKey:p.detail(a.id)}),e.invalidateQueries({queryKey:p.all})}})},w=()=>{let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>(await d.delegationApiService.delete(e),e),onError:(t,i,a)=>{a?.previousDelegationsList&&e.setQueryData(p.all,a.previousDelegationsList),console.error("Failed to delete delegation:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:p.all}),await e.cancelQueries({queryKey:p.detail(t)});let i=e.getQueryData(p.all);return e.setQueryData(p.all,(e=[])=>e.filter(e=>e.id!==t)),e.removeQueries({queryKey:p.detail(t)}),{previousDelegationsList:i}},onSettled:()=>{e.invalidateQueries({queryKey:p.all})}})}}};