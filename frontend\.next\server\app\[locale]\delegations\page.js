(()=>{var e={};e.id=7385,e.ids=[7385],e.modules={550:(e,s,a)=>{Promise.resolve().then(a.bind(a,28318))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7889:(e,s,a)=>{"use strict";a.d(s,{GW:()=>n});var t=a(60687);a(43210);var r=a(95668),l=a(22482);let i=({children:e,className:s="",config:a})=>(0,t.jsx)(r.A,{children:(0,t.jsx)("div",{className:(0,l.cn)("min-h-screen bg-background",s),children:(0,t.jsx)("main",{className:"flex-1",children:(0,t.jsx)("div",{className:"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8",children:e})})})}),n=({children:e,className:s="",config:a})=>(0,t.jsx)(i,{config:a,className:s,children:(0,t.jsx)("div",{className:"space-y-8",children:e})})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17854:(e,s,a)=>{Promise.resolve().then(a.bind(a,63893))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23338:(e,s,a)=>{"use strict";a.d(s,{y:()=>r});var t=a(43210);let r=e=>(0,t.useMemo)(()=>{let s=e.escorts&&e.escorts.length>0&&e.escorts[0]?.employee?e.escorts[0].employee:null,a=e.drivers&&e.drivers.length>0&&e.drivers[0]?.employee?e.drivers[0].employee:null,t=e.vehicles&&e.vehicles.length>0&&e.vehicles[0]?.vehicle?e.vehicles[0].vehicle:null,r=!!(e.arrivalFlight||e.departureFlight),l=!s&&"Completed"!==e.status&&"Cancelled"!==e.status;return{escortInfo:s,driverInfo:a,vehicleInfo:t,hasFlightDetails:r,needsEscortAssignment:l,isActive:"In_Progress"===e.status}},[e])},27910:e=>{"use strict";e.exports=require("stream")},28318:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>ew});var t=a(60687),r=a(33886),l=a(35265),i=a(58369),n=a(85814),d=a.n(n),c=a(43210),o=a(95668),m=a(15036),x=a(48206),h=a(3662);a(7889);var u=a(29385),g=a(63502);r.A,m.A,x.A,h.A;var p=a(37392);let v={entityType:"delegation",title:"Delegation Dashboard",description:"Track and manage all your events, trips, and delegate information.",viewModes:["cards","table","list","calendar"],defaultViewMode:"cards",enableBulkActions:!0,enableExport:!0,refreshInterval:3e4},j=({className:e=""})=>{let{layout:s,monitoring:a,setViewMode:r,setGridColumns:l,toggleCompactMode:i,setMonitoringEnabled:n,setRefreshInterval:d,toggleAutoRefresh:c,resetSettings:o}=(0,u.fX)("delegation")();return(0,t.jsx)(p.s,{config:v,entityType:"delegation",layout:s,monitoring:a,setViewMode:r,setGridColumns:l,toggleCompactMode:i,setMonitoringEnabled:n,setRefreshInterval:d,toggleAutoRefresh:c,resetSettings:o,className:e})};var f=a(88514),N=a(78726),b=a(92876),y=a(26398),w=a(24920),k=a(41936),C=a(90586),A=a(29523),z=a(89667),S=a(80013),D=a(96834),R=a(56896),M=a(26373),$=a(35950),P=a(40988),T=a(67146),F=a(22482),E=a(75699);let q=[{value:"Planned",label:"Planned",icon:f.A,color:"border-blue-200 text-blue-700"},{value:"Confirmed",label:"Confirmed",icon:h.A,color:"border-green-200 text-green-700"},{value:"In_Progress",label:"In Progress",icon:f.A,color:"border-yellow-200 text-yellow-700"},{value:"Completed",label:"Completed",icon:h.A,color:"border-emerald-200 text-emerald-700"},{value:"Cancelled",label:"Cancelled",icon:N.A,color:"border-red-200 text-red-700"},{value:"No_details",label:"No Details",icon:f.A,color:"border-gray-200 text-gray-700"}],L=({onFiltersChange:e,className:s,initialFilters:a={},employeesList:r=[],vehiclesList:l=[],locationsList:i=[]})=>{let[n,d]=(0,c.useState)(!1),[o,m]=(0,c.useState)({search:"",status:[],dateRange:{},location:[],drivers:[],escorts:[],vehicles:[],...a}),u=s=>{let a={...o,...s};m(a),e?.(a)},g=()=>{let s={search:"",status:[],dateRange:{},location:[],drivers:[],escorts:[],vehicles:[]};m(s),e?.(s)},p=e=>{u({status:o.status.includes(e)?o.status.filter(s=>s!==e):[...o.status,e]})},v=e=>{u({location:o.location.includes(e)?o.location.filter(s=>s!==e):[...o.location,e]})},j=e=>{u({drivers:o.drivers.includes(e)?o.drivers.filter(s=>s!==e):[...o.drivers,e]})},f=e=>{u({vehicles:o.vehicles.includes(e)?o.vehicles.filter(s=>s!==e):[...o.vehicles,e]})},L=e=>{u({dateRange:{from:e?.from??void 0,to:e?.to??void 0}})},_=+!!o.search+o.status.length+o.location.length+o.drivers.length+o.escorts.length+o.vehicles.length+(o.dateRange.from||o.dateRange.to?1:0);return(0,t.jsxs)("div",{className:(0,F.cn)("flex flex-col gap-4",s),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(k.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(z.p,{placeholder:"Search delegations (Event, Location, Delegate, Status...)",value:o.search,onChange:e=>u({search:e.target.value}),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,t.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,t.jsx)(()=>(0,t.jsxs)(P.AM,{children:[(0,t.jsx)(P.Wv,{asChild:!0,children:(0,t.jsxs)(A.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(h.A,{className:"size-4"}),"Status",o.status.length>0&&(0,t.jsx)(D.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:o.status.length})]})}),(0,t.jsx)(P.hl,{className:"w-56 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Status"}),(0,t.jsx)(A.$,{variant:"ghost",size:"sm",onClick:()=>u({status:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)($.w,{}),(0,t.jsx)("div",{className:"space-y-2",children:q.map(e=>{let s=e.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.S,{id:`status-${e.value}`,checked:o.status.includes(e.value),onCheckedChange:()=>p(e.value)}),(0,t.jsxs)(S.J,{htmlFor:`status-${e.value}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(s,{className:"size-3"}),(0,t.jsx)(D.E,{variant:"outline",className:(0,F.cn)("text-xs border",e.color),children:e.label})]})]},e.value)})})]})})]}),{}),(0,t.jsx)(()=>(0,t.jsxs)(P.AM,{children:[(0,t.jsx)(P.Wv,{asChild:!0,children:(0,t.jsxs)(A.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(b.A,{className:"size-4"}),"Date Range",(o.dateRange.from||o.dateRange.to)&&(0,t.jsx)(D.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:"1"})]})}),(0,t.jsx)(P.hl,{className:"w-auto p-0",align:"start",children:(0,t.jsxs)("div",{className:"p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Date Range"}),(0,t.jsx)(A.$,{variant:"ghost",size:"sm",onClick:()=>u({dateRange:{}}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(M.V,{mode:"range",selected:{from:o.dateRange.from,to:o.dateRange.to},onSelect:L,numberOfMonths:2,className:"rounded-md border-0"}),(0,t.jsx)("div",{className:"mt-3 text-xs text-muted-foreground text-center",children:o.dateRange.from&&!o.dateRange.to?"Select end date to complete range":"Click start date, then end date"})]})})]}),{}),i.length>0&&(0,t.jsx)(()=>(0,t.jsxs)(P.AM,{children:[(0,t.jsx)(P.Wv,{asChild:!0,children:(0,t.jsxs)(A.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(y.A,{className:"size-4"}),"Location",o.location.length>0&&(0,t.jsx)(D.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:o.location.length})]})}),(0,t.jsx)(P.hl,{className:"w-64 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Location"}),(0,t.jsx)(A.$,{variant:"ghost",size:"sm",onClick:()=>u({location:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)($.w,{}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:i.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.S,{id:`location-${e}`,checked:o.location.includes(e),onCheckedChange:()=>v(e)}),(0,t.jsxs)(S.J,{htmlFor:`location-${e}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(y.A,{className:"size-3"}),(0,t.jsx)("span",{children:e})]})]},e))})]})})]}),{}),r.some(e=>"driver"===e.role)&&(0,t.jsx)(()=>(0,t.jsxs)(P.AM,{children:[(0,t.jsx)(P.Wv,{asChild:!0,children:(0,t.jsxs)(A.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(x.A,{className:"size-4"}),"Drivers",o.drivers.length>0&&(0,t.jsx)(D.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:o.drivers.length})]})}),(0,t.jsx)(P.hl,{className:"w-64 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Drivers"}),(0,t.jsx)(A.$,{variant:"ghost",size:"sm",onClick:()=>u({drivers:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)($.w,{}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:r.filter(e=>"driver"===e.role).map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.S,{id:`driver-${e.id}`,checked:o.drivers.includes(e.id),onCheckedChange:()=>j(e.id)}),(0,t.jsxs)(S.J,{htmlFor:`driver-${e.id}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(x.A,{className:"size-3"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.role})]})]})]},e.id))})]})})]}),{}),l.length>0&&(0,t.jsx)(()=>(0,t.jsxs)(P.AM,{children:[(0,t.jsx)(P.Wv,{asChild:!0,children:(0,t.jsxs)(A.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(w.A,{className:"size-4"}),"Vehicles",o.vehicles.length>0&&(0,t.jsx)(D.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:o.vehicles.length})]})}),(0,t.jsx)(P.hl,{className:"w-64 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Vehicles"}),(0,t.jsx)(A.$,{variant:"ghost",size:"sm",onClick:()=>u({vehicles:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)($.w,{}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:l.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.S,{id:`vehicle-${e.id}`,checked:o.vehicles.includes(e.id),onCheckedChange:()=>f(e.id)}),(0,t.jsxs)(S.J,{htmlFor:`vehicle-${e.id}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(w.A,{className:"size-3"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:e.type})]})]})]},e.id))})]})})]}),{})]}),(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsxs)(T.cj,{open:n,onOpenChange:d,children:[(0,t.jsx)(T.CG,{asChild:!0,children:(0,t.jsxs)(A.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(C.A,{className:"size-4"}),"Filters",_>0&&(0,t.jsx)(D.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:_})]})}),(0,t.jsxs)(T.h,{side:"bottom",className:"max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(T.Fm,{children:[(0,t.jsx)(T.qp,{children:"Filter Delegations"}),(0,t.jsx)(T.Qs,{children:"Apply filters to find specific delegations"})]}),(0,t.jsxs)("div",{className:"mt-6 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(S.J,{className:"text-sm font-medium",children:"Status"}),(0,t.jsx)("div",{className:"grid gap-2",children:q.map(e=>{let s=e.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,t.jsx)(R.S,{id:`mobile-status-${e.value}`,checked:o.status.includes(e.value),onCheckedChange:()=>p(e.value)}),(0,t.jsxs)(S.J,{htmlFor:`mobile-status-${e.value}`,className:"cursor-pointer text-sm flex-1 flex items-center gap-2",children:[(0,t.jsx)(s,{className:"size-3"}),e.label]})]},e.value)})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(S.J,{className:"text-sm font-medium",children:"Date Range"}),(0,t.jsx)("div",{className:"border rounded-md p-3",children:(0,t.jsx)(M.V,{mode:"range",selected:{from:o.dateRange.from,to:o.dateRange.to},onSelect:L,numberOfMonths:1,className:"rounded-md border-0"})})]}),_>0&&(0,t.jsxs)(A.$,{variant:"outline",onClick:g,className:"w-full gap-2",children:[(0,t.jsx)(N.A,{className:"size-4"}),"Clear All Filters (",_,")"]})]})]})]})}),_>0&&(0,t.jsxs)(A.$,{variant:"ghost",size:"sm",onClick:g,className:"gap-1 text-muted-foreground hover:text-foreground hidden md:flex",children:[(0,t.jsx)(N.A,{className:"size-3"}),"Clear (",_,")"]})]}),_>0&&(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Active filters:"}),o.status.map(e=>{let s=q.find(s=>s.value===e);return s?(0,t.jsxs)(D.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,t.jsx)(A.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>p(e),children:(0,t.jsx)(N.A,{className:"size-3"})})]},e):null}),o.location.map(e=>(0,t.jsxs)(D.E,{variant:"secondary",className:"gap-1",children:["Location: ",e,(0,t.jsx)(A.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>v(e),children:(0,t.jsx)(N.A,{className:"size-3"})})]},e)),o.drivers.map(e=>{let s=r.find(s=>s.id===e);return(0,t.jsxs)(D.E,{variant:"secondary",className:"gap-1",children:["Driver: ",s?.name||"Unknown",(0,t.jsx)(A.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>j(e),children:(0,t.jsx)(N.A,{className:"size-3"})})]},e)}),o.vehicles.map(e=>{let s=l.find(s=>s.id===e);return(0,t.jsxs)(D.E,{variant:"secondary",className:"gap-1",children:["Vehicle: ",s?.name||"Unknown",(0,t.jsx)(A.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>f(e),children:(0,t.jsx)(N.A,{className:"size-3"})})]},e)}),(o.dateRange.from||o.dateRange.to)&&(0,t.jsxs)(D.E,{variant:"secondary",className:"gap-1",children:["Date:"," ",o.dateRange.from?(0,E.GP)(o.dateRange.from,"MMM d"):"?"," ","-"," ",o.dateRange.to?(0,E.GP)(o.dateRange.to,"MMM d, yyyy"):"?",(0,t.jsx)(A.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>u({dateRange:{}}),children:(0,t.jsx)(N.A,{className:"size-3"})})]})]})]})};var _=a(26622),G=a(5068),I=a(54608),V=a(23949),B=a(3389);let W=({delegations:e,className:s="",onDelete:a,onBulkDelete:r,onBulkArchive:l})=>{let{toast:i}=(0,B.dj)(),n=e=>{try{return(0,E.GP)(new Date(e),"MMM dd, yyyy")}catch{return"Invalid Date"}},d=[(0,V.BZ)(),{accessorKey:"eventName",header:(0,V.YB)("Event Name"),cell:({row:e})=>{let s=e.getValue("eventName"),a=e.original.notes;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-foreground",children:s||"-"}),a&&(0,t.jsx)("div",{className:"line-clamp-1 text-xs text-muted-foreground",children:a})]})}},(0,V.ZI)("status","Status",{Planned:{variant:"secondary",label:"Planned"},"In Progress":{variant:"default",label:"In Progress"},Completed:{variant:"success",label:"Completed"},Cancelled:{variant:"destructive",label:"Cancelled"}}),(0,V.nh)("location","Location",({className:e})=>(0,t.jsx)(y.A,{className:e})),{accessorKey:"durationFrom",header:(0,V.YB)("Start Date"),cell:({row:e})=>{let s=e.getValue("durationFrom");return(0,t.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,t.jsx)(_.A,{className:"size-3 text-muted-foreground"}),n(s)]})}},{accessorKey:"durationTo",header:(0,V.YB)("End Date"),cell:({row:e})=>{let s=e.getValue("durationTo");return(0,t.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,t.jsx)(_.A,{className:"size-3 text-muted-foreground"}),n(s)]})}},(0,V.nh)("delegates","Delegates",({className:e})=>(0,t.jsx)(x.A,{className:e}),{formatter:e=>e?.length??0}),(0,V.Wy)({viewHref:e=>`/delegations/${e.id}`,editHref:e=>`/delegations/${e.id}/edit`,...a&&{onDelete:e=>{a(e)}},showCopyId:!0,customActions:[{label:"Duplicate",onClick:e=>{i({title:"Feature Coming Soon",description:`Duplicate functionality for ${e.eventName}`})}}]})],c=[{label:"Delete Selected",icon:({className:e})=>(0,t.jsx)(G.A,{className:e}),onClick:async e=>{r&&(await r(e),i({title:"Delegations Deleted",description:`${e.length} delegations have been deleted`}))},variant:"destructive"},{label:"Archive Selected",icon:({className:e})=>(0,t.jsx)(I.A,{className:e}),onClick:async e=>{l&&(await l(e),i({title:"Delegations Archived",description:`${e.length} delegations have been archived`}))}}];return(0,t.jsx)(V.bQ,{data:e,columns:d,className:s,searchPlaceholder:"Search delegations by event name or location...",searchColumn:"eventName",emptyMessage:"No delegations found. Create your first delegation to get started.",pageSize:15,enableRowSelection:!0,enableBulkActions:!0,bulkActions:c,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900",rowClassName:"hover:bg-gray-50/50 dark:hover:bg-gray-800/50"})};function Z({children:e,searchTerm:s="",filters:a}){console.log("DelegationListContainer rendered");let{data:r=[],error:l,isLoading:i,refetch:n}=(0,g.BD)(),d=l?l.message:null,o=(0,c.useMemo)(()=>{console.log("Filtering delegations",{allDelegationsCount:r.length,searchTerm:s,filters:a});let e=[...r],t=a?.search||s;if(t){let s=t.toLowerCase();e=e.filter(e=>e.eventName.toLowerCase().includes(s)||e.location.toLowerCase().includes(s)||e.status.toLowerCase().includes(s)||e.delegates?.some(e=>e.name.toLowerCase().includes(s)))}return a?.status&&a.status.length>0&&(e=e.filter(e=>a.status.includes(e.status))),a?.location&&a.location.length>0&&(e=e.filter(e=>a.location.includes(e.location))),a?.dateRange&&(a.dateRange.from||a.dateRange.to)&&(e=e.filter(e=>{let s=new Date(e.durationFrom),t=new Date(e.durationTo);return a.dateRange.from&&a.dateRange.to?s<=a.dateRange.to&&t>=a.dateRange.from:a.dateRange.from?t>=a.dateRange.from:!a.dateRange.to||s<=a.dateRange.to})),a?.drivers&&a.drivers.length>0&&(e=e.filter(e=>e.drivers?.some(e=>a.drivers.includes(e.employee?.id.toString()||"")))),a?.escorts&&a.escorts.length>0&&(e=e.filter(e=>e.escorts?.some(e=>a.escorts.includes(e.employee?.id.toString()||"")))),a?.vehicles&&a.vehicles.length>0&&(e=e.filter(e=>e.vehicles?.some(e=>a.vehicles.includes(e.vehicle?.id.toString()||"")))),e},[s,r,a]),m=(0,c.useCallback)(async()=>{console.log("Manual fetch triggered via fetchDelegations prop (refetch)"),await n()},[n]);return(0,t.jsx)(t.Fragment,{children:e({delegations:o,error:d,fetchDelegations:m,loading:i})})}var H=a(42102),Y=a(46127),J=a(47138),O=a(82592),K=a(43559),U=a(43967),X=a(74158),Q=a(44493);let ee=e=>{switch(e){case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},es=e=>{let s=new Date(e);return Number.isNaN(s.getTime())?"N/A":(0,E.GP)(s,"MMM d, yyyy")},ea=e=>{let s=new Date(e);return Number.isNaN(s.getTime())?"N/A":(0,E.GP)(s,"HH:mm")};var et=a(15795);let er=(e,s)=>e.filter(e=>{let a=new Date(e.durationFrom),t=new Date(e.durationTo);return s>=a&&s<=t}),el=({className:e="",delegations:s})=>{let[a,r]=c.useState(new Date),l=(0,H.w)(a),i=function(e,s){let a=(0,J.a)(e.start),t=(0,J.a)(e.end),r=+a>+t,l=r?+a:+t,i=r?t:a;i.setHours(0,0,0,0);let n=(void 0)??1;if(!n)return[];n<0&&(n=-n,r=!r);let d=[];for(;+i<=l;)d.push((0,J.a)(i)),i.setDate(i.getDate()+n),i.setHours(0,0,0,0);return r?d.reverse():d}({end:(0,Y.p)(a),start:l});return(0,t.jsxs)("div",{className:(0,F.cn)("space-y-6",e),children:[(0,t.jsx)(Q.Zp,{className:"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900",children:(0,t.jsx)(Q.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[(0,t.jsxs)(Q.ZB,{className:"flex items-center gap-3 text-2xl font-semibold text-gray-900 dark:text-white",children:[(0,t.jsx)("div",{className:"rounded-full bg-blue-600 p-2",children:(0,t.jsx)(_.A,{className:"size-6 text-white"})}),(0,E.GP)(a,"MMMM yyyy")]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(A.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{r(new Date)},size:"sm",variant:"outline",children:"Today"}),(0,t.jsx)(A.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{r(new Date(a.getFullYear(),a.getMonth()-1,1))},size:"sm",variant:"outline",children:(0,t.jsx)(U.A,{className:"size-4"})}),(0,t.jsx)(A.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{r(new Date(a.getFullYear(),a.getMonth()+1,1))},size:"sm",variant:"outline",children:(0,t.jsx)(X.A,{className:"size-4"})})]})]})})}),(0,t.jsx)(Q.Zp,{className:"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900",children:(0,t.jsxs)(Q.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-7 gap-1",children:[["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 bg-gray-50 p-3 text-center text-sm font-semibold text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:e},e)),i.map(e=>{let a=er(s,e),r=function(e){return(0,K.r)(e,(0,O.A)(e))}(e);return(0,t.jsxs)("div",{className:(0,F.cn)("min-h-[120px] border border-gray-200 dark:border-gray-700 p-2 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200",r&&"bg-blue-50 dark:bg-blue-950/30 border-blue-300 dark:border-blue-700"),children:[(0,t.jsx)("div",{className:(0,F.cn)("text-sm font-medium mb-2 flex items-center justify-center w-6 h-6 rounded-full",r?"bg-blue-600 text-white shadow-sm":"text-gray-700 dark:text-gray-300"),children:(0,E.GP)(e,"d")}),(0,t.jsxs)("div",{className:"space-y-1",children:[a.slice(0,2).map(e=>(0,t.jsx)(d(),{className:"block",href:`/delegations/${e.id}`,children:(0,t.jsxs)("div",{className:(0,F.cn)("text-xs p-2 rounded border cursor-pointer hover:shadow-sm transition-all duration-200",ee(e.status)),title:`${e.eventName} - ${(0,et.fZ)(e.status)}`,children:[(0,t.jsx)("div",{className:"truncate font-medium",children:e.eventName}),(0,t.jsx)("div",{className:"mt-0.5 text-xs opacity-75",children:ea(e.durationFrom)})]})},e.id)),a.length>2&&(0,t.jsxs)("div",{className:"rounded border border-gray-200 bg-gray-100 p-1 text-center text-xs text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400",children:["+",a.length-2," more"]})]})]},e.toISOString())})]}),(0,t.jsxs)("div",{className:"mt-8 border-t border-gray-200 pt-6 dark:border-gray-700",children:[(0,t.jsx)("div",{className:"mb-4 text-sm font-semibold text-gray-900 dark:text-white",children:"Status Legend"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-3",children:[{label:"Planned",status:"Planned"},{label:"Confirmed",status:"Confirmed"},{label:"In Progress",status:"In_Progress"},{label:"Completed",status:"Completed"},{label:"Cancelled",status:"Cancelled"}].map(({label:e,status:s})=>(0,t.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 dark:border-gray-700 dark:bg-gray-800",children:[(0,t.jsx)("div",{className:(0,F.cn)("w-3 h-3 rounded-full",ee(s))}),(0,t.jsx)("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:e})]},s))})]}),(0,t.jsx)("div",{className:"mt-6 border-t border-gray-200 pt-4 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-sm text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:s.length})," ","delegation",1===s.length?"":"s"," this month"]})})]})})]})};var ei=a(52856),en=a(58595),ed=a(14975),ec=a(99196),eo=a(76311),em=a(68752);let ex=({status:e,className:s,size:a="md",floating:r=!1})=>{let l=(0,F.cn)("font-medium border shadow-sm transition-all duration-200",{sm:"text-xs py-1 px-2",md:"text-xs py-1.5 px-3",lg:"text-sm py-2 px-4"}[a],r&&"absolute top-4 right-4",ee(e));return(0,t.jsx)(D.E,{className:(0,F.cn)(l,s),children:(0,et.fZ)(e)})};var eh=a(23338);function eu({delegation:e,className:s}){let{driverInfo:a,escortInfo:r,hasFlightDetails:l,isActive:i,needsEscortAssignment:n,vehicleInfo:c}=(0,eh.y)(e);return(0,t.jsxs)(Q.Zp,{className:(0,F.cn)("flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md","transition-all duration-200 ease-in-out","hover:shadow-lg hover:border-primary/30","group",s),children:[(0,t.jsx)("div",{className:(0,F.cn)("h-1 w-full transition-all duration-200",i?"bg-gradient-to-r from-primary to-accent":"Completed"===e.status?"bg-green-500":"Cancelled"===e.status?"bg-destructive":"bg-muted")}),(0,t.jsxs)(Q.aR,{className:"p-5 pb-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)(Q.ZB,{className:"text-xl font-semibold text-primary mb-1 line-clamp-2",children:e.eventName}),(0,t.jsxs)(Q.BT,{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(y.A,{className:"size-4 text-muted-foreground shrink-0"}),(0,t.jsx)("span",{className:"truncate",children:e.location})]})]}),(0,t.jsx)(ex,{size:"sm",status:e.status,className:"shrink-0"})]}),i&&(0,t.jsxs)("div",{className:"flex w-fit items-center gap-2 rounded-full bg-primary/10 px-3 py-1.5 border border-primary/20 mt-3",children:[(0,t.jsx)("div",{className:"size-2 animate-pulse rounded-full bg-primary"}),(0,t.jsx)("span",{className:"text-xs font-medium text-primary",children:"Currently Active"})]})]}),(0,t.jsxs)(Q.Wu,{className:"flex-1 p-5 pt-0",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(b.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Duration"})]}),(0,t.jsxs)("p",{className:"text-sm font-medium",children:[es(e.durationFrom)," -"," ",es(e.durationTo)]})]}),(0,t.jsx)($.w,{className:"my-4"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Delegates"})]}),(0,t.jsx)(D.E,{variant:"secondary",className:"text-xs",children:e.delegates?.length??0})]}),l&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ei.A,{className:"size-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Flight Details"})]}),(0,t.jsx)(D.E,{variant:"outline",className:"text-xs text-blue-600 border-blue-200",children:"Available"})]}),(r||a||c)&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)($.w,{className:"my-3"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("span",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Assignments"}),(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(en.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Escort"})]}),(0,t.jsx)("span",{className:"text-sm font-medium truncate max-w-32",children:(0,et.DV)(r)})]}),a&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(en.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Driver"})]}),(0,t.jsx)("span",{className:"text-sm font-medium truncate max-w-32",children:(0,et.DV)(a)})]}),c&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Vehicle"})]}),(0,t.jsxs)("span",{className:"text-sm font-medium truncate max-w-32",children:[c.make," ",c.model]})]})]})]})]}),n&&(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-destructive/10 border border-destructive/20 mt-3",children:[(0,t.jsx)(ed.A,{className:"size-4 text-destructive shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-destructive",children:"Escort Required"}),(0,t.jsx)("p",{className:"text-xs text-destructive/80",children:"No escort assigned"})]})]})]}),e.notes&&(0,t.jsx)("div",{className:"mt-4 p-3 rounded-lg bg-muted/30",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(ec.A,{className:"size-4 text-muted-foreground mt-0.5 shrink-0"}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("p",{className:"text-xs font-medium text-muted-foreground mb-1",children:"Notes"}),(0,t.jsx)("p",{className:"text-sm line-clamp-2 text-muted-foreground",children:e.notes})]})]})})]}),(0,t.jsx)(Q.wL,{className:"border-t bg-muted/20 p-4",children:(0,t.jsx)(em.r,{actionType:"primary",asChild:!0,className:"w-full",icon:(0,t.jsx)(eo.A,{className:"size-4"}),children:(0,t.jsx)(d(),{href:`/delegations/${e.id}`,children:"View Details"})})})]})}let eg=({className:e="",compactMode:s,delegations:a,gridColumns:r,viewMode:l})=>{switch(l){case"calendar":return(0,t.jsx)(el,{className:e,delegations:a});case"list":return(0,t.jsx)("div",{className:(0,F.cn)("flex flex-col",s?"gap-2":"gap-4",e),children:a.map(e=>(0,t.jsx)(eu,{delegation:e},e.id))});case"table":return(0,t.jsx)(W,{className:e,delegations:a});default:return(0,t.jsx)("div",{className:(0,F.cn)("grid grid-cols-1 gap-6",`md:grid-cols-2 lg:grid-cols-${r}`,s&&"gap-3",e),children:a.map(e=>(0,t.jsx)(eu,{delegation:e},e.id))})}};var ep=a(69981),ev=a(12662),ej=a(63503),ef=a(52027),eN=a(48041),eb=a(19599),ey=a(72273);function ew(){let[e,s]=(0,c.useState)(""),[a,n]=(0,c.useState)({dateRange:{},drivers:[],escorts:[],location:[],search:"",status:[],vehicles:[]}),{layout:m}=(0,u.fX)("delegation")(),{data:x=[]}=(0,g.BD)(),{data:h=[]}=(0,eb.nR)(),{data:p=[]}=(0,ey.T$)(),v=[...new Set(x.map(e=>e.location).filter(Boolean))],f=h.map(e=>({id:e.id.toString(),name:e.name,role:e.role})),N=p.map(e=>({id:e.id.toString(),name:`${e.make} ${e.model}`,type:`${e.year} ${e.licensePlate}`}));return(0,t.jsx)(o.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(ev.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,t.jsx)(eN.z,{description:"Track and manage all your events, trips, and delegate information.",icon:r.A,title:"Manage Delegations",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(em.r,{actionType:"primary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"mr-2 size-4"}),children:(0,t.jsx)(d(),{href:"/delegations/add",children:"Add New Delegation"})}),(0,t.jsx)(ep.M,{getReportUrl:()=>{let s=new URLSearchParams({searchTerm:a.search||e}).toString();return`/delegations/report/list?${s}`},isList:!0}),(0,t.jsxs)(ej.lG,{children:[(0,t.jsx)(ej.zM,{asChild:!0,children:(0,t.jsx)(em.r,{actionType:"secondary",icon:(0,t.jsx)(i.A,{className:"size-4"}),children:"Settings"})}),(0,t.jsxs)(ej.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsx)(ej.L3,{children:"Dashboard Settings"}),(0,t.jsx)(ej.rr,{children:"Customize the display and behavior of your delegation dashboard."}),(0,t.jsx)(j,{})]})]})]})}),(0,t.jsx)("div",{className:"mb-6 rounded-lg bg-card p-4 shadow-md",children:(0,t.jsx)(L,{employeesList:f,initialFilters:{dateRange:{},drivers:[],escorts:[],location:[],search:e,status:[],vehicles:[]},locationsList:v,onFiltersChange:e=>{n(e),s(e.search)},vehiclesList:N})}),(0,t.jsx)(Z,{filters:a,searchTerm:e,children:({delegations:s,error:a,fetchDelegations:i,loading:n})=>(0,t.jsx)(ef.gO,{data:s,emptyComponent:(0,t.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,t.jsx)(r.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,t.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:e?"No Delegations Match Your Search":"No Delegations Yet!"}),(0,t.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:e?"Try adjusting your search terms or add a new delegation.":"It looks like you haven't added any delegations yet. Get started by adding one."}),!e&&(0,t.jsx)(em.r,{actionType:"primary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"size-4"}),size:"lg",children:(0,t.jsx)(d(),{href:"/delegations/add",children:"Add Your First Delegation"})})]}),error:a,isLoading:n,loadingComponent:(0,t.jsx)(ef.jt,{count:3,variant:"card"}),onRetry:i,children:e=>(0,t.jsx)(eg,{compactMode:m.compactMode,delegations:e,gridColumns:m.gridColumns,viewMode:m.viewMode})})})]})})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41881:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(65239),r=a(48088),l=a(88170),i=a.n(l),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c={children:["",{children:["[locale]",{children:["delegations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,63893)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/delegations/page",pathname:"/[locale]/delegations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},52856:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(82614).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63893:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\delegations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82592:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(35780);function r(e){return(0,t.w)(e,Date.now())}},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,4825,625,9851,8390,2670,4897,6362,6805,2890,2482,9794,9599,3502,5009,9637,3439,9922,5563],()=>a(41881));module.exports=t})();