(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{26126:(e,s,r)=>{"use strict";r.d(s,{E:()=>l});var t=r(95155),a=r(74466);r(12115);var i=r(54036);let c=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function l(e){let{className:s,variant:r,...a}=e;return(0,t.jsx)("div",{className:(0,i.cn)(c({variant:r}),s),...a})}},44462:(e,s,r)=>{Promise.resolve().then(r.bind(r,48432))},48432:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>P});var t=r(95155),a=r(35695),i=r(66695),c=r(68856),l=r(22346),d=r(30285),n=r(26126),o=r(55365),m=r(90010),x=r(6874),h=r.n(x),u=r(12543),j=r(28328),f=r(18763),v=r(77223),N=r(31949),g=r(58127),p=r(15300),w=r(51920),y=r(88390),b=r(69321),A=r(40879),E=r(98691);r(12115);var k=r(61840),D=r(5484);function S(e){let{vehicleInfo:s,showViewButton:r=!0}=e;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Vehicle Information"]})}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Make & Model"}),(0,t.jsxs)("p",{className:"font-semibold",children:[s.make," ",s.model]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Year"}),(0,t.jsx)("p",{className:"font-semibold",children:s.year})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"License Plate"}),(0,t.jsx)("p",{className:"font-semibold",children:s.licensePlate})]}),s.color&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Color"}),(0,t.jsx)("p",{className:"font-semibold",children:s.color})]})]}),r&&(0,t.jsx)(d.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,t.jsxs)(h(),{href:"/vehicles/".concat(s.id),children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"View Vehicle Details",(0,t.jsx)(k.A,{className:"ml-2 h-4 w-4"})]})})]})]})}function C(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Vehicle Information"]})}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Make & Model"}),(0,t.jsx)(c.E,{className:"h-6 w-32"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Year"}),(0,t.jsx)(c.E,{className:"h-6 w-16"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"License Plate"}),(0,t.jsx)(c.E,{className:"h-6 w-24"})]})]}),(0,t.jsx)(c.E,{className:"h-10 w-full"})]})]})}function R(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Vehicle Information"]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Unable to load vehicle information"})})]})}function I(e){let{vehicleId:s,showViewButton:r=!0,className:a}=e,{vehicleInfo:c,isLoading:l,error:d}=(0,D.g)(s);return(0,t.jsxs)(i.Zp,{className:a,children:[l&&(0,t.jsx)(C,{}),d&&(0,t.jsx)(R,{}),c&&(0,t.jsx)(S,{vehicleInfo:c,showViewButton:r})]})}function P(){var e,s;let r=(0,a.useParams)(),x=(0,a.useRouter)(),{toast:k}=(0,A.dj)();if(!r||"string"!=typeof r.id)return(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,t.jsx)("p",{children:"Error: Invalid or missing Service Record ID."}),(0,t.jsx)(d.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(h(),{href:"/service-history",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})})]});let D=r.id,{data:S,isLoading:C,error:R}=(0,E.WV)(D,{enabled:!!D}),P=(0,E.xT)(),B=async()=>{if(S)try{await P.mutateAsync({id:D,vehicleId:S.vehicleId}),k({title:"Deleted!",description:"Service record deleted successfully.",variant:"default"}),x.push("/service-history")}catch(e){k({title:"Error",description:"Failed to delete service record. Please try again.",variant:"destructive"})}};return C?(0,t.jsx)("div",{className:"container mx-auto py-8",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(c.E,{className:"h-8 w-3/4"})}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsx)(c.E,{className:"h-4 w-full"}),(0,t.jsx)(c.E,{className:"h-4 w-full"}),(0,t.jsx)(c.E,{className:"h-4 w-2/3"})]})]})}):R?(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,t.jsxs)("p",{children:["Error loading service record: ",R.message]}),(0,t.jsx)(d.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(h(),{href:"/service-history",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})})]}):S||C?S?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.$,{asChild:!0,variant:"outline",size:"sm",children:(0,t.jsxs)(h(),{href:"/service-history",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(n.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,t.jsx)(j.A,{className:"h-3 w-3"}),S.vehicleMake," ",S.vehicleModel]}),(0,t.jsx)(d.$,{asChild:!0,size:"sm",children:(0,t.jsxs)(h(),{href:"/service-records/".concat(S.id,"/edit"),children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,t.jsxs)(m.Lt,{children:[(0,t.jsx)(m.tv,{asChild:!0,children:(0,t.jsxs)(d.$,{variant:"destructive",size:"sm",disabled:P.isPending,children:[(0,t.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Delete"]})}),(0,t.jsxs)(m.EO,{children:[(0,t.jsxs)(m.wd,{children:[(0,t.jsxs)(m.r7,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5 text-destructive"}),"Delete Service Record"]}),(0,t.jsxs)(m.$v,{children:["Are you sure you want to delete this service record? This action cannot be undone.",(0,t.jsxs)("div",{className:"mt-2 p-3 bg-muted rounded-md",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Record Details:"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[null==(e=S.servicePerformed)?void 0:e.join(", ")," -"," ",new Date(S.date).toLocaleDateString()]})]})]})]}),(0,t.jsxs)(m.ck,{children:[(0,t.jsx)(m.Zr,{children:"Cancel"}),(0,t.jsx)(m.Rx,{onClick:B,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete Record"})]})]})]})]})]}),(0,t.jsxs)(o.Fc,{className:"border-green-200 bg-green-50",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)(o.TN,{className:"text-green-800",children:"Service record loaded successfully. All information is up to date."})]}),(0,t.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,t.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5"}),"Service Details"]})}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,t.jsx)(w.A,{className:"h-5 w-5 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Service Date"}),(0,t.jsx)("p",{className:"font-semibold",children:new Date(S.date).toLocaleDateString()})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Cost"}),(0,t.jsxs)("p",{className:"font-semibold",children:["$",(null!=(s=S.cost)?s:0).toFixed(2)]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Odometer"}),(0,t.jsxs)("p",{className:"font-semibold",children:[S.odometer.toLocaleString()," miles"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Record ID"}),(0,t.jsx)("p",{className:"font-semibold font-mono text-sm",children:S.id})]})]})]}),(0,t.jsx)(l.w,{}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Services Performed"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:S.servicePerformed.map((e,s)=>(0,t.jsx)(n.E,{variant:"outline",children:e},s))})]}),S.notes&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l.w,{}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Notes"}),(0,t.jsx)("p",{className:"text-sm bg-muted/50 p-3 rounded-lg",children:S.notes})]})]})]})]})}),(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)(I,{vehicleId:S.vehicleId})})]})]}):null:(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-gray-500",children:[(0,t.jsx)("p",{children:"No service record data available."}),(0,t.jsx)(d.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(h(),{href:"/service-history",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,6897,3860,9664,375,6874,5106,1852,4036,4767,8950,1701,8441,1684,7358],()=>s(44462)),_N_E=e.O()}]);