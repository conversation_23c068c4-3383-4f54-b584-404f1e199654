"use strict";exports.id=6342,exports.ids=[6342],exports.modules={34729:(e,a,l)=>{l.d(a,{T:()=>i});var s=l(60687),t=l(43210),r=l(22482);let i=t.forwardRef(({className:e,...a},l)=>(0,s.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:l,...a}));i.displayName="Textarea"},55817:(e,a,l)=>{l.d(a,{A:()=>s});let s=(0,l(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},71273:(e,a,l)=>{l.d(a,{A:()=>s});let s=(0,l(82614).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},71669:(e,a,l)=>{l.d(a,{C5:()=>v,MJ:()=>g,Rr:()=>j,eI:()=>h,lR:()=>p,lV:()=>d,zB:()=>m});var s=l(60687),t=l(43210),r=l(8730),i=l(27605),n=l(22482),o=l(80013);let d=i.Op,c=t.createContext({}),m=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(i.xI,{...e})}),x=()=>{let e=t.useContext(c),a=t.useContext(u),{getFieldState:l,formState:s}=(0,i.xW)(),r=l(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=a;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...r}},u=t.createContext({}),h=t.forwardRef(({className:e,...a},l)=>{let r=t.useId();return(0,s.jsx)(u.Provider,{value:{id:r},children:(0,s.jsx)("div",{ref:l,className:(0,n.cn)("space-y-2",e),...a})})});h.displayName="FormItem";let p=t.forwardRef(({className:e,...a},l)=>{let{error:t,formItemId:r}=x();return(0,s.jsx)(o.J,{ref:l,className:(0,n.cn)(t&&"text-destructive",e),htmlFor:r,...a})});p.displayName="FormLabel";let g=t.forwardRef(({...e},a)=>{let{error:l,formItemId:t,formDescriptionId:i,formMessageId:n}=x();return(0,s.jsx)(r.DX,{ref:a,id:t,"aria-describedby":l?`${i} ${n}`:`${i}`,"aria-invalid":!!l,...e})});g.displayName="FormControl";let j=t.forwardRef(({className:e,...a},l)=>{let{formDescriptionId:t}=x();return(0,s.jsx)("p",{ref:l,id:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...a})});j.displayName="FormDescription";let v=t.forwardRef(({className:e,children:a,...l},t)=>{let{error:r,formMessageId:i}=x(),o=r?String(r?.message??""):a;return o?(0,s.jsx)("p",{ref:t,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...l,children:o}):null});v.displayName="FormMessage"},92718:(e,a,l)=>{l.d(a,{I:()=>n});var s=l(60687),t=l(63442),r=l(27605),i=l(71669);let n=({children:e,defaultValues:a,onSubmit:l,schema:n,className:o="",ariaAttributes:d={}})=>{let c=(0,r.mN)({...a&&{defaultValues:a},resolver:(0,t.u)(n)}),m=async e=>{await l(e)};return(0,s.jsx)(i.lV,{...c,children:(0,s.jsx)("form",{onSubmit:c.handleSubmit(m),className:o,...d,children:e})})}},92929:(e,a,l)=>{l.d(a,{z:()=>d});var s=l(60687);l(43210);var t=l(27605),r=l(71669),i=l(89667),n=l(34729),o=l(15079);let d=({className:e="",disabled:a=!1,label:l,name:d,placeholder:c,render:m,type:x="text",options:u=[],defaultValue:h,icon:p,...g})=>{let{control:j}=(0,t.xW)();return(0,s.jsxs)(r.eI,{className:e,children:[(0,s.jsx)(r.lR,{htmlFor:d,children:l}),(0,s.jsx)(t.xI,{control:j,name:d,render:m||(({field:e,fieldState:{error:t}})=>(0,s.jsx)(r.MJ,{children:"select"===x?(0,s.jsxs)(o.l6,{onValueChange:e.onChange,value:e.value||h||"",disabled:a,children:[(0,s.jsx)(o.bq,{className:t?"border-red-500":"",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[p&&(0,s.jsx)(p,{className:"h-4 w-4 text-gray-500"}),(0,s.jsx)(o.yv,{placeholder:c||`Select ${l.toLowerCase()}`})]})}),(0,s.jsx)(o.gC,{children:u.map(e=>(0,s.jsx)(o.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===x?(0,s.jsxs)("div",{className:"relative",children:[p&&(0,s.jsx)(p,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,s.jsx)(n.T,{...e,...g,value:e.value??"",className:`${t?"border-red-500":""} ${p?"pl-10":""}`,disabled:a,id:d,placeholder:c})]}):(0,s.jsxs)("div",{className:"relative",children:[p&&(0,s.jsx)(p,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,s.jsx)(i.p,{...e,...g,value:e.value??"",className:`${t?"border-red-500":""} ${p?"pl-10":""}`,disabled:a,id:d,placeholder:c,type:x})]})}))}),(0,s.jsx)(r.C5,{})]})}},96342:(e,a,l)=>{l.d(a,{N:()=>j});var s=l(60687),t=l(55817),r=l(71273),i=l(16189);l(43210);var n=l(27605),o=l(29523),d=l(44493),c=l(92718),m=l(92929),x=l(89667),u=l(15079),h=l(74880),p=l(1814),g=l(15795);let j=({initialData:e={},isEditing:a=!1,onSubmit:l})=>{let n=(0,i.useRouter)(),m={availability:e.availability??void 0,contactEmail:e.contactEmail??"",contactInfo:e.contactInfo??e.contactEmail??"",contactMobile:e.contactMobile??"",contactPhone:e.contactPhone??"",currentLocation:e.currentLocation??"",department:e.department??"",employeeId:e.employeeId??"",fullName:e.fullName??e.name??"",generalAssignments:e.generalAssignments??[],hireDate:e.hireDate?new Date(e.hireDate).toISOString().split("T")[0]:"",name:e.name??e.fullName??"",notes:e.notes??"",position:e.position??"",profileImageUrl:e.profileImageUrl??"",role:e.role??"other",shiftSchedule:e.shiftSchedule??"",skills:e.skills??[],status:e.status??p.O2.enum.Active,workingHours:e.workingHours??""},x=async e=>{await l(e)};return(0,s.jsx)(c.I,{defaultValues:m,onSubmit:x,schema:p.gT,children:(0,s.jsxs)(d.Zp,{className:"shadow-lg",children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-2xl text-primary",children:a?"Edit Employee":"Add New Employee"})}),(0,s.jsx)(d.Wu,{className:"space-y-6",children:(0,s.jsx)(v,{})}),(0,s.jsxs)(d.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,s.jsxs)(o.$,{onClick:()=>n.back(),type:"button",variant:"outline",children:[(0,s.jsx)(t.A,{className:"mr-2 size-4"}),"Cancel"]}),(0,s.jsxs)(o.$,{className:"bg-accent text-accent-foreground hover:bg-accent/90",type:"submit",children:[(0,s.jsx)(r.A,{className:"mr-2 size-4"}),a?"Save Changes":"Create Employee"]})]})]})})},v=()=>{let{watch:e}=(0,n.xW)(),a=e("role");return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsx)(m.z,{label:"Display Name",name:"name"}),(0,s.jsx)(m.z,{label:"Full Name (Optional)",name:"fullName"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsx)(m.z,{label:"Employee ID (Business Key)",name:"employeeId"}),(0,s.jsx)(m.z,{label:"Position/Title",name:"position"})]}),(0,s.jsx)(m.z,{label:"Department",name:"department"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Contact Information"}),(0,s.jsx)(m.z,{label:"Primary Contact (Email/Phone)",name:"contactInfo"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsx)(m.z,{label:"Contact Email (Optional)",name:"contactEmail",type:"email"}),(0,s.jsx)(m.z,{label:"Contact Phone (Optional)",name:"contactPhone",type:"tel"})]}),(0,s.jsx)(m.z,{label:"Contact Mobile (Optional)",name:"contactMobile",type:"tel"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Employment Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsx)(m.z,{label:"Hire Date",name:"hireDate",type:"date"}),(0,s.jsx)(m.z,{label:"Status",name:"status",render:({field:e})=>(0,s.jsxs)(u.l6,{onValueChange:e.onChange,value:e.value,children:[(0,s.jsx)(u.bq,{id:"status",children:(0,s.jsx)(u.yv,{placeholder:"Select status"})}),(0,s.jsx)(u.gC,{children:p.O2.options.map(e=>(0,s.jsx)(u.eb,{value:e,children:(0,g.vq)(e)},e))})]})})]}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Role & Availability"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsx)(m.z,{label:"Role",name:"role",render:({field:e})=>(0,s.jsxs)(u.l6,{onValueChange:e.onChange,value:e.value,children:[(0,s.jsx)(u.bq,{id:"role",children:(0,s.jsx)(u.yv,{placeholder:"Select role"})}),(0,s.jsx)(u.gC,{children:p.Q.options.map(e=>(0,s.jsx)(u.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")},e))})]})}),"driver"===a&&(0,s.jsx)(m.z,{label:"Availability (for Drivers)",name:"availability",render:({field:e})=>(0,s.jsxs)(u.l6,{value:e.value||"",onValueChange:e.onChange,children:[(0,s.jsx)(u.bq,{id:"availability",children:(0,s.jsx)(u.yv,{placeholder:"Select availability"})}),(0,s.jsx)(u.gC,{children:h.X.options.map(e=>(0,s.jsx)(u.eb,{value:e,children:e.replace("_"," ")},e))})]})})]}),"driver"===a&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsx)(m.z,{label:"Current Location (Optional, for Drivers)",name:"currentLocation"}),(0,s.jsx)(m.z,{label:"Working Hours (Optional, for Drivers)",name:"workingHours"})]})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Other Details"}),(0,s.jsx)(m.z,{label:"Skills (comma-separated)",name:"skills",render:({field:e})=>(0,s.jsx)(x.p,{onChange:a=>{let l=a.target.value.split(",").map(e=>e.trim()).filter(Boolean);e.onChange(l)},placeholder:"e.g., Diesel Engine Repair, HVAC Systems, Welding",value:Array.isArray(e.value)?e.value.join(", "):""})}),(0,s.jsx)(m.z,{label:"Shift Schedule (Optional)",name:"shiftSchedule"}),(0,s.jsx)(m.z,{label:"General Assignments (comma-separated, Optional)",name:"generalAssignments",render:({field:e})=>(0,s.jsx)(x.p,{onChange:a=>{let l=a.target.value.split(",").map(e=>e.trim()).filter(Boolean);e.onChange(l)},placeholder:"e.g., Workshop Cleanup, Inventory Check",value:Array.isArray(e.value)?e.value.join(", "):""})}),(0,s.jsx)(m.z,{label:"Profile Image URL (Optional)",name:"profileImageUrl"}),(0,s.jsx)(m.z,{label:"Notes (Optional)",name:"notes",type:"textarea"})]})}}};