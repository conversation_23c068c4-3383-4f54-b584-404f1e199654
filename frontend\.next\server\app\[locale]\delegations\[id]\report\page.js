(()=>{var e={};e.id=9360,e.ids=[9360],e.modules={997:(e,t,s)=>{"use strict";s.d(t,{k:()=>x});var r=s(60687),i=s(28946),a=s(11516),n=s(20620),l=s(36644);let o=(0,s(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var c=s(43210),d=s(68752),u=s(21342),h=s(3940),p=s(22482),m=s(22364);function x({className:e,csvData:t,enableCsv:s=!1,entityId:x,fileName:g,reportContentId:b,reportType:y,tableId:f}){let[v,j]=(0,c.useState)(!1),[N,w]=(0,c.useState)(!1),{showFormSuccess:D,showFormError:C}=(0,h.t6)(),k=async()=>{j(!0);try{let e=`/api/reports/${y}${x?`/${x}`:""}`,t=document.createElement("a");t.href=e,t.download=`${g}.pdf`,t.target="_blank",document.body.append(t),t.click(),t.remove(),D({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),C(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{j(!1)}},A=async()=>{if(s){w(!0);try{if(t?.data&&t.headers)(0,m.og)(t.data,t.headers,`${g}.csv`);else if(f){let e=(0,m.tL)(f);(0,m.og)(e.data,e.headers,`${g}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");D({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),C(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{w(!1)}}},M=v||N;return(0,r.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 no-print",e),children:[(0,r.jsx)(d.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,r.jsx)(i.A,{className:"size-4"})}),(0,r.jsxs)(u.rI,{children:[(0,r.jsx)(u.ty,{asChild:!0,children:(0,r.jsx)(d.r,{actionType:"secondary","aria-label":"Download report",disabled:M,size:"icon",title:"Download Report",children:M?(0,r.jsx)(a.A,{className:"size-4 animate-spin"}):(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsxs)(u.SQ,{align:"end",children:[(0,r.jsxs)(u._2,{disabled:v,onClick:k,children:[v?(0,r.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(l.A,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download PDF"})]}),s&&(0,r.jsxs)(u._2,{disabled:N,onClick:A,children:[N?(0,r.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(o,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15795:(e,t,s)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function i(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let t=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(t.toLowerCase())||t.includes("_")){let e=t.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return t}if(e.role){let t=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${t} (Role)`}return"Unknown Employee"}function a(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function n(e){return e.replaceAll("_"," ")}s.d(t,{DV:()=>i,fZ:()=>r,s:()=>a,vq:()=>n})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},26398:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},26457:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(65239),i=s(48088),a=s(88170),n=s.n(a),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["[locale]",{children:["delegations",{children:["[id]",{children:["report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,58796)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\[id]\\report\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,30023)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\[id]\\report\\layout.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\[id]\\report\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/delegations/[id]/report/page",pathname:"/[locale]/delegations/[id]/report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28946:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30023:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a,metadata:()=>i});var r=s(37413);let i={title:"Delegation Report"};function a({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},33407:(e,t,s)=>{Promise.resolve().then(s.bind(s,58796))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38366:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),i=s(75699),a=s(58261),n=s(52856),l=s(15036),o=s(26398),c=s(30474),d=s(16189);s(43210);var u=s(997),h=s(96834),p=s(52027),m=s(63502),x=s(22482),g=s(15795);let b=(e,t=!1)=>{if(!e)return"N/A";try{return(0,i.GP)((0,a.H)(e),t?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch{return"Invalid Date"}},y=e=>{switch(e){case"Cancelled":return"bg-red-100 text-red-800 border-red-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}};function f(){let e=(0,d.useParams)().id,{data:t,error:s,isError:i,isLoading:a}=(0,m.kA)(e);return a?(0,r.jsxs)("div",{className:"mx-auto max-w-4xl p-4",children:[(0,r.jsx)(p.jt,{count:1,variant:"card"}),(0,r.jsx)(p.jt,{className:"mt-6",count:3,variant:"table"}),(0,r.jsx)(p.jt,{className:"mt-6",count:2,variant:"table"})]}):i||!t?(0,r.jsxs)("div",{className:"py-10 text-center",children:["Error loading delegation report or delegation not found."," ",s?.message]}):(0,r.jsxs)("div",{className:"mx-auto max-w-4xl bg-white p-2 text-gray-800 sm:p-4",children:[(0,r.jsx)("div",{className:"no-print mb-4 text-right",children:(0,r.jsx)(u.k,{enableCsv:t.delegates&&t.delegates.length>0||t.statusHistory&&t.statusHistory.length>0,fileName:`delegation-report-${t.eventName.replaceAll(/\s+/g,"-")}`,reportContentId:"#delegation-report-content",tableId:"#delegates-table"})}),(0,r.jsxs)("div",{className:"report-content",id:"delegation-report-content",children:[(0,r.jsxs)("header",{className:"mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Delegation Report"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:t.eventName}),(0,r.jsxs)(h.E,{className:(0,x.cn)("mt-2 text-sm py-1 px-3 font-semibold",y(t.status)),children:["Status: ",(0,g.fZ)(t.status)]})]}),(0,r.jsxs)("section",{className:"card-print mb-6 rounded border border-gray-200 p-4",children:[(0,r.jsx)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:"Delegation Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-x-4 gap-y-2 text-sm md:grid-cols-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Event Name:"})," ",t.eventName]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Location:"})," ",t.location]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Duration:"})," ",b(t.durationFrom)," ","to ",b(t.durationTo)]}),t.invitationFrom&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Invitation From:"})," ",t.invitationFrom]}),t.invitationTo&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Invitation To:"})," ",t.invitationTo]})]}),t.imageUrl&&(0,r.jsx)("div",{className:"no-print relative mx-auto mt-4 aspect-[16/9] w-full max-w-md overflow-hidden rounded",children:(0,r.jsx)(c.default,{alt:t.eventName,"data-ai-hint":"event placeholder",layout:"fill",objectFit:"contain",src:t.imageUrl})}),t.notes&&(0,r.jsxs)("div",{className:"mt-3 text-sm",children:[(0,r.jsx)("strong",{children:"Notes:"})," ",(0,r.jsx)("span",{className:"italic",children:t.notes})]})]}),(0,r.jsxs)("section",{className:"card-print mb-6 rounded border border-gray-200 p-4",children:[(0,r.jsxs)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:["Delegates (",t.delegates?.length||0,")"]}),t.delegates&&t.delegates.length>0?(0,r.jsxs)("table",{className:"w-full text-left text-sm text-gray-600",id:"delegates-table",children:[(0,r.jsx)("thead",{className:"bg-gray-50 text-xs uppercase text-gray-700",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Name"}),(0,r.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Title"}),(0,r.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Notes"})]})}),(0,r.jsx)("tbody",{children:t.delegates.map(e=>(0,r.jsxs)("tr",{className:"border-b bg-white hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-3 py-2 font-medium",children:e.name}),(0,r.jsx)("td",{className:"px-3 py-2",children:e.title}),(0,r.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"})]},e.id))})]}):(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No delegates listed."})]}),(t.flightArrivalDetails||t.flightDepartureDetails)&&(0,r.jsxs)("section",{className:"card-print mb-6 rounded border border-gray-200 p-4",children:[(0,r.jsx)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:"Flight Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 text-sm md:grid-cols-2",children:[t.flightArrivalDetails&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-md mb-1 font-semibold",children:"Arrival Details"}),(0,r.jsxs)("p",{children:[(0,r.jsx)(n.A,{className:"mr-1 inline size-4"}),(0,r.jsx)("strong",{children:"Flight:"})," ",t.flightArrivalDetails.flightNumber]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(l.A,{className:"mr-1 inline size-4"}),(0,r.jsx)("strong",{children:"Time:"})," ",b(t.flightArrivalDetails.dateTime,!0)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(o.A,{className:"mr-1 inline size-4"}),(0,r.jsx)("strong",{children:"Airport:"})," ",t.flightArrivalDetails.airport," ",t.flightArrivalDetails.terminal&&`(Terminal ${t.flightArrivalDetails.terminal})`]}),t.flightArrivalDetails.notes&&(0,r.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,r.jsx)("strong",{children:"Notes:"})," ",t.flightArrivalDetails.notes]})]}),t.flightDepartureDetails&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-md mb-1 font-semibold",children:"Departure Details"}),(0,r.jsxs)("p",{children:[(0,r.jsx)(n.A,{className:"mr-1 inline size-4"}),(0,r.jsx)("strong",{children:"Flight:"})," ",t.flightDepartureDetails.flightNumber]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(l.A,{className:"mr-1 inline size-4"}),(0,r.jsx)("strong",{children:"Time:"})," ",b(t.flightDepartureDetails.dateTime,!0)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(o.A,{className:"mr-1 inline size-4"}),(0,r.jsx)("strong",{children:"Airport:"})," ",t.flightDepartureDetails.airport," ",t.flightDepartureDetails.terminal&&`(Terminal ${t.flightDepartureDetails.terminal})`]}),t.flightDepartureDetails.notes&&(0,r.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,r.jsx)("strong",{children:"Notes:"})," ",t.flightDepartureDetails.notes]})]})]}),!t.flightArrivalDetails&&!t.flightDepartureDetails&&(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No flight details logged."})]}),(0,r.jsxs)("section",{className:"card-print rounded border border-gray-200 p-4",children:[(0,r.jsx)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:"Status History"}),t.statusHistory&&t.statusHistory.length>0?(0,r.jsxs)("table",{className:"w-full text-left text-sm text-gray-600",id:"status-history-table",children:[(0,r.jsx)("thead",{className:"bg-gray-50 text-xs uppercase text-gray-700",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Status"}),(0,r.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Changed At"}),(0,r.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Reason"})]})}),(0,r.jsx)("tbody",{children:[...t.statusHistory].sort((e,t)=>new Date(t.changedAt).getTime()-new Date(e.changedAt).getTime()).map(e=>(0,r.jsxs)("tr",{className:"border-b bg-white hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-3 py-2",children:(0,r.jsx)(h.E,{className:(0,x.cn)("text-xs py-0.5 px-1.5",y(e.status)),children:(0,g.fZ)(e.status)})}),(0,r.jsx)("td",{className:"px-3 py-2",children:b(e.changedAt,!0)}),(0,r.jsx)("td",{className:"px-3 py-2",children:e.reason||"-"})]},e.id))})]}):(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No status history available."})]}),(0,r.jsxs)("footer",{className:"mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,r.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,r.jsx)("p",{children:"WorkHub - Delegation Management"})]})]})]})}},52856:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},54050:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});var r=s(43210),i=s(65406),a=s(33465),n=s(35536),l=s(31212),o=class extends n.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#a(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#a()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#i(){let e=this.#s?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){a.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},c=s(8693);function d(e,t){let s=(0,c.jE)(t),[i]=r.useState(()=>new o(s,e));r.useEffect(()=>{i.setOptions(e)},[i,e]);let n=r.useSyncExternalStore(r.useCallback(e=>i.subscribe(a.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=r.useCallback((e,t)=>{i.mutate(e,t).catch(l.lQ)},[i]);if(n.error&&(0,l.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58796:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\delegations\\\\[id]\\\\report\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\[id]\\report\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70359:(e,t,s)=>{Promise.resolve().then(s.bind(s,38366))},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93425:(e,t,s)=>{"use strict";s.d(t,{E:()=>x});var r=s(43210),i=s(33465),a=s(5563),n=s(35536),l=s(31212);function o(e,t){let s=new Set(t);return e.filter(e=>!s.has(e))}var c=class extends n.Q{#e;#n;#l;#o;#c;#d;#u;#h;#p=[];constructor(e,t,s){super(),this.#e=e,this.#o=s,this.#l=[],this.#c=[],this.#n=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#c.forEach(e=>{e.subscribe(t=>{this.#m(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#c.forEach(e=>{e.destroy()})}setQueries(e,t){this.#l=e,this.#o=t,i.jG.batch(()=>{let e=this.#c,t=this.#x(this.#l);this.#p=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let s=t.map(e=>e.observer),r=s.map(e=>e.getCurrentResult()),i=s.some((t,s)=>t!==e[s]);(e.length!==s.length||i)&&(this.#c=s,this.#n=r,this.hasListeners()&&(o(e,s).forEach(e=>{e.destroy()}),o(s,e).forEach(e=>{e.subscribe(t=>{this.#m(e,t)})}),this.#a()))})}getCurrentResult(){return this.#n}getQueries(){return this.#c.map(e=>e.getCurrentQuery())}getObservers(){return this.#c}getOptimisticResult(e,t){let s=this.#x(e),r=s.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[r,e=>this.#g(e??r,t),()=>this.#b(r,s)]}#b(e,t){return t.map((s,r)=>{let i=e[r];return s.defaultedQueryOptions.notifyOnChangeProps?i:s.observer.trackResult(i,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#g(e,t){return t?(this.#d&&this.#n===this.#h&&t===this.#u||(this.#u=t,this.#h=this.#n,this.#d=(0,l.BH)(this.#d,t(e))),this.#d):e}#x(e){let t=new Map(this.#c.map(e=>[e.options.queryHash,e])),s=[];return e.forEach(e=>{let r=this.#e.defaultQueryOptions(e),i=t.get(r.queryHash);i?s.push({defaultedQueryOptions:r,observer:i}):s.push({defaultedQueryOptions:r,observer:new a.$(this.#e,r)})}),s}#m(e,t){let s=this.#c.indexOf(e);-1!==s&&(this.#n=function(e,t,s){let r=e.slice(0);return r[t]=s,r}(this.#n,s,t),this.#a())}#a(){if(this.hasListeners()){let e=this.#d,t=this.#b(this.#n,this.#p);e!==this.#g(t,this.#o?.combine)&&i.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#n)})})}}},d=s(8693),u=s(24903),h=s(18228),p=s(16142),m=s(76935);function x({queries:e,...t},s){let n=(0,d.jE)(s),o=(0,u.w)(),x=(0,h.h)(),g=r.useMemo(()=>e.map(e=>{let t=n.defaultQueryOptions(e);return t._optimisticResults=o?"isRestoring":"optimistic",t}),[e,n,o]);g.forEach(e=>{(0,m.jv)(e),(0,p.LJ)(e,x)}),(0,p.wZ)(x);let[b]=r.useState(()=>new c(n,g,t)),[y,f,v]=b.getOptimisticResult(g,t.combine),j=!o&&!1!==t.subscribed;r.useSyncExternalStore(r.useCallback(e=>j?b.subscribe(i.jG.batchCalls(e)):l.lQ,[b,j]),()=>b.getCurrentResult(),()=>b.getCurrentResult()),r.useEffect(()=>{b.setQueries(g,t)},[g,t,b]);let N=y.some((e,t)=>(0,m.EU)(g[t],e))?y.flatMap((e,t)=>{let s=g[t];if(s){let t=new a.$(n,s);if((0,m.EU)(s,e))return(0,m.iL)(s,t,x);(0,m.nE)(e,o)&&(0,m.iL)(s,t,x)}return[]}):[];if(N.length>0)throw Promise.all(N);let w=y.find((e,t)=>{let s=g[t];return s&&(0,p.$1)({result:e,errorResetBoundary:x,throwOnError:s.throwOnError,query:n.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(w?.error)throw w.error;return f(v())}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,4825,625,9851,8390,474,2482,9794,3502,5782],()=>s(26457));module.exports=r})();