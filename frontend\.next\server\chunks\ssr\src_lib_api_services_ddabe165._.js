module.exports = {

"[project]/src/lib/api/services/domain/reliabilityApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReliabilityApiService": (()=>ReliabilityApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
const ReliabilityTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class ReliabilityApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/reliability';
    transformer = ReliabilityTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 1 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    async acknowledgeAlert(alertId, note, acknowledgedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/acknowledge`, {
                acknowledgedBy,
                note
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async getActiveAlerts() {
        return this.executeWithInfrastructure('alerts:active', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/alerts');
                return apiResponse?.alerts || [];
            } catch (error) {
                console.error('Failed to get active alerts:', error);
                return [];
            }
        });
    }
    async getAlertHistory(page = 1, limit = 50) {
        return this.executeWithInfrastructure(`alerts:history:${page}:${limit}`, async ()=>{
            const queryParams = new URLSearchParams({
                limit: limit.toString(),
                page: page.toString()
            });
            const response = await this.apiClient.get(`/alerts/history?${queryParams.toString()}`);
            return response;
        });
    }
    async getAlertStatistics() {
        return this.executeWithInfrastructure('alerts:statistics', async ()=>{
            try {
                const response = await this.apiClient.get('/alerts/statistics');
                return response;
            } catch (error) {
                console.error('Failed to get alert statistics:', error);
                return {
                    acknowledged: 0,
                    active: 0,
                    averageResolutionTime: 0,
                    bySeverity: {
                        critical: 0,
                        high: 0,
                        low: 0,
                        medium: 0
                    },
                    recentTrends: {
                        last7Days: 0,
                        last24Hours: 0,
                        last30Days: 0
                    },
                    resolved: 0,
                    total: 0
                };
            }
        });
    }
    async getCircuitBreakerHistory(timeframe = '24h', breakerName) {
        return this.executeWithInfrastructure(`circuit-breakers:history:${timeframe}:${breakerName || 'all'}`, async ()=>{
            const params = new URLSearchParams({
                timeframe
            });
            if (breakerName) {
                params.append('breakerName', breakerName);
            }
            const response = await this.apiClient.get(`/monitoring/circuit-breakers/history?${params.toString()}`);
            return response;
        });
    }
    async getCircuitBreakerStatus() {
        return this.executeWithInfrastructure('monitoring:circuit-breakers', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/circuit-breakers');
                const circuitBreakers = apiResponse?.circuitBreakers || [];
                return {
                    circuitBreakers: circuitBreakers || [],
                    summary: {
                        closed: circuitBreakers?.filter((cb)=>cb.state === 'CLOSED').length || 0,
                        halfOpen: circuitBreakers?.filter((cb)=>cb.state === 'HALF_OPEN').length || 0,
                        open: circuitBreakers?.filter((cb)=>cb.state === 'OPEN').length || 0,
                        total: circuitBreakers?.length || 0
                    }
                };
            } catch (error) {
                console.error('Failed to get circuit breaker status:', error);
                return {
                    circuitBreakers: [],
                    summary: {
                        closed: 0,
                        halfOpen: 0,
                        open: 0,
                        total: 0
                    }
                };
            }
        });
    }
    async getCriticalAlertCount() {
        try {
            const statistics = await this.getAlertStatistics();
            return statistics.bySeverity.critical;
        } catch  {
            return 0;
        }
    }
    async getDeduplicationMetrics() {
        return this.executeWithInfrastructure('monitoring:deduplication', async ()=>{
            const response = await this.apiClient.get('/monitoring/deduplication');
            return response;
        });
    }
    async getDependencyHealth() {
        return this.executeWithInfrastructure('health:dependencies', async ()=>{
            const response = await this.apiClient.get('/health/dependencies');
            return response;
        });
    }
    async getDetailedHealth() {
        return this.executeWithInfrastructure('health:detailed', async ()=>{
            const response = await this.apiClient.get('/health/detailed');
            return response;
        });
    }
    async getHealthTrends(timeframe = '24h') {
        return this.executeWithInfrastructure(`health:trends:${timeframe}`, async ()=>{
            const response = await this.apiClient.get(`/health/trends?timeframe=${timeframe}`);
            return response;
        });
    }
    async getHttpRequestMetrics() {
        return this.executeWithInfrastructure('http:metrics', async ()=>{
            const response = await this.apiClient.get('/http-request-metrics');
            return response;
        });
    }
    async getMetrics() {
        return this.executeWithInfrastructure('metrics:system', async ()=>{
            const response = await this.apiClient.get('/metrics', {
                headers: {
                    Accept: 'application/json'
                }
            });
            return response;
        });
    }
    async getReliabilityDashboardData() {
        const [systemHealth, detailedHealth, circuitBreakers, metrics, activeAlerts, alertStatistics] = await Promise.all([
            this.getSystemHealth(),
            this.getDetailedHealth(),
            this.getCircuitBreakerStatus(),
            this.getMetrics(),
            this.getActiveAlerts(),
            this.getAlertStatistics()
        ]);
        return {
            activeAlerts,
            alertStatistics,
            circuitBreakers,
            detailedHealth,
            metrics,
            systemHealth
        };
    }
    async getSystemHealth() {
        return this.executeWithInfrastructure('health:system', async ()=>{
            const response = await this.apiClient.get('/health');
            return response;
        });
    }
    async isSystemHealthy() {
        try {
            const health = await this.getSystemHealth();
            return health.status === 'healthy';
        } catch  {
            return false;
        }
    }
    async resolveAlert(alertId, reason, resolvedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/resolve`, {
                reason,
                resolvedBy
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async testAlerts() {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post('/alerts/test');
            return {
                message: response?.message || 'Test alert triggered',
                success: response?.status === 'success',
                testAlertId: response?.data?.id
            };
        });
    }
}
}}),
"[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Factory for creating and managing API service instances.
 * @module api/services/apiServiceFactory
 */ __turbopack_context__.s({
    "ApiServiceFactory": (()=>ApiServiceFactory),
    "apiServiceFactory": (()=>apiServiceFactory),
    "delegationApiService": (()=>delegationApiService),
    "employeeApiService": (()=>employeeApiService),
    "reliabilityApiService": (()=>reliabilityApiService),
    "setFactoryAuthTokenProvider": (()=>setFactoryAuthTokenProvider),
    "taskApiService": (()=>taskApiService),
    "vehicleApiService": (()=>vehicleApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/reliabilityApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
// Import secure auth token provider
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
/**
 * Get the current auth token from the secure provider
 * Uses the single source of truth for authentication tokens
 */ function getSecureAuthToken() {
    const provider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSecureAuthTokenProvider"])();
    if (!provider) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');
        }
        return null;
    }
    try {
        return provider();
    } catch (error) {
        console.error('❌ Factory: Error getting auth token from secure provider:', error);
        return null;
    }
}
function setFactoryAuthTokenProvider(provider) {
    console.warn('⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.');
// This function is now a no-op since we use the secure provider
// The warning guides developers to use the correct function
}
class ApiServiceFactory {
    apiClient;
    delegationService;
    employeeService;
    reliabilityService;
    taskService;
    vehicleService;
    /**
   * Creates an instance of ApiServiceFactory.
   * @param config - Configuration for the API services.
   */ constructor(config){
        this.apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClient"]({
            ...config,
            getAuthToken: getSecureAuthToken
        });
    }
    /**
   * Gets the underlying ApiClient instance.
   * @returns The ApiClient instance.
   */ getApiClient() {
        return this.apiClient;
    }
    /**
   * Gets the Delegation API service instance.
   * @returns The DelegationApiService instance.
   */ getDelegationService() {
        if (!this.delegationService) {
            this.delegationService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationApiService"](this.apiClient);
        }
        return this.delegationService;
    }
    /**
   * Gets the Employee API service instance.
   * @returns The EmployeeApiService instance.
   */ getEmployeeService() {
        if (!this.employeeService) {
            this.employeeService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeApiService"](this.apiClient);
        }
        return this.employeeService;
    }
    /**
   * Gets the Reliability API service instance.
   * @returns The ReliabilityApiService instance.
   */ getReliabilityService() {
        if (!this.reliabilityService) {
            this.reliabilityService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReliabilityApiService"](this.apiClient);
        }
        return this.reliabilityService;
    }
    /**
   * Gets the Task API service instance.
   * @returns The TaskApiService instance.
   */ getTaskService() {
        if (!this.taskService) {
            this.taskService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskApiService"](this.apiClient);
        }
        return this.taskService;
    }
    /**
   * Gets the Vehicle API service instance.
   * @returns The VehicleApiService instance.
   */ getVehicleService() {
        if (!this.vehicleService) {
            this.vehicleService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleApiService"](this.apiClient);
        }
        return this.vehicleService;
    }
}
// Create a default factory instance for the application with environment-aware configuration
const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
const defaultConfig = {
    baseURL: envConfig.apiBaseUrl,
    headers: {
        'Content-Type': 'application/json'
    },
    retryAttempts: 3,
    timeout: 10_000
};
const apiServiceFactory = new ApiServiceFactory(defaultConfig);
const vehicleApiService = apiServiceFactory.getVehicleService();
const delegationApiService = apiServiceFactory.getDelegationService();
const taskApiService = apiServiceFactory.getTaskService();
const employeeApiService = apiServiceFactory.getEmployeeService();
const reliabilityApiService = apiServiceFactory.getReliabilityService();
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file API Service Factory - Backward Compatibility Export
 * @module api/services/apiServiceFactory
 *
 * This file provides backward compatibility for imports that expect
 * apiServiceFactory.ts instead of factory.ts
 */ // Re-export everything from the factory module
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
// Re-export secure auth provider for convenience
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
;
;
;
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setFactoryAuthTokenProvider"]),
    "setSecureAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setSecureAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["setFactoryAuthTokenProvider"]),
    "setSecureAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["setSecureAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=src_lib_api_services_ddabe165._.js.map