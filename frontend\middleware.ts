import type { NextRequest } from 'next/server';

import createIntlMiddleware from 'next-intl/middleware';
import { NextResponse } from 'next/server';

import { routing } from '@/i18n/routing';
import {
  generateSecurityHeaders,
  generateStrictCSP,
} from '@/lib/security/cspConfig';

// Create the intl middleware using the routing configuration
const handleI18nRouting = createIntlMiddleware(routing);

// Public routes that don't require authentication
const publicRoutes = new Set([
  '/',
  '/forgot-password',
  '/login',
  '/reset-password',
  '/signup',
]);

// Protected routes that require authentication
const protectedRoutes = [
  '/admin',
  '/delegations',
  '/tasks',
  '/vehicles',
  '/employees',
  '/reports',
  '/reporting',
  '/settings',
  '/reliability',
  '/profile',
  '/gifts',
  '/recipients',
];

// Define JWT payload interface for type safety
interface JWTPayload {
  exp?: number;
  role?: string;
  sub?: string;
  user_role?: string;
}

/**
 * Main middleware function integrating next-intl with security and authentication
 */
export default async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico')
  ) {
    return handleI18nRouting(request);
  }

  // Handle i18n routing first
  const intlResponse = handleI18nRouting(request);

  // If it's a redirect, return it immediately
  if (intlResponse.status >= 300 && intlResponse.status < 400) {
    return intlResponse;
  }

  // Extract locale and path without locale prefix for auth checks
  const localeMatch = pathname.match(/^\/([a-z]{2}-[A-Z]{2})(\/.*)?$/);
  const pathWithoutLocale = localeMatch?.[2] || '/';

  // Check route types
  const isPublicRoute = publicRoutes.has(pathWithoutLocale);
  const isProtectedRoute = protectedRoutes.some(route =>
    pathWithoutLocale.startsWith(route)
  );

  // Handle authentication for protected routes
  if (isProtectedRoute && !isPublicRoute) {
    const accessToken = request.cookies.get('sb-access-token')?.value;

    if (!accessToken) {
      // Redirect to login with current locale
      const locale = localeMatch?.[1] || routing.defaultLocale;
      const loginUrl = new URL(`/${locale}/login`, request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Basic token validation (signature validation should be done server-side)
    try {
      const tokenParts = accessToken.split('.');
      if (tokenParts.length === 3 && tokenParts[1]) {
        const payload = JSON.parse(atob(tokenParts[1])) as JWTPayload;
        const now = Math.floor(Date.now() / 1000);

        if (payload.exp && payload.exp < now) {
          // Token expired, redirect to login
          const locale = localeMatch?.[1] || routing.defaultLocale;
          const loginUrl = new URL(`/${locale}/login`, request.url);
          loginUrl.searchParams.set('redirect', pathname);
          return NextResponse.redirect(loginUrl);
        }
      }
    } catch {
      // Invalid token, redirect to login
      const locale = localeMatch?.[1] || routing.defaultLocale;
      const loginUrl = new URL(`/${locale}/login`, request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  // Generate security headers
  const nonce = generateNonce();
  const response = intlResponse;

  // Set nonce for CSP
  response.headers.set('x-nonce', nonce);
  response.cookies.set('x-nonce', nonce, {
    httpOnly: false,
    path: '/',
    sameSite: 'strict',
    secure: process.env.NODE_ENV === 'production',
  });

  // Apply security headers
  const cspConfig = {
    isDevelopment: process.env.NODE_ENV === 'development',
    nonce,
    reportUri: '/api/csp-report',
  };

  const securityHeaders = {
    'Content-Security-Policy': generateStrictCSP(cspConfig),
    ...generateSecurityHeaders(),
  };

  for (const [key, value] of Object.entries(securityHeaders)) {
    response.headers.set(key, value);
  }

  return response;
}

/**
 * Generate cryptographically secure nonce
 */
function generateNonce(): string {
  const bytes = new Uint8Array(32);
  crypto.getRandomValues(bytes);
  return Buffer.from(bytes).toString('base64');
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    // Match all pathnames except for
    // - API routes (/api/*)
    // - Next.js internals (/_next/*)
    // - Static files (favicon.ico, etc.)
    String.raw`/((?!api|_next|_vercel|.*\..*).*)`,
  ],
};
