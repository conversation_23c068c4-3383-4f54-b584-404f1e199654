(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2174,9607],{3638:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},5041:(e,t,r)=>{"use strict";r.d(t,{n:()=>d});var n=r(12115),s=r(34560),i=r(7165),o=r(25910),a=r(52020),u=class extends o.Q{#e;#t=void 0;#r;#n;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,a.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,a.EN)(t.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#s(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#s(),this.#i()}mutate(e,t){return this.#n=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#s(){let e=this.#r?.state??(0,s.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#n&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,r),this.#n.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#n.onError?.(e.error,t,r),this.#n.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},l=r(26715);function d(e,t){let r=(0,l.jE)(t),[s]=n.useState(()=>new u(r,e));n.useEffect(()=>{s.setOptions(e)},[s,e]);let o=n.useSyncExternalStore(n.useCallback(e=>s.subscribe(i.jG.batchCalls(e)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),d=n.useCallback((e,t)=>{s.mutate(e,t).catch(a.lQ)},[s]);if(o.error&&(0,a.GU)(s.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:d,mutateAsync:o.mutate}}},10233:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},11518:(e,t,r)=>{"use strict";e.exports=r(82269).style},15300:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},18018:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},20203:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},24371:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},27150:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},28328:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},31896:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},34301:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},44956:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},46786:(e,t,r)=>{"use strict";r.d(t,{KU:()=>c,Zr:()=>p,eh:()=>d,lt:()=>u});let n=new Map,s=e=>{let t=n.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},i=(e,t,r)=>{if(void 0===e)return{type:"untracked",connection:t.connect(r)};let s=n.get(r.name);if(s)return{type:"tracked",store:e,...s};let i={connection:t.connect(r),stores:{}};return n.set(r.name,i),{type:"tracked",store:e,...i}},o=(e,t)=>{if(void 0===t)return;let r=n.get(e);r&&(delete r.stores[t],0===Object.keys(r.stores).length&&n.delete(e))},a=e=>{var t,r;if(!e)return;let n=e.split("\n"),s=n.findIndex(e=>e.includes("api.setState"));if(s<0)return;let i=(null==(t=n[s+1])?void 0:t.trim())||"";return null==(r=/.+ (.+) .+/.exec(i))?void 0:r[1]},u=(e,t={})=>(r,n,u)=>{let d,{enabled:c,anonymousActionType:h,store:p,...f}=t;try{d=(null==c||c)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!d)return e(r,n,u);let{connection:y,...m}=i(p,d,f),v=!0;u.setState=(e,t,i)=>{let o=r(e,t);if(!v)return o;let l=a(Error().stack),d=void 0===i?{type:h||l||"anonymous"}:"string"==typeof i?{type:i}:i;return void 0===p?null==y||y.send(d,n()):null==y||y.send({...d,type:`${p}/${d.type}`},{...s(f.name),[p]:u.getState()}),o},u.devtools={cleanup:()=>{y&&"function"==typeof y.unsubscribe&&y.unsubscribe(),o(f.name,p)}};let S=(...e)=>{let t=v;v=!1,r(...e),v=t},_=e(u.setState,n,u);if("untracked"===m.type?null==y||y.init(_):(m.stores[m.store]=u,null==y||y.init(Object.fromEntries(Object.entries(m.stores).map(([e,t])=>[e,e===m.store?_:t.getState()])))),u.dispatchFromDevtools&&"function"==typeof u.dispatch){let e=!1,t=u.dispatch;u.dispatch=(...r)=>{"__setState"!==r[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...r)}}return y.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return l(e.payload,e=>{if("__setState"===e.type){if(void 0===p)return void S(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[p];return void(null==t||JSON.stringify(u.getState())!==JSON.stringify(t)&&S(t))}u.dispatchFromDevtools&&"function"==typeof u.dispatch&&u.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(S(_),void 0===p)return null==y?void 0:y.init(u.getState());return null==y?void 0:y.init(s(f.name));case"COMMIT":if(void 0===p){null==y||y.init(u.getState());break}return null==y?void 0:y.init(s(f.name));case"ROLLBACK":return l(e.state,e=>{if(void 0===p){S(e),null==y||y.init(u.getState());return}S(e[p]),null==y||y.init(s(f.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return l(e.state,e=>{if(void 0===p)return void S(e);JSON.stringify(u.getState())!==JSON.stringify(e[p])&&S(e[p])});case"IMPORT_STATE":{let{nextLiftedState:r}=e.payload,n=null==(t=r.computedStates.slice(-1)[0])?void 0:t.state;if(!n)return;void 0===p?S(n):S(n[p]),null==y||y.send(null,r);break}case"PAUSE_RECORDING":return v=!v}return}}),_},l=(e,t)=>{let r;try{r=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==r&&t(r)},d=e=>(t,r,n)=>{let s=n.subscribe;return n.subscribe=(e,t,r)=>{let i=e;if(t){let s=(null==r?void 0:r.equalityFn)||Object.is,o=e(n.getState());i=r=>{let n=e(r);if(!s(o,n)){let e=o;t(o=n,e)}},(null==r?void 0:r.fireImmediately)&&t(o,o)}return s(i)},e(t,r,n)};function c(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var n;let s=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(n=r.getItem(e))?n:null;return i instanceof Promise?i.then(s):s(i)},setItem:(e,n)=>r.setItem(e,JSON.stringify(n,null==t?void 0:t.replacer)),removeItem:e=>r.removeItem(e)}}let h=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>h(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>h(t)(e)}}},p=(e,t)=>(r,n,s)=>{let i,o={storage:c(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},a=!1,u=new Set,l=new Set,d=o.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...e)},n,s);let p=()=>{let e=o.partialize({...n()});return d.setItem(o.name,{state:e,version:o.version})},f=s.setState;s.setState=(e,t)=>{f(e,t),p()};let y=e((...e)=>{r(...e),p()},n,s);s.getInitialState=()=>y;let m=()=>{var e,t;if(!d)return;a=!1,u.forEach(e=>{var t;return e(null!=(t=n())?t:y)});let s=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=n())?e:y))||void 0;return h(d.getItem.bind(d))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,a]=e;if(r(i=o.merge(a,null!=(t=n())?t:y),!0),s)return p()}).then(()=>{null==s||s(i,void 0),i=n(),a=!0,l.forEach(e=>e(i))}).catch(e=>{null==s||s(void 0,e)})};return s.persist={setOptions:e=>{o={...o,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>m(),hasHydrated:()=>a,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(l.add(e),()=>{l.delete(e)})},o.skipHydration||m(),i||y}},50594:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},60679:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},65453:(e,t,r)=>{"use strict";r.d(t,{v:()=>u});var n=r(12115);let s=e=>{let t,r=new Set,n=(e,n)=>{let s="function"==typeof e?e(t):e;if(!Object.is(s,t)){let e=t;t=(null!=n?n:"object"!=typeof s||null===s)?s:Object.assign({},t,s),r.forEach(r=>r(t,e))}},s=()=>t,i={setState:n,getState:s,getInitialState:()=>o,subscribe:e=>(r.add(e),()=>r.delete(e))},o=t=e(n,s,i);return i},i=e=>e?s(e):s,o=e=>e,a=e=>{let t=i(e),r=e=>(function(e,t=o){let r=n.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},u=e=>e?a(e):a},68375:()=>{},68718:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},73926:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},82269:(e,t,r)=>{"use strict";var n=r(87358);r(68375);var s=r(12115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},u=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,s=t.optimizeForSpeed,i=void 0===s?o:s;l(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",l("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var u="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=u?u.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(l(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];l(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return r?s.insertBefore(n,r):s.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return c[n]||(c[n]="jsx-"+d(e+"-"+r)),c[n]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var f=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,s=t.optimizeForSpeed,i=void 0!==s&&s;this._sheet=n||new u({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),n&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,s=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var i=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=i,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var s=h(n,r);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return p(s,e)}):[p(s,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),y=s.createContext(null);y.displayName="StyleSheetContext";var m=i.default.useInsertionEffect||i.default.useLayoutEffect,v="undefined"!=typeof window?new f:void 0;function S(e){var t=v||s.useContext(y);return t&&("undefined"==typeof window?t.add(e):m(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}S.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=S}}]);