(()=>{var e={};e.id=9589,e.ids=[9589],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3515:(e,r,t)=>{Promise.resolve().then(t.bind(t,66417))},3940:(e,r,t)=>{"use strict";t.d(r,{O_:()=>i,t6:()=>a});var s=t(43210),o=t(49278);function a(){let e=(0,s.useCallback)((e,r)=>o.JP.success(e,r),[]),r=(0,s.useCallback)((e,r)=>o.JP.error(e,r),[]),t=(0,s.useCallback)((e,r)=>o.JP.info(e,r),[]),a=(0,s.useCallback)(r=>e(r?.successTitle||"Success",r?.successDescription||"Operation completed successfully"),[e]),i=(0,s.useCallback)((e,t)=>{let s=e instanceof Error?e.message:e;return r(t?.errorTitle||"Error",t?.errorDescription||s||"An unexpected error occurred")},[r]);return{showSuccess:e,showError:r,showInfo:t,showFormSuccess:a,showFormError:i}}function i(e){let r;switch(e){case"employee":r=t(49278).Ok;break;case"vehicle":r=t(49278).G7;break;case"task":r=t(49278).z0;break;case"delegation":r=t(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,r){let{showFormSuccess:t,showFormError:i}=a(),n=r||(e?(0,o.iw)(e):null),l=(0,s.useCallback)(e=>n?n.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,t]),c=(0,s.useCallback)(e=>n?n.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,t]),d=(0,s.useCallback)(e=>n?n.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,t]),u=(0,s.useCallback)(e=>{if(n){let r=e instanceof Error?e.message:e;return n.entityCreationError(r)}return i(e,{errorTitle:"Creation Failed"})},[n,i]);return{showEntityCreated:l,showEntityUpdated:c,showEntityDeleted:d,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(n){let r=e instanceof Error?e.message:e;return n.entityUpdateError(r)}return i(e,{errorTitle:"Update Failed"})},[n,i]),showEntityDeletionError:(0,s.useCallback)(e=>{if(n){let r=e instanceof Error?e.message:e;return n.entityDeletionError(r)}return i(e,{errorTitle:"Deletion Failed"})},[n,i]),showFormSuccess:t,showFormError:i}}(void 0,r)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14131:(e,r,t)=>{Promise.resolve().then(t.bind(t,15167))},15167:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),o=t(33886),a=t(16189),i=t(51356),n=t(48041),l=t(3940),c=t(63502);function d(){let e=(0,a.useRouter)(),{showEntityCreated:r,showEntityCreationError:t}=(0,l.O_)("delegation"),{error:d,isPending:u,mutateAsync:p}=(0,c.er)(),m=async s=>{let o={...s,delegates:s.delegates.map(e=>({name:e.name,notes:e.notes??"",title:e.title})),drivers:s.driverEmployeeIds.map(e=>({employeeId:e})),durationFrom:new Date(s.durationFrom).toISOString(),durationTo:new Date(s.durationTo).toISOString(),escorts:s.escortEmployeeIds.map(e=>({employeeId:e})),notes:s.notes??"",status:s.status.replace(" ","_"),vehicles:s.vehicleIds.map(e=>({assignedDate:new Date(s.durationFrom).toISOString(),returnDate:new Date(s.durationTo).toISOString(),vehicleId:e}))};try{await p(o);let t={event:s.eventName,location:s.location};r(t),e.push("/delegations")}catch(r){console.error("Error adding delegation:",r);let e="Failed to add delegation. Please try again.";r instanceof Error?e=r.message:d instanceof Error&&(e=d.message),t(e)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(n.z,{description:"Enter the details for the new delegation or event.",icon:o.A,title:"Add New Delegation"}),d&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error: ",d.message]}),(0,s.jsx)(i.GK,{isEditing:!1,onSubmit:m})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22481:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),o=t(48088),a=t(88170),i=t.n(a),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["[locale]",{children:["delegations",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66417)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\add\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\add\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/delegations/add/page",pathname:"/[locale]/delegations/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66417:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\delegations\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\add\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4825,625,9851,8390,2670,9275,6013,6362,7113,417,2482,9794,9599,3502,6413,3089,9004],()=>t(22481));module.exports=s})();