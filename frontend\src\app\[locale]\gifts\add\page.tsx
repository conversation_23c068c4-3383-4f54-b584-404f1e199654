'use client';

import { Gift } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import type { CreateGiftData } from '@/lib/types/domain';

import { GiftForm } from '@/components/features/gifts';
import { PageHeader } from '@/components/ui/PageHeader';
import { useCreateGift } from '@/lib/stores/queries/useGifts';
import { useRecipientOptions } from '@/lib/stores/queries/useRecipients';

export default function AddGiftPage() {
  const router = useRouter();
  const t = useTranslations('gifts');
  const tCommon = useTranslations('common');

  const { mutateAsync: createGift, isPending, error } = useCreateGift();
  const { data: recipientOptions = [] } = useRecipientOptions();

  const handleSubmit = async (data: CreateGiftData) => {
    try {
      await createGift(data);
      router.push('/gifts');
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error creating gift:', error);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        description={t('addGiftDescription')}
        icon={Gift}
        title={t('addGift')}
      />

      {error && (
        <div className="rounded-md bg-red-100 p-3 text-red-500">
          {tCommon('error')}: {error.message}
        </div>
      )}

      <GiftForm
        isEditing={false}
        isLoading={isPending}
        onSubmit={data => handleSubmit(data as CreateGiftData)}
        recipients={recipientOptions}
      />
    </div>
  );
}
