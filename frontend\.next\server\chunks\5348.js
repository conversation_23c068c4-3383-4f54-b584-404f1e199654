"use strict";exports.id=5348,exports.ids=[5348],exports.modules={34729:(e,a,l)=>{l.d(a,{T:()=>s});var t=l(60687),i=l(43210),r=l(22482);let s=i.forwardRef(({className:e,...a},l)=>(0,t.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:l,...a}));s.displayName="Textarea"},70258:(e,a,l)=>{l.d(a,{x:()=>v});var t=l(60687);l(43210);var i=l(24920),r=l(26622),s=l(88853);let n=(0,l(82614).A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var o=l(29333),c=l(36644),d=l(92718),m=l(92929),u=l(9275);u.k5(["Active","Inactive","Maintenance","Out_of_Service"]),u.k5(["Gasoline","Diesel","Electric","Hybrid","CNG","LPG"]);let x=u.Ik({make:u.Yj().min(1,"Make is required"),model:u.Yj().min(1,"Model is required"),year:u.au.number().min(1900,"Year must be 1900 or later").max(new Date().getFullYear()+1,`Year cannot be more than ${new Date().getFullYear()+1}`),color:u.Yj().optional(),licensePlate:u.Yj().min(1,"License plate is required"),vin:u.Yj().optional().refine(e=>!e||/^[A-HJ-NPR-Z0-9]{17}$/.test(e),"VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)"),mileage:u.au.number().min(0,"Mileage cannot be negative").optional(),status:u.k5(["active","maintenance","inactive"]).default("active"),notes:u.Yj().optional(),ownerContact:u.Yj().optional(),ownerName:u.Yj().optional(),imageUrl:u.Yj().url("Invalid image URL").optional().or(u.eu("")),initialOdometer:u.au.number().min(0,"Odometer reading cannot be negative").optional()});var p=l(29523),h=l(44493);let v=({initialData:e,isEditing:a=!1,isLoading:l=!1,onSubmit:u})=>(0,t.jsxs)(h.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,t.jsx)(h.aR,{children:(0,t.jsxs)(h.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(i.A,{className:"h-5 w-5"}),a?"Edit Vehicle":"Add New Vehicle"]})}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)(d.I,{defaultValues:{status:"active",...e},onSubmit:u,schema:x,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Basic Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(m.z,{label:"Make",name:"make",placeholder:"e.g., Toyota, Ford, BMW",icon:i.A,required:!0}),(0,t.jsx)(m.z,{label:"Model",name:"model",placeholder:"e.g., Camry, F-150, X3",icon:i.A,required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(m.z,{label:"Year",name:"year",type:"number",placeholder:"e.g., 2023",icon:r.A,min:1900,max:new Date().getFullYear()+1,required:!0}),(0,t.jsx)(m.z,{label:"Color",name:"color",placeholder:"e.g., Red, Blue, Silver",icon:s.A})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Identification"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(m.z,{label:"License Plate",name:"licensePlate",placeholder:"e.g., ABC-1234",icon:n,required:!0}),(0,t.jsx)(m.z,{label:"VIN (Optional)",name:"vin",placeholder:"17-character VIN (auto-generated if empty)",icon:n,maxLength:17})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Additional Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(m.z,{label:"Mileage",name:"mileage",type:"number",placeholder:"Current mileage",icon:o.A,min:0}),(0,t.jsx)(m.z,{label:"Status",name:"status",type:"select",placeholder:"Select status",options:[{value:"active",label:"Active"},{value:"maintenance",label:"In Maintenance"},{value:"inactive",label:"Inactive"}],defaultValue:"active"})]}),(0,t.jsx)(m.z,{label:"Notes",name:"notes",type:"textarea",placeholder:"Additional notes about the vehicle...",icon:c.A,rows:3})]}),(0,t.jsx)("div",{className:"flex justify-end pt-6 border-t",children:(0,t.jsx)(p.$,{type:"submit",disabled:l,className:"min-w-[120px]",children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),a?"Updating...":"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.A,{className:"h-4 w-4 mr-2"}),a?"Update Vehicle":"Create Vehicle"]})})})]})})]})},71669:(e,a,l)=>{l.d(a,{C5:()=>b,MJ:()=>v,Rr:()=>g,eI:()=>p,lR:()=>h,lV:()=>c,zB:()=>m});var t=l(60687),i=l(43210),r=l(8730),s=l(27605),n=l(22482),o=l(80013);let c=s.Op,d=i.createContext({}),m=({...e})=>(0,t.jsx)(d.Provider,{value:{name:e.name},children:(0,t.jsx)(s.xI,{...e})}),u=()=>{let e=i.useContext(d),a=i.useContext(x),{getFieldState:l,formState:t}=(0,s.xW)(),r=l(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=a;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...r}},x=i.createContext({}),p=i.forwardRef(({className:e,...a},l)=>{let r=i.useId();return(0,t.jsx)(x.Provider,{value:{id:r},children:(0,t.jsx)("div",{ref:l,className:(0,n.cn)("space-y-2",e),...a})})});p.displayName="FormItem";let h=i.forwardRef(({className:e,...a},l)=>{let{error:i,formItemId:r}=u();return(0,t.jsx)(o.J,{ref:l,className:(0,n.cn)(i&&"text-destructive",e),htmlFor:r,...a})});h.displayName="FormLabel";let v=i.forwardRef(({...e},a)=>{let{error:l,formItemId:i,formDescriptionId:s,formMessageId:n}=u();return(0,t.jsx)(r.DX,{ref:a,id:i,"aria-describedby":l?`${s} ${n}`:`${s}`,"aria-invalid":!!l,...e})});v.displayName="FormControl";let g=i.forwardRef(({className:e,...a},l)=>{let{formDescriptionId:i}=u();return(0,t.jsx)("p",{ref:l,id:i,className:(0,n.cn)("text-sm text-muted-foreground",e),...a})});g.displayName="FormDescription";let b=i.forwardRef(({className:e,children:a,...l},i)=>{let{error:r,formMessageId:s}=u(),o=r?String(r?.message??""):a;return o?(0,t.jsx)("p",{ref:i,id:s,className:(0,n.cn)("text-sm font-medium text-destructive",e),...l,children:o}):null});b.displayName="FormMessage"},72273:(e,a,l)=>{l.d(a,{NS:()=>p,T$:()=>d,W_:()=>m,Y1:()=>u,lR:()=>x});var t=l(8693),i=l(54050),r=l(46349),s=l(87676),n=l(48839),o=l(49603);let c={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,r.GK)([...c.all],async()=>(await o.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,a)=>(0,r.GK)([...c.detail(e)],()=>o.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(a?.enabled??!0),staleTime:3e5,...a}),u=()=>{let e=(0,t.jE)(),{showError:a,showSuccess:l}=(0,s.useNotifications)();return(0,i.n)({mutationFn:e=>{let a=n.M.toCreateRequest(e);return o.vehicleApiService.create(a)},onError:e=>{a(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:a=>{e.invalidateQueries({queryKey:c.all}),l(`Vehicle "${a.licensePlate}" has been created successfully!`)}})},x=()=>{let e=(0,t.jE)(),{showError:a,showSuccess:l}=(0,s.useNotifications)();return(0,i.n)({mutationFn:({data:e,id:a})=>{let l=n.M.toUpdateRequest(e);return o.vehicleApiService.update(a,l)},onError:e=>{a(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:a=>{e.invalidateQueries({queryKey:c.all}),e.invalidateQueries({queryKey:c.detail(a.id)}),l(`Vehicle "${a.licensePlate}" has been updated successfully!`)}})},p=()=>{let e=(0,t.jE)(),{showError:a,showSuccess:l}=(0,s.useNotifications)();return(0,i.n)({mutationFn:e=>o.vehicleApiService.delete(e),onError:e=>{a(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(a,t)=>{e.invalidateQueries({queryKey:c.all}),e.removeQueries({queryKey:c.detail(t)}),l("Vehicle has been deleted successfully!")}})}},87676:(e,a,l)=>{l.r(a),l.d(a,{useNotifications:()=>r,useWorkHubNotifications:()=>s});var t=l(43210),i=l(94538);let r=()=>{let e=(0,i.C)(e=>e.addNotification),a=(0,i.C)(e=>e.removeNotification),l=(0,i.C)(e=>e.clearAllNotifications),r=(0,i.C)(e=>e.unreadNotificationCount),s=(0,t.useCallback)(a=>{e({message:a,type:"success"})},[e]),n=(0,t.useCallback)(a=>{e({message:a,type:"error"})},[e]),o=(0,t.useCallback)(a=>{e({message:a,type:"warning"})},[e]),c=(0,t.useCallback)(a=>{e({message:a,type:"info"})},[e]),d=(0,t.useCallback)((e,a,l)=>{e?s(a):n(l)},[s,n]),m=(0,t.useCallback)((l,t,r=5e3)=>{e({message:t,type:l}),setTimeout(()=>{let e=i.C.getState().notifications.at(-1);e&&e.message===t&&a(e.id)},r)},[e,a]),u=(0,t.useCallback)((a="Loading...")=>{e({message:a,type:"info"});let l=i.C.getState().notifications;return l.at(-1)?.id},[e]),x=(0,t.useCallback)((e,l,t)=>{a(e),l?s(t):n(t)},[a,s,n]);return{clearAllNotifications:l,removeNotification:a,showApiResult:d,showError:n,showInfo:c,showLoading:u,showSuccess:s,showTemporary:m,showWarning:o,unreadCount:r,updateLoadingNotification:x}},s=()=>{let{clearAllNotifications:e,removeNotification:a,showError:l,showInfo:s,showSuccess:n,showWarning:o,unreadCount:c}=r(),d=(0,t.useCallback)((e,a)=>{(0,i.C.getState().addNotification)({...a&&{actionUrl:a},category:"delegation",message:e,type:"delegation-update"})},[]),m=(0,t.useCallback)((e,a)=>{(0,i.C.getState().addNotification)({...a&&{actionUrl:a},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),u=(0,t.useCallback)((e,a)=>{(0,i.C.getState().addNotification)({...a&&{actionUrl:a},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:a,showDelegationUpdate:d,showEmployeeUpdate:(0,t.useCallback)((e,a)=>{(0,i.C.getState().addNotification)({...a&&{actionUrl:a},category:"employee",message:e,type:"employee-update"})},[]),showError:l,showInfo:s,showSuccess:n,showTaskAssigned:u,showVehicleMaintenance:m,showWarning:o,unreadCount:c}}},92718:(e,a,l)=>{l.d(a,{I:()=>n});var t=l(60687),i=l(63442),r=l(27605),s=l(71669);let n=({children:e,defaultValues:a,onSubmit:l,schema:n,className:o="",ariaAttributes:c={}})=>{let d=(0,r.mN)({...a&&{defaultValues:a},resolver:(0,i.u)(n)}),m=async e=>{await l(e)};return(0,t.jsx)(s.lV,{...d,children:(0,t.jsx)("form",{onSubmit:d.handleSubmit(m),className:o,...c,children:e})})}},92929:(e,a,l)=>{l.d(a,{z:()=>c});var t=l(60687);l(43210);var i=l(27605),r=l(71669),s=l(89667),n=l(34729),o=l(15079);let c=({className:e="",disabled:a=!1,label:l,name:c,placeholder:d,render:m,type:u="text",options:x=[],defaultValue:p,icon:h,...v})=>{let{control:g}=(0,i.xW)();return(0,t.jsxs)(r.eI,{className:e,children:[(0,t.jsx)(r.lR,{htmlFor:c,children:l}),(0,t.jsx)(i.xI,{control:g,name:c,render:m||(({field:e,fieldState:{error:i}})=>(0,t.jsx)(r.MJ,{children:"select"===u?(0,t.jsxs)(o.l6,{onValueChange:e.onChange,value:e.value||p||"",disabled:a,children:[(0,t.jsx)(o.bq,{className:i?"border-red-500":"",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[h&&(0,t.jsx)(h,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)(o.yv,{placeholder:d||`Select ${l.toLowerCase()}`})]})}),(0,t.jsx)(o.gC,{children:x.map(e=>(0,t.jsx)(o.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===u?(0,t.jsxs)("div",{className:"relative",children:[h&&(0,t.jsx)(h,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,t.jsx)(n.T,{...e,...v,value:e.value??"",className:`${i?"border-red-500":""} ${h?"pl-10":""}`,disabled:a,id:c,placeholder:d})]}):(0,t.jsxs)("div",{className:"relative",children:[h&&(0,t.jsx)(h,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,t.jsx)(s.p,{...e,...v,value:e.value??"",className:`${i?"border-red-500":""} ${h?"pl-10":""}`,disabled:a,id:c,placeholder:d,type:u})]})}))}),(0,t.jsx)(r.C5,{})]})}}};