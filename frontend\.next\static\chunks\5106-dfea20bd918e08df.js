"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5106],{12543:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},15452:(e,t,n)=>{n.d(t,{G$:()=>Y,Hs:()=>D,UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>X});var r=n(12115),o=n(85185),a=n(6101),l=n(46081),i=n(61285),s=n(5845),u=n(19178),d=n(25519),c=n(34378),p=n(28905),f=n(63655),m=n(92293),g=n(31114),v=n(38168),y=n(99708),h=n(95155),x="Dialog",[N,D]=(0,l.A)(x),[A,w]=N(x),b=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:x});return(0,h.jsx)(A,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};b.displayName=x;var j="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=w(j,n),i=(0,a.s)(t,l.triggerRef);return(0,h.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Z(l.open),...r,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=j;var O="DialogPortal",[C,I]=N(O,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,l=w(O,t);return(0,h.jsx)(C,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,h.jsx)(p.C,{present:n||l.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=O;var k="DialogOverlay",M=r.forwardRef((e,t)=>{let n=I(k,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=w(k,e.__scopeDialog);return a.modal?(0,h.jsx)(p.C,{present:r||a.open,children:(0,h.jsx)(_,{...o,ref:t})}):null});M.displayName=k;var T=(0,y.TL)("DialogOverlay.RemoveScroll"),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(k,n);return(0,h.jsx)(g.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(f.sG.div,{"data-state":Z(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),F="DialogContent",P=r.forwardRef((e,t)=>{let n=I(F,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=w(F,e.__scopeDialog);return(0,h.jsx)(p.C,{present:r||a.open,children:a.modal?(0,h.jsx)(U,{...o,ref:t}):(0,h.jsx)(L,{...o,ref:t})})});P.displayName=F;var U=r.forwardRef((e,t)=>{let n=w(F,e.__scopeDialog),l=r.useRef(null),i=(0,a.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,h.jsx)(S,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=w(F,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,h.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),S=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=w(F,n),p=r.useRef(null),f=(0,a.s)(t,p);return(0,m.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,h.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)($,{titleId:c.titleId}),(0,h.jsx)(K,{contentRef:p,descriptionId:c.descriptionId})]})]})}),G="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(G,n);return(0,h.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});W.displayName=G;var q="DialogDescription",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(q,n);return(0,h.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});H.displayName=q;var V="DialogClose",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=w(V,n);return(0,h.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}B.displayName=V;var z="DialogTitleWarning",[Y,J]=(0,l.q)(z,{contentName:F,titleName:G,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,n=J(z),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},K=e=>{let{contentRef:t,descriptionId:n}=e,o=J("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(a))},[a,t,n]),null},Q=b,X=R,ee=E,et=M,en=P,er=W,eo=H,ea=B},17649:(e,t,n)=>{n.d(t,{UC:()=>F,VY:()=>S,ZD:()=>U,ZL:()=>T,bL:()=>k,hE:()=>L,hJ:()=>_,l9:()=>M,rc:()=>P});var r=n(12115),o=n(46081),a=n(6101),l=n(15452),i=n(85185),s=n(99708),u=n(95155),d="AlertDialog",[c,p]=(0,o.A)(d,[l.Hs]),f=(0,l.Hs)(),m=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,u.jsx)(l.bL,{...r,...n,modal:!0})};m.displayName=d;var g=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,u.jsx)(l.l9,{...o,...r,ref:t})});g.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,u.jsx)(l.ZL,{...r,...n})};v.displayName="AlertDialogPortal";var y=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,u.jsx)(l.hJ,{...o,...r,ref:t})});y.displayName="AlertDialogOverlay";var h="AlertDialogContent",[x,N]=c(h),D=(0,s.Dc)("AlertDialogContent"),A=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:o,...s}=e,d=f(n),c=r.useRef(null),p=(0,a.s)(t,c),m=r.useRef(null);return(0,u.jsx)(l.G$,{contentName:h,titleName:w,docsSlug:"alert-dialog",children:(0,u.jsx)(x,{scope:n,cancelRef:m,children:(0,u.jsxs)(l.UC,{role:"alertdialog",...d,...s,ref:p,onOpenAutoFocus:(0,i.m)(s.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=m.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(D,{children:o}),(0,u.jsx)(E,{contentRef:c})]})})})});A.displayName=h;var w="AlertDialogTitle",b=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,u.jsx)(l.hE,{...o,...r,ref:t})});b.displayName=w;var j="AlertDialogDescription",R=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,u.jsx)(l.VY,{...o,...r,ref:t})});R.displayName=j;var O=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,u.jsx)(l.bm,{...o,...r,ref:t})});O.displayName="AlertDialogAction";var C="AlertDialogCancel",I=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=N(C,n),i=f(n),s=(0,a.s)(t,o);return(0,u.jsx)(l.bm,{...i,...r,ref:s})});I.displayName=C;var E=e=>{let{contentRef:t}=e,n="`".concat(h,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(h,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(h,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(n)},[n,t]),null},k=m,M=g,T=v,_=y,F=A,P=O,U=I,L=b,S=R},28328:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),a=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),s=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=d.current,o=i(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(s.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=i(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,l(e)},[])}}(t),s="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),u=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||l.isPresent?r.cloneElement(s,{ref:u}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},31949:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},58127:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},77223:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},87489:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(12115),o=n(63655),a=n(95155),l="horizontal",i=["horizontal","vertical"],s=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:s=l,...u}=e,d=(n=s,i.includes(n))?s:l;return(0,a.jsx)(o.sG.div,{"data-orientation":d,...r?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s}}]);