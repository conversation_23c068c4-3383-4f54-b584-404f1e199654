(()=>{var e={};e.id=2130,e.ids=[2130],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,t,r)=>{"use strict";r.d(t,{O_:()=>n,t6:()=>a});var s=r(43210),i=r(49278);function a(){let e=(0,s.useCallback)((e,t)=>i.JP.success(e,t),[]),t=(0,s.useCallback)((e,t)=>i.JP.error(e,t),[]),r=(0,s.useCallback)((e,t)=>i.JP.info(e,t),[]),a=(0,s.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),n=(0,s.useCallback)((e,r)=>{let s=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||s||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:a,showFormError:n}}function n(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:n}=a(),o=t||(e?(0,i.iw)(e):null),d=(0,s.useCallback)(e=>o?o.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[o,r]),c=(0,s.useCallback)(e=>o?o.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[o,r]),l=(0,s.useCallback)(e=>o?o.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[o,r]),u=(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityCreationError(t)}return n(e,{errorTitle:"Creation Failed"})},[o,n]);return{showEntityCreated:d,showEntityUpdated:c,showEntityDeleted:l,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityUpdateError(t)}return n(e,{errorTitle:"Update Failed"})},[o,n]),showEntityDeletionError:(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityDeletionError(t)}return n(e,{errorTitle:"Deletion Failed"})},[o,n]),showFormSuccess:r,showFormError:n}}(void 0,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12815:(e,t,r)=>{Promise.resolve().then(r.bind(r,92598))},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>p,eb:()=>x,gC:()=>f,l6:()=>l,yv:()=>u});var s=r(60687),i=r(22670),a=r(61662),n=r(89743),o=r(58450),d=r(43210),c=r(22482);let l=i.bL;i.YJ;let u=i.WT,p=d.forwardRef(({children:e,className:t,...r},n)=>(0,s.jsxs)(i.l9,{className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:n,...r,children:[e,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(a.A,{className:"size-4 opacity-50"})})]}));p.displayName=i.l9.displayName;let h=d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.PP,{className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,s.jsx)(n.A,{className:"size-4"})}));h.displayName=i.PP.displayName;let m=d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.wn,{className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,s.jsx)(a.A,{className:"size-4"})}));m.displayName=i.wn.displayName;let f=d.forwardRef(({children:e,className:t,position:r="popper",...a},n)=>(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,ref:n,...a,children:[(0,s.jsx)(h,{}),(0,s.jsx)(i.LM,{className:(0,c.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,s.jsx)(m,{})]})}));f.displayName=i.UC.displayName,d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.JU,{className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:r,...t})).displayName=i.JU.displayName;let x=d.memo(d.forwardRef(({children:e,className:t,...r},a)=>{let n=d.useCallback(e=>{"function"==typeof a?a(e):a&&(a.current=e)},[a]);return(0,s.jsxs)(i.q7,{className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:n,...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(i.p4,{children:e})]})}));x.displayName=i.q7.displayName,d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.wv,{className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),ref:r,...t})).displayName=i.wv.displayName},17705:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c={children:["",{children:["[locale]",{children:["vehicles",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,31056)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\edit\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/vehicles/edit/[id]/page",pathname:"/[locale]/vehicles/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31056:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\vehicles\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\edit\\[id]\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35137:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},48041:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(60687);function i({children:e,description:t,icon:r,title:i}){return(0,s.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,s.jsx)(r,{className:"size-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:i})]}),t&&(0,s.jsx)("p",{className:"mt-1 text-muted-foreground",children:t})]}),e&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:e})]})}r(43210)},49278:(e,t,r)=>{"use strict";r.d(t,{G7:()=>p,Gb:()=>d,JP:()=>c,Ok:()=>l,Qu:()=>u,iw:()=>o,oz:()=>m,z0:()=>h});var s=r(3389);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends i{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class n extends i{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function o(e){return new a(e)}function d(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let c=new i,l=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),p=new a({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),m=new n},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84791:(e,t,r)=>{Promise.resolve().then(r.bind(r,31056))},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(60687),i=r(22482);function a({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},92598:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),i=r(24920),a=r(35137),n=r(16189);r(43210);var o=r(29523),d=r(48041),c=r(85726),l=r(70258),u=r(3940),p=r(72273);let h=()=>{let e=(0,n.useRouter)(),t=(0,n.useParams)(),{showEntityUpdated:r,showEntityUpdateError:h}=(0,u.O_)("vehicle"),m=t?.id,f=m?Number(m):null,{data:x,error:g,isLoading:y}=(0,p.W_)(f),{error:v,isPending:b,mutateAsync:w}=(0,p.lR)(),j=async t=>{if(!f)return void h("Vehicle ID is missing. Cannot update.");try{let s={...t,initialOdometer:void 0===t.initialOdometer?x?.initialOdometer||0:t.initialOdometer};await w({data:s,id:f});let i={make:t.make,model:t.model};r(i),e.push("/vehicles")}catch(e){console.error("Failed to update vehicle:",e),h(e.message||v?.message||"Could not update the vehicle. Please check the details and try again.")}};return f?y?(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(d.z,{description:"Loading vehicle details...",icon:a.A,title:"Edit Vehicle"}),(0,s.jsxs)("div",{className:"mx-auto max-w-2xl space-y-6",children:[(0,s.jsx)(c.E,{className:"h-10 w-1/3"}),(0,s.jsx)(c.E,{className:"h-12 w-full"}),(0,s.jsx)(c.E,{className:"h-12 w-full"}),(0,s.jsx)(c.E,{className:"h-12 w-full"}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsx)(c.E,{className:"h-10 w-24"}),(0,s.jsx)(c.E,{className:"h-10 w-24"})]})]})]}):g?(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,s.jsx)(d.z,{description:g.message||"Could not load vehicle data.",icon:i.A,title:"Error Loading Vehicle"}),(0,s.jsx)(o.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):x?(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(d.z,{description:`Update details for ${x?.make||"vehicle"} ${x?.model||""}`,icon:a.A,title:"Edit Vehicle"}),v&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error updating: ",v.message]}),x&&(0,s.jsx)(l.x,{initialData:x?{...x,id:String(x.id)}:void 0,isEditing:!0,isLoading:b,onSubmit:j})]}):(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,s.jsx)(d.z,{description:"The requested vehicle could not be found.",icon:i.A,title:"Vehicle Not Found"}),(0,s.jsx)(o.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,s.jsx)(d.z,{description:"Invalid Vehicle ID.",icon:i.A,title:"Error"}),(0,s.jsx)(o.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,4825,625,9851,8390,2670,9275,6013,2482,9794,5348],()=>r(17705));module.exports=s})();