{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/add-vehicle", "regex": "^/([^/]+?)/add\\-vehicle(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/add\\-vehicle(?:/)?$"}, {"page": "/[locale]/admin", "regex": "^/([^/]+?)/admin(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin(?:/)?$"}, {"page": "/[locale]/auth-test", "regex": "^/([^/]+?)/auth\\-test(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/auth\\-test(?:/)?$"}, {"page": "/[locale]/delegations", "regex": "^/([^/]+?)/delegations(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/delegations(?:/)?$"}, {"page": "/[locale]/delegations/add", "regex": "^/([^/]+?)/delegations/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/delegations/add(?:/)?$"}, {"page": "/[locale]/delegations/report/list", "regex": "^/([^/]+?)/delegations/report/list(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/delegations/report/list(?:/)?$"}, {"page": "/[locale]/delegations/[id]", "regex": "^/([^/]+?)/delegations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/delegations/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/delegations/[id]/edit", "regex": "^/([^/]+?)/delegations/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/delegations/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/[locale]/delegations/[id]/report", "regex": "^/([^/]+?)/delegations/([^/]+?)/report(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/delegations/(?<nxtPid>[^/]+?)/report(?:/)?$"}, {"page": "/[locale]/employees", "regex": "^/([^/]+?)/employees(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/employees(?:/)?$"}, {"page": "/[locale]/employees/add", "regex": "^/([^/]+?)/employees/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/employees/add(?:/)?$"}, {"page": "/[locale]/employees/new", "regex": "^/([^/]+?)/employees/new(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/employees/new(?:/)?$"}, {"page": "/[locale]/employees/[id]", "regex": "^/([^/]+?)/employees/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/employees/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/employees/[id]/edit", "regex": "^/([^/]+?)/employees/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/employees/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/[locale]/font-size-demo", "regex": "^/([^/]+?)/font\\-size\\-demo(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/font\\-size\\-demo(?:/)?$"}, {"page": "/[locale]/login", "regex": "^/([^/]+?)/login(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/login(?:/)?$"}, {"page": "/[locale]/profile", "regex": "^/([^/]+?)/profile(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/profile(?:/)?$"}, {"page": "/[locale]/reliability", "regex": "^/([^/]+?)/reliability(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reliability(?:/)?$"}, {"page": "/[locale]/reports", "regex": "^/([^/]+?)/reports(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reports(?:/)?$"}, {"page": "/[locale]/reports/analytics", "regex": "^/([^/]+?)/reports/analytics(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reports/analytics(?:/)?$"}, {"page": "/[locale]/reports/data", "regex": "^/([^/]+?)/reports/data(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reports/data(?:/)?$"}, {"page": "/[locale]/service-history", "regex": "^/([^/]+?)/service\\-history(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/service\\-history(?:/)?$"}, {"page": "/[locale]/service-records/[id]", "regex": "^/([^/]+?)/service\\-records/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/service\\-records/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/service-records/[id]/edit", "regex": "^/([^/]+?)/service\\-records/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/service\\-records/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/[locale]/settings", "regex": "^/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/settings(?:/)?$"}, {"page": "/[locale]/tasks", "regex": "^/([^/]+?)/tasks(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tasks(?:/)?$"}, {"page": "/[locale]/tasks/add", "regex": "^/([^/]+?)/tasks/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tasks/add(?:/)?$"}, {"page": "/[locale]/tasks/report", "regex": "^/([^/]+?)/tasks/report(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tasks/report(?:/)?$"}, {"page": "/[locale]/tasks/[id]", "regex": "^/([^/]+?)/tasks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tasks/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/tasks/[id]/edit", "regex": "^/([^/]+?)/tasks/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tasks/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/[locale]/vehicles", "regex": "^/([^/]+?)/vehicles(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/vehicles(?:/)?$"}, {"page": "/[locale]/vehicles/edit/[id]", "regex": "^/([^/]+?)/vehicles/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/vehicles/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/vehicles/new", "regex": "^/([^/]+?)/vehicles/new(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/vehicles/new(?:/)?$"}, {"page": "/[locale]/vehicles/[id]", "regex": "^/([^/]+?)/vehicles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/vehicles/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/vehicles/[id]/report", "regex": "^/([^/]+?)/vehicles/([^/]+?)/report(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/vehicles/(?<nxtPid>[^/]+?)/report(?:/)?$"}, {"page": "/[locale]/vehicles/[id]/report/service-history", "regex": "^/([^/]+?)/vehicles/([^/]+?)/report/service\\-history(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/vehicles/(?<nxtPid>[^/]+?)/report/service\\-history(?:/)?$"}, {"page": "/[locale]/zustand-test", "regex": "^/([^/]+?)/zustand\\-test(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/zustand\\-test(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/api/:path*", "destination": "http://192.168.100.31:3001/api/:path*", "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}