(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5304],{3561:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},8376:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},11133:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},15292:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},18231:(e,s,a)=>{Promise.resolve().then(a.bind(a,60100))},19018:(e,s,a)=>{"use strict";a.d(s,{Breadcrumb:()=>c,BreadcrumbItem:()=>o,BreadcrumbLink:()=>m,BreadcrumbList:()=>d,BreadcrumbPage:()=>u,BreadcrumbSeparator:()=>x});var t=a(95155),r=a(99708),i=a(73158),n=(a(3561),a(12115)),l=a(54036);let c=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("nav",{"aria-label":"breadcrumb",className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",a),ref:s,...r})});c.displayName="Breadcrumb";let d=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("ol",{className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",a),ref:s,...r})});d.displayName="BreadcrumbList";let o=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("li",{className:(0,l.cn)("inline-flex items-center gap-1.5",a),ref:s,...r})});o.displayName="BreadcrumbItem";let m=n.forwardRef((e,s)=>{let{asChild:a,className:i,...n}=e,c=a?r.DX:"a";return(0,t.jsx)(c,{className:(0,l.cn)("transition-colors hover:text-foreground",i),ref:s,...n})});m.displayName="BreadcrumbLink";let u=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,l.cn)("font-normal text-foreground",a),ref:s,role:"link",...r})});u.displayName="BreadcrumbPage";let x=e=>{let{children:s,className:a,...r}=e;return(0,t.jsx)("span",{"aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",a),role:"presentation",...r,children:null!=s?s:(0,t.jsx)(i.A,{className:"size-4"})})};x.displayName="BreadcrumbSeparator"},25318:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},26119:(e,s,a)=>{"use strict";a.d(s,{n:()=>i});var t=a(65453),r=a(46786);let i=(0,t.v)()((0,r.lt)((0,r.Zr)(e=>({autoRefreshInterval:30,closeModal:()=>e({isModalOpen:!1,modalContent:null}),dashboardLayout:"cards",fontSize:"medium",isModalOpen:!1,mapViewPreference:"roadmap",modalContent:null,notificationsEnabled:!0,openModal:s=>e({isModalOpen:!0,modalContent:s}),setAutoRefreshInterval:s=>e({autoRefreshInterval:s}),setDashboardLayout:s=>e({dashboardLayout:s}),setFontSize:s=>e({fontSize:s}),setMapViewPreference:s=>e({mapViewPreference:s}),setTableDensity:s=>e({tableDensity:s}),tableDensity:"comfortable",toggleNotifications:()=>e(e=>({notificationsEnabled:!e.notificationsEnabled}))}),{name:"workhub-ui-store",partialize:e=>({autoRefreshInterval:e.autoRefreshInterval,dashboardLayout:e.dashboardLayout,fontSize:e.fontSize,mapViewPreference:e.mapViewPreference,notificationsEnabled:e.notificationsEnabled,tableDensity:e.tableDensity})}),{name:"ui-store"}))},26126:(e,s,a)=>{"use strict";a.d(s,{E:()=>l});var t=a(95155),r=a(74466);a(12115);var i=a(54036);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function l(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)(n({variant:a}),s),...r})}},28341:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},30285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d,r:()=>c});var t=a(95155),r=a(12115),i=a(99708),n=a(74466),l=a(54036);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:a,variant:r,size:n,asChild:d=!1,...o}=e,m=d?i.DX:"button";return(0,t.jsx)(m,{className:(0,l.cn)(c({variant:r,size:n,className:a})),ref:s,...o})});d.displayName="Button"},36931:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},42366:(e,s,a)=>{"use strict";a.r(s),a.d(s,{useNotifications:()=>i,useWorkHubNotifications:()=>n});var t=a(12115),r=a(96016);let i=()=>{let e=(0,r.C)(e=>e.addNotification),s=(0,r.C)(e=>e.removeNotification),a=(0,r.C)(e=>e.clearAllNotifications),i=(0,r.C)(e=>e.unreadNotificationCount),n=(0,t.useCallback)(s=>{e({message:s,type:"success"})},[e]),l=(0,t.useCallback)(s=>{e({message:s,type:"error"})},[e]),c=(0,t.useCallback)(s=>{e({message:s,type:"warning"})},[e]),d=(0,t.useCallback)(s=>{e({message:s,type:"info"})},[e]),o=(0,t.useCallback)((e,s,a)=>{e?n(s):l(a)},[n,l]),m=(0,t.useCallback)(function(a,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:t,type:a}),setTimeout(()=>{let e=r.C.getState().notifications.at(-1);e&&e.message===t&&s(e.id)},i)},[e,s]),u=(0,t.useCallback)(function(){var s;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:a,type:"info"}),null==(s=r.C.getState().notifications.at(-1))?void 0:s.id},[e]),x=(0,t.useCallback)((e,a,t)=>{s(e),a?n(t):l(t)},[s,n,l]);return{clearAllNotifications:a,removeNotification:s,showApiResult:o,showError:l,showInfo:d,showLoading:u,showSuccess:n,showTemporary:m,showWarning:c,unreadCount:i,updateLoadingNotification:x}},n=()=>{let{clearAllNotifications:e,removeNotification:s,showError:a,showInfo:n,showSuccess:l,showWarning:c,unreadCount:d}=i(),o=(0,t.useCallback)((e,s)=>{(0,r.C.getState().addNotification)({...s&&{actionUrl:s},category:"delegation",message:e,type:"delegation-update"})},[]),m=(0,t.useCallback)((e,s)=>{(0,r.C.getState().addNotification)({...s&&{actionUrl:s},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),u=(0,t.useCallback)((e,s)=>{(0,r.C.getState().addNotification)({...s&&{actionUrl:s},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:s,showDelegationUpdate:o,showEmployeeUpdate:(0,t.useCallback)((e,s)=>{(0,r.C.getState().addNotification)({...s&&{actionUrl:s},category:"employee",message:e,type:"employee-update"})},[]),showError:a,showInfo:n,showSuccess:l,showTaskAssigned:u,showVehicleMaintenance:m,showWarning:c,unreadCount:d}}},50594:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},60100:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>D});var t=a(95155);let r=(0,a(40157).A)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);var i=a(8376),n=a(11133),l=a(50594),c=a(51362),d=a(12115),o=a(15292),m=a(36936),u=a(68027),x=a(95120),h=a(26126),p=a(30285),f=a(66695),j=a(85187);let g=()=>{let{resolvedTheme:e,setTheme:s,systemTheme:a,theme:r}=(0,c.D)(),{currentTheme:i,isDark:n,setTheme:l}=(0,j.useTheme)(),[g,y]=d.useState(!1);return(d.useEffect(()=>{y(!0)},[]),g)?(0,t.jsxs)(f.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"size-5"}),"Theme Debug Information"]})}),(0,t.jsxs)(f.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Next-themes State"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"theme:"}),(0,t.jsx)(h.E,{variant:"outline",children:r||"undefined"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"systemTheme:"}),(0,t.jsx)(h.E,{variant:"outline",children:a||"undefined"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"resolvedTheme:"}),(0,t.jsx)(h.E,{variant:"outline",children:e||"undefined"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Zustand State"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"currentTheme:"}),(0,t.jsx)(h.E,{variant:"outline",children:i})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"isDark:"}),(0,t.jsx)(h.E,{variant:"outline",children:n?"true":"false"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Visual State"}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded border bg-background"}),(0,t.jsx)("span",{className:"text-sm",children:"Background"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded bg-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Foreground"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded bg-primary"}),(0,t.jsx)("span",{className:"text-sm",children:"Primary"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded bg-secondary"}),(0,t.jsx)("span",{className:"text-sm",children:"Secondary"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Test Controls"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium",children:"Next-themes Controls"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(p.$,{onClick:()=>s("light"),size:"sm",variant:"light"===r?"default":"outline",children:[(0,t.jsx)(m.A,{className:"mr-1 size-4"}),"Light"]}),(0,t.jsxs)(p.$,{onClick:()=>s("dark"),size:"sm",variant:"dark"===r?"default":"outline",children:[(0,t.jsx)(u.A,{className:"mr-1 size-4"}),"Dark"]}),(0,t.jsxs)(p.$,{onClick:()=>s("system"),size:"sm",variant:"system"===r?"default":"outline",children:[(0,t.jsx)(x.A,{className:"mr-1 size-4"}),"System"]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium",children:"Zustand Controls"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(p.$,{onClick:()=>l("light"),size:"sm",variant:"light"===i?"default":"outline",children:[(0,t.jsx)(m.A,{className:"mr-1 size-4"}),"Light"]}),(0,t.jsxs)(p.$,{onClick:()=>l("dark"),size:"sm",variant:"dark"===i?"default":"outline",children:[(0,t.jsx)(u.A,{className:"mr-1 size-4"}),"Dark"]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Synchronization Status"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Themes match:"}),(0,t.jsx)(h.E,{variant:e===i?"default":"destructive",children:e===i?"Yes":"No"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Expected sync:"}),(0,t.jsxs)("span",{className:"text-muted-foreground",children:["system"===r?"".concat(a," (from system)"):r," ","→ ",i]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Troubleshooting"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[e!==i&&(0,t.jsx)("div",{className:"rounded border border-yellow-200 bg-yellow-50 p-2 dark:border-yellow-800 dark:bg-yellow-950",children:"⚠️ Themes are not synchronized. This may indicate a sync issue."}),!r&&(0,t.jsx)("div",{className:"rounded border border-red-200 bg-red-50 p-2 dark:border-red-800 dark:bg-red-950",children:"❌ Next-themes not initialized. Check ThemeProvider setup."}),e===i&&(0,t.jsx)("div",{className:"rounded border border-green-200 bg-green-50 p-2 dark:border-green-800 dark:bg-green-950",children:"✅ Themes are properly synchronized."})]})]})]})]}):(0,t.jsxs)(f.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"size-5"}),"Theme Debug (Loading...)"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsx)("p",{children:"Loading theme information..."})})]})};var y=a(28341),v=a(25318),N=a(36931),b=a(18271),k=a(96016),w=a(26119);let C=()=>{let{currentTheme:e,setTheme:s}=(0,k.C)();return(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:["light"===e?(0,t.jsx)(m.A,{className:"size-5"}):(0,t.jsx)(u.A,{className:"size-5"}),"Theme Toggle"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{children:["Current theme: ",e]}),(0,t.jsxs)(p.$,{onClick:()=>s("light"===e?"dark":"light"),size:"sm",variant:"outline",children:["Switch to ","light"===e?"dark":"light"]})]})})]})},z=()=>{let{sidebarOpen:e,toggleSidebar:s}=(0,k.C)();return(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"size-5"}),"Sidebar Control"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{children:["Sidebar is ",e?"open":"closed"]}),(0,t.jsxs)(p.$,{onClick:s,size:"sm",variant:"outline",children:[e?(0,t.jsx)(v.A,{className:"size-4"}):(0,t.jsx)(y.A,{className:"size-4"}),e?"Close":"Open"]})]})})]})},T=()=>{let{addNotification:e,clearAllNotifications:s,markNotificationAsRead:a,notifications:r,removeNotification:i,unreadNotificationCount:n}=(0,k.C)(),l=s=>{e({message:{error:"An error occurred. Please try again.",info:"This is an informational message",success:"Operation completed successfully!",warning:"Please review this warning"}[s],type:s})};return(0,t.jsxs)(f.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"size-5"}),"Notification System",n()>0&&(0,t.jsx)(h.E,{variant:"destructive",children:n()})]})}),(0,t.jsxs)(f.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(p.$,{onClick:()=>l("info"),size:"sm",variant:"outline",children:"Info"}),(0,t.jsx)(p.$,{onClick:()=>l("success"),size:"sm",variant:"outline",children:"Success"}),(0,t.jsx)(p.$,{onClick:()=>l("warning"),size:"sm",variant:"outline",children:"Warning"}),(0,t.jsx)(p.$,{onClick:()=>l("error"),size:"sm",variant:"outline",children:"Error"}),r.length>0&&(0,t.jsx)(p.$,{onClick:s,size:"sm",variant:"destructive",children:"Clear All"})]}),(0,t.jsx)("div",{className:"max-h-64 space-y-2 overflow-y-auto",children:0===r.length?(0,t.jsx)("p",{className:"py-4 text-center text-muted-foreground",children:"No notifications"}):r.map(e=>(0,t.jsx)("div",{className:"rounded border p-3 ".concat(e.read?"bg-muted":"bg-background"," ").concat("error"===e.type?"border-red-200":"warning"===e.type?"border-yellow-200":"success"===e.type?"border-green-200":"border-blue-200"),children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.E,{variant:"error"===e.type?"destructive":"warning"===e.type?"secondary":"success"===e.type?"default":"outline",children:e.type}),!e.read&&(0,t.jsx)(h.E,{variant:"outline",children:"New"})]}),(0,t.jsx)("p",{className:"mt-1 text-sm",children:e.message}),(0,t.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString()})]}),(0,t.jsxs)("div",{className:"ml-2 flex gap-1",children:[!e.read&&(0,t.jsx)(p.$,{onClick:()=>a(e.id),size:"sm",variant:"ghost",children:"Mark Read"}),(0,t.jsx)(p.$,{onClick:()=>i(e.id),size:"sm",variant:"ghost",children:"\xd7"})]})]})},e.id))})]})]})},A=()=>{let{fontSize:e,setFontSize:s}=(0,w.n)();return(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsx)(f.ZB,{children:"Font Size Preference"})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("label",{htmlFor:"font-size",children:"Font Size:"}),(0,t.jsxs)("select",{className:"rounded border px-2 py-1",id:"font-size",onChange:e=>s(e.target.value),value:e,children:[(0,t.jsx)("option",{value:"small",children:"Small"}),(0,t.jsx)("option",{value:"medium",children:"Medium"}),(0,t.jsx)("option",{value:"large",children:"Large"})]})]}),(0,t.jsxs)("div",{className:"rounded border p-3 ".concat("small"===e?"text-sm":"large"===e?"text-lg":"text-base"),children:["This text demonstrates the ",e," font size setting."]})]})})]})},S=()=>{let{closeModal:e,isModalOpen:s,modalContent:a,openModal:r}=(0,w.n)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"size-5"}),"Modal System"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(p.$,{onClick:()=>r("login"),size:"sm",variant:"outline",children:"Login Modal"}),(0,t.jsx)(p.$,{onClick:()=>r("signup"),size:"sm",variant:"outline",children:"Signup Modal"}),(0,t.jsx)(p.$,{onClick:()=>r("settings"),size:"sm",variant:"outline",children:"Settings Modal"})]})})]}),s&&(0,t.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",onClick:e,children:(0,t.jsxs)("div",{className:"mx-4 w-full max-w-md rounded-lg bg-white p-6",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"mb-4 flex items-center justify-between",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold capitalize",children:[a," Modal"]}),(0,t.jsx)(p.$,{onClick:e,size:"sm",variant:"ghost",children:"\xd7"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:["login"===a&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"Login form would go here..."}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Email",type:"email"}),(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Password",type:"password"})]})]}),"signup"===a&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"Signup form would go here..."}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Full Name",type:"text"}),(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Email",type:"email"}),(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Password",type:"password"})]})]}),"settings"===a&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"Settings form would go here..."}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center gap-2",children:[(0,t.jsx)("input",{type:"checkbox"}),"Enable notifications"]}),(0,t.jsxs)("label",{className:"flex items-center gap-2",children:[(0,t.jsx)("input",{type:"checkbox"}),"Dark mode"]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,t.jsx)(p.$,{className:"flex-1",onClick:e,variant:"outline",children:"Cancel"}),(0,t.jsx)(p.$,{className:"flex-1",children:"login"===a?"Login":"signup"===a?"Sign Up":"Save"})]})]})]})})]})},B=()=>(0,t.jsxs)("div",{className:"space-y-6 p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Zustand Stores Showcase"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(C,{}),(0,t.jsx)(z,{}),(0,t.jsx)(A,{}),(0,t.jsx)(S,{})]}),(0,t.jsx)(T,{})]});var Z=a(89440),M=a(42366);let R=()=>{let{currentTheme:e,setTheme:s}=(0,j.useTheme)(),{setTheme:a,theme:r}=(0,c.D)(),{showInfo:i,showSuccess:n}=(0,M.useNotifications)();return(0,t.jsxs)(f.Zp,{className:"mb-6",children:[(0,t.jsx)(f.aR,{children:(0,t.jsx)(f.ZB,{className:"flex items-center gap-2",children:"\uD83C\uDFA8 Theme Integration Test"})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"rounded border p-3",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Zustand Theme"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current: ",e]})]}),(0,t.jsxs)("div",{className:"rounded border p-3",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Next-themes"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current: ",r]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(p.$,{onClick:()=>{i("Testing theme synchronization between Next-themes and Zustand..."),setTimeout(()=>{s("dark"),i("Set Zustand theme to dark")},1e3),setTimeout(()=>{s("light"),i("Set Zustand theme to light")},2e3),setTimeout(()=>{a("dark"),i("Set Next-themes to dark")},3e3),setTimeout(()=>{n("Theme synchronization test completed!")},4e3)},variant:"outline",children:"Test Theme Sync"}),(0,t.jsx)(p.$,{onClick:()=>s("dark"),size:"sm",variant:"outline",children:"Zustand Dark"}),(0,t.jsx)(p.$,{onClick:()=>s("light"),size:"sm",variant:"outline",children:"Zustand Light"}),(0,t.jsx)(p.$,{onClick:()=>a("system"),size:"sm",variant:"outline",children:"System Theme"})]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Both theme values should stay synchronized. Test by clicking buttons and checking values above."})]})})]})},$=()=>{let{currentTheme:e}=(0,k.C)(),{fontSize:s,notificationsEnabled:a}=(0,w.n)(),{unreadCount:l}=(0,M.useNotifications)(),c=[{description:"Current theme: ".concat(e),name:"Theme Persistence",status:e?"pass":"fail"},{description:"Font size: ".concat(s),name:"Font Size Setting",status:s?"pass":"fail"},{description:"Unread notifications: ".concat(l()),name:"Notification System",status:"function"==typeof l?"pass":"fail"},{description:"Notifications enabled: ".concat(a),name:"UI Preferences",status:"boolean"==typeof a?"pass":"fail"}];return(0,t.jsxs)(f.Zp,{className:"mb-6",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(r,{className:"size-5"}),"Zustand Store Integration Tests"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:c.map((e,s)=>(0,t.jsx)("div",{className:"flex items-center justify-between rounded border p-3",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsxs)(h.E,{variant:"pass"===e.status?"default":"destructive",children:["pass"===e.status?(0,t.jsx)(i.A,{className:"mr-1 size-3"}):(0,t.jsx)(n.A,{className:"mr-1 size-3"}),e.status]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})},s))})})]})},E=()=>{let{showInfo:e,showSuccess:s}=(0,M.useNotifications)();return(0,t.jsxs)(f.Zp,{className:"mb-6",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"size-5"}),"Persistence Testing"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Test that your theme and UI preferences persist across browser sessions."}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(p.$,{onClick:()=>{e("Refresh the page to test persistence. Theme and font size should be maintained.")},variant:"outline",children:"Test Persistence"}),(0,t.jsx)(p.$,{onClick:()=>{s("Persistence test passed! Your preferences were maintained after refresh.")},variant:"default",children:"Confirm Persistence Works"})]})]})})]})};function D(){return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsx)(Z.AppBreadcrumb,{}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"mb-2 text-3xl font-bold",children:"Zustand Store Testing"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"This page demonstrates and tests all Zustand store functionality in WorkHub. Use this page to verify that stores are working correctly and persistence is functioning."})]}),(0,t.jsx)(g,{}),(0,t.jsx)(R,{}),(0,t.jsx)($,{}),(0,t.jsx)(E,{}),(0,t.jsx)(B,{})]})}},66695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>l,wL:()=>m});var t=a(95155),r=a(12115),i=a(54036);let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),ref:s,...r})});n.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),ref:s,...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),ref:s,...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)("text-sm text-muted-foreground",a),ref:s,...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)("p-6 pt-0",a),ref:s,...r})});o.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex items-center p-6 pt-0",a),ref:s,...r})});m.displayName="CardFooter"},73158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},74466:(e,s,a)=>{"use strict";a.d(s,{F:()=>n});var t=a(52596);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=t.$,n=(e,s)=>a=>{var t;if((null==s?void 0:s.variants)==null)return i(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:n,defaultVariants:l}=s,c=Object.keys(n).map(e=>{let s=null==a?void 0:a[e],t=null==l?void 0:l[e];if(null===s)return null;let i=r(s)||r(t);return n[e][i]}),d=a&&Object.entries(a).reduce((e,s)=>{let[a,t]=s;return void 0===t||(e[a]=t),e},{});return i(e,c,null==s||null==(t=s.compoundVariants)?void 0:t.reduce((e,s)=>{let{class:a,className:t,...r}=s;return Object.entries(r).every(e=>{let[s,a]=e;return Array.isArray(a)?a.includes({...l,...d}[s]):({...l,...d})[s]===a})?[...e,a,t]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}},85187:(e,s,a)=>{"use strict";a.r(s),a.d(s,{useTheme:()=>i});var t=a(12115),r=a(96016);let i=()=>{let e=(0,r.C)(e=>e.currentTheme),s=(0,r.C)(e=>e.setTheme),a=(0,t.useCallback)(()=>{s("light"===e?"dark":"light")},[e,s]),i="dark"===e,n="light"===e,l=(0,t.useCallback)(()=>{s("light")},[s]),c=(0,t.useCallback)(()=>{s("dark")},[s]),d=(0,t.useCallback)(()=>({background:i?"bg-gray-900":"bg-white",border:i?"border-gray-700":"border-gray-200",isDark:i,isLight:n,root:e,text:i?"text-white":"text-gray-900"}),[e,i,n]);return{currentTheme:e,getThemeClasses:d,isDark:i,isLight:n,setDarkTheme:c,setLightTheme:l,setTheme:s,toggleTheme:a}}},89440:(e,s,a)=>{"use strict";a.d(s,{AppBreadcrumb:()=>o});var t=a(95155),r=a(6874),i=a.n(r),n=a(35695),l=a(12115),c=a(19018),d=a(54036);function o(e){let{className:s,homeHref:a="/",homeLabel:r="Dashboard",showContainer:o=!0}=e,m=(0,n.usePathname)(),u=m?m.split("/").filter(Boolean):[],x=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let s={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return s[e]?s[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},h=u.map((e,s)=>{let a="/"+u.slice(0,s+1).join("/"),r=s===u.length-1,n=x(e);return(0,t.jsxs)(l.Fragment,{children:[(0,t.jsx)(c.BreadcrumbItem,{children:r?(0,t.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:n}):(0,t.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:a,children:n})})}),!r&&(0,t.jsx)(c.BreadcrumbSeparator,{})]},a)}),p=(0,t.jsx)(c.Breadcrumb,{className:(0,d.cn)("text-sm",s),children:(0,t.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,t.jsx)(c.BreadcrumbItem,{children:(0,t.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:a,children:r})})}),u.length>0&&(0,t.jsx)(c.BreadcrumbSeparator,{}),h]})});return o?(0,t.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"flex items-center",children:p})}):p}},96016:(e,s,a)=>{"use strict";a.d(s,{C:()=>i});var t=a(65453),r=a(46786);let i=(0,t.v)()((0,r.lt)((0,r.Zr)((e,s)=>({addNotification:s=>e(e=>({notifications:[...e.notifications,{...s,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:s=>e(e=>({notifications:e.notifications.map(e=>e.id===s?{...e,read:!0}:e)})),notifications:[],removeNotification:s=>e(e=>({notifications:e.notifications.filter(e=>e.id!==s)})),setTheme:s=>{e({currentTheme:s})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=s();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,6874,87,4036,8441,1684,7358],()=>s(18231)),_N_E=e.O()}]);