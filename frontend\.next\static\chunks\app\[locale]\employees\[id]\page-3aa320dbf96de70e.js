(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5283],{40879:(e,t,s)=>{"use strict";s.d(t,{dj:()=>u,oR:()=>m});var r=s(12115);let a=0,i=new Map,l=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),o({toastId:e,type:"REMOVE_TOAST"})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:s}=t;if(s)l(s);else for(let t of e.toasts)l(t.id);return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},c=[],d={toasts:[]};function o(e){for(let t of(d=n(d,e),c))t(d)}function m(e){let{...t}=e,s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>o({toastId:s,type:"DISMISS_TOAST"});return o({toast:{...t,id:s,onOpenChange:e=>{e||r()},open:!0},type:"ADD_TOAST"}),{dismiss:r,id:s,update:e=>o({toast:{...e,id:s},type:"UPDATE_TOAST"})}}function u(){let[e,t]=r.useState(d);return r.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);-1!==e&&c.splice(e,1)}),[e]),{...e,dismiss:e=>o({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:m}}},51123:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Q});var r=s(95155),a=s(41784),i=s(83343),l=s(31949),n=s(12543),c=s(79239),d=s(67270),o=s(60335),m=s(67554),u=s(18763),p=s(77223),h=s(19637),x=s(31554),g=s(98328),f=s(57082),y=s(86950),j=s(76570),v=s(28328),N=s(3235),b=s(37648),A=s(50594),w=s(66766),k=s(6874),E=s.n(k),S=s(35695),D=s(12115),T=s(85752),z=s(58127),C=s(40207),L=s(88628),R=s(83662),F=s(88234),M=s(26126),O=s(30285),U=s(66695),_=s(40879);let I=e=>{let{employee:t,refreshInterval:s=3e4}=e,{toast:a}=(0,_.dj)(),[i,n]=(0,D.useState)(null),[c,d]=(0,D.useState)(null),[o,m]=(0,D.useState)({lastUpdate:new Date,status:"active"}),[u,p]=(0,D.useState)(!1),[h,g]=(0,D.useState)(null),f=(0,D.useRef)(null),y=(0,D.useRef)(null),j=(0,D.useRef)(null);return(0,D.useEffect)(()=>{console.log("Vehicle assignment is now context-specific for employee:",t.id)},[t.id,t.currentLocation]),(0,D.useEffect)(()=>((async()=>{if(f.current)try{let e=T.map(f.current).setView([40.7128,-74.006],13);T.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:'&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',maxZoom:19}).addTo(e),y.current=e,j.current=T.marker([40.7128,-74.006]).addTo(e).bindPopup("".concat(t.name," - ").concat(t.role))}catch(e){console.error("Failed to initialize map:",e),a({description:"Failed to load map component.",title:"Map Error",variant:"destructive"})}})(),()=>{y.current&&y.current.remove()}),[t.name,t.role,a]),(0,D.useEffect)(()=>{if(!u)return;let e=setInterval(()=>{let e={accuracy:Math.floor(10*Math.random())+5,heading:Math.floor(360*Math.random()),latitude:40.7128+(Math.random()-.5)*.01,longitude:-74.006+(Math.random()-.5)*.01,speed:Math.floor(60*Math.random())+20,timestamp:new Date};if(d(e),g(new Date),y.current&&j.current){let t=T.latLng(e.latitude,e.longitude);j.current.setLatLng(t),y.current.setView(t)}let t=["active","break","active","active"],s=t[Math.floor(Math.random()*t.length)];m(e=>({...e,currentTask:e.currentTask||"No current task",lastUpdate:new Date,status:s}))},s);return()=>clearInterval(e)},[u,s]),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(U.Zp,{children:[(0,r.jsx)(U.aR,{children:(0,r.jsxs)(U.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(C.A,{className:"size-5"}),"Driver Status"]}),(0,r.jsx)("div",{className:"flex gap-2",children:u?(0,r.jsx)(O.$,{onClick:()=>{p(!1),a({description:"Real-time tracking disabled for ".concat(t.name),title:"Tracking Stopped",variant:"default"})},size:"sm",variant:"outline",children:"Stop Tracking"}):(0,r.jsxs)(O.$,{onClick:()=>{p(!0),a({description:"Real-time tracking enabled for ".concat(t.name),title:"Tracking Started",variant:"default"})},size:"sm",children:[(0,r.jsx)(L.A,{className:"mr-2 size-4"}),"Start Tracking"]})})]})}),(0,r.jsx)(U.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"size-3 rounded-full ".concat((e=>{switch(e){case"active":return"bg-green-500";case"break":return"bg-yellow-500";case"emergency":return"bg-red-500";default:return"bg-gray-500"}})(o.status))}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(e=>{switch(e){case"active":return(0,r.jsx)(z.A,{className:"size-4"});case"break":return(0,r.jsx)(b.A,{className:"size-4"});case"emergency":return(0,r.jsx)(l.A,{className:"size-4"});default:return(0,r.jsx)(C.A,{className:"size-4"})}})(o.status),(0,r.jsx)("span",{className:"font-medium capitalize",children:o.status})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Updated: ",o.lastUpdate.toLocaleTimeString()]})]})]}),o.currentTask&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(R.A,{className:"size-4 text-blue-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Current Task"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:o.currentTask})]})]}),h&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"size-4 text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Last Location Update"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:h.toLocaleTimeString()})]})]})]})})]}),c&&(0,r.jsxs)(U.Zp,{children:[(0,r.jsx)(U.aR,{children:(0,r.jsxs)(U.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(R.A,{className:"size-5"}),"Live Location"]})}),(0,r.jsxs)(U.Wu,{children:[(0,r.jsxs)("div",{className:"mb-4 grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Coordinates"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[c.latitude.toFixed(6),","," ",c.longitude.toFixed(6)]})]}),c.speed&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Speed"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[c.speed," mph"]})]}),c.accuracy&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Accuracy"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xb1",c.accuracy,"m"]})]}),c.heading&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Heading"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[c.heading,"\xb0"]})]})]}),(0,r.jsx)("div",{className:"h-64 w-full rounded-lg border",ref:f,style:{minHeight:"250px"}})]})]}),i&&(0,r.jsxs)(U.Zp,{children:[(0,r.jsx)(U.aR,{children:(0,r.jsxs)(U.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"size-5"}),"Assigned Vehicle"]})}),(0,r.jsx)(U.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Vehicle"}),(0,r.jsxs)("p",{className:"text-lg",children:[i.make," ",i.model," (",i.year,")"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"License Plate"}),(0,r.jsx)("p",{className:"w-fit rounded bg-muted px-2 py-1 font-mono text-sm",children:i.licensePlate})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Status"}),(0,r.jsx)(M.E,{variant:"Available"===i.status?"default":"secondary",children:i.status})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[i.mileage&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Mileage"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[i.mileage.toLocaleString()," miles"]})]}),i.fuelLevel&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Fuel Level"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(F.A,{className:"size-4"}),(0,r.jsx)("div",{className:"h-2 flex-1 rounded-full bg-muted",children:(0,r.jsx)("div",{className:"h-2 rounded-full bg-blue-500",style:{width:"".concat(i.fuelLevel,"%")}})}),(0,r.jsxs)("span",{className:"text-sm",children:[i.fuelLevel,"%"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Location"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:i.currentLocation||"Location unknown"})]})]})]})})]}),(0,r.jsxs)(U.Zp,{children:[(0,r.jsx)(U.aR,{children:(0,r.jsxs)(U.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(x.A,{className:"size-5"}),"Communication"]})}),(0,r.jsx)(U.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsxs)(O.$,{className:"justify-start",variant:"outline",children:[(0,r.jsx)(x.A,{className:"mr-2 size-4"}),"Call Driver"]}),(0,r.jsxs)(O.$,{className:"justify-start",variant:"outline",children:[(0,r.jsx)(R.A,{className:"mr-2 size-4"}),"Send Location"]})]})})]})]})};var P=s(6560),V=s(55365),Z=s(89440),B=s(77023),W=s(95647),G=s(22346),H=s(83940),$=s(83761),X=s(54036);let q=e=>{if(!e)return"bg-gray-500/20 text-gray-700 border-gray-500/30";switch(e){case"Active":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"On_Leave":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Terminated":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},J=e=>{if(!e)return"bg-gray-500/20 text-gray-700 border-gray-500/30";switch(e){case"Busy":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Off_Shift":default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20";case"On_Break":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"On_Shift":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20"}},Q=()=>{let e=(0,S.useRouter)(),t=(0,S.useParams)(),s=null==t?void 0:t.id,k=Number.parseInt(s,10),{data:D,error:T,isLoading:z,refetch:C}=(0,$.uC)(s),{isPending:L,mutate:R}=(0,$.FT)(),F=async()=>{if(isNaN(k)||!D)return void H.Ok.entityDeletionError("Invalid employee ID or employee data missing.");let t=D.name||"Employee";globalThis.confirm("Are you sure you want to permanently delete ".concat(t,"?\n\nThis action cannot be undone and will remove all employee data from the system."))&&R(s,{onError:e=>{var t,s,r,a,i,l;console.error("Failed to delete employee:",e);let n="Could not delete employee. Please try again.";(null==(t=e.message)?void 0:t.includes("Network error"))?n="Network error. Please check your connection and try again.":(null==(s=e.message)?void 0:s.includes("404"))?n="Employee not found or already deleted.":(null==(r=e.message)?void 0:r.includes("403"))?n="You do not have permission to delete this employee.":(null==(a=e.message)?void 0:a.includes("500"))?n="Server error. Please contact support if this persists.":(null==(l=e.response)||null==(i=l.data)?void 0:i.error)?n=e.response.data.error:e.message&&(n=e.message),H.Ok.entityDeletionError(n)},onSuccess:()=>{H.Ok.entityDeleted({name:t}),e.push("/employees")}})},O=e=>{let{icon:t,label:s,value:a,valueClassName:i}=e;return a||0===a?(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(t,{className:"mr-3 mt-0.5 size-5 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:s}),(0,r.jsx)("p",{className:(0,X.cn)("text-base font-semibold text-foreground",i),children:a})]})]}):null};return(0,r.jsxs)("div",{className:"container mx-auto space-y-6 py-8",children:[(0,r.jsx)(Z.AppBreadcrumb,{}),(0,r.jsx)(B.gO,{data:D,emptyComponent:(0,r.jsxs)("div",{className:"py-10 text-center",children:[(0,r.jsx)(W.z,{icon:l.A,title:"Employee Not Found"}),(0,r.jsx)("p",{className:"mb-4",children:"The requested employee could not be found."}),(0,r.jsx)(P.r,{actionType:"primary",icon:(0,r.jsx)(n.A,{className:"size-4"}),onClick:()=>e.push("/employees"),children:"Back to Employees"})]}),error:T?T.message:null,isLoading:z,loadingComponent:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(W.z,{icon:c.A,title:"Loading Employee..."}),(0,r.jsx)(B.jt,{count:1,variant:"card"})]}),onRetry:()=>{C()},children:e=>{var t;let s=e.name||"Employee";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(W.z,{description:"Details for Employee ID: ".concat(e.id),icon:c.A,title:s}),T&&(0,r.jsxs)(V.Fc,{variant:"destructive",children:[(0,r.jsx)(d.A,{className:"size-5"}),(0,r.jsx)(V.XL,{children:"An Error Occurred"}),(0,r.jsx)(V.TN,{children:T.message})]}),(0,r.jsxs)(U.Zp,{className:"shadow-lg",children:[(0,r.jsx)(U.aR,{className:"p-5",children:(0,r.jsxs)("div",{className:"flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"relative flex size-20 items-center justify-center overflow-hidden rounded-full bg-muted ring-2 ring-primary/30",children:e.profileImageUrl?(0,r.jsx)(w.default,{alt:s,"data-ai-hint":"employee profile",layout:"fill",objectFit:"cover",src:e.profileImageUrl}):(0,r.jsx)(o.A,{className:"size-12 text-muted-foreground"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(U.ZB,{className:"text-2xl font-bold text-primary",children:s}),(0,r.jsxs)(U.BT,{className:"text-sm",children:[e.position," -"," ",e.department]}),(0,r.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,r.jsx)(M.E,{className:(0,X.cn)("text-xs",q(e.status)),children:e.status}),"driver"===e.role&&e.availability&&(0,r.jsx)(M.E,{className:(0,X.cn)("text-xs",J(e.availability)),children:null==(t=e.availability)?void 0:t.replace("_"," ")})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 self-start sm:self-center",children:[(0,r.jsxs)(P.r,{actionType:"tertiary",className:"group relative",disabled:z,onClick:()=>{C()},size:"icon",title:"Refresh Employee Data",children:[(0,r.jsx)(m.A,{className:(0,X.cn)("h-4 w-4",z&&"animate-spin")}),(0,r.jsx)("span",{className:"sr-only",children:z?"Refreshing...":"Refresh employee data"})]}),(0,r.jsx)(P.r,{actionType:"secondary",asChild:!0,className:"group relative",size:"icon",title:"Edit Employee Details",children:(0,r.jsxs)(E(),{className:"inline-flex items-center justify-center",href:"/employees/".concat(e.id,"/edit"),children:[(0,r.jsx)(u.A,{className:"size-4"}),(0,r.jsxs)("span",{className:"sr-only",children:["Edit ",s]})]})}),(0,r.jsxs)(P.r,{actionType:"danger",className:"group relative",disabled:L,isLoading:L,loadingText:"",onClick:F,size:"icon",title:"Delete ".concat(s),children:[(0,r.jsx)(p.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:L?"Deleting...":"Delete ".concat(s)})]})]})]})}),(0,r.jsxs)(U.Wu,{className:"mt-2 space-y-8 p-5",children:[(0,r.jsxs)("section",{children:[(0,r.jsxs)("h3",{className:"mb-3 flex items-center text-xl font-semibold text-primary",children:[(0,r.jsx)(o.A,{className:"mr-2 size-5 text-accent"})," ","Contact & Employment"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-x-6 gap-y-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsx)(O,{icon:h.A,label:"Email / Primary Contact",value:e.contactEmail}),e.contactMobile&&(0,r.jsx)(O,{icon:x.A,label:"Mobile",value:e.contactMobile}),e.contactPhone&&!e.contactMobile&&(0,r.jsx)(O,{icon:x.A,label:"Phone",value:e.contactPhone}),(0,r.jsx)(O,{icon:g.A,label:"Hire Date",value:e.hireDate?(0,a.GP)((0,i.H)(e.hireDate),"MMMM d, yyyy"):"N/A"}),(0,r.jsx)(O,{icon:f.A,label:"Role",value:e.role?e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "):"N/A"}),(0,r.jsx)(O,{icon:y.A,label:"Department",value:e.department}),(0,r.jsx)(O,{icon:j.A,label:"Employee ID (System)",value:e.id.toString()})]})]}),("driver"===e.role||e.skills&&e.skills.length>0||e.shiftSchedule||e.generalAssignments&&e.generalAssignments.length>0)&&(0,r.jsx)(G.w,{className:"my-4"}),"driver"===e.role&&(0,r.jsxs)("section",{children:[(0,r.jsxs)("h3",{className:"mb-3 flex items-center text-xl font-semibold text-primary",children:[(0,r.jsx)(v.A,{className:"mr-2 size-5 text-accent"})," Driver Information"]}),(0,r.jsx)(I,{employee:e})]}),e.skills&&e.skills.length>0||e.shiftSchedule||e.generalAssignments&&e.generalAssignments.length>0?(0,r.jsxs)("section",{children:[(0,r.jsxs)("h3",{className:"mb-3 flex items-center text-xl font-semibold text-primary",children:[(0,r.jsx)(N.A,{className:"mr-2 size-5 text-accent"})," Work Details"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-x-6 gap-y-4 md:grid-cols-2",children:[e.skills&&e.skills.length>0&&(0,r.jsx)(O,{icon:N.A,label:"Skills",value:e.skills.join(", ")}),(0,r.jsx)(O,{icon:b.A,label:"Shift Schedule",value:e.shiftSchedule}),e.generalAssignments&&e.generalAssignments.length>0&&(0,r.jsx)(O,{icon:f.A,label:"General Assignments",value:e.generalAssignments.join(", ")})]})]}):null,e.notes&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(G.w,{className:"my-4"}),(0,r.jsxs)("section",{children:[(0,r.jsxs)("h3",{className:"mb-2 flex items-center text-xl font-semibold text-primary",children:[(0,r.jsx)(A.A,{className:"mr-2 size-5 text-accent"})," Notes"]}),(0,r.jsx)("p",{className:"whitespace-pre-wrap rounded-md bg-muted/50 p-3 text-sm text-foreground",children:e.notes})]})]})]}),(0,r.jsxs)(U.wL,{className:"border-t p-5 pt-4 text-xs text-muted-foreground",children:["Registered:"," ",new Date(e.createdAt).toLocaleString()," | Last updated: ",new Date(e.updatedAt).toLocaleString()]})]})]})}})]})}},55955:(e,t,s)=>{Promise.resolve().then(s.bind(s,51123))},83940:(e,t,s)=>{"use strict";s.d(t,{G7:()=>u,Gb:()=>c,JP:()=>d,Ok:()=>o,Qu:()=>m,iw:()=>n,oz:()=>h,z0:()=>p});var r=s(40879);class a{show(e){return(0,r.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class i extends a{entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}constructor(e){super(),this.config=e}}class l extends a{serviceRecordCreated(e,t){return this.success("Service Record Added","".concat(t,' service for "').concat(e,'" has been successfully logged.'))}serviceRecordUpdated(e,t){return this.success("Service Record Updated","".concat(t,' service for "').concat(e,'" has been updated.'))}serviceRecordDeleted(e,t){return this.success("Service Record Deleted","".concat(t,' service record for "').concat(e,'" has been permanently removed.'))}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new i(e)}function c(e,t){return new i({entityName:e,getDisplayName:t,messages:{created:{title:"".concat(e," Created"),description:t=>"The ".concat(e.toLowerCase(),' "').concat(t,'" has been successfully created.')},updated:{title:"".concat(e," Updated Successfully"),description:e=>"".concat(e," has been updated.")},deleted:{title:"".concat(e," Deleted Successfully"),description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create ".concat(e),description:t=>t||"An unexpected error occurred while creating the ".concat(e.toLowerCase(),".")},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the ".concat(e.toLowerCase(),".")},deletionError:{title:"Failed to Delete ".concat(e),description:t=>t||"An unexpected error occurred while deleting the ".concat(e.toLowerCase(),".")}}})}let d=new a,o=new i({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>'The employee "'.concat(e,'" has been successfully created.')},updated:{title:"Employee Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Employee Deleted Successfully",description:e=>"".concat(e," has been permanently removed from the system.")},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),m=new i({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>'The delegation "'.concat(e,'" has been successfully created.')},updated:{title:"Delegation Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Delegation Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),u=new i({entityName:"Vehicle",getDisplayName:e=>"".concat(e.make," ").concat(e.model),messages:{created:{title:"Vehicle Added",description:e=>'The vehicle "'.concat(e,'" has been successfully created.')},updated:{title:"Vehicle Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Vehicle Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),p=new i({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>'The task "'.concat(e,'" has been successfully created.')},updated:{title:"Task Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Task Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new l}},e=>{var t=t=>e(e.s=t);e.O(0,[1761,6476,7047,3860,9664,6874,6766,5405,4036,4767,8950,7515,6762,8441,1684,7358],()=>t(55955)),_N_E=e.O()}]);