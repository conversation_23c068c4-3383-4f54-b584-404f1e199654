"use strict";exports.id=4423,exports.ids=[4423],exports.modules={2775:(e,r,t)=>{t.d(r,{Ln:()=>y,WV:()=>u,fs:()=>m,kI:()=>p,xH:()=>v,xT:()=>x});var a=t(8693),i=t(54050),s=t(77312),l=t(46349),c=t(8342);let n={fromApi(e){let r={cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId};return e.vehicleMake||e.vehicleModel||e.vehicleYear?{...r,vehicleMake:e.vehicleMake||"Unknown",vehicleModel:e.vehicleModel||"Unknown",vehicleYear:e.vehicleYear||new Date().getFullYear(),licensePlate:e.licensePlate||null,employeeName:e.employeeName||null}:r},toApi:e=>e};class o extends s.v{constructor(e,r){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...r}),this.endpoint="/servicerecords",this.transformer=n}async getById(e){return this.executeWithInfrastructure(`${this.endpoint}:${e}`,async()=>{let r=await this.apiClient.get(`${this.endpoint}/${e}`);return this.transformer.fromApi?this.transformer.fromApi(r):r})}async updateRecord(e,r,t){return this.executeWithInfrastructure(null,async()=>{let{vehicleId:a,...i}=t,s=await this.apiClient.put(`/vehicles/${r}/servicerecords/${e}`,i);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:${e}`)),this.cache.invalidatePattern(RegExp(`^vehicles:${r}:`)),this.transformer.fromApi?this.transformer.fromApi(s):s})}async deleteRecord(e,r){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete(`/vehicles/${r}/servicerecords/${e}`),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:${e}`)),this.cache.invalidatePattern(RegExp(`^vehicles:${r}:`))})}async getVehicleServiceRecords(e){return this.executeWithInfrastructure(`vehicles:${e}:servicerecords`,async()=>await this.apiClient.get(`/vehicles/${e}/servicerecords`))}async createVehicleServiceRecord(e,r){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.post(`/vehicles/${e}/servicerecords`,r);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^vehicles:${e}:`)),this.transformer.fromApi?this.transformer.fromApi(t):t})}async getAllEnriched(){return this.executeWithInfrastructure(`${this.endpoint}:enriched`,async()=>(await this.apiClient.get(`${this.endpoint}/enriched`)).map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e))}}let d=new o(c.uE),h="serviceRecords",u=(e,r)=>(0,l.GK)([h,e],()=>d.getById(e),"serviceRecord",{enabled:r?.enabled??!!e,staleTime:3e5}),m=e=>(0,l.GK)([h,"allEnriched"],()=>d.getAllEnriched(),"serviceRecord",{enabled:e?.enabled??!0,staleTime:3e5}),v=(e,r)=>(0,l.GK)([h,"forVehicle",e],()=>d.getVehicleServiceRecords(e),"serviceRecord",{enabled:r?.enabled??!0,staleTime:3e5}),p=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async e=>{let{vehicleId:r}=e;return d.createVehicleServiceRecord(r,e)},onSuccess:(r,t)=>{e.invalidateQueries({queryKey:[h,"allEnriched"]}),e.invalidateQueries({queryKey:[h,"forVehicle",t.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",t.vehicleId]})}})},y=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async({id:e,vehicleId:r,data:t})=>d.updateRecord(e,r,t),onSuccess:(r,t)=>{e.invalidateQueries({queryKey:[h,"allEnriched"]}),e.invalidateQueries({queryKey:[h,t.id]}),e.invalidateQueries({queryKey:[h,"forVehicle",t.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",t.vehicleId]})}})},x=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async({id:e,vehicleId:r})=>d.deleteRecord(e,r),onSuccess:(r,t)=>{e.invalidateQueries({queryKey:[h,"allEnriched"]}),e.invalidateQueries({queryKey:[h,t.id]}),e.invalidateQueries({queryKey:[h,"forVehicle"]}),e.invalidateQueries({queryKey:["vehicle"]})}})}},5068:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(82614).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},24847:(e,r,t)=>{t.d(r,{R:()=>N});var a=t(60687),i=t(77368),s=t(44610),l=t(35265);t(43210);var c=t(96834),n=t(44493),o=t(22482);function d({className:e,records:r,vehicleSpecific:t=!1}){let i=r.length,s=r.reduce((e,r)=>e+(Number(r.cost)||0),0),l=r.map(e=>new Date(e.date).getTime()),d=l.length>0?new Date(Math.min(...l)):null,u=l.length>0?new Date(Math.max(...l)):null,m=Object.entries(r.reduce((e,r)=>{for(let t of r.servicePerformed)e[t]=(e[t]||0)+1;return e},{})).sort((e,r)=>r[1]-e[1]).slice(0,3),v=t&&r.length>0?Math.max(...r.map(e=>e.odometer))-Math.min(...r.map(e=>e.odometer)):0,p=!t&&r.length>0?new Set(r.map(e=>e.vehicleId)).size:0;return(0,a.jsxs)("div",{className:(0,o.cn)("mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4 summary-grid",e),children:[(0,a.jsx)(h,{className:"border-primary/10",label:"Total Services",value:i}),(0,a.jsx)(h,{className:"border-primary/10",label:"Total Cost",value:`$${s.toFixed(2)}`}),!t&&r.length>0&&(0,a.jsx)(h,{className:"border-primary/10",label:"Vehicles Serviced",value:p}),t&&r.length>0&&(0,a.jsx)(h,{className:"border-primary/10",label:"Odometer Range Covered",value:v.toLocaleString()}),r.length>0&&(0,a.jsx)(h,{className:"border-primary/10",colSpan:"col-span-2 sm:col-span-3",label:"Date Range",value:`${d?.toLocaleDateString()} - ${u?.toLocaleDateString()}`}),m.length>0&&(0,a.jsx)(n.Zp,{className:"col-span-2 overflow-hidden border-primary/10 sm:col-span-3",children:(0,a.jsxs)(n.Wu,{className:"p-4",children:[(0,a.jsx)("h3",{className:"mb-2 text-sm font-semibold text-card-foreground",children:"Top Services"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:m.map(([e,r])=>(0,a.jsxs)(c.E,{"aria-label":`${e}: ${r} services`,className:"px-2 py-1 text-xs",variant:"secondary",children:[e," (",r,")"]},e))})]})})]})}function h({className:e,colSpan:r,label:t,textColor:i="text-muted-foreground",value:s}){return(0,a.jsx)(n.Zp,{className:(0,o.cn)("overflow-hidden",e,r),children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-semibold text-card-foreground",children:s}),(0,a.jsx)("p",{className:(0,o.cn)("text-sm",i),children:t})]})})}var u=t(29523),m=t(33086),v=t(52027),p=t(3389),y=t(2775),x=t(5068),f=t(23949);function g({className:e="",records:r,showVehicleInfo:t=!0,onDelete:i,onBulkDelete:s}){let l=[(0,f.BZ)(),(0,f.vk)("date","Date","MMM dd, yyyy"),...t?[{accessorKey:"vehicleMake",header:(0,f.YB)("Vehicle"),cell:({row:e})=>{let r=e.original;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"font-medium text-sm",children:[r.vehicleMake," ",r.vehicleModel," (",r.vehicleYear,")"]}),r.licensePlate&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground font-mono",children:r.licensePlate})]})}}]:[],{accessorKey:"servicePerformed",header:(0,f.YB)("Service(s)"),cell:({row:e})=>{let r=e.getValue("servicePerformed").join(", ");return(0,a.jsx)("div",{className:"max-w-xs truncate",title:r,children:r})}},{accessorKey:"odometer",header:(0,f.YB)("Odometer"),cell:({row:e})=>{let r=e.getValue("odometer");return r?r.toLocaleString():"-"}},(0,f.yX)("cost","Cost",{decimals:2,prefix:"$"}),(0,f.K)("notes","Notes",{maxLength:50,className:"max-w-xs"}),(0,f.Wy)({viewHref:e=>`/service-records/${e.id}`,editHref:e=>`/service-records/${e.id}/edit`,...i&&{onDelete:e=>{i(e)}},showCopyId:!0,customActions:[{label:"View Vehicle",onClick:e=>{window.location.href=`/vehicles/${e.vehicleId}`}}]})],c=[...s?[{label:"Delete Selected",icon:({className:e})=>(0,a.jsx)(x.A,{className:e}),onClick:async e=>{await s(e)},variant:"destructive"}]:[]];return(0,a.jsx)(f.bQ,{data:r,columns:l,className:e,searchPlaceholder:"Search service records by service type or notes...",searchColumn:"servicePerformed",emptyMessage:"No service records found. Add your first service record to get started.",pageSize:15,enableRowSelection:!0,enableBulkActions:c.length>0,bulkActions:c,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20",rowClassName:"hover:bg-green-50/50 dark:hover:bg-green-900/10"})}function N({className:e,error:r,isLoading:t,onRetry:c,records:o,showVehicleInfo:h=!0,vehicleSpecific:x=!1}){let{toast:f}=(0,p.dj)(),N=(0,y.xT)(),b=async e=>{try{await N.mutateAsync({id:e.id,vehicleId:e.vehicleId}),f({title:"Deleted!",description:"Service record deleted successfully.",variant:"default"}),c()}catch(e){f({title:"Error",description:"Failed to delete service record. Please try again.",variant:"destructive"})}},k=async e=>{try{await Promise.all(e.map(e=>N.mutateAsync({id:e.id,vehicleId:e.vehicleId}))),f({title:"Deleted!",description:`${e.length} service records deleted successfully.`,variant:"default"}),c()}catch(e){f({title:"Error",description:"Failed to delete some service records. Please try again.",variant:"destructive"})}};return t?(0,a.jsx)("div",{className:"space-y-4","data-testid":"loading-skeleton",children:(0,a.jsx)(v.jt,{count:5,variant:"table"})}):r?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(m.zc,{context:"Loading Service Records",error:r}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(u.$,{"aria-label":"Try loading service records again",onClick:c,size:"sm",variant:"outline",children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),"Try Again"]})})]}):0===o.length?(0,a.jsx)(v.pp,{title:"No Service Records Found",description:x?"No service records available for this vehicle. You can log a new service record to get started.":"There are no service records matching your current filters. You can log a new service record or adjust your filters.",icon:s.A,primaryAction:{label:"Log New Service",href:"/vehicles",icon:(0,a.jsx)(l.A,{className:"size-4"})}}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(d,{records:o,vehicleSpecific:x}),(0,a.jsx)(n.Zp,{className:"card-print shadow-md",children:(0,a.jsx)(n.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)(g,{records:o,showVehicleInfo:h,onDelete:b,onBulkDelete:k})})})})]})}},33086:(e,r,t)=>{t.d(r,{zc:()=>d});var a=t(60687),i=t(97025),s=t(14975),l=t(99196);t(43210);var c=t(91821),n=t(3940),o=t(48184);function d({className:e="",context:r,error:t,showToast:d=!1}){var h;let{showFormError:u}=(0,n.t6)();if(!t)return null;let m=(0,o.iG)(t,r),v=(h=m.code||"UNKNOWN_ERROR",["VALIDATION_ERROR","ASSIGNMENT_ERROR","ROLE_ERROR"].some(e=>h.includes(e))?"error":["BUSINESS_RULE_WARNING","DEPRECATION_WARNING"].some(e=>h.includes(e))?"warning":"info");return(0,a.jsxs)(c.Fc,{className:`${e} ${function(e){switch(e){case"error":return"border-destructive/50 text-destructive dark:border-destructive";case"warning":return"border-yellow-500/50 text-yellow-600 dark:border-yellow-500 dark:text-yellow-400";default:return"border-blue-500/50 text-blue-600 dark:border-blue-500 dark:text-blue-400"}}(v)}`,children:[function(e){switch(e){case"error":return(0,a.jsx)(i.A,{className:"size-4"});case"warning":return(0,a.jsx)(s.A,{className:"size-4"});default:return(0,a.jsx)(l.A,{className:"size-4"})}}(v),(0,a.jsx)(c.XL,{children:m.title}),(0,a.jsxs)(c.TN,{children:[m.message,m.code&&(0,a.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Error Code: ",m.code]}),m.field&&(0,a.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Field: ",m.field]})]})]})}},35265:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(82614).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},65456:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(82614).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},72273:(e,r,t)=>{t.d(r,{NS:()=>v,T$:()=>d,W_:()=>h,Y1:()=>u,lR:()=>m});var a=t(8693),i=t(54050),s=t(46349),l=t(87676),c=t(48839),n=t(49603);let o={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,s.GK)([...o.all],async()=>(await n.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),h=(e,r)=>(0,s.GK)([...o.detail(e)],()=>n.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(r?.enabled??!0),staleTime:3e5,...r}),u=()=>{let e=(0,a.jE)(),{showError:r,showSuccess:t}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>{let r=c.M.toCreateRequest(e);return n.vehicleApiService.create(r)},onError:e=>{r(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:r=>{e.invalidateQueries({queryKey:o.all}),t(`Vehicle "${r.licensePlate}" has been created successfully!`)}})},m=()=>{let e=(0,a.jE)(),{showError:r,showSuccess:t}=(0,l.useNotifications)();return(0,i.n)({mutationFn:({data:e,id:r})=>{let t=c.M.toUpdateRequest(e);return n.vehicleApiService.update(r,t)},onError:e=>{r(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:r=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(r.id)}),t(`Vehicle "${r.licensePlate}" has been updated successfully!`)}})},v=()=>{let e=(0,a.jE)(),{showError:r,showSuccess:t}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>n.vehicleApiService.delete(e),onError:e=>{r(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(r,a)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(a)}),t("Vehicle has been deleted successfully!")}})}},72322:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(82614).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},72975:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(82614).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},87676:(e,r,t)=>{t.r(r),t.d(r,{useNotifications:()=>s,useWorkHubNotifications:()=>l});var a=t(43210),i=t(94538);let s=()=>{let e=(0,i.C)(e=>e.addNotification),r=(0,i.C)(e=>e.removeNotification),t=(0,i.C)(e=>e.clearAllNotifications),s=(0,i.C)(e=>e.unreadNotificationCount),l=(0,a.useCallback)(r=>{e({message:r,type:"success"})},[e]),c=(0,a.useCallback)(r=>{e({message:r,type:"error"})},[e]),n=(0,a.useCallback)(r=>{e({message:r,type:"warning"})},[e]),o=(0,a.useCallback)(r=>{e({message:r,type:"info"})},[e]),d=(0,a.useCallback)((e,r,t)=>{e?l(r):c(t)},[l,c]),h=(0,a.useCallback)((t,a,s=5e3)=>{e({message:a,type:t}),setTimeout(()=>{let e=i.C.getState().notifications.at(-1);e&&e.message===a&&r(e.id)},s)},[e,r]),u=(0,a.useCallback)((r="Loading...")=>{e({message:r,type:"info"});let t=i.C.getState().notifications;return t.at(-1)?.id},[e]),m=(0,a.useCallback)((e,t,a)=>{r(e),t?l(a):c(a)},[r,l,c]);return{clearAllNotifications:t,removeNotification:r,showApiResult:d,showError:c,showInfo:o,showLoading:u,showSuccess:l,showTemporary:h,showWarning:n,unreadCount:s,updateLoadingNotification:m}},l=()=>{let{clearAllNotifications:e,removeNotification:r,showError:t,showInfo:l,showSuccess:c,showWarning:n,unreadCount:o}=s(),d=(0,a.useCallback)((e,r)=>{(0,i.C.getState().addNotification)({...r&&{actionUrl:r},category:"delegation",message:e,type:"delegation-update"})},[]),h=(0,a.useCallback)((e,r)=>{(0,i.C.getState().addNotification)({...r&&{actionUrl:r},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),u=(0,a.useCallback)((e,r)=>{(0,i.C.getState().addNotification)({...r&&{actionUrl:r},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:r,showDelegationUpdate:d,showEmployeeUpdate:(0,a.useCallback)((e,r)=>{(0,i.C.getState().addNotification)({...r&&{actionUrl:r},category:"employee",message:e,type:"employee-update"})},[]),showError:t,showInfo:l,showSuccess:c,showTaskAssigned:u,showVehicleMaintenance:h,showWarning:n,unreadCount:o}}},90357:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(82614).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},95668:(e,r,t)=>{t.d(r,{A:()=>d});var a=t(60687),i=t(14975),s=t(77368),l=t(43210),c=t(91821),n=t(29523);class o extends l.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,r){this.setState({errorInfo:r}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",r.componentStack),this.props.onError&&this.props.onError(e,r)}render(){let{description:e="An unexpected error occurred.",resetLabel:r="Try Again",title:t="Something went wrong"}=this.props;return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsxs)(c.Fc,{className:"my-4",variant:"destructive",children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),(0,a.jsx)(c.XL,{className:"text-lg font-semibold",children:t}),(0,a.jsxs)(c.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:this.state.error?.message||e}),!1,(0,a.jsxs)(n.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,a.jsx)(s.A,{className:"mr-2 size-4"}),r]})]})]}):this.props.children}}let d=o},95688:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(82614).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])}};