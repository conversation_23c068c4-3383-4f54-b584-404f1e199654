(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4032],{12626:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(95155),a=r(28328),c=r(35695),i=r(30836),l=r(80937);function n(){let e=(0,c.useRouter)(),s=(0,l.Y1)(),r=async r=>{try{await s.mutateAsync(r),e.push("/vehicles")}catch(e){console.error("Error adding vehicle:",e)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"mb-6 flex items-center space-x-2",children:[(0,t.jsx)(a.A,{className:"size-8 text-primary"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Add New Vehicle"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Enter the details of your new vehicle."})]})]}),(0,t.jsx)(i.x,{onSubmit:r})]})}},82111:(e,s,r)=>{Promise.resolve().then(r.bind(r,12626))}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,5669,4629,4036,4767,8950,8122,8441,1684,7358],()=>s(82111)),_N_E=e.O()}]);