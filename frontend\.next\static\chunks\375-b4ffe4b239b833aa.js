"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[375],{5845:(e,t,n)=>{n.d(t,{i:()=>i});var r,o=n(12115),a=n(52712),u=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function i({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[a,i,c]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),a=o.useRef(n),i=o.useRef(t);return u(()=>{i.current=t},[t]),o.useEffect(()=>{a.current!==n&&(i.current?.(n),a.current=n)},[n,a]),[n,r,i]}({defaultProp:t,onChange:n}),l=void 0!==e,s=l?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[s,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&c.current?.(n)}else i(t)},[l,e,i,c])]}Symbol("RADIX:SYNC_STATE")},19178:(e,t,n)=>{n.d(t,{lg:()=>g,qW:()=>f,bL:()=>p});var r,o=n(12115),a=n(85185),u=n(63655),i=n(6101),c=n(39033),l=n(95155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:p,onPointerDownOutside:g,onFocusOutside:E,onInteractOutside:y,onDismiss:b,...w}=e,C=o.useContext(d),[S,L]=o.useState(null),R=null!=(f=null==S?void 0:S.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,T]=o.useState({}),k=(0,i.s)(t,e=>L(e)),N=Array.from(C.layers),[A]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),M=N.indexOf(A),P=S?N.indexOf(S):-1,x=C.layersWithOutsidePointerEventsDisabled.size>0,D=P>=M,W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,c.c)(e),a=o.useRef(!1),u=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=t,n.addEventListener("click",u.current,{once:!0})):t()}else n.removeEventListener("click",u.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",u.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));D&&!n&&(null==g||g(e),null==y||y(e),e.defaultPrevented||null==b||b())},R),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,c.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==E||E(e),null==y||y(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let n=(0,c.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===C.layers.size-1&&(null==p||p(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),o.useEffect(()=>{if(S)return v&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(S)),C.layers.add(S),m(),()=>{v&&1===C.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[S,R,v,C]),o.useEffect(()=>()=>{S&&(C.layers.delete(S),C.layersWithOutsidePointerEventsDisabled.delete(S),m())},[S,C]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(u.sG.div,{...w,ref:k,style:{pointerEvents:x?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,W.onPointerDownCapture)})});f.displayName="DismissableLayer";var v=o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,i.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(u.sG.div,{...e,ref:a})});function m(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,u.hO)(a,i):a.dispatchEvent(i)}v.displayName="DismissableLayerBranch";var p=f,g=v},25519:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(12115),o=n(6101),a=n(63655),u=n(39033),i=n(95155),c="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:p,onUnmountAutoFocus:g,...E}=e,[y,b]=r.useState(null),w=(0,u.c)(p),C=(0,u.c)(g),S=r.useRef(null),L=(0,o.s)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!y)return;let t=e.target;y.contains(t)?S.current=t:m(S.current,{select:!0})},t=function(e){if(R.paused||!y)return;let t=e.relatedTarget;null!==t&&(y.contains(t)||m(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(y)});return y&&n.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,y,R.paused]),r.useEffect(()=>{if(y){h.add(R);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(c,s);y.addEventListener(c,w),y.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(y))}return()=>{y.removeEventListener(c,w),setTimeout(()=>{let t=new CustomEvent(l,s);y.addEventListener(l,C),y.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),y.removeEventListener(l,C),h.remove(R)},0)}}},[y,w,C,R]);let T=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[v(t,e),v(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(a,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,i.jsx)(a.sG.div,{tabIndex:-1,...E,ref:L,onKeyDown:T})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=p(e,t)).unshift(t)},remove(t){var n;null==(n=(e=p(e,t))[0])||n.resume()}}}();function p(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},31114:(e,t,n)=>{n.d(t,{A:()=>q});var r,o,a=n(39249),u=n(12115),i="right-scroll-bar-position",c="width-before-scroll-bar";function l(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?u.useLayoutEffect:u.useEffect,d=new WeakMap;function f(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o,u=(t=null,void 0===n&&(n=f),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},u=function(){return Promise.resolve().then(a)};u(),r={push:function(e){t.push(e),u()},filter:function(e){return t=t.filter(e),r}}}});return u.options=(0,a.Cl)({async:!0,ssr:!1},e),u}(),m=function(){},h=u.forwardRef(function(e,t){var n,r,o,i,c=u.useRef(null),f=u.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=f[0],p=f[1],g=e.forwardProps,E=e.children,y=e.className,b=e.removeScrollBar,w=e.enabled,C=e.shards,S=e.sideCar,L=e.noRelative,R=e.noIsolation,T=e.inert,k=e.allowPinchZoom,N=e.as,A=e.gapMode,M=(0,a.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[c,t],r=function(e){return n.forEach(function(t){return l(t,e)})},(o=(0,u.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,s(function(){var e=d.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||l(e,null)}),r.forEach(function(e){t.has(e)||l(e,o)})}d.set(i,n)},[n]),i),x=(0,a.Cl)((0,a.Cl)({},M),h);return u.createElement(u.Fragment,null,w&&u.createElement(S,{sideCar:v,removeScrollBar:b,shards:C,noRelative:L,noIsolation:R,inert:T,setCallbacks:p,allowPinchZoom:!!k,lockRef:c,gapMode:A}),g?u.cloneElement(u.Children.only(E),(0,a.Cl)((0,a.Cl)({},x),{ref:P})):u.createElement(void 0===N?"div":N,(0,a.Cl)({},x,{className:y,ref:P}),E))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:c,zeroRight:i};var p=function(e){var t=e.sideCar,n=(0,a.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return u.createElement(r,(0,a.Cl)({},n))};p.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,u;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),u=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(u)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=g();return function(t,n){u.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},w=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[w(n),w(r),w(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},L=y(),R="data-scroll-locked",T=function(e,t,n,r){var o=e.left,a=e.top,u=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},N=function(){u.useEffect(function(){return document.body.setAttribute(R,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},A=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var a=u.useMemo(function(){return S(o)},[o]);return u.createElement(L,{styles:T(a,!t,o,n?"":"!important")})},M=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return M=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){M=!1}var x=!!M&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},W=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,n,r,o){var a,u=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=u*r,c=n.target,l=t.contains(c),s=!1,d=i>0,f=0,v=0;do{if(!c)break;var m=F(e,c),h=m[0],p=m[1]-m[2]-u*h;(h||p)&&O(e,c)&&(f+=p,v+=h);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&i>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-i>v)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},K=function(e){return e&&"current"in e?e.current:e},X=0,j=[];let Y=(r=function(e){var t=u.useRef([]),n=u.useRef([0,0]),r=u.useRef(),o=u.useState(X++)[0],i=u.useState(y)[0],c=u.useRef(e);u.useEffect(function(){c.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,a.fX)([e.lockRef.current],(e.shards||[]).map(K),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=u.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,a=B(e),u=n.current,i="deltaX"in e?e.deltaX:u[0]-a[0],l="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,d=Math.abs(i)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=W(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=W(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(i||l)&&(r.current=o),!o)return!0;var v=r.current||o;return I(v,t,e,"h"===v?i:l,!0)},[]),s=u.useCallback(function(e){if(j.length&&j[j.length-1]===i){var n="deltaY"in e?_(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(K).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=u.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=u.useCallback(function(e){n.current=B(e),r.current=void 0},[]),v=u.useCallback(function(t){d(t.type,_(t),t.target,l(t,e.lockRef.current))},[]),m=u.useCallback(function(t){d(t.type,B(t),t.target,l(t,e.lockRef.current))},[]);u.useEffect(function(){return j.push(i),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:m}),document.addEventListener("wheel",s,x),document.addEventListener("touchmove",s,x),document.addEventListener("touchstart",f,x),function(){j=j.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,x),document.removeEventListener("touchmove",s,x),document.removeEventListener("touchstart",f,x)}},[]);var h=e.removeScrollBar,p=e.inert;return u.createElement(u.Fragment,null,p?u.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?u.createElement(A,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),p);var z=u.forwardRef(function(e,t){return u.createElement(h,(0,a.Cl)({},e,{ref:t,sideCar:Y}))});z.classNames=h.classNames;let q=z},34378:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(12115),o=n(47650),a=n(63655),u=n(52712),i=n(95155),c=r.forwardRef((e,t)=>{var n,c;let{container:l,...s}=e,[d,f]=r.useState(!1);(0,u.N)(()=>f(!0),[]);let v=l||d&&(null==(c=globalThis)||null==(n=c.document)?void 0:n.body);return v?o.createPortal((0,i.jsx)(a.sG.div,{...s,ref:t}),v):null});c.displayName="Portal"},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,u={},i=0,c=function(e){return e&&(e.host||c(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});u[n]||(u[n]=new WeakMap);var s=u[n],d=[],f=new Set,v=new Set(l),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};l.forEach(m);var h=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),u=null!==t&&"false"!==t,i=(o.get(e)||0)+1,c=(s.get(e)||0)+1;o.set(e,i),s.set(e,c),d.push(e),1===i&&u&&a.set(e,!0),1===c&&e.setAttribute(n,"true"),u||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),i++,function(){d.forEach(function(e){var t=o.get(e)-1,u=s.get(e)-1;o.set(e,t),s.set(e,u),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),u||e.removeAttribute(n)}),--i||(o=new WeakMap,o=new WeakMap,a=new WeakMap,u={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),l(o,a,n,"aria-hidden")):function(){return null}}},39033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(12115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},61285:(e,t,n)=>{n.d(t,{B:()=>c});var r,o=n(12115),a=n(52712),u=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function c(e){let[t,n]=o.useState(u());return(0,a.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},92293:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(12115),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:u()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:u()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function u(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}}}]);