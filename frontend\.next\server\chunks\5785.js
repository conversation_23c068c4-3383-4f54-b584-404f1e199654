"use strict";exports.id=5785,exports.ids=[5785],exports.modules={1814:(e,t,r)=>{r.d(t,{O2:()=>s,Q:()=>l,gT:()=>n});var i=r(9275),a=r(74880);let s=i.k5(["Active","On_Leave","Terminated","Inactive"]),l=i.k5(["driver","mechanic","administrator","office_staff","manager","service_advisor","technician","other"]),n=i.Ik({availability:a.X.optional().nullable(),contactEmail:i.Yj().email("Invalid email address").nullable().optional(),contactInfo:i.Yj().min(1,"Contact Info is required"),contactMobile:i.Yj().nullable().optional(),contactPhone:i.Yj().nullable().optional(),currentLocation:i.Yj().nullable().optional(),department:i.Yj().nullable().optional(),employeeId:i.Yj().min(1,"Employee ID (unique business ID) is required"),fullName:i.Yj().nullable().optional(),generalAssignments:i.YO(i.Yj()),hireDate:i.Yj().nullable().optional().refine(e=>null===e||""===e||null==e||""===e||!isNaN(Date.parse(e)),{message:"Invalid hire date"}),name:i.Yj().min(1,"Name is required"),notes:i.Yj().nullable().optional(),position:i.Yj().nullable().optional(),profileImageUrl:i.Yj().optional().nullable().refine(e=>{if(!e||""===e.trim())return!0;try{return new URL(e),!0}catch{return!1}},{message:"Invalid URL for profile image"}),role:l,shiftSchedule:i.Yj().nullable().optional(),skills:i.YO(i.Yj()),status:s,workingHours:i.Yj().nullable().optional()})},15079:(e,t,r)=>{r.d(t,{bq:()=>p,eb:()=>g,gC:()=>f,l6:()=>c,yv:()=>u});var i=r(60687),a=r(22670),s=r(61662),l=r(89743),n=r(58450),o=r(43210),d=r(22482);let c=a.bL;a.YJ;let u=a.WT,p=o.forwardRef(({children:e,className:t,...r},l)=>(0,i.jsxs)(a.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:l,...r,children:[e,(0,i.jsx)(a.In,{asChild:!0,children:(0,i.jsx)(s.A,{className:"size-4 opacity-50"})})]}));p.displayName=a.l9.displayName;let m=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(a.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,i.jsx)(l.A,{className:"size-4"})}));m.displayName=a.PP.displayName;let h=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(a.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,i.jsx)(s.A,{className:"size-4"})}));h.displayName=a.wn.displayName;let f=o.forwardRef(({children:e,className:t,position:r="popper",...s},l)=>(0,i.jsx)(a.ZL,{children:(0,i.jsxs)(a.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,ref:l,...s,children:[(0,i.jsx)(m,{}),(0,i.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,i.jsx)(h,{})]})}));f.displayName=a.UC.displayName,o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(a.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:r,...t})).displayName=a.JU.displayName;let g=o.memo(o.forwardRef(({children:e,className:t,...r},s)=>{let l=o.useCallback(e=>{"function"==typeof s?s(e):s&&(s.current=e)},[s]);return(0,i.jsxs)(a.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:l,...r,children:[(0,i.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,i.jsx)(a.VF,{children:(0,i.jsx)(n.A,{className:"size-4"})})}),(0,i.jsx)(a.p4,{children:e})]})}));g.displayName=a.q7.displayName,o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(a.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),ref:r,...t})).displayName=a.wv.displayName},15795:(e,t,r)=>{function i(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function a(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let t=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(t.toLowerCase())||t.includes("_")){let e=t.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return t}if(e.role){let t=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${t} (Role)`}return"Unknown Employee"}function s(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function l(e){return e.replaceAll("_"," ")}r.d(t,{DV:()=>a,fZ:()=>i,s:()=>s,vq:()=>l})},48041:(e,t,r)=>{r.d(t,{z:()=>a});var i=r(60687);function a({children:e,description:t,icon:r,title:a}){return(0,i.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,i.jsx)(r,{className:"size-8 text-primary"}),(0,i.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:a})]}),t&&(0,i.jsx)("p",{className:"mt-1 text-muted-foreground",children:t})]}),e&&(0,i.jsx)("div",{className:"flex items-center gap-2",children:e})]})}r(43210)},49278:(e,t,r)=>{r.d(t,{G7:()=>p,Gb:()=>o,JP:()=>d,Ok:()=>c,Qu:()=>u,iw:()=>n,oz:()=>h,z0:()=>m});var i=r(3389);class a{show(e){return(0,i.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class s extends a{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class l extends a{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new s(e)}function o(e,t){return new s({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let d=new a,c=new s({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new s({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),p=new s({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),m=new s({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new l},74880:(e,t,r)=>{r.d(t,{X:()=>i});let i=r(9275).k5(["On_Shift","Off_Shift","On_Break","Busy"])}};