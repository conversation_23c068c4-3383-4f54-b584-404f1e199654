/**
 * @file Recipient Query Hooks
 * @description React Query hooks for recipient management operations
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { UseQueryOptions } from '@tanstack/react-query';

import { useCrudQuery } from '@/hooks/api/useSmartQuery';
import { useNotifications } from '@/hooks/ui/useNotifications';
import { recipientApiService } from '@/lib/api/services/factory';
import type {
  CreateRecipientData,
  Recipient,
  UpdateRecipientData,
} from '@/lib/types/domain';
import type { ApiError } from '@/lib/types/api';

// Query keys for recipients
export const recipientQueryKeys = {
  all: ['recipients'],
  lists: () => [...recipientQueryKeys.all, 'list'],
  list: (filters: Record<string, unknown>) => [
    ...recipientQueryKeys.lists(),
    { filters },
  ],
  details: () => [...recipientQueryKeys.all, 'detail'],
  detail: (id: string) => [...recipientQueryKeys.details(), id],
  search: (searchTerm: string) => [
    ...recipientQueryKeys.all,
    'search',
    searchTerm,
  ],
  withGiftCounts: () => [...recipientQueryKeys.all, 'with-gift-counts'],
};

/**
 * Hook to fetch all recipients with optional filtering
 */
export const useRecipients = (
  filters?: Record<string, unknown>,
  options?: Omit<UseQueryOptions<Recipient[], ApiError>, 'queryKey' | 'queryFn'>
) => {
  return useCrudQuery<Recipient[], ApiError>(
    recipientQueryKeys.list(filters || {}),
    () =>
      recipientApiService.getAll(filters).then((result: any) => result.data),
    'recipient',
    {
      staleTime: 10 * 60 * 1000, // 10 minutes (recipients change less frequently)
      ...options,
    }
  );
};

/**
 * Hook to fetch a single recipient by ID
 */
export const useRecipient = (
  id: string | null,
  options?: Omit<
    UseQueryOptions<Recipient, ApiError>,
    'queryKey' | 'queryFn' | 'enabled'
  > & { enabled?: boolean }
) => {
  return useCrudQuery<Recipient, ApiError>(
    recipientQueryKeys.detail(id!),
    () => recipientApiService.getById(id!),
    'recipient',
    {
      enabled: !!id && (options?.enabled ?? true),
      staleTime: 10 * 60 * 1000,
      ...options,
    }
  );
};

/**
 * Hook to search recipients by name
 */
export const useSearchRecipients = (
  searchTerm: string | null,
  options?: Omit<
    UseQueryOptions<Recipient[], ApiError>,
    'queryKey' | 'queryFn' | 'enabled'
  >
) => {
  return useQuery({
    queryKey: recipientQueryKeys.search(searchTerm!),
    queryFn: () => recipientApiService.searchByName(searchTerm!),
    enabled: !!searchTerm && searchTerm.length >= 2, // Only search with 2+ characters
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook to fetch recipients with gift counts
 */
export const useRecipientsWithGiftCounts = (
  options?: Omit<UseQueryOptions<Recipient[], ApiError>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: recipientQueryKeys.withGiftCounts(),
    queryFn: () => recipientApiService.getWithGiftCounts(),
    staleTime: 10 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook to create a new recipient
 */
export const useCreateRecipient = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation({
    mutationFn: (data: CreateRecipientData) => recipientApiService.create(data),
    onSuccess: newRecipient => {
      // Invalidate and refetch recipient queries
      queryClient.invalidateQueries({ queryKey: recipientQueryKeys.all });

      showSuccess('Recipient created successfully');
    },
    onError: (error: ApiError) => {
      showError(`Failed to create recipient: ${error.message}`);
    },
  });
};

/**
 * Hook to update an existing recipient
 */
export const useUpdateRecipient = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateRecipientData }) =>
      recipientApiService.update(id, data),
    onSuccess: updatedRecipient => {
      // Update the specific recipient in cache
      queryClient.setQueryData(
        recipientQueryKeys.detail((updatedRecipient as any).id),
        updatedRecipient
      );

      // Invalidate list queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: recipientQueryKeys.lists() });

      // Also invalidate gift queries that might reference this recipient
      queryClient.invalidateQueries({
        queryKey: ['gifts', 'recipient', (updatedRecipient as any).id],
      });

      showSuccess('Recipient updated successfully');
    },
    onError: (error: ApiError) => {
      showError(`Failed to update recipient: ${error.message}`);
    },
  });
};

/**
 * Hook to delete a recipient
 */
export const useDeleteRecipient = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation({
    mutationFn: (id: string) => recipientApiService.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove the recipient from cache
      queryClient.removeQueries({
        queryKey: recipientQueryKeys.detail(deletedId),
      });

      // Invalidate list queries
      queryClient.invalidateQueries({ queryKey: recipientQueryKeys.lists() });

      // Invalidate gift queries that might reference this recipient
      queryClient.invalidateQueries({
        queryKey: ['gifts', 'recipient', deletedId],
      });

      showSuccess('Recipient deleted successfully');
    },
    onError: (error: ApiError) => {
      showError(`Failed to delete recipient: ${error.message}`);
    },
  });
};

/**
 * Hook to get recipient options for dropdowns/selects
 * Returns a simplified format suitable for form components
 */
export const useRecipientOptions = (
  options?: Omit<
    UseQueryOptions<Array<{ id: string; name: string }>, ApiError>,
    'queryKey' | 'queryFn'
  >
) => {
  return useQuery({
    queryKey: [...recipientQueryKeys.all, 'options'],
    queryFn: async () => {
      const result = await recipientApiService.getAll();
      return result.data.map(recipient => ({
        id: recipient.id,
        name: recipient.name,
      }));
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for options (very stable data)
    ...options,
  });
};
