"use strict";exports.id=9603,exports.ids=[9603],exports.modules={49603:(e,t,i)=>{i.d(t,{cl:()=>d,delegationApiService:()=>S,employeeApiService:()=>w,reliabilityApiService:()=>C,taskApiService:()=>m,vehicleApiService:()=>v});var r=i(79772),a=i(44194),s=i(10212),c=i(77312);let n={fromApi:e=>e,toApi:e=>e};class l extends c.v{constructor(e,t){super(e,{cacheDuration:6e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/reliability",this.transformer=n}async acknowledgeAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let r=await this.apiClient.post(`/alerts/${e}/acknowledge`,{acknowledgedBy:i,note:t});return this.cache.invalidatePattern(RegExp("^alerts:")),r})}async getActiveAlerts(){return this.executeWithInfrastructure("alerts:active",async()=>{try{let e=await this.apiClient.get("/alerts");return e?.alerts||[]}catch(e){return console.error("Failed to get active alerts:",e),[]}})}async getAlertHistory(e=1,t=50){return this.executeWithInfrastructure(`alerts:history:${e}:${t}`,async()=>{let i=new URLSearchParams({limit:t.toString(),page:e.toString()});return await this.apiClient.get(`/alerts/history?${i.toString()}`)})}async getAlertStatistics(){return this.executeWithInfrastructure("alerts:statistics",async()=>{try{return await this.apiClient.get("/alerts/statistics")}catch(e){return console.error("Failed to get alert statistics:",e),{acknowledged:0,active:0,averageResolutionTime:0,bySeverity:{critical:0,high:0,low:0,medium:0},recentTrends:{last7Days:0,last24Hours:0,last30Days:0},resolved:0,total:0}}})}async getCircuitBreakerHistory(e="24h",t){return this.executeWithInfrastructure(`circuit-breakers:history:${e}:${t||"all"}`,async()=>{let i=new URLSearchParams({timeframe:e});return t&&i.append("breakerName",t),await this.apiClient.get(`/monitoring/circuit-breakers/history?${i.toString()}`)})}async getCircuitBreakerStatus(){return this.executeWithInfrastructure("monitoring:circuit-breakers",async()=>{try{let e=await this.apiClient.get("/circuit-breakers"),t=e?.circuitBreakers||[];return{circuitBreakers:t||[],summary:{closed:t?.filter(e=>"CLOSED"===e.state).length||0,halfOpen:t?.filter(e=>"HALF_OPEN"===e.state).length||0,open:t?.filter(e=>"OPEN"===e.state).length||0,total:t?.length||0}}}catch(e){return console.error("Failed to get circuit breaker status:",e),{circuitBreakers:[],summary:{closed:0,halfOpen:0,open:0,total:0}}}})}async getCriticalAlertCount(){try{return(await this.getAlertStatistics()).bySeverity.critical}catch{return 0}}async getDeduplicationMetrics(){return this.executeWithInfrastructure("monitoring:deduplication",async()=>await this.apiClient.get("/monitoring/deduplication"))}async getDependencyHealth(){return this.executeWithInfrastructure("health:dependencies",async()=>await this.apiClient.get("/health/dependencies"))}async getDetailedHealth(){return this.executeWithInfrastructure("health:detailed",async()=>await this.apiClient.get("/health/detailed"))}async getHealthTrends(e="24h"){return this.executeWithInfrastructure(`health:trends:${e}`,async()=>await this.apiClient.get(`/health/trends?timeframe=${e}`))}async getHttpRequestMetrics(){return this.executeWithInfrastructure("http:metrics",async()=>await this.apiClient.get("/http-request-metrics"))}async getMetrics(){return this.executeWithInfrastructure("metrics:system",async()=>await this.apiClient.get("/metrics",{headers:{Accept:"application/json"}}))}async getReliabilityDashboardData(){let[e,t,i,r,a,s]=await Promise.all([this.getSystemHealth(),this.getDetailedHealth(),this.getCircuitBreakerStatus(),this.getMetrics(),this.getActiveAlerts(),this.getAlertStatistics()]);return{activeAlerts:a,alertStatistics:s,circuitBreakers:i,detailedHealth:t,metrics:r,systemHealth:e}}async getSystemHealth(){return this.executeWithInfrastructure("health:system",async()=>await this.apiClient.get("/health"))}async isSystemHealthy(){try{let e=await this.getSystemHealth();return"healthy"===e.status}catch{return!1}}async resolveAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let r=await this.apiClient.post(`/alerts/${e}/resolve`,{reason:t,resolvedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),r})}async testAlerts(){return this.executeWithInfrastructure(null,async()=>{let e=await this.apiClient.post("/alerts/test");return{message:e?.message||"Test alert triggered",success:e?.status==="success",testAlertId:e?.data?.id}})}}var h=i(76783),u=i(30930),o=i(23133),g=i(8342);function y(){let e=(0,g.Sk)();if(!e)return null;try{return e()}catch(e){return console.error("❌ Factory: Error getting auth token from secure provider:",e),null}}class p{constructor(e){this.apiClient=new r.O({...e,getAuthToken:y})}getApiClient(){return this.apiClient}getDelegationService(){return this.delegationService||(this.delegationService=new a.y(this.apiClient)),this.delegationService}getEmployeeService(){return this.employeeService||(this.employeeService=new s.Q(this.apiClient)),this.employeeService}getReliabilityService(){return this.reliabilityService||(this.reliabilityService=new l(this.apiClient)),this.reliabilityService}getTaskService(){return this.taskService||(this.taskService=new h.D(this.apiClient)),this.taskService}getVehicleService(){return this.vehicleService||(this.vehicleService=new u.C(this.apiClient)),this.vehicleService}}let d=new p({baseURL:(0,o.Qq)().apiBaseUrl,headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4}),v=d.getVehicleService(),S=d.getDelegationService(),m=d.getTaskService(),w=d.getEmployeeService(),C=d.getReliabilityService()}};