'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Save } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import type { CreateGiftData, Gift, UpdateGiftData } from '@/lib/types/domain';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Gift form validation schema
const giftFormSchema = z.object({
  dateSent: z.string().min(1, 'Date sent is required'),
  itemDescription: z
    .string()
    .min(1, 'Item description is required')
    .max(500, 'Item description must be less than 500 characters'),
  notes: z
    .string()
    .max(1000, 'Notes must be less than 1000 characters')
    .default(''),
  occasion: z
    .string()
    .max(100, 'Occasion must be less than 100 characters')
    .default(''),
  recipientId: z.string().min(1, 'Recipient is required'),
  senderName: z
    .string()
    .min(1, 'Sender name is required')
    .max(255, 'Sender name must be less than 255 characters'),
});

type GiftFormData = z.infer<typeof giftFormSchema>;

interface GiftFormProps {
  initialData?: Partial<Gift>;
  isEditing?: boolean;
  isLoading?: boolean;
  onSubmit: (data: CreateGiftData | UpdateGiftData) => Promise<void>;
  recipients?: { id: string; name: string }[];
}

// Common occasions for quick selection
const commonOccasions = [
  'Birthday',
  'Anniversary',
  'Christmas',
  'Wedding',
  'Graduation',
  'Baby Shower',
  'Retirement',
  'Promotion',
  'Thank You',
  'Get Well Soon',
  'Sympathy',
  'Housewarming',
  "Valentine's Day",
  "Mother's Day",
  "Father's Day",
];

export const GiftForm: React.FC<GiftFormProps> = ({
  initialData,
  isEditing = false,
  isLoading = false,
  onSubmit,
  recipients = [],
}) => {
  const router = useRouter();
  const tLabels = useTranslations('forms.labels');
  const tButtons = useTranslations('forms.buttons');
  const tPlaceholders = useTranslations('forms.placeholders');
  const tTitles = useTranslations('forms.titles');

  const form = useForm({
    defaultValues: {
      dateSent: (initialData?.dateSent
        ? initialData.dateSent.split('T')[0]
        : new Date().toISOString().split('T')[0]) as string,
      itemDescription: initialData?.itemDescription ?? '',
      notes: initialData?.notes ?? '',
      occasion: initialData?.occasion ?? '',
      recipientId: initialData?.recipientId ?? '',
      senderName: initialData?.senderName ?? '',
    },
    resolver: zodResolver(giftFormSchema),
  });

  const handleSubmit = async (data: GiftFormData) => {
    try {
      // Convert date to ISO string
      const formattedData = {
        ...data,
        dateSent: new Date(data.dateSent).toISOString(),
        notes: data.notes || null,
        occasion: data.occasion || null,
      };

      await onSubmit(formattedData);
    } catch (error) {
      console.error('Error submitting gift form:', error);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <Card className="mx-auto max-w-2xl">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Button
            className="p-2"
            onClick={handleCancel}
            size="sm"
            variant="ghost"
          >
            <ArrowLeft className="size-4" />
          </Button>
          <CardTitle>
            {isEditing ? tTitles('editGift') : tTitles('addGift')}
          </CardTitle>
        </div>
      </CardHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-6">
            {/* Item Description */}
            <FormField
              control={form.control}
              name="itemDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('itemDescription')} *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={tPlaceholders('enterDescription')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Recipient */}
            <FormField
              control={form.control}
              name="recipientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Recipient *</FormLabel>
                  <Select
                    defaultValue={field.value}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a recipient" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {recipients.map(recipient => (
                        <SelectItem key={recipient.id} value={recipient.id}>
                          {recipient.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose who received this gift
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Sent */}
            <FormField
              control={form.control}
              name="dateSent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date Sent *</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>When was this gift sent?</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Sender Name */}
            <FormField
              control={form.control}
              name="senderName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sender Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Who sent this gift?" {...field} />
                  </FormControl>
                  <FormDescription>
                    Name of the person who sent the gift
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Occasion */}
            <FormField
              control={form.control}
              name="occasion"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Occasion</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an occasion (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {commonOccasions.map(occasion => (
                        <SelectItem key={occasion} value={occasion}>
                          {occasion}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    What was the occasion for this gift?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      className="min-h-[100px]"
                      placeholder="Any additional notes about this gift..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional notes or additional details
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="flex justify-end gap-4">
            <Button
              disabled={isLoading}
              onClick={handleCancel}
              type="button"
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              className="min-w-[100px]"
              disabled={isLoading}
              type="submit"
            >
              {isLoading ? (
                'Saving...'
              ) : (
                <>
                  <Save className="mr-2 size-4" />
                  {isEditing ? 'Update Gift' : 'Save Gift'}
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
};
