{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/layout.tsx"], "sourcesContent": ["/**\r\n * @file Root Layout - i18n Routing Entry Point\r\n * @module app/layout\r\n *\r\n * Root layout that handles locale routing and redirects.\r\n * This layout only handles the root level routing - actual content\r\n * is handled by the locale-specific layouts.\r\n */\r\n\r\nimport type { ReactNode } from 'react';\r\n\r\nimport './globals.css';\r\n\r\n// This layout is required for the app directory structure\r\n// but the actual content is handled by [locale]/layout.tsx\r\ninterface RootLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\n/**\r\n * Root Layout Component\r\n *\r\n * Minimal root layout for i18n routing.\r\n * The actual layout logic is in [locale]/layout.tsx\r\n */\r\nexport default function RootLayout({ children }: RootLayoutProps) {\r\n  return children;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AAkBc,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,OAAO;AACT", "debugId": null}}]}