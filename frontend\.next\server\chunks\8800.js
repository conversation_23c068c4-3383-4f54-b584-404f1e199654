"use strict";exports.id=8800,exports.ids=[8800],exports.modules={8800:e=>{e.exports=JSON.parse('{"metadata":{"title":"WorkHub - Comprehensive Management System","description":"Streamline your operations with our comprehensive management platform","keywords":"workforce management, employee tracking, performance monitoring, productivity optimization, HR management, business operations, team management, organizational efficiency"},"common":{"loading":"Loading...","error":"An error occurred","retry":"Retry","cancel":"Cancel","save":"Save","edit":"Edit","delete":"Delete","add":"Add","search":"Search","filter":"Filter","clear":"Clear","submit":"Submit","back":"Back","next":"Next","previous":"Previous","close":"Close","confirm":"Confirm","yes":"Yes","no":"No"},"navigation":{"dashboard":"Dashboard","delegations":"Delegations","employees":"Employees","reports":"Reports","settings":"Settings","profile":"Profile","logout":"Logout","assets":"Assets","fleet":"Fleet","maintenance":"Maintenance","serviceRecords":"Service Records","projects":"Projects","tasks":"Tasks","team":"Team","teamMembers":"Team Members","analytics":"Analytics","reliability":"Reliability","monitoring":"Monitoring","admin":"Admin","administration":"Administration","allSettings":"All Settings","sections":{"overview":"Overview","operations":"Operations","workforce":"Workforce","system":"System"},"badges":{"new":"New"},"fontSize":{"small":"Small","medium":"Medium","large":"Large"},"search":{"placeholder":"Search navigation..."}},"auth":{"login":"Login","logout":"Logout","email":"Email","password":"Password","forgotPassword":"Forgot Password?","rememberMe":"Remember Me","loginError":"Invalid email or password","loginSuccess":"Login successful","welcomeBack":"Welcome back","signInToAccount":"Sign in to your WorkHub account","emailAddress":"Email address","signIn":"Sign in","signingIn":"Signing in...","noInternetConnection":"No internet connection","offlineWarning":"You\'re currently offline. Please check your internet connection to sign in.","showPassword":"Show password","hidePassword":"Hide password","validation":{"emailRequired":"Email is required","emailInvalid":"Please enter a valid email address","passwordRequired":"Password is required","passwordTooShort":"Password must be at least 6 characters"},"errors":{"authenticationFailed":"Failed to initialize authentication","invalidCredentials":"Invalid email or password","networkError":"Network error. Please try again.","unknownError":"An unexpected error occurred"}},"dashboard":{"welcomeBack":"Welcome back","welcomeToWorkHub":"Welcome to WorkHub","signInToAccess":"Please sign in to access your dashboard","signIn":"Sign In","operationsToday":"Here\'s what\'s happening with your operations today.","keyMetrics":"Key Metrics","stats":{"totalVehicles":"Total Vehicles","activeFleetAssets":"Active fleet assets","activeProjects":"Active Projects","inProgressDelegations":"In progress delegations","pendingTasks":"Pending Tasks","awaitingCompletion":"Awaiting completion","maintenanceDue":"Maintenance Due","requiresAttention":"Requires attention"},"recentActivity":"Recent Activity","viewAll":"View All","quickActions":"Quick Actions","addVehicle":"Add Vehicle","createDelegation":"Create Delegation","assignTask":"Assign Task","scheduleService":"Schedule Service","fleetPerformance":"Fleet Performance","fleetEfficiencyOverview":"Overview of fleet efficiency and maintenance status","fleetUtilization":"Fleet Utilization","maintenanceUpToDate":"Maintenance Up-to-date","fuelEfficiency":"Fuel Efficiency","taskCompletionRate":"Task Completion Rate","latestUpdates":"Latest updates from your operations","addNewVehicle":"Add New Vehicle","registerNewAsset":"Register a new asset to your fleet","scheduleMaintenance":"Schedule Maintenance","planUpcomingService":"Plan upcoming service appointments","viewAnalytics":"View Analytics","seeDetailedReports":"See detailed reports and insights","createAndDelegate":"Create and delegate new tasks","activity":{"maintenanceCompleted":"maintenance completed","newTaskAssigned":"New task assigned","milestoneCompleted":"milestone completed","oilChangeFinished":"Oil change and tire rotation finished","safetyInspectionDue":"Quarterly safety inspection due next week","deliverablesSubmitted":"Phase 2 deliverables submitted"},"timeAgo":{"hoursAgo":"{hours} hours ago","dayAgo":"{days} day ago","daysAgo":"{days} days ago"}},"reliability":{"dashboard":"Reliability Dashboard","systemMonitoring":"System monitoring","connectionStatus":{"connected":"Connected","connecting":"Connecting","reconnecting":"Reconnecting","disconnected":"Disconnected","error":"Error","polling":"Polling","realTimeActive":"Real-time connection active","establishingConnection":"Establishing connection...","attemptingReconnect":"Attempting to reconnect...","connectionError":"Connection error occurred","pollingFallback":"Using polling fallback (WebSocket requires authentication)","connectionLost":"Real-time connection lost"},"widgets":{"systemHealth":"System Health","systemHealthDesc":"Overall system status and health indicators","healthStatusIndicators":"Health Status Indicators","healthStatusDesc":"Real-time component health status indicators","dependencyStatus":"Dependency Status","dependencyStatusDesc":"External dependency health monitoring","healthTrends":"Health Trends","healthTrendsDesc":"Historical health trend visualization","systemResources":"System Resources","systemResourcesDesc":"CPU, memory, and connection monitoring","performanceOverview":"Performance Overview","performanceOverviewDesc":"Comprehensive performance metrics and scoring","systemMetrics":"System Performance","systemMetricsDesc":"Performance-focused system metrics monitoring","httpMetrics":"HTTP Request Metrics","httpMetricsDesc":"Request performance and throughput analysis","deduplicationMetrics":"Deduplication Metrics","deduplicationMetricsDesc":"Cache efficiency and request deduplication analysis"}},"forms":{"validation":{"required":"This field is required","invalidEmail":"Please enter a valid email address","invalidPhone":"Please enter a valid phone number","invalidDate":"Please enter a valid date","minLength":"Must be at least {min} characters","maxLength":"Must be no more than {max} characters","invalidFormat":"Invalid format"},"placeholders":{"enterEmail":"Enter your email address","enterPassword":"Enter your password","enterName":"Enter name","enterDescription":"Enter description","enterPhone":"Enter phone number","enterAddress":"Enter address","enterNotes":"Enter notes","selectOption":"Select an option","selectRecipient":"Select a recipient","searchPlaceholder":"Search..."},"labels":{"name":"Name","email":"Email","phone":"Phone","address":"Address","notes":"Notes","recipient":"Recipient","dateSent":"Date Sent","itemDescription":"Item Description","occasion":"Occasion","senderName":"Sender Name"},"buttons":{"submit":"Submit","save":"Save","cancel":"Cancel","edit":"Edit","delete":"Delete","add":"Add","update":"Update","create":"Create","goBack":"Go Back"},"titles":{"addRecipient":"Add New Recipient","editRecipient":"Edit Recipient","addGift":"Add New Gift","editGift":"Edit Gift"}}}')}};