"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2999],{85057:(e,s,r)=>{r.d(s,{J:()=>c});var t=r(95155),a=r(12115),i=r(40968),n=r(74466),o=r(54036);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(i.b,{ref:s,className:(0,o.cn)(l(),r),...a})});c.displayName=i.b.displayName},86719:(e,s,r)=>{r.d(s,{M:()=>i});var t=r(12115),a=r(17652);function i(){let[e,s]=(0,t.useState)({}),[r,i]=(0,t.useState)(!1),n=(0,a.c3)("auth.validation"),o=(0,t.useCallback)(e=>e?/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)?void 0:n("emailInvalid"):n("emailRequired"),[n]),l=(0,t.useCallback)(e=>e?e.length<6?n("passwordTooShort"):void 0:n("passwordRequired"),[n]),c=(0,t.useCallback)(e=>{let r={},t=o(e.email),a=l(e.password);return t&&(r.email=t),a&&(r.password=a),s(r),{isValid:0===Object.keys(r).length,errors:r}},[o,l]),d=(0,t.useCallback)((e,r)=>{let t;switch(e){case"email":t=o(r);break;case"password":t=l(r);break;default:return}s(s=>({...s,[e]:t}))},[o,l]),m=(0,t.useCallback)(e=>{s(s=>{let r={...s};return delete r[e],r})},[]),u=(0,t.useCallback)(()=>{s({})},[]),x=(0,t.useCallback)(()=>{i(!0)},[]),p=(0,t.useCallback)(()=>{s({}),i(!1)},[]),h=(0,t.useCallback)((s,t)=>r&&t&&!e[s],[e,r]);return{errors:e,isFormTouched:r,validateForm:c,validateField:d,clearFieldError:m,clearAllErrors:u,markFormTouched:x,resetValidation:p,isFieldValid:h}}},92999:(e,s,r)=>{r.d(s,{LoginForm:()=>z});var t=r(95155),a=r(32087),i=r(31573),n=r(48639),o=r(11133),l=r(19637),c=r(10518),d=r(4607),m=r(17607),u=r(50172),x=r(19968),p=r(45731),h=r(17652),g=r(12115),b=r(40283),f=r(86719),v=r(55365),j=r(30285),N=r(47262),y=r(62523),w=r(85057),k=r(54036);function A(e){let{className:s,stage:r="authenticating",message:a}=e,i=(()=>{switch(r){case"authenticating":return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Authenticating credentials...",description:"Verifying your identity securely"};case"verifying":return{icon:(0,t.jsx)(p.A,{className:"size-6 text-accent animate-pulse"}),text:a||"Verifying security...",description:"Checking account permissions"};case"redirecting":return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Preparing your dashboard...",description:"Setting up your workspace"};case"success":return{icon:(0,t.jsx)(c.A,{className:"size-6 text-green-600"}),text:a||"Welcome back!",description:"Login successful"};default:return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Loading...",description:"Please wait"}}})();return(0,t.jsxs)("div",{className:(0,k.cn)("flex flex-col items-center justify-center p-8 text-center space-y-4",s),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 rounded-full bg-primary/10 animate-ping"}),(0,t.jsx)("div",{className:"relative flex items-center justify-center w-12 h-12 rounded-full bg-background border border-border/60 shadow-lg",children:i.icon})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium text-foreground",children:i.text}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:i.description})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:[0,1,2].map(e=>(0,t.jsx)("div",{className:(0,k.cn)("w-2 h-2 rounded-full bg-primary/30 animate-pulse","transition-all duration-300"),style:{animationDelay:"".concat(.2*e,"s"),animationDuration:"1.5s"}},e))})]})}function z(e){let{onForgotPassword:s,onSignUp:r,onSuccess:k}=e,{clearError:z,error:C,loading:S,signIn:T}=(0,b.useAuthContext)(),{clearAllErrors:F,clearFieldError:E,errors:_,isFieldValid:L,markFormTouched:M,validateForm:Z}=(0,f.M)(),I=(0,h.c3)("auth"),[D,P]=(0,g.useState)({email:"",password:"",rememberMe:!1}),[J,R]=(0,g.useState)(!1),[V,B]=(0,g.useState)("authenticating"),[W,$]=(0,g.useState)(!0),[q,H]=(0,g.useState)(null),[O,G]=(0,g.useState)(!1);(0,g.useEffect)(()=>{if(void 0!==globalThis.window&&"undefined"!=typeof navigator){$(navigator.onLine);let e=()=>$(!0),s=()=>$(!1);return globalThis.addEventListener("online",e),globalThis.addEventListener("offline",s),()=>{globalThis.removeEventListener("online",e),globalThis.removeEventListener("offline",s)}}return()=>{}},[]),(0,g.useEffect)(()=>{if(void 0!==globalThis.window&&"undefined"!=typeof localStorage){let e=localStorage.getItem("workhub_remember_email");e&&P(s=>({...s,email:e,rememberMe:!0}))}},[]);let K=async e=>{if(e.preventDefault(),!O&&!S){if(M(),G(!0),z(),F(),!Z(D).isValid||!W)return void G(!1);try{B("authenticating");let{error:e}=await T(D.email,D.password);e?G(!1):(B("success"),void 0!==globalThis.window&&"undefined"!=typeof localStorage&&(D.rememberMe?localStorage.setItem("workhub_remember_email",D.email):localStorage.removeItem("workhub_remember_email")),setTimeout(()=>{G(!1),null==k||k()},800))}catch(e){console.error("Login error:",e),G(!1)}}},Q=(e,s)=>{P(r=>({...r,[e]:s})),"string"==typeof s&&_[e]&&E(e),C&&z()},U=e=>{H(e)},X=()=>{H(null)};return O?(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,t.jsx)("div",{className:"w-full max-w-md",children:(0,t.jsx)("div",{className:"rounded-2xl border border-border/60 bg-card shadow-xl backdrop-blur-sm",children:(0,t.jsx)(A,{stage:V})})})}):(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[!W&&(0,t.jsxs)("div",{className:"mb-4 flex items-center gap-2 rounded-xl border border-destructive/20 bg-destructive/10 p-3 text-destructive",children:[(0,t.jsx)(a.A,{className:"size-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:I("noInternetConnection")})]}),(0,t.jsxs)("div",{className:"mb-8 text-center",children:[(0,t.jsx)("div",{className:"group mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ring-1 ring-primary/20 transition-all duration-300 hover:ring-primary/40",children:(0,t.jsx)(i.A,{className:"size-8 text-primary-foreground transition-transform group-hover:scale-110"})}),(0,t.jsx)("h1",{className:"mb-2 text-3xl font-bold tracking-tight text-foreground",children:I("welcomeBack")}),(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-muted-foreground",children:[(0,t.jsx)("span",{children:I("signInToAccount")}),W&&(0,t.jsx)(n.A,{className:"size-4 text-green-600"})]})]}),(0,t.jsxs)("div",{className:"rounded-2xl border border-border/60 bg-card p-8 shadow-xl backdrop-blur-sm",children:[C&&(0,t.jsxs)(v.Fc,{className:"mb-6 border-destructive/20 bg-destructive/5",variant:"destructive",children:[(0,t.jsx)(o.A,{className:"size-4"}),(0,t.jsx)(v.TN,{className:"text-destructive",children:C})]}),!W&&(0,t.jsxs)(v.Fc,{className:"mb-6 border-yellow-200 bg-yellow-50",children:[(0,t.jsx)(a.A,{className:"size-4"}),(0,t.jsx)(v.TN,{children:I("offlineWarning")})]}),(0,t.jsxs)("form",{className:"space-y-6",onSubmit:K,children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{className:"text-sm font-medium transition-colors ".concat("email"===q?"text-primary":"text-foreground"),htmlFor:"email",children:"Email address"}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,t.jsx)(l.A,{className:"size-5 transition-colors ".concat("email"===q?"text-primary":"text-muted-foreground")})}),(0,t.jsx)(y.p,{autoComplete:"email",className:"h-12 pl-10 transition-all duration-200 ".concat(_.email?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"," ").concat(L("email",D.email)?"border-green-500/50 focus-visible:ring-green-500/20":""),disabled:S||!W||O,id:"email",onBlur:X,onChange:e=>Q("email",e.target.value),onFocus:()=>U("email"),placeholder:"Enter your email",type:"email",value:D.email}),L("email",D.email)&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3",children:(0,t.jsx)(c.A,{className:"size-5 text-green-500 duration-200 animate-in fade-in-0 zoom-in-95"})})]}),_.email&&(0,t.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,t.jsx)(o.A,{className:"size-4"}),_.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{className:"text-sm font-medium transition-colors ".concat("password"===q?"text-primary":"text-foreground"),htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,t.jsx)(i.A,{className:"size-5 transition-colors ".concat("password"===q?"text-primary":"text-muted-foreground")})}),(0,t.jsx)(y.p,{autoComplete:"current-password",className:"h-12 pl-10 pr-12 transition-all duration-200 ".concat(_.password?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"," ").concat(L("password",D.password)?"border-green-500/50 focus-visible:ring-green-500/20":""),disabled:S||!W||O,id:"password",onBlur:X,onChange:e=>Q("password",e.target.value),onFocus:()=>U("password"),placeholder:"Enter your password",type:J?"text":"password",value:D.password}),(0,t.jsx)("button",{"aria-label":J?"Hide password":"Show password",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground transition-colors hover:text-foreground",disabled:S||O,onClick:()=>R(!J),type:"button",children:J?(0,t.jsx)(d.A,{className:"size-5"}):(0,t.jsx)(m.A,{className:"size-5"})})]}),_.password&&(0,t.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,t.jsx)(o.A,{className:"size-4"}),_.password]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.S,{checked:D.rememberMe,className:"data-[state=checked]:border-primary data-[state=checked]:bg-primary",disabled:S||O,id:"remember-me",onCheckedChange:e=>Q("rememberMe",e)}),(0,t.jsx)(w.J,{className:"cursor-pointer text-sm text-muted-foreground transition-colors hover:text-foreground",htmlFor:"remember-me",children:"Remember me"})]}),s&&(0,t.jsx)("button",{className:"text-sm font-medium text-primary transition-colors hover:text-primary/80",disabled:S||O,onClick:s,type:"button",children:"Forgot password?"})]}),(0,t.jsx)(j.$,{className:"group h-12 w-full rounded-xl bg-gradient-to-r from-primary to-accent font-semibold text-primary-foreground shadow-lg transition-all duration-200 hover:from-primary/90 hover:to-accent/90 hover:shadow-xl disabled:opacity-50",disabled:S||!W||O,type:"submit",children:S||O?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"mr-2 size-5 animate-spin"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:["Sign in",(0,t.jsx)(x.A,{className:"ml-2 size-5 transition-transform group-hover:translate-x-1"})]})}),(0,t.jsxs)("div",{className:"rounded-xl border border-border/40 bg-muted/30 p-4",children:[(0,t.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Demo Access"})]}),(0,t.jsxs)("div",{className:"space-y-1 text-xs text-muted-foreground",children:[(0,t.jsx)("div",{className:"font-mono",children:"<EMAIL>"}),(0,t.jsx)("div",{className:"font-mono",children:"demo123"})]})]})]})]}),r&&(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,t.jsx)("button",{className:"font-medium text-primary transition-colors hover:text-primary/80",disabled:S||O,onClick:r,children:"Create one now"})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("div",{className:"inline-flex items-center gap-2 rounded-full border border-border/40 bg-card px-4 py-2 text-xs text-muted-foreground",children:[(0,t.jsx)(p.A,{className:"size-4 text-green-600"}),(0,t.jsx)("span",{children:"Protected by enterprise-grade security"})]})}),(0,t.jsxs)("footer",{className:"mt-8 text-center text-xs text-muted-foreground",children:[(0,t.jsx)("p",{children:"\xa9 2024 WorkHub. All rights reserved."}),(0,t.jsxs)("div",{className:"mt-2 space-x-4",children:[(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Terms"}),(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Privacy"}),(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Support"})]})]})]})})}}}]);