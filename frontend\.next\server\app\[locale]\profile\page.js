(()=>{var e={};e.id=2943,e.ids=[2943],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35480:(e,r,t)=>{Promise.resolve().then(t.bind(t,38083))},36152:(e,r,t)=>{Promise.resolve().then(t.bind(t,44297))},38083:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\profile\\page.tsx","default")},44297:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),i=t(11516);t(43210);var o=t(34570),n=t(44493),a=t(60436),c=t(63213);function l(){let{loading:e}=(0,c.useAuthContext)();return e?(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:(0,s.jsx)(n.Zp,{className:"mx-auto w-full max-w-md",children:(0,s.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(i.A,{className:"mb-4 size-8 animate-spin text-blue-600"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading user profile..."})]})})}):(0,s.jsx)(o.ProtectedRoute,{children:(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,s.jsx)(a.F,{variant:"detailed"})})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93025:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>u,tree:()=>l});var s=t(65239),i=t(48088),o=t(88170),n=t.n(o),a=t(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);t.d(r,c);let l={children:["",{children:["[locale]",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38083)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\profile\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/profile/page",pathname:"/[locale]/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4825,625,9851,2482],()=>t(93025));module.exports=s})();