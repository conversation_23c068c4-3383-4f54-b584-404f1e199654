"use strict";exports.id=3439,exports.ids=[3439],exports.modules={12662:(e,r,t)=>{t.d(r,{AppBreadcrumb:()=>m});var s=t(60687),a=t(85814),o=t.n(a),i=t(16189),n=t(43210),l=t.n(n),c=t(70640),d=t(22482);function m({className:e,homeHref:r="/",homeLabel:t="Dashboard",showContainer:a=!0}){let n=(0,i.usePathname)(),m=n?n.split("/").filter(Boolean):[],u=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},g=m.map((e,r)=>{let t="/"+m.slice(0,r+1).join("/"),a=r===m.length-1,i=u(e);return(0,s.jsxs)(l().Fragment,{children:[(0,s.jsx)(c.BreadcrumbItem,{children:a?(0,s.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:i}):(0,s.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(o(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:i})})}),!a&&(0,s.jsx)(c.BreadcrumbSeparator,{})]},t)}),h=(0,s.jsx)(c.Breadcrumb,{className:(0,d.cn)("text-sm",e),children:(0,s.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,s.jsx)(c.BreadcrumbItem,{children:(0,s.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(o(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:r,children:t})})}),m.length>0&&(0,s.jsx)(c.BreadcrumbSeparator,{}),g]})});return a?(0,s.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,s.jsx)("div",{className:"flex items-center",children:h})}):h}},29385:(e,r,t)=>{t.d(r,{fX:()=>n});var s=t(43210),a=t(26787),o=t(59350);let i=new Map;function n(e){return(0,s.useMemo)(()=>(i.has(e)||i.set(e,(0,a.v)()((0,o.lt)((0,o.Zr)((e,r)=>({activeTab:"all",layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:"",setActiveTab:r=>e({activeTab:r},!1,"setActiveTab"),setViewMode:r=>e(e=>({layout:{...e.layout,viewMode:r}}),!1,"setViewMode"),setGridColumns:r=>e(e=>({layout:{...e.layout,gridColumns:r}}),!1,"setGridColumns"),toggleCompactMode:()=>e(e=>({layout:{...e.layout,compactMode:!e.layout.compactMode}}),!1,"toggleCompactMode"),toggleFilters:()=>e(e=>({layout:{...e.layout,showFilters:!e.layout.showFilters}}),!1,"toggleFilters"),toggleSettings:()=>e(e=>({layout:{...e.layout,showSettings:!e.layout.showSettings}}),!1,"toggleSettings"),updateFilter:(r,t)=>e(e=>({filters:{...e.filters,[r]:t}}),!1,"updateFilter"),clearFilters:()=>e({filters:{}},!1,"clearFilters"),setSorting:(r,t)=>e({sortBy:r,sortDirection:t},!1,"setSorting"),setSearchTerm:r=>e({searchTerm:r},!1,"setSearchTerm"),toggleItemSelection:r=>e(e=>{let t=new Set(e.selectedItems);return t.has(r)?t.delete(r):t.add(r),{selectedItems:t}},!1,"toggleItemSelection"),clearSelection:()=>e({selectedItems:new Set},!1,"clearSelection"),selectAll:r=>e({selectedItems:new Set(r)},!1,"selectAll"),setMonitoringEnabled:r=>e(e=>({monitoring:{...e.monitoring,enabled:r}}),!1,"setMonitoringEnabled"),setRefreshInterval:r=>e(e=>({monitoring:{...e.monitoring,refreshInterval:r}}),!1,"setRefreshInterval"),toggleAutoRefresh:()=>e(e=>({monitoring:{...e.monitoring,autoRefresh:!e.monitoring.autoRefresh}}),!1,"toggleAutoRefresh"),pauseDataType:r=>e(e=>({monitoring:{...e.monitoring,pausedDataTypes:new Set([...e.monitoring.pausedDataTypes,r])}}),!1,"pauseDataType"),resumeDataType:r=>e(e=>{let t=new Set(e.monitoring.pausedDataTypes);return t.delete(r),{monitoring:{...e.monitoring,pausedDataTypes:t}}},!1,"resumeDataType"),resetSettings:()=>e({layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:""},!1,"resetSettings"),getFilteredData:(e,t)=>{let s=r(),a=[...e];if(s.searchTerm){let e=s.searchTerm.toLowerCase();a=a.filter(r=>Object.values(r).some(r=>String(r).toLowerCase().includes(e)))}return Object.entries(s.filters).forEach(([e,r])=>{null!=r&&""!==r&&(a=a.filter(s=>{let a=t.filters?.find(r=>r.id===e);if(!a)return!0;switch(a.type){case"select":return s[e]===r;case"multiselect":return!Array.isArray(r)||r.includes(s[e]);case"toggle":return!r||s[e];default:return!0}}))}),a.sort((e,r)=>{let t=e[s.sortBy],a=r[s.sortBy],o="asc"===s.sortDirection?1:-1;return t<a?-1*o:t>a?+o:0}),a},getSelectedCount:()=>r().selectedItems.size,hasActiveFilters:()=>{let e=r();return e.searchTerm.length>0||Object.values(e.filters).some(e=>null!=e&&""!==e)}}),{name:`workhub-dashboard-${e}`,partialize:e=>({layout:e.layout,monitoring:e.monitoring,filters:e.filters,sortBy:e.sortBy,sortDirection:e.sortDirection})}),{name:`dashboard-${e}`}))),i.get(e)),[e])}},70640:(e,r,t)=>{t.d(r,{Breadcrumb:()=>l,BreadcrumbItem:()=>d,BreadcrumbLink:()=>m,BreadcrumbList:()=>c,BreadcrumbPage:()=>u,BreadcrumbSeparator:()=>g});var s=t(60687),a=t(8730),o=t(74158),i=(t(69795),t(43210)),n=t(22482);let l=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("nav",{"aria-label":"breadcrumb",className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:t,...r}));l.displayName="Breadcrumb";let c=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("ol",{className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:t,...r}));c.displayName="BreadcrumbList";let d=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("li",{className:(0,n.cn)("inline-flex items-center gap-1.5",e),ref:t,...r}));d.displayName="BreadcrumbItem";let m=i.forwardRef(({asChild:e,className:r,...t},o)=>{let i=e?a.DX:"a";return(0,s.jsx)(i,{className:(0,n.cn)("transition-colors hover:text-foreground",r),ref:o,...t})});m.displayName="BreadcrumbLink";let u=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,n.cn)("font-normal text-foreground",e),ref:t,role:"link",...r}));u.displayName="BreadcrumbPage";let g=({children:e,className:r,...t})=>(0,s.jsx)("span",{"aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",r),role:"presentation",...t,children:e??(0,s.jsx)(o.A,{className:"size-4"})});g.displayName="BreadcrumbSeparator"},95668:(e,r,t)=>{t.d(r,{A:()=>d});var s=t(60687),a=t(14975),o=t(77368),i=t(43210),n=t(91821),l=t(29523);class c extends i.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,r){this.setState({errorInfo:r}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",r.componentStack),this.props.onError&&this.props.onError(e,r)}render(){let{description:e="An unexpected error occurred.",resetLabel:r="Try Again",title:t="Something went wrong"}=this.props;return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,s.jsx)(a.A,{className:"mr-2 size-4"}),(0,s.jsx)(n.XL,{className:"text-lg font-semibold",children:t}),(0,s.jsxs)(n.TN,{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-2",children:this.state.error?.message||e}),!1,(0,s.jsxs)(l.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,s.jsx)(o.A,{className:"mr-2 size-4"}),r]})]})]}):this.props.children}}let d=c}};