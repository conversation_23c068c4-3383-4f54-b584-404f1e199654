(()=>{var e={};e.id=925,e.ids=[925],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>p,gC:()=>g,l6:()=>c,yv:()=>m});var s=r(60687),a=r(22670),l=r(61662),i=r(89743),n=r(58450),d=r(43210),o=r(22482);let c=a.bL;a.YJ;let m=a.WT,u=d.forwardRef(({children:e,className:t,...r},i)=>(0,s.jsxs)(a.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:i,...r,children:[e,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]}));u.displayName=a.l9.displayName;let x=d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,s.jsx)(i.A,{className:"size-4"})}));x.displayName=a.PP.displayName;let h=d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,s.jsx)(l.A,{className:"size-4"})}));h.displayName=a.wn.displayName;let g=d.forwardRef(({children:e,className:t,position:r="popper",...l},i)=>(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,ref:i,...l,children:[(0,s.jsx)(x,{}),(0,s.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,s.jsx)(h,{})]})}));g.displayName=a.UC.displayName,d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:r,...t})).displayName=a.JU.displayName;let p=d.memo(d.forwardRef(({children:e,className:t,...r},l)=>{let i=d.useCallback(e=>{"function"==typeof l?l(e):l&&(l.current=e)},[l]);return(0,s.jsxs)(a.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:i,...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:e})]})}));p.displayName=a.q7.displayName,d.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),ref:r,...t})).displayName=a.wv.displayName},17967:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\delegations\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\[id]\\page.tsx","default")},18810:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eS});var s=r(60687),a=r(14975),l=r(33886),i=r(16189),n=r(75699),d=r(58261),o=r(97025),c=r(82614);let m=(0,c.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);var u=r(38765);let x=(0,c.A)("CirclePlay",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]);var h=r(80489),g=r(72963),p=r(44610),b=r(77368),y=r(54052),f=r(26622),j=r(58595),v=r(15036),N=r(43210),k=r(15795),w=r(29523),A=r(63503),C=r(89667),M=r(80013),D=r(15079);let R=({currentStatus:e,delegationId:t,isOpen:r,onClose:a,onConfirm:l})=>{let[i,n]=(0,N.useState)(e),[d,o]=(0,N.useState)(""),[c,m]=(0,N.useState)(null);return(0,s.jsx)(A.lG,{onOpenChange:a,open:r,children:(0,s.jsxs)(A.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(A.c7,{children:(0,s.jsx)(A.L3,{children:"Update Delegation Status"})}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(M.J,{className:"text-right",htmlFor:"current-status",children:"Current Status"}),(0,s.jsx)(C.p,{className:"col-span-3",id:"current-status",readOnly:!0,value:(0,k.fZ)(e)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(M.J,{className:"text-right",htmlFor:"new-status",children:"New Status"}),(0,s.jsxs)(D.l6,{onValueChange:e=>n(e),value:i,children:[(0,s.jsx)(D.bq,{className:"col-span-3",children:(0,s.jsx)(D.yv,{placeholder:"Select new status"})}),(0,s.jsx)(D.gC,{children:["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"].map(t=>(0,s.jsx)(D.eb,{disabled:t===e,value:t,children:(0,k.fZ)(t)},t))})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(M.J,{className:"text-right",htmlFor:"reason",children:"Reason"}),(0,s.jsx)(C.p,{className:"col-span-3",id:"reason",onChange:e=>o(e.target.value),placeholder:"Enter reason for status change",value:d})]}),c&&(0,s.jsx)("p",{className:"col-span-4 text-center text-sm text-red-500",children:c})]}),(0,s.jsxs)(A.Es,{children:[(0,s.jsx)(w.$,{onClick:a,variant:"outline",children:"Cancel"}),(0,s.jsx)(w.$,{onClick:()=>d.trim()?i===e?void m("Please select a different status."):void(m(null),l(i,d)):void m("Reason for status change is required."),children:"Confirm"})]})]})})};var F=r(96834),P=r(44493),E=r(35950),q=r(76242),z=r(22482);let S=e=>{switch(e){case"Cancelled":return"bg-gradient-to-r from-red-500/20 to-rose-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:from-red-500/10 dark:to-rose-500/10 dark:border-red-500/20";case"Completed":return"bg-gradient-to-r from-purple-500/20 to-violet-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:from-purple-500/10 dark:to-violet-500/10 dark:border-purple-500/20";case"Confirmed":return"bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:from-green-500/10 dark:to-emerald-500/10 dark:border-green-500/20";case"In_Progress":return"bg-gradient-to-r from-yellow-500/20 to-amber-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:from-yellow-500/10 dark:to-amber-500/10 dark:border-yellow-500/20";case"Planned":return"bg-gradient-to-r from-blue-500/20 to-blue-600/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:from-blue-500/10 dark:to-blue-600/10 dark:border-blue-500/20";default:return"bg-gradient-to-r from-gray-500/20 to-slate-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:from-gray-500/10 dark:to-slate-500/10 dark:border-gray-500/20"}},T=e=>{switch(e){case"Cancelled":return o.A;case"Completed":return m;case"Confirmed":return u.A;case"In_Progress":return x;case"Planned":return h.A;default:return g.A}},I=e=>{switch(e){case"Cancelled":return"from-red-500/20 to-rose-500/20 border-red-500/30";case"Completed":return"from-purple-500/20 to-violet-500/20 border-purple-500/30";case"Confirmed":return"from-green-500/20 to-emerald-500/20 border-green-500/30";case"In_Progress":return"from-yellow-500/20 to-amber-500/20 border-yellow-500/30";case"Planned":return"from-blue-500/20 to-blue-600/20 border-blue-500/30";default:return"from-gray-500/20 to-slate-500/20 border-gray-500/30"}},O=(e,t=!1)=>{if(!e)return"N/A";try{return(0,n.GP)((0,d.H)(e),t?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch{return"Invalid Date"}};function $({currentStatus:e,delegationId:t,isUpdating:r=!1,onStatusUpdate:a,statusHistory:l}){let[i,n]=(0,N.useState)(!1),d=async(e,t)=>{try{await a(e,t),n(!1)}catch(e){console.error("Status update failed:",e)}},o=l?.slice().sort((e,t)=>new Date(t.changedAt).getTime()-new Date(e.changedAt).getTime())||[];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(P.Zp,{className:"border-border/60 bg-gradient-to-br from-card to-card/95 shadow-lg backdrop-blur-sm",children:[(0,s.jsx)(P.aR,{className:"pb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex min-w-0 flex-1 items-center gap-3",children:[(0,s.jsx)("div",{className:"shrink-0 rounded-lg bg-primary/10 p-2 text-primary",children:(0,s.jsx)(p.A,{className:"size-5"})}),(0,s.jsxs)("div",{className:"flex min-w-0 items-center gap-2",children:[(0,s.jsx)(P.ZB,{className:"whitespace-nowrap text-xl font-semibold text-primary",children:"Status History"}),(0,s.jsxs)(F.E,{className:"shrink-0 text-xs",variant:"secondary",children:[o.length," ",1===o.length?"entry":"entries"]})]})]}),(0,s.jsx)(q.Bc,{children:(0,s.jsxs)(q.m_,{children:[(0,s.jsx)(q.k$,{asChild:!0,children:(0,s.jsx)(w.$,{className:"shrink-0 gap-2 whitespace-nowrap bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-md transition-all duration-200 hover:from-primary/90 hover:to-primary hover:shadow-lg",disabled:r,onClick:()=>{n(!0)},size:"sm",children:r?(0,s.jsx)(b.A,{className:"size-4 animate-spin"}):(0,s.jsx)(y.A,{className:"size-4"})})}),(0,s.jsx)(q.ZI,{children:(0,s.jsx)("p",{children:"Change delegation status"})})]})})]})}),(0,s.jsx)(P.Wu,{className:"pt-0",children:o.length>0?(0,s.jsx)("div",{className:"scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent max-h-96 space-y-4 overflow-y-auto pr-2",children:o.map((e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"group relative rounded-xl border border-border/50 bg-gradient-to-r from-background/50 to-background/30 p-4 transition-all duration-200 hover:border-border",children:[t<o.length-1&&(0,s.jsx)("div",{className:"absolute left-7 top-16 h-8 w-0.5 bg-gradient-to-b from-border via-border/50 to-transparent"}),(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(()=>{let t=T(e.status),r=I(e.status);return(0,s.jsx)("div",{className:(0,z.cn)("p-2.5 rounded-full bg-gradient-to-br border-2 transition-all duration-200 group-hover:scale-105",r),children:(0,s.jsx)(t,{className:"size-4"})})})(),0===t&&(0,s.jsx)("div",{className:"absolute -right-1 -top-1 size-3 animate-pulse rounded-full border-2 border-background bg-green-500 shadow-lg"})]}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"mb-2 flex items-center justify-between gap-3",children:[(0,s.jsxs)(F.E,{className:(0,z.cn)("text-sm py-1.5 px-3 font-medium border shadow-sm flex items-center gap-1.5 flex-shrink-0 whitespace-nowrap",S(e.status)),children:[(()=>{let t=T(e.status);return(0,s.jsx)(t,{className:"size-3.5 shrink-0"})})(),(0,s.jsx)("span",{className:"whitespace-nowrap",children:(0,k.fZ)(e.status)})]}),(0,s.jsxs)("div",{className:"flex shrink-0 items-center gap-2 text-xs text-muted-foreground",children:[(0,s.jsx)(f.A,{className:"size-3"}),(0,s.jsx)("span",{className:"whitespace-nowrap",children:O(e.changedAt,!0)})]})]}),e.reason&&(0,s.jsx)("div",{className:"mt-2 rounded-lg border border-border/30 bg-muted/30 p-3",children:(0,s.jsxs)("p",{className:"flex items-start gap-2 text-sm italic text-muted-foreground",children:[(0,s.jsx)(j.A,{className:"mt-0.5 size-3 shrink-0"}),(0,s.jsx)("span",{className:"shrink-0 font-medium",children:"Reason:"}),(0,s.jsx)("span",{className:"break-words",children:e.reason})]})})]})]})]}),t<o.length-1&&(0,s.jsx)(E.w,{className:"my-2 bg-gradient-to-r from-transparent via-border to-transparent"})]},e.id||t))}):(0,s.jsxs)("div",{className:"py-12 text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-muted/30 p-4",children:(0,s.jsx)(v.A,{className:"size-8 text-muted-foreground"})}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"No status history available."}),(0,s.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:"Status changes will appear here."})]})})]}),(0,s.jsx)(R,{currentStatus:e,delegationId:t,isOpen:i,onClose:()=>n(!1),onConfirm:d})]})}var H=r(30474),Z=r(26398),L=r(92876),_=r(48206),B=r(99196),G=r(14163),U=N.forwardRef((e,t)=>{let{ratio:r=1,style:a,...l}=e;return(0,s.jsx)("div",{style:{position:"relative",width:"100%",paddingBottom:`${100/r}%`},"data-radix-aspect-ratio-wrapper":"",children:(0,s.jsx)(G.sG.div,{...l,ref:t,style:{...a,position:"absolute",top:0,right:0,bottom:0,left:0}})})});function V({children:e,icon:t,label:r,value:a,valueClassName:l,className:i}){return(0,s.jsxs)("div",{className:(0,z.cn)("flex items-start space-x-3",i),children:[(0,s.jsx)("div",{className:"mt-0.5 rounded-full bg-blue-50 dark:bg-blue-900/30 p-2",children:(0,s.jsx)(t,{className:"size-4 text-blue-600 dark:text-blue-400"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1",children:r}),(0,s.jsx)("div",{className:(0,z.cn)("font-medium text-gray-900 dark:text-white",l),children:e||a||"N/A"})]})]})}U.displayName="AspectRatio";var Q=r(28149);let W=e=>{if(!e)return"N/A";try{return(0,n.GP)((0,d.H)(e),"MMM d, yyyy")}catch{return"Invalid Date"}};function J({delegation:e,className:t}){return(0,s.jsxs)(P.Zp,{className:t,children:[(0,s.jsx)(P.aR,{children:(0,s.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(Z.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Event Overview"})]})}),(0,s.jsxs)(P.Wu,{className:"space-y-6",children:[(0,s.jsx)(U,{ratio:16/9,className:"overflow-hidden rounded-lg",children:(0,s.jsx)(H.default,{src:(0,Q._x)(e.imageUrl,e.id,"detail"),alt:e.eventName,className:"object-cover transition-transform hover:scale-105",fill:!0,priority:!0})}),(0,s.jsx)(E.w,{}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsx)(V,{icon:L.A,label:"Duration",value:`${W(e.durationFrom)} - ${W(e.durationTo)}`}),(0,s.jsx)(V,{icon:Z.A,label:"Location",value:e.location}),e.invitationFrom&&(0,s.jsx)(V,{icon:_.A,label:"Invitation From",value:e.invitationFrom}),e.invitationTo&&(0,s.jsx)(V,{icon:_.A,label:"Invitation To",value:e.invitationTo})]}),e.notes&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(V,{icon:B.A,label:"General Notes",valueClassName:"whitespace-pre-wrap",value:e.notes})})]})]})]})}var X=r(85763),Y=r(52856),K=r(93242),ee=r(48409);let et=e=>{if(!e)return"N/A";try{return(0,n.GP)((0,d.H)(e),"MMM d, yyyy, HH:mm")}catch{return"Invalid Date"}};function er({delegation:e,className:t}){return e.arrivalFlight||e.departureFlight?(0,s.jsxs)(P.Zp,{className:t,children:[(0,s.jsx)(P.aR,{children:(0,s.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(Y.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Flight Information"})]}),(0,s.jsxs)(F.E,{variant:"secondary",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",children:[[e.arrivalFlight,e.departureFlight].filter(Boolean).length," ","flight",1!==[e.arrivalFlight,e.departureFlight].filter(Boolean).length?"s":""]})]})}),(0,s.jsxs)(P.Wu,{className:"space-y-6",children:[e.arrivalFlight&&(0,s.jsxs)("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-900/20",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(K.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-green-900 dark:text-green-100",children:"Arrival Flight"})]}),(0,s.jsxs)("div",{className:"grid gap-3 md:grid-cols-2",children:[(0,s.jsx)(V,{icon:Y.A,label:"Flight Number",value:e.arrivalFlight.flightNumber}),(0,s.jsx)(V,{icon:v.A,label:"Date & Time",value:et(e.arrivalFlight.dateTime)}),(0,s.jsx)(V,{icon:Z.A,label:"Airport",value:e.arrivalFlight.airport}),e.arrivalFlight.terminal&&(0,s.jsx)(V,{icon:Z.A,label:"Terminal",value:e.arrivalFlight.terminal})]}),e.arrivalFlight.notes&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{className:"my-3"}),(0,s.jsx)(V,{icon:B.A,label:"Notes",value:e.arrivalFlight.notes,valueClassName:"whitespace-pre-wrap"})]})]}),e.departureFlight&&(0,s.jsxs)("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-900/20",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(ee.A,{className:"h-5 w-5 text-orange-600 dark:text-orange-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-orange-900 dark:text-orange-100",children:"Departure Flight"})]}),(0,s.jsxs)("div",{className:"grid gap-3 md:grid-cols-2",children:[(0,s.jsx)(V,{icon:Y.A,label:"Flight Number",value:e.departureFlight.flightNumber}),(0,s.jsx)(V,{icon:v.A,label:"Date & Time",value:et(e.departureFlight.dateTime)}),(0,s.jsx)(V,{icon:Z.A,label:"Airport",value:e.departureFlight.airport}),e.departureFlight.terminal&&(0,s.jsx)(V,{icon:Z.A,label:"Terminal",value:e.departureFlight.terminal})]}),e.departureFlight.notes&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{className:"my-3"}),(0,s.jsx)(V,{icon:B.A,label:"Notes",value:e.departureFlight.notes,valueClassName:"whitespace-pre-wrap"})]})]})]})]}):(0,s.jsxs)(P.Zp,{className:t,children:[(0,s.jsx)(P.aR,{children:(0,s.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(Y.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Flight Information"})]})}),(0,s.jsx)(P.Wu,{children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center",children:(0,s.jsx)(Y.A,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"No Flight Details"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No flight information has been provided for this delegation."})]})})]})}function es({title:e,icon:t,items:r,renderItem:a,emptyMessage:l,className:i}){return(0,s.jsxs)(P.Zp,{className:i,children:[(0,s.jsx)(P.aR,{children:(0,s.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(t,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:e})]}),(0,s.jsx)(F.E,{variant:"secondary",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",children:r.length})]})}),(0,s.jsx)(P.Wu,{children:r.length>0?(0,s.jsx)("div",{className:"space-y-3",children:r.map(a)}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"rounded-full bg-gray-100 dark:bg-gray-800 p-3 mb-3",children:(0,s.jsx)(t,{className:"h-6 w-6 text-gray-400"})}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 font-medium",children:l})]})})]})}function ea({className:e,delegation:t}){return(0,s.jsx)(es,{className:e??"",emptyMessage:"No delegates assigned to this delegation.",icon:_.A,items:t.delegates??[],renderItem:(e,t)=>(0,s.jsxs)("div",{className:"space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.name}),e.title&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.title}),e.notes&&(0,s.jsx)("p",{className:"rounded bg-gray-50 p-2 text-xs italic text-gray-500 dark:bg-gray-700 dark:text-gray-500",children:e.notes})]},e.id??t),title:"Delegates"})}function el({className:e,delegation:t}){return(0,s.jsx)(es,{className:e??"",emptyMessage:"No drivers assigned to this delegation.",icon:j.A,items:t.drivers??[],renderItem:(e,t)=>(0,s.jsxs)("div",{className:"space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.employee?(0,k.DV)(e.employee):`Employee ID: ${e.employeeId}`}),e.employee?.role&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:(0,k.s)(e.employee.role)}),e.employee?.contactEmail&&(0,s.jsxs)("p",{className:"rounded bg-gray-50 p-2 text-xs text-gray-500 dark:bg-gray-700 dark:text-gray-500",children:["\uD83D\uDCE7 ",e.employee.contactEmail]})]},e.employeeId||t),title:"Drivers"})}var ei=r(53597);function en({delegation:e,className:t}){return(0,s.jsx)(es,{title:"Escorts",icon:ei.A,items:e.escorts??[],renderItem:(e,t)=>(0,s.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.employee?(0,k.DV)(e.employee):`Employee ID: ${e.employeeId}`}),e.employee?.role&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:(0,k.s)(e.employee.role)}),e.employee?.contactEmail&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded",children:["\uD83D\uDCE7 ",e.employee.contactEmail]})]},e.employeeId||t),emptyMessage:"No escorts assigned to this delegation.",className:t||void 0})}r(21342);var ed=r(24920);function eo({delegation:e,className:t}){return(0,s.jsx)(es,{title:"Vehicles",icon:ed.A,items:e.vehicles??[],renderItem:(e,t)=>(0,s.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.vehicle?`${e.vehicle.make} ${e.vehicle.model} (${e.vehicle.year})`:`Vehicle ID: ${e.vehicleId}`}),e.vehicle?.licensePlate&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["\uD83D\uDE97 License Plate: ",e.vehicle.licensePlate]}),e.vehicle?.ownerName&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded",children:["\uD83D\uDC64 Owner: ",e.vehicle.ownerName]})]},e.vehicleId||t),emptyMessage:"No vehicles assigned to this delegation.",className:t||void 0})}function ec({delegation:e,onStatusUpdate:t,className:r}){return(0,s.jsxs)(X.tU,{defaultValue:"overview",className:r,children:[(0,s.jsxs)(X.j7,{className:"grid w-full grid-cols-4 mb-6",children:[(0,s.jsx)(X.Xi,{value:"overview",className:"text-sm",children:"Overview"}),(0,s.jsx)(X.Xi,{value:"assignments",className:"text-sm",children:"Assignments"}),(0,s.jsx)(X.Xi,{value:"flights",className:"text-sm",children:"Flights"}),(0,s.jsx)(X.Xi,{value:"history",className:"text-sm",children:"History"})]}),(0,s.jsx)(X.av,{value:"overview",className:"space-y-6 mt-0",children:(0,s.jsx)(J,{delegation:e})}),(0,s.jsx)(X.av,{value:"assignments",className:"space-y-6 mt-0",children:(0,s.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[(0,s.jsx)(ea,{delegation:e}),(0,s.jsx)(en,{delegation:e}),(0,s.jsx)(el,{delegation:e}),(0,s.jsx)(eo,{delegation:e})]})}),(0,s.jsx)(X.av,{value:"flights",className:"space-y-6 mt-0",children:(0,s.jsx)(er,{delegation:e})}),(0,s.jsx)(X.av,{value:"history",className:"space-y-6 mt-0",children:t?(0,s.jsx)($,{currentStatus:e.status,delegationId:e.id,statusHistory:e.statusHistory||[],onStatusUpdate:t}):(0,s.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Status History"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Status history will be displayed here when available."})]})})]})}var em=r(85814),eu=r.n(em),ex=r(55817),eh=r(35137),eg=r(57207),ep=r(69795),eb=r(67146),ey=r(68752),ef=r(69981),ej=r(55925);let ev=e=>{switch(e){case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}};function eN({delegation:e,onDelete:t,className:r}){let a=(0,i.useRouter)(),l=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(ey.r,{actionType:"tertiary",icon:(0,s.jsx)(ex.A,{className:"size-4"}),onClick:()=>a.push("/delegations"),size:"sm",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Back to List"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Back"})]}),(0,s.jsx)(ey.r,{actionType:"secondary",asChild:!0,icon:(0,s.jsx)(eh.A,{className:"size-4"}),size:"sm",children:(0,s.jsx)(eu(),{href:`/delegations/${e.id}/edit`,children:"Edit"})}),(0,s.jsx)(ef.M,{href:`/delegations/${e.id}/report`}),t&&(0,s.jsxs)(ej.Lt,{children:[(0,s.jsx)(ej.tv,{asChild:!0,children:(0,s.jsxs)(ey.r,{actionType:"danger",icon:(0,s.jsx)(eg.A,{className:"size-4"}),size:"sm",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Delete Delegation"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Delete"})]})}),(0,s.jsxs)(ej.EO,{children:[(0,s.jsxs)(ej.wd,{children:[(0,s.jsx)(ej.r7,{children:"Are you sure?"}),(0,s.jsx)(ej.$v,{children:"This action cannot be undone. This will permanently delete the delegation and all its related information."})]}),(0,s.jsxs)(ej.ck,{children:[(0,s.jsx)(ej.Zr,{children:"Cancel"}),(0,s.jsx)(ej.Rx,{className:"bg-destructive hover:bg-destructive/90",onClick:t,children:"Delete"})]})]})]})]});return(0,s.jsx)("div",{className:(0,z.cn)("border-b bg-white dark:bg-gray-800",r),children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between lg:hidden",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white truncate",children:e.eventName}),(0,s.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:[(0,s.jsx)(F.E,{className:(0,z.cn)("text-sm py-1 px-3 font-semibold",ev(e.status)),children:(0,k.fZ)(e.status)}),e.location&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{orientation:"vertical",className:"h-4"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400 truncate",children:e.location})]})]})]}),(0,s.jsxs)(eb.cj,{children:[(0,s.jsx)(eb.CG,{asChild:!0,children:(0,s.jsx)(w.$,{variant:"outline",size:"sm",children:(0,s.jsx)(ep.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(eb.h,{side:"bottom",className:"h-[80vh]",children:[(0,s.jsx)(eb.Fm,{children:(0,s.jsx)(eb.qp,{children:"Delegation Actions"})}),(0,s.jsx)("div",{className:"grid gap-4 py-4",children:(0,s.jsx)(l,{})})]})]})]}),(0,s.jsxs)("div",{className:"hidden lg:flex lg:items-center lg:justify-between",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-gray-900 dark:text-white",children:e.eventName}),(0,s.jsxs)("div",{className:"mt-2 flex items-center space-x-4",children:[(0,s.jsx)(F.E,{className:(0,z.cn)("text-sm py-1 px-3 font-semibold",ev(e.status)),children:(0,k.fZ)(e.status)}),e.location&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{orientation:"vertical",className:"h-4"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.location})]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)(l,{})})]})]})})}var ek=r(36644),ew=r(28946),eA=r(76288);function eC({icon:e,label:t,value:r,color:a,bgColor:l}){return(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:`p-2 rounded-full ${l}`,children:(0,s.jsx)(e,{className:`h-4 w-4 ${a}`})}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t})]}),(0,s.jsx)(F.E,{variant:"secondary",className:"font-semibold",children:r})]})}let eM=(e,t)=>{if(!e||!t)return"N/A";try{let r=(0,d.H)(e),s=(0,d.H)(t),a=(0,eA.c)(s,r)+1;return`${a} day${1!==a?"s":""}`}catch{return"Invalid"}};function eD({delegation:e,className:t}){let r=[{icon:_.A,label:"Delegates",value:e.delegates?.length||0,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-50 dark:bg-blue-900/30"},{icon:ei.A,label:"Escorts",value:e.escorts?.length||0,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-50 dark:bg-green-900/30"},{icon:j.A,label:"Drivers",value:e.drivers?.length||0,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-50 dark:bg-purple-900/30"},{icon:ed.A,label:"Vehicles",value:e.vehicles?.length||0,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-50 dark:bg-orange-900/30"},{icon:Y.A,label:"Flights",value:[e.arrivalFlight,e.departureFlight].filter(Boolean).length,color:"text-indigo-600 dark:text-indigo-400",bgColor:"bg-indigo-50 dark:bg-indigo-900/30"},{icon:v.A,label:"Duration",value:eM(e.durationFrom,e.durationTo),color:"text-gray-600 dark:text-gray-400",bgColor:"bg-gray-50 dark:bg-gray-900/30"}],a=(e.delegates?.length||0)+(e.escorts?.length||0)+(e.drivers?.length||0);return(0,s.jsxs)(P.Zp,{className:t,children:[(0,s.jsx)(P.aR,{children:(0,s.jsxs)(P.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Delegation Metrics"})]}),(0,s.jsxs)(F.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",children:[a," people total"]})]})}),(0,s.jsxs)(P.Wu,{className:"space-y-3",children:[r.map((e,t)=>(0,s.jsx)(eC,{icon:e.icon,label:e.label,value:e.value,color:e.color,bgColor:e.bgColor},t)),e.location&&(0,s.jsxs)("div",{className:"mt-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(Z.A,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Location"})]}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400 ml-6",children:e.location})]})]})]})}function eR({delegation:e,className:t}){return(0,s.jsx)("div",{className:t,children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(P.Zp,{children:[(0,s.jsx)(P.aR,{children:(0,s.jsx)(P.ZB,{className:"text-lg",children:"Quick Actions"})}),(0,s.jsxs)(P.Wu,{className:"space-y-3",children:[(0,s.jsx)(w.$,{variant:"outline",className:"w-full justify-start",size:"sm",asChild:!0,children:(0,s.jsxs)(eu(),{href:`/delegations/${e.id}/edit`,children:[(0,s.jsx)(eh.A,{className:"mr-2 h-4 w-4"}),"Edit Delegation"]})}),(0,s.jsx)(w.$,{variant:"outline",className:"w-full justify-start",size:"sm",asChild:!0,children:(0,s.jsxs)(eu(),{href:`/delegations/${e.id}/report`,target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(ek.A,{className:"mr-2 h-4 w-4"}),"View Report"]})}),(0,s.jsxs)(w.$,{variant:"outline",className:"w-full justify-start",size:"sm",onClick:()=>{},children:[(0,s.jsx)(ew.A,{className:"mr-2 h-4 w-4"}),"Print Details"]})]})]}),(0,s.jsx)(eD,{delegation:e}),(0,s.jsxs)(P.Zp,{children:[(0,s.jsx)(P.aR,{children:(0,s.jsx)(P.ZB,{className:"text-lg",children:"Status Information"})}),(0,s.jsxs)(P.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:e.status?.replace("_"," ")||"No Status"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Current Status"})]}),e.durationFrom&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:(0,n.GP)((0,d.H)(e.durationFrom),"MMM d, yyyy")}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Start Date"})]})]}),e.durationTo&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:(0,n.GP)((0,d.H)(e.durationTo),"MMM d, yyyy")}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"End Date"})]})]})]})]})]})})}var eF=r(12662),eP=r(52027),eE=r(48041),eq=r(3940),ez=r(63502);function eS(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),{showEntityDeleted:r,showEntityDeletionError:n,showEntityUpdated:d,showEntityUpdateError:o}=(0,eq.O_)("delegation"),c=e.id,{data:m,error:u,isLoading:x,refetch:h}=(0,ez.kA)(c),g=(0,ez.nB)(),p=(0,ez.lG)(),b=async()=>{if(m)try{await g.mutateAsync(m.id);let e={event:m.eventName,location:m.location};r(e),t.push("/delegations")}catch(e){console.error("Error deleting delegation:",e),n(e.message||"Failed to delete delegation. Please try again.")}},y=async(e,t)=>{if(m)try{await p.mutateAsync({id:m.id,status:e,statusChangeReason:t});let r={event:m.eventName,location:m.location};d(r)}catch(e){console.error("Error updating delegation status:",e),o(e.message||"Failed to update delegation status. Please try again.")}};return(0,s.jsx)(eP.gO,{data:m,emptyComponent:(0,s.jsxs)("div",{className:"py-10 text-center",children:[(0,s.jsx)(eE.z,{icon:a.A,title:"Delegation Not Found"}),(0,s.jsx)("p",{className:"mb-4",children:"The requested delegation could not be found."})]}),error:u?u.message:null,isLoading:x,loadingComponent:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(eE.z,{icon:l.A,title:"Loading Delegation..."}),(0,s.jsx)(eP.jt,{count:1,variant:"card"}),(0,s.jsxs)("div",{className:"grid items-start gap-6 md:grid-cols-3",children:[(0,s.jsx)(eP.jt,{className:"md:col-span-2",count:1,variant:"card"}),(0,s.jsx)(eP.jt,{count:1,variant:"card"})]})]}),onRetry:h,children:e=>(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("div",{className:"container mx-auto px-4 pt-6",children:(0,s.jsx)(eF.AppBreadcrumb,{})}),(0,s.jsx)(eN,{delegation:e,onDelete:b}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"grid gap-8 lg:grid-cols-4",children:[(0,s.jsx)("div",{className:"lg:col-span-3",children:(0,s.jsx)(ec,{delegation:e,onStatusUpdate:y})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(eR,{delegation:e})})]})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20755:(e,t,r)=>{Promise.resolve().then(r.bind(r,17967))},21820:e=>{"use strict";e.exports=require("os")},25521:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o={children:["",{children:["[locale]",{children:["delegations",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,17967)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\[id]\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/delegations/[id]/page",pathname:"/[locale]/delegations/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},26398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28946:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35137:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},38765:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},48409:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("PlaneTakeoff",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M6.36 17.4 4 17l-2-4 1.1-.55a2 2 0 0 1 1.8 0l.17.1a2 2 0 0 0 1.8 0L8 12 5 6l.9-.45a2 2 0 0 1 2.09.2l4.02 3a2 2 0 0 0 2.1.2l4.19-2.06a2.41 2.41 0 0 1 1.73-.17L21 7a1.4 1.4 0 0 1 .87 1.99l-.38.76c-.23.46-.6.84-1.07 1.08L7.58 17.2a2 2 0 0 1-1.22.18Z",key:"fkigj9"}]])},52856:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},54052:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55817:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},55925:(e,t,r)=>{"use strict";r.d(t,{Lt:()=>q,Rx:()=>Z,Zr:()=>L,EO:()=>T,$v:()=>H,ck:()=>O,wd:()=>I,r7:()=>$,tv:()=>z});var s=r(60687),a=r(43210),l=r(11273),i=r(98599),n=r(26134),d=r(70569),o=r(8730),c="AlertDialog",[m,u]=(0,l.A)(c,[n.Hs]),x=(0,n.Hs)(),h=e=>{let{__scopeAlertDialog:t,...r}=e,a=x(t);return(0,s.jsx)(n.bL,{...a,...r,modal:!0})};h.displayName=c;var g=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,l=x(r);return(0,s.jsx)(n.l9,{...l,...a,ref:t})});g.displayName="AlertDialogTrigger";var p=e=>{let{__scopeAlertDialog:t,...r}=e,a=x(t);return(0,s.jsx)(n.ZL,{...a,...r})};p.displayName="AlertDialogPortal";var b=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,l=x(r);return(0,s.jsx)(n.hJ,{...l,...a,ref:t})});b.displayName="AlertDialogOverlay";var y="AlertDialogContent",[f,j]=m(y),v=(0,o.Dc)("AlertDialogContent"),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:l,...o}=e,c=x(r),m=a.useRef(null),u=(0,i.s)(t,m),h=a.useRef(null);return(0,s.jsx)(n.G$,{contentName:y,titleName:k,docsSlug:"alert-dialog",children:(0,s.jsx)(f,{scope:r,cancelRef:h,children:(0,s.jsxs)(n.UC,{role:"alertdialog",...c,...o,ref:u,onOpenAutoFocus:(0,d.m)(o.onOpenAutoFocus,e=>{e.preventDefault(),h.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(v,{children:l}),(0,s.jsx)(F,{contentRef:m})]})})})});N.displayName=y;var k="AlertDialogTitle",w=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,l=x(r);return(0,s.jsx)(n.hE,{...l,...a,ref:t})});w.displayName=k;var A="AlertDialogDescription",C=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,l=x(r);return(0,s.jsx)(n.VY,{...l,...a,ref:t})});C.displayName=A;var M=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,l=x(r);return(0,s.jsx)(n.bm,{...l,...a,ref:t})});M.displayName="AlertDialogAction";var D="AlertDialogCancel",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:l}=j(D,r),d=x(r),o=(0,i.s)(t,l);return(0,s.jsx)(n.bm,{...d,...a,ref:o})});R.displayName=D;var F=({contentRef:e})=>{let t=`\`${y}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${y}\` by passing a \`${A}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${y}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return a.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},P=r(29523),E=r(22482);let q=h,z=g,S=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(b,{className:(0,E.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));S.displayName=b.displayName;let T=a.forwardRef(({className:e,...t},r)=>(0,s.jsxs)(p,{children:[(0,s.jsx)(S,{}),(0,s.jsx)(N,{className:(0,E.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),ref:r,...t})]}));T.displayName=N.displayName;let I=({className:e,...t})=>(0,s.jsx)("div",{className:(0,E.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});I.displayName="AlertDialogHeader";let O=({className:e,...t})=>(0,s.jsx)("div",{className:(0,E.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});O.displayName="AlertDialogFooter";let $=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(w,{className:(0,E.cn)("text-lg font-semibold",e),ref:r,...t}));$.displayName=w.displayName;let H=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(C,{className:(0,E.cn)("text-sm text-muted-foreground",e),ref:r,...t}));H.displayName=C.displayName;let Z=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(M,{className:(0,E.cn)((0,P.r)(),e),ref:r,...t}));Z.displayName=M.displayName;let L=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(R,{className:(0,E.cn)((0,P.r)({variant:"outline"}),"mt-2 sm:mt-0",e),ref:r,...t}));L.displayName=R.displayName},57207:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57707:(e,t,r)=>{Promise.resolve().then(r.bind(r,18810))},60368:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76242:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>n,ZI:()=>c,k$:()=>o,m_:()=>d});var s=r(60687),a=r(9989),l=r(43210),i=r(22482);let n=a.Kq,d=a.bL,o=a.l9,c=l.forwardRef(({className:e,sideOffset:t=4,...r},l)=>(0,s.jsx)(a.UC,{className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),ref:l,sideOffset:t,...r}));c.displayName=a.UC.displayName},76288:(e,t,r)=>{"use strict";r.d(t,{c:()=>l});var s=r(89106),a=r(47138);function l(e,t){let r=(0,a.a)(e),l=(0,a.a)(t),n=i(r,l),d=Math.abs((0,s.m)(r,l));r.setDate(r.getDate()-n*d);let o=Number(i(r,l)===-n),c=n*(d-o);return 0===c?0:c}function i(e,t){let r=e.getFullYear()-t.getFullYear()||e.getMonth()-t.getMonth()||e.getDate()-t.getDate()||e.getHours()-t.getHours()||e.getMinutes()-t.getMinutes()||e.getSeconds()-t.getSeconds()||e.getMilliseconds()-t.getMilliseconds();return r<0?-1:r>0?1:r}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},92876:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},93242:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("PlaneLanding",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M3.77 10.77 2 9l2-4.5 1.1.55c.55.28.9.84.9 1.45s.35 1.17.9 1.45L8 8.5l3-6 1.05.53a2 2 0 0 1 1.09 1.52l.72 5.4a2 2 0 0 0 1.09 1.52l4.4 2.2c.42.22.78.55 1.01.96l.6 1.03c.49.88-.06 1.98-1.06 2.1l-1.18.15c-.47.06-.95-.02-1.37-.24L4.29 11.15a2 2 0 0 1-.52-.38Z",key:"1ma21e"}]])},93425:(e,t,r)=>{"use strict";r.d(t,{E:()=>g});var s=r(43210),a=r(33465),l=r(5563),i=r(35536),n=r(31212);function d(e,t){let r=new Set(t);return e.filter(e=>!r.has(e))}var o=class extends i.Q{#e;#t;#r;#s;#a;#l;#i;#n;#d=[];constructor(e,t,r){super(),this.#e=e,this.#s=r,this.#r=[],this.#a=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#a.forEach(e=>{e.subscribe(t=>{this.#o(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#a.forEach(e=>{e.destroy()})}setQueries(e,t){this.#r=e,this.#s=t,a.jG.batch(()=>{let e=this.#a,t=this.#c(this.#r);this.#d=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let r=t.map(e=>e.observer),s=r.map(e=>e.getCurrentResult()),a=r.some((t,r)=>t!==e[r]);(e.length!==r.length||a)&&(this.#a=r,this.#t=s,this.hasListeners()&&(d(e,r).forEach(e=>{e.destroy()}),d(r,e).forEach(e=>{e.subscribe(t=>{this.#o(e,t)})}),this.#m()))})}getCurrentResult(){return this.#t}getQueries(){return this.#a.map(e=>e.getCurrentQuery())}getObservers(){return this.#a}getOptimisticResult(e,t){let r=this.#c(e),s=r.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[s,e=>this.#u(e??s,t),()=>this.#x(s,r)]}#x(e,t){return t.map((r,s)=>{let a=e[s];return r.defaultedQueryOptions.notifyOnChangeProps?a:r.observer.trackResult(a,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#u(e,t){return t?(this.#l&&this.#t===this.#n&&t===this.#i||(this.#i=t,this.#n=this.#t,this.#l=(0,n.BH)(this.#l,t(e))),this.#l):e}#c(e){let t=new Map(this.#a.map(e=>[e.options.queryHash,e])),r=[];return e.forEach(e=>{let s=this.#e.defaultQueryOptions(e),a=t.get(s.queryHash);a?r.push({defaultedQueryOptions:s,observer:a}):r.push({defaultedQueryOptions:s,observer:new l.$(this.#e,s)})}),r}#o(e,t){let r=this.#a.indexOf(e);-1!==r&&(this.#t=function(e,t,r){let s=e.slice(0);return s[t]=r,s}(this.#t,r,t),this.#m())}#m(){if(this.hasListeners()){let e=this.#l,t=this.#x(this.#t,this.#d);e!==this.#u(t,this.#s?.combine)&&a.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},c=r(8693),m=r(24903),u=r(18228),x=r(16142),h=r(76935);function g({queries:e,...t},r){let i=(0,c.jE)(r),d=(0,m.w)(),g=(0,u.h)(),p=s.useMemo(()=>e.map(e=>{let t=i.defaultQueryOptions(e);return t._optimisticResults=d?"isRestoring":"optimistic",t}),[e,i,d]);p.forEach(e=>{(0,h.jv)(e),(0,x.LJ)(e,g)}),(0,x.wZ)(g);let[b]=s.useState(()=>new o(i,p,t)),[y,f,j]=b.getOptimisticResult(p,t.combine),v=!d&&!1!==t.subscribed;s.useSyncExternalStore(s.useCallback(e=>v?b.subscribe(a.jG.batchCalls(e)):n.lQ,[b,v]),()=>b.getCurrentResult(),()=>b.getCurrentResult()),s.useEffect(()=>{b.setQueries(p,t)},[p,t,b]);let N=y.some((e,t)=>(0,h.EU)(p[t],e))?y.flatMap((e,t)=>{let r=p[t];if(r){let t=new l.$(i,r);if((0,h.EU)(r,e))return(0,h.iL)(r,t,g);(0,h.nE)(e,d)&&(0,h.iL)(r,t,g)}return[]}):[];if(N.length>0)throw Promise.all(N);let k=y.find((e,t)=>{let r=p[t];return r&&(0,x.$1)({result:e,errorResetBoundary:g,throwOnError:r.throwOnError,query:i.getQueryCache().get(r.queryHash),suspense:r.suspense})});if(k?.error)throw k.error;return f(j())}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,4825,625,9851,8390,2670,474,6006,2482,9794,3502,9922,622],()=>r(25521));module.exports=s})();