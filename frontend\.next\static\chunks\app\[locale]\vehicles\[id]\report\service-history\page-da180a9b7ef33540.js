(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9239],{33271:(e,s,a)=>{"use strict";a.d(s,{k:()=>p});var i=a(95155),r=a(18018),t=a(50172),d=a(68718),l=a(15300),n=a(60679),c=a(12115),o=a(6560),m=a(44838),x=a(53712),h=a(54036),f=a(16146);function p(e){let{className:s,csvData:a,enableCsv:p=!1,entityId:j,fileName:v,reportContentId:u,reportType:N,tableId:g}=e,[b,y]=(0,c.useState)(!1),[w,k]=(0,c.useState)(!1),{showFormSuccess:D,showFormError:C}=(0,x.t6)(),S=async()=>{y(!0);try{let e="/api/reports/".concat(N).concat(j?"/".concat(j):""),s=document.createElement("a");s.href=e,s.download="".concat(v,".pdf"),s.target="_blank",document.body.append(s),s.click(),s.remove(),D({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),C("PDF download failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{y(!1)}},P=async()=>{if(p){k(!0);try{if((null==a?void 0:a.data)&&a.headers)(0,f.og)(a.data,a.headers,"".concat(v,".csv"));else if(g){let e=(0,f.tL)(g);(0,f.og)(e.data,e.headers,"".concat(v,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");D({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),C("CSV generation failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{k(!1)}}},V=b||w;return(0,i.jsxs)("div",{className:(0,h.cn)("flex items-center gap-2 no-print",s),children:[(0,i.jsx)(o.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,i.jsx)(r.A,{className:"size-4"})}),(0,i.jsxs)(m.rI,{children:[(0,i.jsx)(m.ty,{asChild:!0,children:(0,i.jsx)(o.r,{actionType:"secondary","aria-label":"Download report",disabled:V,size:"icon",title:"Download Report",children:V?(0,i.jsx)(t.A,{className:"size-4 animate-spin"}):(0,i.jsx)(d.A,{className:"size-4"})})}),(0,i.jsxs)(m.SQ,{align:"end",children:[(0,i.jsxs)(m._2,{disabled:b,onClick:S,children:[b?(0,i.jsx)(t.A,{className:"mr-2 size-4 animate-spin"}):(0,i.jsx)(l.A,{className:"mr-2 size-4"}),(0,i.jsx)("span",{children:"Download PDF"})]}),p&&(0,i.jsxs)(m._2,{disabled:w,onClick:P,children:[w?(0,i.jsx)(t.A,{className:"mr-2 size-4 animate-spin"}):(0,i.jsx)(n.A,{className:"mr-2 size-4"}),(0,i.jsx)("span",{children:"Download CSV"})]})]})]})]})}},53787:(e,s,a)=>{Promise.resolve().then(a.bind(a,54573))},54573:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>D});var i=a(95155),r=a(11518),t=a.n(r),d=a(3638),l=a(28328),n=a(66766),c=a(6874),o=a.n(c),m=a(35695),x=a(12115),h=a(88240),f=a(33271),p=a(58824),j=a(6560),v=a(30285),u=a(66695),N=a(77023),g=a(95647),b=a(40283),y=a(80937),w=a(98691),k=a(50546);function D(){let e=(0,m.useParams)(),{loading:s,session:a,user:r}=(0,b.useAuthContext)(),c=!!r&&!!(null==a?void 0:a.access_token),D=null==e?void 0:e.id,{data:C,error:S,isLoading:P,refetch:V}=(0,y.W_)(Number(D),{enabled:c}),{data:A=[],error:z,isLoading:T,refetch:_}=(0,w.xH)(Number(D),{enabled:c&&!!(null==C?void 0:C.id)});(0,x.useEffect)(()=>{C&&(document.title="".concat(C.make," ").concat(C.model," - Service History Report"))},[C]);let L=(0,x.useCallback)(()=>{V(),_()},[V,_]),O=S||z;return s||P||T?(0,i.jsx)(h.A,{children:(0,i.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(N.jt,{count:1,variant:"card"}),(0,i.jsx)(N.jt,{count:5,variant:"table"})]})})}):O||!C?(0,i.jsx)(h.A,{children:(0,i.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,i.jsx)(u.Zp,{className:"shadow-md",children:(0,i.jsxs)(u.Wu,{className:"p-6",children:[(0,i.jsx)("h2",{className:"mb-2 text-xl font-semibold text-red-600",children:"Error"}),(0,i.jsx)("p",{className:"mb-4 text-gray-700",children:(0,k.u1)(O)||"Vehicle not found"}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(v.$,{onClick:L,children:"Try Again"}),(0,i.jsx)(v.$,{asChild:!0,variant:"outline",children:(0,i.jsx)(o(),{href:"/vehicles",children:"Back to Vehicles"})})]})]})})})}):(0,i.jsx)(h.A,{children:(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86 print-container mx-auto max-w-5xl space-y-6 p-4",children:[(0,i.jsx)(g.z,{description:"".concat(C.make," ").concat(C.model," (").concat(C.year,")"),icon:d.A,title:"Vehicle Service History",children:(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86 no-print flex gap-2",children:[(0,i.jsx)(j.r,{actionType:"tertiary",asChild:!0,icon:(0,i.jsx)(l.A,{className:"size-4"}),children:(0,i.jsx)(o(),{href:"/vehicles/".concat(D),children:"View Vehicle"})}),(0,i.jsx)(f.k,{enableCsv:A.length>0,entityId:D,fileName:"vehicle-service-history-".concat(C.make,"-").concat(C.model),reportContentId:"#vehicle-service-history-content",reportType:"vehicle-service-history",tableId:"#service-history-table"})]})}),(0,i.jsxs)("div",{id:"vehicle-service-history-content",className:"jsx-690e25f84e8ddf86 report-content",children:[(0,i.jsx)(u.Zp,{className:"card-print mb-6 shadow-md",children:(0,i.jsx)(u.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86 col-span-1 md:col-span-2",children:[(0,i.jsx)("h2",{className:"jsx-690e25f84e8ddf86 mb-4 text-xl font-semibold text-gray-800",children:"Vehicle Details"}),(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86 grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,i.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Make:"})," ",C.make]}),(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,i.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Model:"})," ",C.model]}),(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,i.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Year:"})," ",C.year]}),C.licensePlate&&(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,i.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Plate Number:"})," ",C.licensePlate]}),C.color&&(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,i.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Color:"})," ",C.color]}),(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,i.jsx)("strong",{className:"jsx-690e25f84e8ddf86",children:"Initial Odometer:"})," ",null!==C.initialOdometer&&void 0!==C.initialOdometer?"".concat(C.initialOdometer.toLocaleString()," miles"):"Not recorded"]}),(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,i.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Current Odometer:"})," ",A.length>0?"".concat(Math.max(...A.map(e=>e.odometer)).toLocaleString()," miles"):null!==C.initialOdometer&&void 0!==C.initialOdometer?"".concat(C.initialOdometer.toLocaleString()," miles"):"No odometer data available"]}),(0,i.jsxs)("div",{className:"jsx-690e25f84e8ddf86 col-span-2",children:[(0,i.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Last Updated:"})," ",A.length>0?new Date(Math.max(...A.map(e=>new Date(e.date).getTime()))).toLocaleDateString():"No service records"]})]})]}),C.imageUrl&&(0,i.jsx)("div",{className:"jsx-690e25f84e8ddf86 no-print col-span-1",children:(0,i.jsx)("div",{className:"jsx-690e25f84e8ddf86 relative aspect-[4/3] w-full overflow-hidden rounded",children:(0,i.jsx)(n.default,{alt:"".concat(C.make," ").concat(C.model),fill:!0,sizes:"(max-width: 768px) 100vw, 300px",src:C.imageUrl,style:{objectFit:"cover"}})})})]})})}),(0,i.jsxs)("header",{className:"jsx-690e25f84e8ddf86 print-only mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,i.jsx)("h1",{className:"jsx-690e25f84e8ddf86 text-3xl font-bold text-gray-800",children:"Vehicle Service History Report"}),(0,i.jsxs)("p",{className:"jsx-690e25f84e8ddf86 text-md text-gray-600",children:[C.make," ",C.model," (",C.year,")",C.licensePlate&&" - ".concat(C.licensePlate)]})]}),(0,i.jsx)(p.R,{error:null,isLoading:!1,onRetry:L,records:A,showVehicleInfo:!1,vehicleSpecific:!0}),(0,i.jsxs)("footer",{className:"jsx-690e25f84e8ddf86 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,i.jsxs)("p",{className:"jsx-690e25f84e8ddf86",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,i.jsx)("p",{className:"jsx-690e25f84e8ddf86",children:"WorkHub - Vehicle Service Management"})]})]}),(0,i.jsx)(t(),{id:"690e25f84e8ddf86",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-service-col{max-width:200px;white-space:normal!important}.print-notes-col{max-width:200px;white-space:normal!important}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.overflow-x-auto{overflow-x:auto}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,6874,5247,6463,6766,9607,4036,4767,8950,3712,3615,5320,6554,6563,8441,1684,7358],()=>s(53787)),_N_E=e.O()}]);