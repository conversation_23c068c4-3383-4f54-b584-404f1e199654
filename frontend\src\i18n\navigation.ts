/**
 * @file i18n Navigation Configuration
 * @module i18n/navigation
 *
 * Provides locale-aware navigation utilities for next-intl.
 * These are lightweight wrappers around Next.js navigation APIs
 * that consider the routing configuration.
 */

import { createNavigation } from 'next-intl/navigation';
import { routing } from './routing';

/**
 * Locale-aware navigation utilities
 * 
 * These utilities automatically handle locale prefixes and routing
 * based on the routing configuration.
 */
export const { Link, redirect, usePathname, useRouter, getPathname } =
  createNavigation(routing);
