(()=>{var e={};e.id=9677,e.ids=[9677],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32232:(e,r,s)=>{Promise.resolve().then(s.bind(s,41187))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,r,s)=>{"use strict";s.d(r,{T:()=>n});var t=s(60687),i=s(43210),a=s(22482);let n=i.forwardRef(({className:e,...r},s)=>(0,t.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...r}));n.displayName="Textarea"},41187:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>T});var t=s(60687),i=s(63442),a=s(55817),n=s(57207),o=s(14975),c=s(38765),d=s(71273),l=s(85814),m=s.n(l),u=s(16189);s(43210);var x=s(27605),p=s(24920),h=s(85726),v=s(68012);function f({vehicleInfo:e}){return(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Vehicle:"}),(0,t.jsxs)("span",{className:"font-semibold text-foreground",children:[e.make," ",e.model," (",e.year,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:"License Plate:"}),(0,t.jsx)("span",{className:"font-mono font-semibold text-foreground",children:e.licensePlate})]})]})}function j(){return(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Vehicle:"}),(0,t.jsx)(h.E,{className:"h-4 w-32"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:"License Plate:"}),(0,t.jsx)(h.E,{className:"h-4 w-20"})]})]})}function g(){return(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Vehicle information unavailable"})]})}function y({vehicleId:e,className:r}){let{vehicleInfo:s,isLoading:i,error:a}=(0,v.g)(e);return(0,t.jsxs)("div",{className:r,children:[i&&(0,t.jsx)(j,{}),a&&(0,t.jsx)(g,{}),s&&(0,t.jsx)(f,{vehicleInfo:s})]})}var b=s(91821),N=s(55925),C=s(29523),S=s(44493),P=s(71669),I=s(89667),w=s(35950),k=s(34729),R=s(3389),q=s(65574),A=s(2775);function T(){let e=(0,u.useParams)(),r=(0,u.useRouter)(),{toast:s}=(0,R.dj)(),l=e?.id||"",p=(0,A.Ln)(),v=(0,A.xT)(),{data:f,error:j,isLoading:g}=(0,A.WV)(l,{enabled:!!l&&"string"==typeof e?.id}),T=(0,x.mN)({defaultValues:{cost:0,date:"",employeeId:null,notes:"",odometer:0,servicePerformed:[]},mode:"onChange",resolver:(0,i.u)(q.mm)});if(!e||"string"!=typeof e.id)return(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,t.jsx)("p",{children:"Error: Invalid or missing Service Record ID."}),(0,t.jsx)(C.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(m(),{href:"/service-history",children:[(0,t.jsx)(a.A,{className:"size-4"})," Back to Service History"]})})]});let E=async e=>{if(!f)return void s({description:"Service record data is not available. Please refresh the page.",title:"Error",variant:"destructive"});if(!f.vehicleId){s({description:"Vehicle ID is missing. Cannot update service record.",title:"Error",variant:"destructive"}),console.error("Service record missing vehicleId:",f);return}let t=q.fe.transformToUpdatePayload(e);try{await p.mutateAsync({data:t,id:l,vehicleId:f.vehicleId}),s({description:"Service record updated successfully! \uD83C\uDF89",title:"Success!",variant:"default"}),r.push(`/service-records/${l}`)}catch(e){console.error("Failed to update service record:",e),s({description:"Failed to update service record. Please try again.",title:"Error",variant:"destructive"})}},z=async()=>{if(!f)return void s({description:"Service record data is not available.",title:"Error",variant:"destructive"});if(!f.vehicleId){s({description:"Vehicle ID is missing. Cannot delete service record.",title:"Error",variant:"destructive"}),console.error("Service record missing vehicleId:",f);return}try{await v.mutateAsync({id:l,vehicleId:f.vehicleId}),s({description:"Service record deleted successfully.",title:"Deleted!",variant:"default"}),r.push("/service-history")}catch(e){console.error("Failed to delete service record:",e),s({description:"Failed to delete service record. Please try again.",title:"Error",variant:"destructive"})}},F=e=>{let r=q.fe.parseServicePerformed(e);T.setValue("servicePerformed",r,{shouldValidate:!0})};return g?(0,t.jsx)("div",{className:"container mx-auto py-8",children:(0,t.jsxs)(S.Zp,{children:[(0,t.jsx)(S.aR,{children:(0,t.jsx)(h.E,{className:"h-8 w-3/4"})}),(0,t.jsxs)(S.Wu,{className:"space-y-4",children:[(0,t.jsx)(h.E,{className:"h-4 w-full"}),(0,t.jsx)(h.E,{className:"h-4 w-full"}),(0,t.jsx)(h.E,{className:"h-4 w-2/3"})]})]})}):j?(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,t.jsxs)("p",{children:["Error loading service record: ",j.message]}),(0,t.jsx)(C.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(m(),{href:"/service-history",children:[(0,t.jsx)(a.A,{className:"mr-2 size-4"})," Back to Service History"]})})]}):f?(0,t.jsxs)("div",{className:"container mx-auto space-y-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(C.$,{asChild:!0,size:"sm",variant:"outline",children:(0,t.jsxs)(m(),{href:`/service-records/${f.id}`,children:[(0,t.jsx)(a.A,{className:"mr-2 size-4"})," Back to Details"]})}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)(N.Lt,{children:[(0,t.jsx)(N.tv,{asChild:!0,children:(0,t.jsxs)(C.$,{disabled:v.isPending,size:"sm",variant:"destructive",children:[(0,t.jsx)(n.A,{className:"mr-2 size-4"}),"Delete"]})}),(0,t.jsxs)(N.EO,{children:[(0,t.jsxs)(N.wd,{children:[(0,t.jsxs)(N.r7,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"size-5 text-destructive"}),"Delete Service Record"]}),(0,t.jsxs)(N.$v,{children:["Are you sure you want to delete this service record? This action cannot be undone.",(0,t.jsxs)("div",{className:"mt-2 rounded-md bg-muted p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Record Details:"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[f.servicePerformed.join(", ")," -"," ",new Date(f.date).toLocaleDateString()]})]})]})]}),(0,t.jsxs)(N.ck,{children:[(0,t.jsx)(N.Zr,{children:"Cancel"}),(0,t.jsx)(N.Rx,{className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",onClick:z,children:"Delete Record"})]})]})]})})]}),T.formState.isValid&&T.formState.isDirty&&(0,t.jsxs)(b.Fc,{className:"border-green-200 bg-green-50",children:[(0,t.jsx)(c.A,{className:"size-4 text-green-600"}),(0,t.jsx)(b.TN,{className:"text-green-800",children:"All fields are valid. Ready to save changes."})]}),(0,t.jsxs)(S.Zp,{children:[(0,t.jsx)(S.aR,{children:(0,t.jsxs)(S.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"size-5"}),"Edit Service Record"]})}),(0,t.jsx)(S.Wu,{children:(0,t.jsx)(P.lV,{...T,children:(0,t.jsxs)("form",{className:"space-y-6",onSubmit:T.handleSubmit(E),children:[(0,t.jsx)("div",{className:"rounded-lg bg-muted/50 p-4",children:(0,t.jsx)(y,{vehicleId:f.vehicleId})}),(0,t.jsx)(w.w,{}),(0,t.jsx)(P.zB,{control:T.control,name:"date",render:({field:e})=>(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Service Date"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(I.p,{type:"date",...e})}),(0,t.jsx)(P.C5,{})]})}),(0,t.jsx)(P.zB,{control:T.control,name:"servicePerformed",render:({field:e})=>(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Services Performed"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(I.p,{onChange:e=>F(e.target.value),placeholder:"Oil change, brake inspection, tire rotation...",value:q.fe.formatServicePerformed(e.value||[])})}),(0,t.jsxs)(P.Rr,{children:["Enter services separated by commas."," ",e.value?.length||0,"/10 services"]}),(0,t.jsx)(P.C5,{})]})}),(0,t.jsx)(P.zB,{control:T.control,name:"odometer",render:({field:e})=>(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Odometer Reading"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(I.p,{placeholder:"0",type:"number",...e,onChange:r=>e.onChange(Number(r.target.value)||0)})}),(0,t.jsx)(P.Rr,{children:"Current mileage on the vehicle"}),(0,t.jsx)(P.C5,{})]})}),(0,t.jsx)(P.zB,{control:T.control,name:"cost",render:({field:e})=>(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Total Cost"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(I.p,{placeholder:"0.00",step:"0.01",type:"number",...e,onChange:r=>e.onChange(Number(r.target.value)||0)})}),(0,t.jsx)(P.Rr,{children:"Total cost of the service in dollars"}),(0,t.jsx)(P.C5,{})]})}),(0,t.jsx)(P.zB,{control:T.control,name:"notes",render:({field:e})=>(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Notes (Optional)"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(k.T,{className:"min-h-[100px]",placeholder:"Additional notes about the service...",...e})}),(0,t.jsxs)(P.Rr,{children:[e.value?.length??0,"/1000 characters"]}),(0,t.jsx)(P.C5,{})]})}),(0,t.jsx)(w.w,{}),(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)(C.$,{asChild:!0,type:"button",variant:"outline",children:(0,t.jsx)(m(),{href:`/service-records/${l}`,children:"Cancel"})}),(0,t.jsx)(C.$,{className:"min-w-[120px]",disabled:p.isPending||!T.formState.isValid,type:"submit",children:p.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"mr-2 size-4 animate-spin rounded-full border-2 border-background border-t-transparent"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 size-4"}),"Save Changes"]})})]})]})})})]})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-gray-500",children:[(0,t.jsx)("p",{children:"No service record data available for editing."}),(0,t.jsx)(C.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(m(),{href:"/service-history",children:[(0,t.jsx)(a.A,{className:"mr-2 size-4"})," Back to Service History"]})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65574:(e,r,s)=>{"use strict";s.d(r,{fe:()=>c,mm:()=>n,oS:()=>o});var t=s(9275),i=s(65594);let a={date:t.Yj().min(1,"Date is required"),servicePerformed:t.YO(t.Yj().min(1,"Service cannot be empty")).min(1,"At least one service must be performed").max(10,"Maximum 10 services allowed"),odometer:t.au.number({required_error:"Odometer reading is required"}).min(0,"Odometer cannot be negative").max(9999999,"Odometer reading seems unrealistic"),cost:t.au.number({required_error:"Cost is required"}).min(0,"Cost cannot be negative").max(999999,"Cost seems unrealistic"),notes:t.Yj().max(1e3,"Notes cannot exceed 1000 characters").optional(),employeeId:t.au.number().nullable().optional()},n=t.Ik(a),o=t.Ik({...a,vehicleId:t.ai().positive("Vehicle ID is required")});t.Ik(a).partial();let c={validateFormData:e=>n.safeParse(e),transformToApiPayload:(e,r)=>{let s={date:e.date,servicePerformed:e.servicePerformed,odometer:e.odometer,vehicleId:r};return"number"==typeof e.cost&&e.cost>=0&&(s.cost=e.cost),e.notes&&e.notes.trim().length>0&&(s.notes=e.notes.trim()),void 0!==e.employeeId&&(s.employeeId=e.employeeId),s},transformToUpdatePayload:e=>{let r={};return e.date&&(r.date=(0,i.B7)(e.date,{primaryFormat:"US",fallbackToBothFormats:!0})),void 0!==e.servicePerformed&&(r.servicePerformed=e.servicePerformed),void 0!==e.odometer&&(r.odometer=e.odometer),"number"==typeof e.cost&&e.cost>0?r.cost=e.cost:0===e.cost&&(r.cost=void 0),void 0!==e.notes&&e.notes&&e.notes.trim().length>0&&(r.notes=e.notes.trim()),void 0!==e.employeeId&&(r.employeeId=e.employeeId),r},validateServicePerformed:e=>e.length>0&&e.length<=10&&e.every(e=>e.trim().length>0),formatServicePerformed:e=>e.join(", "),parseServicePerformed:e=>e.split(",").map(e=>e.trim()).filter(e=>e.length>0)}},68680:(e,r,s)=>{Promise.resolve().then(s.bind(s,83229))},71273:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},71669:(e,r,s)=>{"use strict";s.d(r,{C5:()=>j,MJ:()=>v,Rr:()=>f,eI:()=>p,lR:()=>h,lV:()=>d,zB:()=>m});var t=s(60687),i=s(43210),a=s(8730),n=s(27605),o=s(22482),c=s(80013);let d=n.Op,l=i.createContext({}),m=({...e})=>(0,t.jsx)(l.Provider,{value:{name:e.name},children:(0,t.jsx)(n.xI,{...e})}),u=()=>{let e=i.useContext(l),r=i.useContext(x),{getFieldState:s,formState:t}=(0,n.xW)(),a=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...a}},x=i.createContext({}),p=i.forwardRef(({className:e,...r},s)=>{let a=i.useId();return(0,t.jsx)(x.Provider,{value:{id:a},children:(0,t.jsx)("div",{ref:s,className:(0,o.cn)("space-y-2",e),...r})})});p.displayName="FormItem";let h=i.forwardRef(({className:e,...r},s)=>{let{error:i,formItemId:a}=u();return(0,t.jsx)(c.J,{ref:s,className:(0,o.cn)(i&&"text-destructive",e),htmlFor:a,...r})});h.displayName="FormLabel";let v=i.forwardRef(({...e},r)=>{let{error:s,formItemId:i,formDescriptionId:n,formMessageId:o}=u();return(0,t.jsx)(a.DX,{ref:r,id:i,"aria-describedby":s?`${n} ${o}`:`${n}`,"aria-invalid":!!s,...e})});v.displayName="FormControl";let f=i.forwardRef(({className:e,...r},s)=>{let{formDescriptionId:i}=u();return(0,t.jsx)("p",{ref:s,id:i,className:(0,o.cn)("text-sm text-muted-foreground",e),...r})});f.displayName="FormDescription";let j=i.forwardRef(({className:e,children:r,...s},i)=>{let{error:a,formMessageId:n}=u(),c=a?String(a?.message??""):r;return c?(0,t.jsx)("p",{ref:i,id:n,className:(0,o.cn)("text-sm font-medium text-destructive",e),...s,children:c}):null});j.displayName="FormMessage"},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83229:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\service-records\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\service-records\\[id]\\edit\\page.tsx","default")},83997:e=>{"use strict";e.exports=require("tty")},89401:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>u,tree:()=>d});var t=s(65239),i=s(48088),a=s(88170),n=s.n(a),o=s(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(r,c);let d={children:["",{children:["[locale]",{children:["service-records",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83229)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\service-records\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\service-records\\[id]\\edit\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/service-records/[id]/edit/page",pathname:"/[locale]/service-records/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,4825,625,9851,8390,9275,6013,2482,9794,6813],()=>s(89401));module.exports=t})();