/**
 * @file Gift Query Hooks
 * @description React Query hooks for gift management operations
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { UseQueryOptions } from '@tanstack/react-query';

import { useCrudQuery } from '@/hooks/api/useSmartQuery';
import { useNotifications } from '@/hooks/ui/useNotifications';
import { giftApiService } from '@/lib/api/services';
import type { CreateGiftData, Gift, UpdateGiftData } from '@/lib/types/domain';
import type { ApiError } from '@/lib/types/errors';

// Query keys for gifts
export const giftQueryKeys = {
  all: ['gifts'] as const,
  lists: () => [...giftQueryKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...giftQueryKeys.lists(), { filters }] as const,
  details: () => [...giftQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...giftQueryKeys.details(), id] as const,
  byRecipient: (recipientId: string) => [...giftQueryKeys.all, 'recipient', recipientId] as const,
  byOccasion: (occasion: string) => [...giftQueryKeys.all, 'occasion', occasion] as const,
  bySender: (senderName: string) => [...giftQueryKeys.all, 'sender', senderName] as const,
  recent: () => [...giftQueryKeys.all, 'recent'] as const,
};

/**
 * Hook to fetch all gifts with optional filtering
 */
export const useGifts = (
  filters?: Record<string, unknown>,
  options?: Omit<UseQueryOptions<Gift[], ApiError>, 'queryKey' | 'queryFn'>
) => {
  return useCrudQuery<Gift[], ApiError>(
    giftQueryKeys.list(filters || {}),
    () => giftApiService.getAll(filters).then(result => result.data),
    'gift',
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
};

/**
 * Hook to fetch a single gift by ID
 */
export const useGift = (
  id: string | null,
  options?: Omit<UseQueryOptions<Gift, ApiError>, 'queryKey' | 'queryFn' | 'enabled'> & { enabled?: boolean }
) => {
  return useCrudQuery<Gift, ApiError>(
    giftQueryKeys.detail(id!),
    () => giftApiService.getById(id!),
    'gift',
    {
      enabled: !!id && (options?.enabled ?? true),
      staleTime: 5 * 60 * 1000,
      ...options,
    }
  );
};

/**
 * Hook to fetch gifts by recipient
 */
export const useGiftsByRecipient = (
  recipientId: string | null,
  options?: Omit<UseQueryOptions<Gift[], ApiError>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: giftQueryKeys.byRecipient(recipientId!),
    queryFn: () => giftApiService.getByRecipient(recipientId!),
    enabled: !!recipientId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook to fetch gifts by occasion
 */
export const useGiftsByOccasion = (
  occasion: string | null,
  options?: Omit<UseQueryOptions<Gift[], ApiError>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: giftQueryKeys.byOccasion(occasion!),
    queryFn: () => giftApiService.getByOccasion(occasion!),
    enabled: !!occasion,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook to fetch recent gifts
 */
export const useRecentGifts = (
  options?: Omit<UseQueryOptions<Gift[], ApiError>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: giftQueryKeys.recent(),
    queryFn: () => giftApiService.getRecent(),
    staleTime: 2 * 60 * 1000, // 2 minutes for recent data
    ...options,
  });
};

/**
 * Hook to create a new gift
 */
export const useCreateGift = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation({
    mutationFn: (data: CreateGiftData) => giftApiService.create(data),
    onSuccess: (newGift) => {
      // Invalidate and refetch gift queries
      queryClient.invalidateQueries({ queryKey: giftQueryKeys.all });
      
      // Also invalidate recipient queries if the gift has a recipient
      if (newGift.recipientId) {
        queryClient.invalidateQueries({ 
          queryKey: ['recipients', 'detail', newGift.recipientId] 
        });
      }

      showSuccess('Gift created successfully');
    },
    onError: (error: ApiError) => {
      showError(`Failed to create gift: ${error.message}`);
    },
  });
};

/**
 * Hook to update an existing gift
 */
export const useUpdateGift = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateGiftData }) =>
      giftApiService.update(id, data),
    onSuccess: (updatedGift) => {
      // Update the specific gift in cache
      queryClient.setQueryData(
        giftQueryKeys.detail(updatedGift.id),
        updatedGift
      );
      
      // Invalidate list queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: giftQueryKeys.lists() });
      
      // Also invalidate recipient queries if the gift has a recipient
      if (updatedGift.recipientId) {
        queryClient.invalidateQueries({ 
          queryKey: ['recipients', 'detail', updatedGift.recipientId] 
        });
      }

      showSuccess('Gift updated successfully');
    },
    onError: (error: ApiError) => {
      showError(`Failed to update gift: ${error.message}`);
    },
  });
};

/**
 * Hook to delete a gift
 */
export const useDeleteGift = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation({
    mutationFn: (id: string) => giftApiService.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove the gift from cache
      queryClient.removeQueries({ queryKey: giftQueryKeys.detail(deletedId) });
      
      // Invalidate list queries
      queryClient.invalidateQueries({ queryKey: giftQueryKeys.lists() });
      
      // Invalidate all recipient queries since we don't know which recipient was affected
      queryClient.invalidateQueries({ queryKey: ['recipients'] });

      showSuccess('Gift deleted successfully');
    },
    onError: (error: ApiError) => {
      showError(`Failed to delete gift: ${error.message}`);
    },
  });
};
