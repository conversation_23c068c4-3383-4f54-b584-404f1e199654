'use client';

import { Gift, PlusCircle, Search, Settings } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import React, { useState } from 'react';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { GiftFilters, GiftList } from '@/components/features/gifts';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { useGifts } from '@/lib/stores/queries/useGifts';
import { useRecipientOptions } from '@/lib/stores/queries/useRecipients';

interface GiftFilterValues {
  dateRange: {
    from?: Date;
    to?: Date;
  };
  occasion: string;
  recipientId: string;
  search: string;
  senderName: string;
}

export default function GiftsPage() {
  const t = useTranslations('gifts');
  const tCommon = useTranslations('common');
  const tForms = useTranslations('forms');

  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<GiftFilterValues>({
    dateRange: {},
    occasion: '',
    recipientId: '',
    search: '',
    senderName: '',
  });

  // Build query filters for API
  const queryFilters = {
    ...(filters.search && { search: filters.search }),
    ...(filters.recipientId && { recipientId: filters.recipientId }),
    ...(filters.occasion && { occasion: filters.occasion }),
    ...(filters.senderName && { senderName: filters.senderName }),
    ...(filters.dateRange.from && {
      startDate: filters.dateRange.from.toISOString(),
    }),
    ...(filters.dateRange.to && {
      endDate: filters.dateRange.to.toISOString(),
    }),
  };

  // Fetch data
  const {
    data: gifts = [],
    error,
    isLoading,
    refetch,
  } = useGifts(queryFilters);
  const { data: recipientOptions = [] } = useRecipientOptions();

  // Handle filter changes
  const handleFiltersChange = (newFilters: GiftFilterValues) => {
    setFilters(newFilters);
    setSearchTerm(newFilters.search);
  };

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <AppBreadcrumb homeHref="/" homeLabel={tCommon('dashboard')} />
        <PageHeader
          description={t('pageDescription')}
          icon={Gift}
          title={t('pageTitle')}
        >
          <div className="flex items-center gap-2">
            <ActionButton
              actionType="primary"
              asChild
              icon={<PlusCircle className="mr-2 size-4" />}
            >
              <Link href="/gifts/add">{t('addGift')}</Link>
            </ActionButton>

            {/* Settings Button */}
            <Dialog>
              <DialogTrigger asChild>
                <ActionButton
                  actionType="secondary"
                  icon={<Settings className="size-4" />}
                >
                  {tCommon('settings')}
                </ActionButton>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogTitle>{t('giftSettings')}</DialogTitle>
                <DialogDescription>
                  {t('giftSettingsDescription')}
                </DialogDescription>
                {/* TODO: Add gift dashboard settings component */}
                <div className="p-4 text-center text-muted-foreground">
                  {tCommon('comingSoon')}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </PageHeader>

        {/* Search and Filters */}
        <div className="mb-6 rounded-lg bg-card p-4 shadow-md">
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                className="pl-10"
                onChange={e => {
                  setSearchTerm(e.target.value);
                  setFilters(prev => ({ ...prev, search: e.target.value }));
                }}
                placeholder={tForms('placeholders.searchGifts')}
                value={searchTerm}
              />
            </div>
          </div>

          <GiftFilters
            filters={filters}
            gifts={gifts || []}
            hasActiveFilters={Object.keys(filters).length > 0}
            onClearFilters={() => setFilters({})}
            onFilterChange={handleFiltersChange}
          />
        </div>

        {/* Gift List */}
        <DataLoader
          data={gifts}
          emptyComponent={
            <div className="rounded-lg bg-card py-12 text-center shadow-md">
              <Gift className="mx-auto mb-6 size-16 text-muted-foreground" />
              <h3 className="mb-2 text-2xl font-semibold text-foreground">
                {searchTerm || Object.values(queryFilters).some(Boolean)
                  ? t('noGiftsMatch')
                  : t('noGiftsYet')}
              </h3>
              <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
                {searchTerm || Object.values(queryFilters).some(Boolean)
                  ? t('tryAdjustingFilters')
                  : t('getStartedWithGifts')}
              </p>
              {!searchTerm && !Object.values(queryFilters).some(Boolean) && (
                <ActionButton
                  actionType="primary"
                  asChild
                  icon={<PlusCircle className="size-4" />}
                  size="lg"
                >
                  <Link href="/gifts/add">{t('addFirstGift')}</Link>
                </ActionButton>
              )}
            </div>
          }
          error={error?.message || null}
          isLoading={isLoading}
          loadingComponent={<SkeletonLoader count={3} variant="card" />}
          onRetry={refetch}
        >
          {giftsData => <GiftList gifts={giftsData} />}
        </DataLoader>
      </div>
    </ErrorBoundary>
  );
}
