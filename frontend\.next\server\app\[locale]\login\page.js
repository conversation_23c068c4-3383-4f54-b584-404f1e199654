(()=>{var e={};e.id=5495,e.ids=[5495],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20423:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\login\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32800:(e,r,t)=>{Promise.resolve().then(t.bind(t,54845))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45952:(e,r,t)=>{Promise.resolve().then(t.bind(t,20423))},54845:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(60687),o=t(16189);t(43210);var n=t(55387),i=t(63213);function a(){let e=(0,o.useRouter)();return(0,s.jsx)(i.AuthProvider,{children:(0,s.jsx)(n.LoginForm,{onForgotPassword:()=>{console.log("Forgot password clicked")},onSignUp:()=>{console.log("Sign up clicked")},onSuccess:()=>{e.push("/")}})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58593:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>d,tree:()=>l});var s=t(65239),o=t(48088),n=t(88170),i=t.n(n),a=t(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);t.d(r,c);let l={children:["",{children:["[locale]",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20423)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/login/page",pathname:"/[locale]/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4825,625,9851,2482],()=>t(58593));module.exports=s})();