/**
 * @file Locale-Specific Layout - i18n Enabled Layout
 * @module app/[locale]/layout
 *
 * Layout component for locale-specific pages with i18n providers.
 * Handles RTL support, translations, and locale-specific metadata.
 */

import type { Metadata } from 'next';
import type { ReactNode } from 'react';

import { NextIntlClientProvider } from 'next-intl';
import { getMessages, getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';

import { type Locale, locales } from '@/i18n/config';
import { RTLProvider } from '@/lib/i18n/RTLProvider';

import ServerLayout from './layout-server';
import '../globals.css';

import 'leaflet/dist/leaflet.css';

interface LocaleLayoutProps {
  children: ReactNode;
  params: Promise<{ locale: string }>;
}

/**
 * Generate metadata for each locale
 */
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'metadata' });

  return {
    authors: [{ name: 'WorkHub Team' }],
    description: t('description'),
    keywords: t('keywords').split(','),
    openGraph: {
      description: t('description'),
      locale: locale === 'ar-IQ' ? 'ar_IQ' : 'en_US',
      title: t('title'),
      type: 'website',
    },
    robots: 'index, follow',
    title: t('title'),
  };
}

/**
 * Generate static params for all supported locales
 */
export function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

/**
 * Locale-Specific Layout Component
 *
 * Provides i18n context and RTL support for all pages within a locale.
 * Validates locale and sets up translation providers.
 */
export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params;

  // Validate locale
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  // Get messages for the locale
  const messages = await getMessages();

  return (
    <html className="h-full" lang={locale} suppressHydrationWarning>
      <body className="flex min-h-screen flex-col font-sans antialiased">
        <NextIntlClientProvider messages={messages}>
          <RTLProvider>
            <ServerLayout>{children}</ServerLayout>
          </RTLProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
