{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_40252751._.js", "server/edge/chunks/[root-of-the-server]__3b81985b._.js", "server/edge/chunks/edge-wrapper_091b4d48.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+4y0UnvznaGdUrB5ErKZiXmNYD7+AUYbicNS4ko67b0=", "__NEXT_PREVIEW_MODE_ID": "6c144ff1f51487d33e600fb424f2c720", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4f462497c80451db5761e09a009c978c44cacb77b283a81a8ec7c5262e2624c8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "82cf9df7c6e888d27e2b400dc773411414350fa274ef3ebf30530df4bc05e16e"}}}, "sortedMiddleware": ["/"], "functions": {}}