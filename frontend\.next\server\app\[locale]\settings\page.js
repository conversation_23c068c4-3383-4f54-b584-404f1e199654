(()=>{var e={};e.id=2455,e.ids=[2455],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12662:(e,s,r)=>{"use strict";r.d(s,{AppBreadcrumb:()=>m});var t=r(60687),a=r(85814),i=r.n(a),n=r(16189),c=r(43210),l=r.n(c),o=r(70640),d=r(22482);function m({className:e,homeHref:s="/",homeLabel:r="Dashboard",showContainer:a=!0}){let c=(0,n.usePathname)(),m=c?c.split("/").filter(Boolean):[],u=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let s={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return s[e]?s[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},x=m.map((e,s)=>{let r="/"+m.slice(0,s+1).join("/"),a=s===m.length-1,n=u(e);return(0,t.jsxs)(l().Fragment,{children:[(0,t.jsx)(o.BreadcrumbItem,{children:a?(0,t.jsx)(o.BreadcrumbPage,{className:"font-medium text-foreground",children:n}):(0,t.jsx)(o.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:r,children:n})})}),!a&&(0,t.jsx)(o.BreadcrumbSeparator,{})]},r)}),p=(0,t.jsx)(o.Breadcrumb,{className:(0,d.cn)("text-sm",e),children:(0,t.jsxs)(o.BreadcrumbList,{className:"flex-wrap",children:[(0,t.jsx)(o.BreadcrumbItem,{children:(0,t.jsx)(o.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:r})})}),m.length>0&&(0,t.jsx)(o.BreadcrumbSeparator,{}),x]})});return a?(0,t.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"flex items-center",children:p})}):p}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},48880:(e,s,r)=>{Promise.resolve().then(r.bind(r,78857))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58679:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var t=r(60687),a=r(58369),i=r(88853),n=r(10218);r(43210);var c=r(8639),l=r(33938),o=r(12662),d=r(29523),m=r(44493),u=r(64968);function x(){let{dashboardLayout:e,fontSize:s,notificationsEnabled:r,resetPreferences:x,tableDensity:p}=(0,u.useUiPreferences)(),{theme:h}=(0,n.D)();return(0,t.jsxs)("div",{className:"container mx-auto space-y-6 py-8",children:[(0,t.jsx)(o.AppBreadcrumb,{}),(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"flex items-center gap-2 text-3xl font-bold",children:[(0,t.jsx)(a.A,{className:"size-8"}),"Settings"]}),(0,t.jsx)("p",{className:"mt-1 text-muted-foreground",children:"Customize your WorkHub experience and preferences"})]})}),(0,t.jsxs)("div",{className:"grid gap-8",children:[(0,t.jsxs)("section",{children:[(0,t.jsxs)("h2",{className:"mb-4 flex items-center gap-2 text-2xl font-semibold",children:[(0,t.jsx)(i.A,{className:"size-6"}),"Theme Preferences"]}),(0,t.jsx)(l.LG,{})]}),(0,t.jsxs)("section",{children:[(0,t.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Display Preferences"}),(0,t.jsx)(c.uq,{})]}),(0,t.jsxs)("section",{children:[(0,t.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Current Configuration"}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"Settings Summary"}),(0,t.jsx)(m.BT,{children:"Overview of your current preferences and settings"})]}),(0,t.jsxs)(m.Wu,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Appearance"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Theme:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:h||"system"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Font Size:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:s})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Notifications"}),(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Enabled:"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:r?"Yes":"No"})]})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Layout"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Table Density:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:p})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Dashboard:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:e})]})]})]})]}),(0,t.jsx)("div",{className:"mt-6 border-t pt-6",children:(0,t.jsxs)(d.$,{className:"flex items-center gap-2",onClick:x,variant:"outline",children:[(0,t.jsx)(a.A,{className:"size-4"}),"Reset All to Defaults"]})})]})]})]}),(0,t.jsxs)("section",{children:[(0,t.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Help & Information"}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"About Settings"}),(0,t.jsx)(m.BT,{children:"Information about how settings work in WorkHub"})]}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-2 font-medium",children:"Font Size"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Font size settings apply globally across the entire application. Changes are saved automatically and will persist across browser sessions."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-2 font-medium",children:"Accessibility"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use larger font sizes for better readability. All font size changes maintain proper contrast ratios and accessibility standards."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-2 font-medium",children:"Performance"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Settings are stored locally in your browser and synchronized across tabs. No server requests are made when changing preferences."})]})]})]})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69795:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},70640:(e,s,r)=>{"use strict";r.d(s,{Breadcrumb:()=>l,BreadcrumbItem:()=>d,BreadcrumbLink:()=>m,BreadcrumbList:()=>o,BreadcrumbPage:()=>u,BreadcrumbSeparator:()=>x});var t=r(60687),a=r(8730),i=r(74158),n=(r(69795),r(43210)),c=r(22482);let l=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("nav",{"aria-label":"breadcrumb",className:(0,c.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:r,...s}));l.displayName="Breadcrumb";let o=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("ol",{className:(0,c.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:r,...s}));o.displayName="BreadcrumbList";let d=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("li",{className:(0,c.cn)("inline-flex items-center gap-1.5",e),ref:r,...s}));d.displayName="BreadcrumbItem";let m=n.forwardRef(({asChild:e,className:s,...r},i)=>{let n=e?a.DX:"a";return(0,t.jsx)(n,{className:(0,c.cn)("transition-colors hover:text-foreground",s),ref:i,...r})});m.displayName="BreadcrumbLink";let u=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,c.cn)("font-normal text-foreground",e),ref:r,role:"link",...s}));u.displayName="BreadcrumbPage";let x=({children:e,className:s,...r})=>(0,t.jsx)("span",{"aria-hidden":"true",className:(0,c.cn)("[&>svg]:size-3.5",s),role:"presentation",...r,children:e??(0,t.jsx)(i.A,{className:"size-4"})});x.displayName="BreadcrumbSeparator"},71869:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),c=r(30893),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);r.d(s,l);let o={children:["",{children:["[locale]",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,78857)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\settings\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/settings/page",pathname:"/[locale]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},74075:e=>{"use strict";e.exports=require("zlib")},78857:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\settings\\page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83728:(e,s,r)=>{Promise.resolve().then(r.bind(r,58679))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,4825,625,9851,2482],()=>r(71869));module.exports=t})();