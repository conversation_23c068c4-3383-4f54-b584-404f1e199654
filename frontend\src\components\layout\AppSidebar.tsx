/**
 * @file Modern sidebar navigation component for the expanding WorkHub application
 * @module components/layout/AppSidebar
 */

'use client';

import {
  Settings as AdminIcon,
  BarChart3,
  Briefcase,
  ChevronDown,
  ChevronRight,
  ClipboardList,
  Users as EmployeesIcon,
  FolderOpen,
  History,
  Home,
  Layers,
  Monitor,
  Car as MyVehiclesIcon,
  Search,
  Shield,
  Truck,
  Wrench,
  Building2 as WorkHubLogoIcon,
} from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface NavItem {
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  label: string;
  badge?: string;
  isNew?: boolean;
}

interface NavSection {
  title: string;
  items: NavItem[];
  defaultOpen?: boolean;
}

interface AppSidebarProps {
  className?: string;
  collapsed?: boolean;
  onToggle?: () => void;
}

// Move navigationSections inside component to access translations
const createNavigationSections = (t: any): NavSection[] => [
  {
    title: t('sections.overview'),
    defaultOpen: true,
    items: [
      { href: '/', icon: Home, label: t('dashboard') },
      { href: '/reports', icon: BarChart3, label: t('analytics'), isNew: true },
    ],
  },
  {
    title: t('sections.operations'),
    defaultOpen: true,
    items: [
      { href: '/vehicles', icon: MyVehiclesIcon, label: t('fleet') },
      { href: '/service-history', icon: History, label: t('maintenance') },
      { href: '/service-records', icon: Wrench, label: t('serviceRecords') },
    ],
  },
  {
    title: t('sections.workforce'),
    defaultOpen: false,
    items: [
      { href: '/employees', icon: EmployeesIcon, label: t('teamMembers') },
      { href: '/delegations', icon: Briefcase, label: t('projects') },
      { href: '/tasks', icon: ClipboardList, label: t('tasks') },
    ],
  },
  {
    title: t('sections.system'),
    defaultOpen: false,
    items: [
      { href: '/reliability', icon: Monitor, label: t('monitoring') },
      { href: '/admin', icon: AdminIcon, label: t('administration') },
    ],
  },
];

export const AppSidebar: React.FC<AppSidebarProps> = ({
  className,
  collapsed = false,
}) => {
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = useState('');
  const t = useTranslations('navigation');
  const navigationSections = createNavigationSections(t);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(navigationSections.filter(s => s.defaultOpen).map(s => s.title))
  );

  const toggleSection = (title: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(title)) {
      newExpanded.delete(title);
    } else {
      newExpanded.add(title);
    }
    setExpandedSections(newExpanded);
  };

  const filteredSections = navigationSections
    .map(section => ({
      ...section,
      items: section.items.filter(item =>
        item.label.toLowerCase().includes(searchQuery.toLowerCase())
      ),
    }))
    .filter(section => section.items.length > 0);

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname?.startsWith(href);
  };

  if (collapsed) {
    return (
      <aside
        className={cn('flex w-16 flex-col border-r bg-background', className)}
      >
        <div className="flex h-16 items-center justify-center border-b">
          <Link href="/" className="flex items-center">
            <WorkHubLogoIcon className="size-6 text-primary" />
          </Link>
        </div>
        <div className="flex-1 overflow-y-auto">
          <div className="space-y-2 p-2">
            {navigationSections
              .flatMap(section => section.items)
              .map(item => (
                <Button
                  key={item.href}
                  asChild
                  variant={isActive(item.href) ? 'secondary' : 'ghost'}
                  size="icon"
                  className="size-12"
                >
                  <Link href={item.href} title={item.label}>
                    <item.icon className="size-5" />
                  </Link>
                </Button>
              ))}
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside
      className={cn('flex w-64 flex-col border-r bg-background', className)}
    >
      {/* Header */}
      <div className="flex h-16 items-center border-b px-6">
        <Link
          href="/"
          className="flex items-center space-x-2 text-lg font-semibold transition-opacity hover:opacity-80"
        >
          <WorkHubLogoIcon className="size-6 text-primary" />
          <span>WorkHub</span>
        </Link>
      </div>

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={t('search.placeholder')}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {/* Navigation - Remove ScrollArea to prevent double scrollbar */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-2 p-4">
          {filteredSections.map(section => {
            const isExpanded = expandedSections.has(section.title);
            const hasVisibleItems = section.items.length > 0;

            if (!hasVisibleItems) return null;

            return (
              <div key={section.title} className="space-y-1">
                <Button
                  variant="ghost"
                  className="w-full justify-between px-2 py-1.5 text-xs font-medium uppercase text-muted-foreground hover:text-foreground"
                  onClick={() => toggleSection(section.title)}
                >
                  <span>{section.title}</span>
                  {isExpanded ? (
                    <ChevronDown className="size-3" />
                  ) : (
                    <ChevronRight className="size-3" />
                  )}
                </Button>

                {isExpanded && (
                  <div className="ml-2 space-y-1">
                    {section.items.map(item => (
                      <Button
                        key={item.href}
                        asChild
                        variant={isActive(item.href) ? 'secondary' : 'ghost'}
                        className="w-full justify-start px-3 py-2"
                      >
                        <Link
                          href={item.href}
                          className="flex items-center space-x-3"
                        >
                          <item.icon className="size-4" />
                          <span className="flex-1">{item.label}</span>
                          {item.isNew && (
                            <span className="rounded bg-primary px-1.5 py-0.5 text-xs text-primary-foreground">
                              {t('badges.new')}
                            </span>
                          )}
                          {item.badge && (
                            <span className="rounded bg-muted px-1.5 py-0.5 text-xs">
                              {item.badge}
                            </span>
                          )}
                        </Link>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="border-t p-4">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>v2.1.0</span>
          <Link href="/settings" className="hover:text-foreground">
            Settings
          </Link>
        </div>
      </div>
    </aside>
  );
};
