(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2494,6762],{6560:(e,s,a)=>{"use strict";a.d(s,{r:()=>n});var r=a(95155),t=a(50172),l=a(12115),d=a(30285),i=a(54036);let n=l.forwardRef((e,s)=>{let{actionType:a="primary",asChild:l=!1,children:n,className:c,disabled:o,icon:m,isLoading:x=!1,loadingText:u,...f}=e,{className:p,variant:g}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[a];return(0,r.jsx)(d.$,{asChild:l,className:(0,i.cn)(p,c),disabled:x||o,ref:s,variant:g,...f,children:x?(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,r.jsx)(t.A,{className:"mr-2 size-4 animate-spin"}),u||n]}):(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,r.jsx)("span",{className:"mr-2",children:m}),n]})})});n.displayName="ActionButton"},17432:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var r=a(95155),t=a(41784),l=a(83343),d=a(31949),i=a(35079),n=a(18763),c=a(77223),o=a(83662),m=a(98328),x=a(37648),u=a(50286),f=a(15300),p=a(28328),g=a(8376),h=a(91721),j=a(13896),N=a(24371),y=a(40320),b=a(6874),v=a.n(b),w=a(35695),k=a(12115),A=a(90010),E=a(89440),z=a(91394),C=a(26126),I=a(30285),R=a(66695),T=a(85057),B=a(77023),L=a(95647),D=a(59409),P=a(22346),_=a(53712),S=a(83761),F=a(61051),U=a(54036),q=a(99673);let V=e=>{switch(e){case"Assigned":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";case"Cancelled":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";case"Completed":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"In_Progress":return"bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30";case"Pending":return"bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},Z=e=>{switch(e){case"High":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";case"Low":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"Medium":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},M=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"N/A";try{return(0,t.GP)((0,l.H)(e),s?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}};function $(){let e=(0,w.useParams)(),s=(0,w.useRouter)(),{showEntityDeleted:a,showEntityDeletionError:t,showEntityUpdated:l,showEntityUpdateError:y,showFormError:b,showFormSuccess:z}=(0,_.O_)("task"),q=null==e?void 0:e.id,{data:$,error:J,isLoading:Y,refetch:K}=(0,F.xo)(q),{data:W}=(0,S.nR)(),X=(null==W?void 0:W.filter(e=>"Active"===e.status))||[],{mutateAsync:G}=(0,F.AK)(),{mutateAsync:Q}=(0,F.K)(),[ee,es]=(0,k.useState)(""),ea=async()=>{if(null==$?void 0:$.id)try{await G($.id);let e={name:$.description.slice(0,30)+($.description.length>30?"...":""),title:$.description.slice(0,30)+($.description.length>30?"...":"")};a(e),s.push("/tasks")}catch(e){console.error("Error deleting task:",e),t(e.message||"Failed to delete task. Please try again.")}},er=async()=>{if((null==$?void 0:$.id)&&ee)try{await Q({data:{driverEmployeeId:Number.parseInt(ee)},id:$.id}),l({name:"Task assigned to employee ID ".concat(ee),title:"Task assigned to employee ID ".concat(ee)}),K(),es("")}catch(e){console.error("Error assigning task:",e),y(e.message||"Failed to assign task. Please try again.")}},et=async e=>{if(null==$?void 0:$.id){let s={};if($.driverEmployeeId===e)s.driverEmployeeId=void 0;else if($.staffEmployeeId===e)return void b("Cannot unassign the primary staff member directly. Please reassign.",{errorTitle:"Action Not Allowed"});else return void b("Employee not found in task assignments.");if(0===Object.keys(s).length)return void z({successDescription:"No changes to apply for unassignment.",successTitle:"Info"});try{await Q({data:s,id:$.id}),l({name:"Employee unassigned from task",title:"Employee unassigned from task"}),K()}catch(e){console.error("Error updating task assignment:",e),y(e.message||"Failed to unassign task. Please try again.")}}},el=(null==$?void 0:$.status)==="Completed"||(null==$?void 0:$.status)==="Cancelled";return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(E.AppBreadcrumb,{}),(0,r.jsx)(B.gO,{data:$,emptyComponent:(0,r.jsxs)("div",{className:"py-10 text-center",children:[(0,r.jsx)(L.z,{icon:d.A,title:"Task Not Found"}),(0,r.jsx)("p",{className:"mb-4",children:"The requested task could not be found."})]}),error:J?J.message:null,isLoading:null!=Y&&Y,loadingComponent:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(L.z,{icon:i.A,title:"Loading Task..."}),(0,r.jsx)(B.jt,{count:1,variant:"card"}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsx)(B.jt,{className:"lg:col-span-2",count:1,variant:"card"}),(0,r.jsx)(B.jt,{count:1,variant:"card"})]})]}),onRetry:K,children:e=>{var s,a;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(L.z,{description:"Manage details and assignment for this task.",icon:i.A,title:e.description||"Task Details",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,r.jsx)(I.$,{asChild:!0,className:"gap-2",variant:"default",children:(0,r.jsxs)(v(),{href:"/tasks/".concat(e.id,"/edit"),children:[(0,r.jsx)(n.A,{className:"size-4"}),"Edit"]})}),(0,r.jsxs)(A.Lt,{children:[(0,r.jsx)(A.tv,{asChild:!0,children:(0,r.jsxs)(I.$,{className:"gap-2",variant:"destructive",children:[(0,r.jsx)(c.A,{className:"size-4"}),"Delete Task"]})}),(0,r.jsxs)(A.EO,{children:[(0,r.jsxs)(A.wd,{children:[(0,r.jsx)(A.r7,{children:"Are you sure?"}),(0,r.jsx)(A.$v,{children:"This action cannot be undone. This will permanently delete the task."})]}),(0,r.jsxs)(A.ck,{children:[(0,r.jsx)(A.Zr,{children:"Cancel"}),(0,r.jsx)(A.Rx,{className:"bg-destructive hover:bg-destructive/90",onClick:ea,children:"Delete"})]})]})]})]})}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsxs)(R.Zp,{className:"shadow-sm lg:col-span-2",children:[(0,r.jsx)(R.aR,{className:"border-b",children:(0,r.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(R.ZB,{className:"text-2xl font-bold leading-tight",children:[e.description," "]})," "]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(C.E,{className:(0,U.cn)("justify-center",V(e.status)),children:e.status}),(0,r.jsxs)(C.E,{className:(0,U.cn)("justify-center",Z(e.priority)),variant:"outline",children:[e.priority," Priority"]})]})]})}),(0,r.jsxs)(R.Wu,{className:"space-y-6 p-6",children:[(0,r.jsx)(O,{icon:o.A,label:"Location",value:e.location}),(0,r.jsx)(O,{icon:m.A,label:"Start Date & Time",value:M(e.dateTime,!0)}),(0,r.jsx)(O,{icon:x.A,label:"Estimated Duration",value:"".concat(e.estimatedDuration," minutes")}),e.deadline&&(0,r.jsx)(O,{icon:x.A,label:"Deadline",value:M(e.deadline,!1)}),e.requiredSkills&&e.requiredSkills.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required Skills"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.requiredSkills.map((e,s)=>(0,r.jsx)(C.E,{className:"text-xs",variant:"secondary",children:e},s))})]}),e.notes&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Notes"})]}),(0,r.jsx)("p",{className:"rounded-lg bg-muted/30 p-3 text-sm text-muted-foreground",children:e.notes})]}),e.vehicleId&&(0,r.jsx)(O,{icon:p.A,label:"Assigned Vehicle",children:(0,r.jsxs)("div",{className:"rounded-lg border bg-muted/30 p-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.vehicle?"".concat(e.vehicle.make," ").concat(e.vehicle.model):"Vehicle ID: ".concat(e.vehicleId)}),(null==(s=e.vehicle)?void 0:s.licensePlate)&&(0,r.jsxs)("p",{className:"mt-1 text-xs text-muted-foreground",children:["License Plate: ",e.vehicle.licensePlate]}),(null==(a=e.vehicle)?void 0:a.year)&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Year: ",e.vehicle.year]})]})}),e.subtasks&&e.subtasks.length>0&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(P.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"mb-3 flex items-center gap-2 text-lg font-semibold",children:[(0,r.jsx)(i.A,{className:"size-4"}),"Sub-Tasks"]}),(0,r.jsx)("ul",{className:"space-y-2",children:e.subtasks.map(e=>(0,r.jsxs)("li",{className:(0,U.cn)("flex items-center gap-2 text-sm",e.completed&&"line-through text-muted-foreground"),children:[e.completed?(0,r.jsx)(g.A,{className:"size-4 text-green-500"}):(0,r.jsx)("div",{className:"size-4 rounded-sm border"}),e.title]},e.id))})]})]})]}),(0,r.jsx)(R.wL,{className:"flex items-center justify-center border-t bg-muted/20",children:(0,r.jsxs)("div",{className:"flex w-full flex-col gap-2 text-xs text-muted-foreground sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"font-medium",children:"Created:"}),(0,r.jsx)("span",{children:M(e.createdAt,!0)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"font-medium",children:"Updated:"}),(0,r.jsx)("span",{children:M(e.updatedAt,!0)})]})]})})]}),(0,r.jsxs)(R.Zp,{className:"h-fit shadow-sm",children:[(0,r.jsx)(R.aR,{children:(0,r.jsxs)(R.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"size-5 text-primary"}),"Task Assignment"]})}),(0,r.jsxs)(R.Wu,{className:"space-y-4",children:[e.staffEmployeeId&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Staff Assignment"})]}),(0,r.jsx)(H,{employee:e.staffEmployee,employeeId:e.staffEmployeeId,isTaskCompleted:el,onUnassign:()=>{e.staffEmployeeId&&et(e.staffEmployeeId)}})]}),e.driverEmployeeId&&(0,r.jsxs)(r.Fragment,{children:[e.staffEmployeeId&&(0,r.jsx)(P.w,{className:"my-4"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Driver Assignment"})]}),(0,r.jsx)(H,{employee:e.driverEmployee,employeeId:e.driverEmployeeId,isTaskCompleted:el,onUnassign:()=>{e.driverEmployeeId&&et(e.driverEmployeeId)}})]})]}),!e.staffEmployeeId&&!e.driverEmployeeId&&(0,r.jsx)("div",{className:"py-4 text-center",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."})}),!el&&!(e.staffEmployeeId||e.driverEmployeeId)&&(0,r.jsxs)("div",{className:"space-y-4 border-t pt-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Assign task to an employee:"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(T.J,{className:"text-sm font-medium",htmlFor:"assignee-select",children:"Select Employee"}),(0,r.jsxs)(D.l6,{onValueChange:es,value:ee,children:[(0,r.jsx)(D.bq,{children:(0,r.jsx)(D.yv,{placeholder:"Select an employee"})}),(0,r.jsx)(D.gC,{children:X.length>0?X.map(e=>(0,r.jsxs)(D.eb,{value:String(e.id),children:[e.fullName||e.name," (",e.position||e.role,")"]},e.id)):(0,r.jsx)("div",{className:"p-2 text-sm text-muted-foreground",children:"No available employees."})})]}),(0,r.jsxs)(I.$,{className:"w-full gap-2",disabled:!ee,onClick:er,children:[(0,r.jsx)(j.A,{className:"size-4"}),"Assign to Selected Employee"]})]})]}),"Completed"===e.status&&(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20",children:[(0,r.jsx)(g.A,{className:"size-4 text-green-600"}),(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-400",children:"Task completed."})]}),"Cancelled"===e.status&&(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20",children:[(0,r.jsx)(N.A,{className:"size-4 text-red-600"}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:"Task cancelled."})]})]})]})]})]})}})]})}function H(e){let{employee:s,employeeId:a,isTaskCompleted:t,onUnassign:l}=e;if(!a&&0!==a)return(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."});if(!s)return(0,r.jsxs)("p",{className:"text-sm text-destructive",children:["Could not load assignee (ID: ",a,")."]});let d=(0,q.DV)(s);return(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border bg-muted/30 p-2",children:[(0,r.jsx)(z.eu,{className:"size-8",children:(0,r.jsx)(z.q5,{className:"text-xs font-medium",children:d.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(v(),{className:"text-sm font-medium hover:text-primary",href:"/employees/".concat(s.employeeId),children:d}),(0,r.jsx)("span",{className:"text-xs capitalize text-muted-foreground",children:s.position||s.role})]}),!t&&l&&a&&(0,r.jsx)(I.$,{className:"size-6 p-0 text-muted-foreground hover:text-destructive",onClick:()=>l(a),size:"sm",variant:"ghost",children:(0,r.jsx)(y.A,{className:"size-3"})})]})})}function O(e){let{children:s,className:a,icon:t,label:l,value:d}=e;return void 0!==d||s?(0,r.jsxs)("div",{className:(0,U.cn)("flex items-start gap-3",a),children:[(0,r.jsx)(t,{className:"mt-1 size-4 shrink-0 text-muted-foreground"}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:l}),null!=d&&(0,r.jsx)("p",{className:"text-base font-medium text-foreground",children:d}),s]})]}):null}},19018:(e,s,a)=>{"use strict";a.d(s,{Breadcrumb:()=>n,BreadcrumbItem:()=>o,BreadcrumbLink:()=>m,BreadcrumbList:()=>c,BreadcrumbPage:()=>x,BreadcrumbSeparator:()=>u});var r=a(95155),t=a(99708),l=a(73158),d=(a(3561),a(12115)),i=a(54036);let n=d.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("nav",{"aria-label":"breadcrumb",className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",a),ref:s,...t})});n.displayName="Breadcrumb";let c=d.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("ol",{className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",a),ref:s,...t})});c.displayName="BreadcrumbList";let o=d.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("li",{className:(0,i.cn)("inline-flex items-center gap-1.5",a),ref:s,...t})});o.displayName="BreadcrumbItem";let m=d.forwardRef((e,s)=>{let{asChild:a,className:l,...d}=e,n=a?t.DX:"a";return(0,r.jsx)(n,{className:(0,i.cn)("transition-colors hover:text-foreground",l),ref:s,...d})});m.displayName="BreadcrumbLink";let x=d.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,i.cn)("font-normal text-foreground",a),ref:s,role:"link",...t})});x.displayName="BreadcrumbPage";let u=e=>{let{children:s,className:a,...t}=e;return(0,r.jsx)("span",{"aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",a),role:"presentation",...t,children:null!=s?s:(0,r.jsx)(l.A,{className:"size-4"})})};u.displayName="BreadcrumbSeparator"},22346:(e,s,a)=>{"use strict";a.d(s,{w:()=>i});var r=a(95155),t=a(87489),l=a(12115),d=a(54036);let i=l.forwardRef((e,s)=>{let{className:a,decorative:l=!0,orientation:i="horizontal",...n}=e;return(0,r.jsx)(t.b,{className:(0,d.cn)("shrink-0 bg-border","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",a),decorative:l,orientation:i,ref:s,...n})});i.displayName=t.b.displayName},26126:(e,s,a)=>{"use strict";a.d(s,{E:()=>i});var r=a(95155),t=a(74466);a(12115);var l=a(54036);let d=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function i(e){let{className:s,variant:a,...t}=e;return(0,r.jsx)("div",{className:(0,l.cn)(d({variant:a}),s),...t})}},46040:(e,s,a)=>{Promise.resolve().then(a.bind(a,17432))},55365:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>n,TN:()=>o,XL:()=>c});var r=a(95155),t=a(74466),l=a(12115),d=a(54036);let i=(0,t.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),n=l.forwardRef((e,s)=>{let{className:a,variant:t,...l}=e;return(0,r.jsx)("div",{className:(0,d.cn)(i({variant:t}),a),ref:s,role:"alert",...l})});n.displayName="Alert";let c=l.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h5",{className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",a),ref:s,...t})});c.displayName="AlertTitle";let o=l.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,d.cn)("text-sm [&_p]:leading-relaxed",a),ref:s,...t})});o.displayName="AlertDescription"},59409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>x,eb:()=>g,gC:()=>p,l6:()=>o,yv:()=>m});var r=a(95155),t=a(31992),l=a(79556),d=a(77381),i=a(10518),n=a(12115),c=a(54036);let o=t.bL;t.YJ;let m=t.WT,x=n.forwardRef((e,s)=>{let{children:a,className:d,...i}=e;return(0,r.jsxs)(t.l9,{className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",d),ref:s,...i,children:[a,(0,r.jsx)(t.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"size-4 opacity-50"})})]})});x.displayName=t.l9.displayName;let u=n.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.PP,{className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),ref:s,...l,children:(0,r.jsx)(d.A,{className:"size-4"})})});u.displayName=t.PP.displayName;let f=n.forwardRef((e,s)=>{let{className:a,...d}=e;return(0,r.jsx)(t.wn,{className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),ref:s,...d,children:(0,r.jsx)(l.A,{className:"size-4"})})});f.displayName=t.wn.displayName;let p=n.forwardRef((e,s)=>{let{children:a,className:l,position:d="popper",...i}=e;return(0,r.jsx)(t.ZL,{children:(0,r.jsxs)(t.UC,{className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",l),position:d,ref:s,...i,children:[(0,r.jsx)(u,{}),(0,r.jsx)(t.LM,{className:(0,c.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(f,{})]})})});p.displayName=t.UC.displayName,n.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.JU,{className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),ref:s,...l})}).displayName=t.JU.displayName;let g=n.memo(n.forwardRef((e,s)=>{let{children:a,className:l,...d}=e,o=n.useCallback(e=>{"function"==typeof s?s(e):s&&(s.current=e)},[s]);return(0,r.jsxs)(t.q7,{className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),ref:o,...d,children:[(0,r.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(t.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(t.p4,{children:a})]})}));g.displayName=t.q7.displayName,n.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.wv,{className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),ref:s,...l})}).displayName=t.wv.displayName},68856:(e,s,a)=>{"use strict";a.d(s,{E:()=>l});var r=a(95155),t=a(54036);function l(e){let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",s),...a})}},77023:(e,s,a)=>{"use strict";a.d(s,{gO:()=>x,jt:()=>g,pp:()=>u});var r=a(95155),t=a(11133),l=a(50172);a(12115);var d=a(6560),i=a(55365),n=a(68856),c=a(54036);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:s,className:a,data:t,emptyComponent:l,error:d,errorComponent:i,isLoading:n,loadingComponent:o,onRetry:m}=e;return n?o||(0,r.jsx)(p,{...a&&{className:a},text:"Loading..."}):d?i||(0,r.jsx)(f,{...a&&{className:a},message:d,...m&&{onRetry:m}}):!t||Array.isArray(t)&&0===t.length?l||(0,r.jsx)("div",{className:(0,c.cn)("text-center py-8",a),children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{className:a,children:s(t)})}function u(e){let{className:s,description:a,icon:t,primaryAction:l,secondaryAction:i,title:n}=e;return(0,r.jsxs)("div",{className:(0,c.cn)("space-y-6 text-center py-12",s),children:[t&&(0,r.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,r.jsx)(t,{className:"h-10 w-10 text-muted-foreground"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:n}),a&&(0,r.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:a})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[l&&(0,r.jsx)(d.r,{actionType:"primary",asChild:!!l.href,icon:l.icon,onClick:l.onClick,children:l.href?(0,r.jsx)("a",{href:l.href,children:l.label}):l.label}),i&&(0,r.jsx)(d.r,{actionType:"tertiary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,r.jsx)("a",{href:i.href,children:i.label}):i.label})]})]})}function f(e){let{className:s,message:a,onRetry:n}=e;return(0,r.jsxs)(i.Fc,{className:(0,c.cn)("my-4",s),variant:"destructive",children:[(0,r.jsx)(t.A,{className:"size-4"}),(0,r.jsx)(i.XL,{children:"Error"}),(0,r.jsx)(i.TN,{children:(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:a}),n&&(0,r.jsx)(d.r,{actionType:"tertiary",icon:(0,r.jsx)(l.A,{className:"size-4"}),onClick:n,size:"sm",children:"Try Again"})]})})]})}function p(e){let{className:s,fullPage:a=!1,size:t="md",text:d}=e;return(0,r.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",a&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(l.A,{className:(0,c.cn)("animate-spin text-primary",o[t])}),d&&(0,r.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",m[t]),children:d})]})})}function g(e){let{className:s,count:a=1,testId:t="loading-skeleton",variant:l="default"}=e;return"card"===l?(0,r.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",s),"data-testid":t,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,r.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsx)(n.E,{className:"mb-1 h-7 w-3/4"}),(0,r.jsx)(n.E,{className:"mb-3 h-4 w-1/2"}),(0,r.jsx)(n.E,{className:"my-3 h-px w-full"}),(0,r.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.E,{className:"mr-2.5 size-5 rounded-full"}),(0,r.jsx)(n.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===l?(0,r.jsxs)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":t,children:[(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsx)(n.E,{className:"h-8 flex-1"},s))}),Array(a).fill(0).map((e,s)=>(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsx)(n.E,{className:"h-6 flex-1"},s))},s))]}):"list"===l?(0,r.jsx)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":t,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(n.E,{className:"size-12 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)(n.E,{className:"h-4 w-1/3"}),(0,r.jsx)(n.E,{className:"h-4 w-full"})]})]},s))}):"stats"===l?(0,r.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",s),"data-testid":t,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(n.E,{className:"h-5 w-1/3"}),(0,r.jsx)(n.E,{className:"size-5 rounded-full"})]}),(0,r.jsx)(n.E,{className:"mt-3 h-8 w-1/2"}),(0,r.jsx)(n.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,r.jsx)("div",{className:(0,c.cn)("space-y-2",s),"data-testid":t,children:Array(a).fill(0).map((e,s)=>(0,r.jsx)(n.E,{className:"h-5 w-full"},s))})}},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var r=a(95155),t=a(12115),l=a(40968),d=a(74466),i=a(54036);let n=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(l.b,{ref:s,className:(0,i.cn)(n(),a),...t})});c.displayName=l.b.displayName},89440:(e,s,a)=>{"use strict";a.d(s,{AppBreadcrumb:()=>o});var r=a(95155),t=a(6874),l=a.n(t),d=a(35695),i=a(12115),n=a(19018),c=a(54036);function o(e){let{className:s,homeHref:a="/",homeLabel:t="Dashboard",showContainer:o=!0}=e,m=(0,d.usePathname)(),x=m?m.split("/").filter(Boolean):[],u=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let s={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return s[e]?s[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},f=x.map((e,s)=>{let a="/"+x.slice(0,s+1).join("/"),t=s===x.length-1,d=u(e);return(0,r.jsxs)(i.Fragment,{children:[(0,r.jsx)(n.BreadcrumbItem,{children:t?(0,r.jsx)(n.BreadcrumbPage,{className:"font-medium text-foreground",children:d}):(0,r.jsx)(n.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:a,children:d})})}),!t&&(0,r.jsx)(n.BreadcrumbSeparator,{})]},a)}),p=(0,r.jsx)(n.Breadcrumb,{className:(0,c.cn)("text-sm",s),children:(0,r.jsxs)(n.BreadcrumbList,{className:"flex-wrap",children:[(0,r.jsx)(n.BreadcrumbItem,{children:(0,r.jsx)(n.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:a,children:t})})}),x.length>0&&(0,r.jsx)(n.BreadcrumbSeparator,{}),f]})});return o?(0,r.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"flex items-center",children:p})}):p}},90010:(e,s,a)=>{"use strict";a.d(s,{$v:()=>g,EO:()=>x,Lt:()=>n,Rx:()=>h,Zr:()=>j,ck:()=>f,r7:()=>p,tv:()=>c,wd:()=>u});var r=a(95155),t=a(17649),l=a(12115),d=a(30285),i=a(54036);let n=t.bL,c=t.l9,o=t.ZL,m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l,ref:s})});m.displayName=t.hJ.displayName;let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsxs)(o,{children:[(0,r.jsx)(m,{}),(0,r.jsx)(t.UC,{className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),ref:s,...l})]})});x.displayName=t.UC.displayName;let u=e=>{let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...a})};u.displayName="AlertDialogHeader";let f=e=>{let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};f.displayName="AlertDialogFooter";let p=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.hE,{className:(0,i.cn)("text-lg font-semibold",a),ref:s,...l})});p.displayName=t.hE.displayName;let g=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.VY,{className:(0,i.cn)("text-sm text-muted-foreground",a),ref:s,...l})});g.displayName=t.VY.displayName;let h=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.rc,{className:(0,i.cn)((0,d.r)(),a),ref:s,...l})});h.displayName=t.rc.displayName;let j=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.ZD,{className:(0,i.cn)((0,d.r)({variant:"outline"}),"mt-2 sm:mt-0",a),ref:s,...l})});j.displayName=t.ZD.displayName},91394:(e,s,a)=>{"use strict";a.d(s,{BK:()=>n,eu:()=>i,q5:()=>c});var r=a(95155),t=a(54011),l=a(12115),d=a(54036);let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.bL,{className:(0,d.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),ref:s,...l})});i.displayName=t.bL.displayName;let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t._V,{className:(0,d.cn)("aspect-square h-full w-full",a),ref:s,...l})});n.displayName=t._V.displayName;let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(t.H4,{className:(0,d.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),ref:s,...l})});c.displayName=t.H4.displayName},95647:(e,s,a)=>{"use strict";a.d(s,{z:()=>t});var r=a(95155);function t(e){let{children:s,description:a,icon:t,title:l}=e;return(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,r.jsx)(t,{className:"size-8 text-primary"}),(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:l})]}),a&&(0,r.jsx)("p",{className:"mt-1 text-muted-foreground",children:a})]}),s&&(0,r.jsx)("div",{className:"flex items-center gap-2",children:s})]})}a(12115)},99673:(e,s,a)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function t(e){var s,a;if(null==(s=e.fullName)?void 0:s.trim())return e.fullName.trim();if(null==(a=e.name)?void 0:a.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(s," (Role)")}return"Unknown Employee"}function l(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function d(e){return e.replaceAll("_"," ")}a.d(s,{DV:()=>t,fZ:()=>r,s:()=>l,vq:()=>d})}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,6874,2700,7179,4036,4767,8950,3712,7515,1051,8441,1684,7358],()=>s(46040)),_N_E=e.O()}]);