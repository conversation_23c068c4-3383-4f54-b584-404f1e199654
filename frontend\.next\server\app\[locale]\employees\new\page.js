(()=>{var e={};e.id=430,e.ids=[430],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,r,t)=>{"use strict";t.d(r,{O_:()=>a,t6:()=>n});var s=t(43210),o=t(49278);function n(){let e=(0,s.useCallback)((e,r)=>o.JP.success(e,r),[]),r=(0,s.useCallback)((e,r)=>o.JP.error(e,r),[]),t=(0,s.useCallback)((e,r)=>o.JP.info(e,r),[]),n=(0,s.useCallback)(r=>e(r?.successTitle||"Success",r?.successDescription||"Operation completed successfully"),[e]),a=(0,s.useCallback)((e,t)=>{let s=e instanceof Error?e.message:e;return r(t?.errorTitle||"Error",t?.errorDescription||s||"An unexpected error occurred")},[r]);return{showSuccess:e,showError:r,showInfo:t,showFormSuccess:n,showFormError:a}}function a(e){let r;switch(e){case"employee":r=t(49278).Ok;break;case"vehicle":r=t(49278).G7;break;case"task":r=t(49278).z0;break;case"delegation":r=t(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,r){let{showFormSuccess:t,showFormError:a}=n(),i=r||(e?(0,o.iw)(e):null),l=(0,s.useCallback)(e=>i?i.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[i,t]),c=(0,s.useCallback)(e=>i?i.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[i,t]),u=(0,s.useCallback)(e=>i?i.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[i,t]),p=(0,s.useCallback)(e=>{if(i){let r=e instanceof Error?e.message:e;return i.entityCreationError(r)}return a(e,{errorTitle:"Creation Failed"})},[i,a]);return{showEntityCreated:l,showEntityUpdated:c,showEntityDeleted:u,showEntityCreationError:p,showEntityUpdateError:(0,s.useCallback)(e=>{if(i){let r=e instanceof Error?e.message:e;return i.entityUpdateError(r)}return a(e,{errorTitle:"Update Failed"})},[i,a]),showEntityDeletionError:(0,s.useCallback)(e=>{if(i){let r=e instanceof Error?e.message:e;return i.entityDeletionError(r)}return a(e,{errorTitle:"Deletion Failed"})},[i,a]),showFormSuccess:t,showFormError:a}}(void 0,r)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17294:(e,r,t)=>{Promise.resolve().then(t.bind(t,47738))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28399:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47738:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\employees\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\employees\\new\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58504:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),o=t(28399),n=t(16189);t(43210);var a=t(96342),i=t(48041),l=t(3940),c=t(19599);let u=()=>{let e=(0,n.useRouter)(),{showEntityCreated:r,showEntityCreationError:t}=(0,l.O_)("employee"),u=(0,c.Ar)(),p=async s=>{let o={availability:s.availability??null,contactEmail:s.contactEmail??null,contactInfo:s.contactInfo,contactMobile:s.contactMobile??null,contactPhone:s.contactPhone??null,department:s.department??null,employeeId:s.employeeId,fullName:s.fullName??null,generalAssignments:s.generalAssignments??[],hireDate:s.hireDate??null,name:s.name,notes:s.notes??null,position:s.position??null,profileImageUrl:s.profileImageUrl??null,role:s.role,shiftSchedule:s.shiftSchedule??null,skills:s.skills??[],status:s.status.replace(" ","_")};try{let t=await u.mutateAsync(o);r(t),e.push("/employees")}catch(e){console.error("Failed to add employee:",e),t(e.response?.data?.error||e.message||"An unexpected error occurred.")}};return(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(i.z,{description:"Enter the details for the new employee.",icon:o.A,title:"Add New Employee"}),u.error&&(0,s.jsxs)("div",{className:"relative mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700",role:"alert",children:[(0,s.jsx)("strong",{className:"font-bold",children:"Error: "}),(0,s.jsx)("span",{className:"block sm:inline",children:u.error?.message||"Failed to add employee."})]}),(0,s.jsx)(a.N,{isEditing:!1,isLoading:u.isPending,onSubmit:p})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78753:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>d,tree:()=>c});var s=t(65239),o=t(48088),n=t(88170),a=t.n(n),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c={children:["",{children:["[locale]",{children:["employees",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47738)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\employees\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\employees\\new\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/employees/new/page",pathname:"/[locale]/employees/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80342:(e,r,t)=>{Promise.resolve().then(t.bind(t,58504))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4825,625,9851,8390,2670,9275,6013,2482,9794,9599,5785,6342],()=>t(78753));module.exports=s})();