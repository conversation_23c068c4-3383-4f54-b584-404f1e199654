{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/i18n/config.ts"], "sourcesContent": ["/**\n * @file i18n Configuration\n * @module i18n/config\n *\n * Central configuration for internationalization settings.\n * Defines supported locales, default locale, and locale-specific settings.\n */\n\n/**\n * Supported locales in the application\n */\nexport const locales = ['en-US', 'ar-IQ'] as const;\n\n/**\n * Default locale for the application\n */\nexport const defaultLocale = 'en-US' as const;\n\n/**\n * Type for supported locales\n */\nexport type Locale = (typeof locales)[number];\n\n/**\n * RTL (Right-to-Left) locales\n */\nexport const rtlLocales = ['ar-IQ'] as const;\n\n/**\n * Check if a locale is RTL\n */\nexport function isRTLLocale(locale: string): boolean {\n  return rtlLocales.includes(locale as any);\n}\n\n/**\n * Locale display names for UI\n */\nexport const localeNames: Record<Locale, string> = {\n  'en-US': 'English',\n  'ar-IQ': 'العربية',\n};\n\n/**\n * Locale configuration for next-intl\n */\nexport const localeConfig = {\n  locales,\n  defaultLocale,\n  localePrefix: 'always' as const,\n};\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;;CAEC;;;;;;;;AACM,MAAM,UAAU;IAAC;IAAS;CAAQ;AAKlC,MAAM,gBAAgB;AAUtB,MAAM,aAAa;IAAC;CAAQ;AAK5B,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAKO,MAAM,cAAsC;IACjD,SAAS;IACT,SAAS;AACX;AAKO,MAAM,eAAe;IAC1B;IACA;IACA,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/page.tsx"], "sourcesContent": ["/**\n * @file Root Page - Locale Redirect\n * @module app/page\n *\n * Root page that redirects to the default locale.\n * This ensures all routes are properly localized.\n */\n\nimport { redirect } from 'next/navigation';\n\nimport { defaultLocale } from '@/i18n/config';\n\n/**\n * Root Page Component\n *\n * Redirects to the default locale to ensure all routes are localized.\n * This page should never be rendered as users will be redirected.\n */\nexport default function RootPage() {\n  redirect(`/${defaultLocale}`);\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AAAA;AAEA;;;AAQe,SAAS;IACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC,EAAE,qHAAA,CAAA,gBAAa,EAAE;AAC9B", "debugId": null}}]}